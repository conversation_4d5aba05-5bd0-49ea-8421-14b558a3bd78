# Dashboard Transformation Project

## Overview
Transform the existing landing page at `resources/views/landing.blade.php` into a comprehensive system dashboard by renaming it to 'dashboard' and implementing detailed specifications for system monitoring and administration.

## Primary Objective
Convert the current landing page into a professional system dashboard that provides administrators with comprehensive visibility into the eP CRM Integration system's health, scheduled tasks, available commands, and database connectivity.

## File Structure Changes

### Files to Rename/Move
- `resources/views/landing.blade.php` → `resources/views/dashboard.blade.php`

### New Files to Create

#### Views and Components
- `resources/views/components/dashboard/layout.blade.php` - Main dashboard wrapper with navigation
- `resources/views/components/dashboard/tab-container.blade.php` - Tab navigation and content switching logic
- `resources/views/components/dashboard/status-card.blade.php` - Reusable status display cards
- `resources/views/components/dashboard/data-table.blade.php` - Responsive table component with sorting
- `resources/views/components/dashboard/status-badge.blade.php` - Colored status indicators
- `resources/views/components/dashboard/metric-card.blade.php` - Numerical metric display cards
- `resources/views/components/dashboard/connection-status.blade.php` - Database connection status component

#### Controllers
- `app/Http/Controllers/DashboardController.php` - Main dashboard controller

#### Services
- `app/Services/DashboardService.php` - Dashboard business logic and data fetching

#### Tests
- `tests/Feature/DashboardTest.php` - Feature tests for dashboard functionality

## Implementation Requirements

### Dashboard Layout & Branding
- **System Title**: Display "eP CRM Integration" as the main header with professional typography
- **Layout Structure**: Clean, modern dashboard with:
  - Header section with system branding and current timestamp
  - Two-column top section for key metrics
  - Tab-based navigation below for detailed views
  - Responsive design (desktop, tablet, mobile)

### Top Section Layout (Two-Column Grid)

#### Left Column - System Status Overview
- Application environment badge (Production/Staging/Development) with color coding
- Laravel version and PHP version display
- System uptime and current server timestamp
- Memory usage and available disk space
- Primary database connection status with visual indicator
- Cache driver status (Redis/File/Database) and session driver status

#### Right Column - Database Connections Summary
- Tabular display: Connection Name, Database Type, Host, Database Name, Status, Primary Table Count
- Read all database connections from `config/database.php` and environment variables
- Test connectivity to each configured database connection
- Display connection parameters while masking sensitive credentials
- Show record counts for important tables in each database
- Status indicators (green: connected, red: failed, yellow: untested)

### Tab Navigation System (4 Tabs)

#### Tab 1: "Scheduled Tasks"
- Columns: Task Name, Cron Expression, Human-Readable Schedule, Description, Last Execution, Next Execution, Status
- Fetch data using Laravel's `Schedule` facade
- Display cron expressions with human-readable translations
- Show execution status with colored badges
- Include task frequency descriptions and estimated duration

#### Tab 2: "Console Commands"
- Columns: Command Signature, Description, Category/Namespace, Arguments, Options, Usage Example
- Dynamically populate using `Artisan::all()`
- Group commands by namespace
- Client-side search/filter functionality
- Copy-to-clipboard functionality for command signatures

#### Tab 3: "Database Details"
- Expanded view with detailed database information
- Columns: Connection, Type, Host:Port, Database, Tables Count, Total Records, Last Query Time, Connection Pool Status
- Show detailed connection parameters and performance metrics
- Database-specific information (versions, etc.)
- Recent query performance from Laravel's query log

#### Tab 4: "System Logs"
- Recent application logs with filtering capabilities
- Log levels: Emergency, Alert, Critical, Error, Warning, Notice, Info, Debug
- Include timestamp, log level, message preview, and context
- Log level filtering and search functionality
- Show log file sizes and rotation status

## Technical Specifications

### Styling & UI Framework
- Use Tailwind CSS exclusively with consistent design system
- CSS custom properties for theme colors:
  - `--primary: #3B82F6`
  - `--success: #10B981`
  - `--warning: #F59E0B`
  - `--danger: #EF4444`
- Responsive prefixes (sm:, md:, lg:, xl:) for mobile-first design
- Smooth transitions using Tailwind's transition utilities
- Proper focus states and ARIA labels for accessibility

### Backend Implementation

#### Controller Methods (DashboardController)
- `index()` - Main dashboard view
- `getSystemStatus()` - AJAX endpoint for real-time status
- `testDatabaseConnections()` - Database connectivity testing
- `getScheduledTasks()` - Fetch scheduled task information
- `getConsoleCommands()` - Retrieve Artisan commands

#### Service Methods (DashboardService)
- `getSystemMetrics()` - Gather system information
- `testAllDatabaseConnections()` - Test all configured databases
- `getScheduledTasksInfo()` - Parse Laravel schedule
- `getArtisanCommands()` - Retrieve and categorize commands
- `getRecentLogs()` - Parse application logs

### Data Fetching & Performance
- Implement Laravel caching for expensive operations:
  - Cache database connection tests for 10 minutes
  - Cache system metrics for 5 minutes
  - Cache Artisan command list for 1 hour
- Use Laravel's `DB::connection()->getPdo()` to test database connectivity
- Proper exception handling with try-catch blocks
- Use Laravel's `Log` facade for dashboard access and errors

### Security & Error Handling
- Never expose database passwords, API keys, or sensitive configuration
- Sanitize all output using Laravel's `{{ }}` syntax
- Implement proper authentication middleware if needed
- Use Laravel's CSRF protection for AJAX requests
- Add rate limiting to prevent dashboard abuse
- Graceful fallbacks when services are unavailable

### Route Configuration
Update `routes/web.php`:
```php
Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
Route::get('/dashboard/status', [DashboardController::class, 'getSystemStatus'])->name('dashboard.status');
```

### JavaScript Functionality
- Tab switching without page reload using Alpine.js or vanilla JavaScript
- Auto-refresh functionality for real-time data (every 30 seconds for status)
- Loading states and error handling for AJAX requests
- Responsive table behavior (horizontal scroll on mobile, stacked layout option)

## Route Updates Required
- Update all route references from 'landing' to 'dashboard' in web routes and controllers
- Update any navigation links or redirects that reference the old landing page

## Testing Requirements
- Create feature tests for all dashboard endpoints
- Test database connection failure scenarios
- Verify responsive design across different screen sizes
- Test accessibility compliance using screen readers
- Validate performance with large datasets

## Deployment Considerations
- Ensure dashboard works in production environment with proper caching
- Configure appropriate log levels for dashboard operations
- Set up monitoring for dashboard performance and errors
- Document any additional server requirements (PHP extensions, permissions)

## Development Notes
- The dashboard should be professional and provide real value to administrators
- Focus on performance and reliability - this is a system monitoring tool
- Ensure all data is accurate and refreshed appropriately
- Make the interface intuitive and easy to navigate
- Consider adding keyboard shortcuts for power users