<?php

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2020-10-01 10:03:45
 * @modify date 2020-10-01 10:03:45
 */

namespace App\Console\Commands;

use App\Migrate\MigrateUtils;
use App\Report\Aspect\ReportAspectDailyCallDetail;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class AspectDailyCallDetail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'aspect-daily-call-detail';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Aspect daily call detail report for cs';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::info(self::class . ' starting ..', [
            'Date' => Carbon::now()
        ]);
        $dtStartTime = Carbon::now();

        $dateReport = Carbon::yesterday();

        try {

            $report = new ReportAspectDailyCallDetail();

            $report->run($dateReport);

            $logsdata = self::class . ' Query Date Start : ' . $dtStartTime . ' , '
                . 'Query Date End : ' . Carbon::now() . ' , Completed --- Taken Time : ' .
                json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);

            Log::info($logsdata);
            dump($logsdata);
        } catch (\Exception $exc) {
            Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            echo $exc->getTraceAsString();
            $this->sendErrorEmail($exc);
        }
    }

    /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error)
    {

        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => 'Server (' . env('APP_ENV') . ') - Error executing Aspect - Daily Call Detail Report '
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toFormattedDateString(), 'error' => $error], function ($m) use ($data) {
                $m->from('<EMAIL>', 'Pentadbir');
                $m->to($data["to"])
                    //->cc($data["cc"])
                    ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }
}
