<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Migrate\Crm\AutocloseCase;
use App\Migrate\Crm\AutoFillCategoryEaduan;
use Carbon\Carbon;
use App\Migrate\MigrateUtils;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class AutoFillCategoryCaseCancelledEaduanSchedule extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'auto-fill-category-cancelled-eaduan';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This will fill category for status Cancelled Eaduan case where categories is empty.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::info(self::class . ' starting ..', [
            'Date' => Carbon::now()
        ]);

        $dtStartTime = Carbon::now();
        $startDate = Carbon::now();
        $endDate = Carbon::now();

        try {
            AutoFillCategoryEaduan::runUpdateCaseCatgories($startDate, $endDate);

            $logData = [
                'Query Date Start' => $startDate,
                'Query Date End' => $endDate,
                'Time' => MigrateUtils::getTakenTime($dtStartTime)
            ];

            Log::info(self::class . ' Completed --- ' . json_encode($logData));
        } catch (\Exception $exc) {
            Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            Log::error(self::class . '>> error happen!! ', $exc->getTrace());
            Log::error(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($exc->getTrace()));
            echo $exc->getTraceAsString();
        }
    }

    /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error)
    {
        try {
            Mail::send('emails.errorSchedulerMail', [
                'subject' => get_class($this) . ':: Server (' . env('APP_ENV') . ') - Error Update Case Category for Closed_Cancelled_Eaduan',
                'date' => Carbon::now()->toDateString(),
                'error' => $error
            ], function ($m) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to(['<EMAIL>'])->subject($m->subject);
            });

            Log::info('Send email error to ' . $m->to()[0]);
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => '<EMAIL>', 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }
}
