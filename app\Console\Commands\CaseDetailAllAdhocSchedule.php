<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Report\Crm\CaseDetailAll;
use Carbon\Carbon;
use Mail;
use App\Migrate\MigrateUtils;
use Log;
use Config;

class CaseDetailAllAdhocSchedule extends Command {

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'case-detail-all-adhoc';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This will generate Case Detail All Daily Report';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        Log::info(self::class . ' starting ..', [
            'Date' => Carbon::now()]);
        $dtStartTime = Carbon::now();
        $dateStart  = '2023-01-01'; //Carbon::now()->format('Y-m-d');
        $dateEnd    = '2023-10-31'; //Carbon::now()->format('Y-m-d');
        $requestType =  array('incident','service','enquiry') ; //to be included
        $category = array('10721','10722','10719','10720'); //to be excluded
        $subcategory = array('10712_15034','10714_15842','10713_15534','10704_12247','10711_13213'); //to be excluded
        try {
            CaseDetailAll::runAdhoc($dateStart, $dateEnd, $requestType, $category, $subcategory);
            $logsdata = self::class . ' Query Date Start : '.$dateStart.' , Query Date End : '.$dateEnd.' , Completed --- Taken Time : '.  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
            Log::info($logsdata);
        } catch (\Exception $exc) {

            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            $err = $exc->getTrace();
            \Log::error(self::class . '>> error happen!! ' . json_encode($err));
            \Log::error(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($err));
            echo $exc->getTraceAsString();
        }
        
    }

    /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error Generate Case Detail All Daily Report'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }

}
