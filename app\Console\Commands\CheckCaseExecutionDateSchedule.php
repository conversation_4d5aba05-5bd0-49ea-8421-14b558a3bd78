<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Migrate\Crm\CheckCaseExecutionDate;
use Carbon\Carbon;
use Mail;
use App\Migrate\MigrateUtils;
use Log;
use Config;

class CheckCaseExecutionDateSchedule extends Command {

    protected $signature = 'auto-check-execution-date-callin';
    protected $description = 'This will check case with null execution date for contact mode call-in';

    public function __construct() {
        parent::__construct();
    }

    public function handle() {
        Log::info(self::class . ' starting ..', [
            'Date' => Carbon::now()]);
        $dtStartTime = Carbon::now();
        try {
            CheckCaseExecutionDate::run();
            $logsdata = self::class . ' Completed --- Taken Time : ' . json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
            Log::info($logsdata);
        } catch (\Exception $exc) {

            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            $err = $exc->getTrace();
            \Log::error(self::class . '>> error happen!! ' . json_encode($err));
            \Log::error(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($err));
            echo $exc->getTraceAsString();
        }
    }

    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server (' . env('APP_ENV') . ') - Error Schedule CheckCaseExecutionDate'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }

}
