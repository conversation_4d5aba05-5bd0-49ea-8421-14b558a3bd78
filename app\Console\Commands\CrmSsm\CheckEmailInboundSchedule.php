<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Console\Commands\CrmSsm;

use Illuminate\Console\Command;
use App\Migrate\MigrateUtils;
use Carbon\Carbon;
use Log;
use App\Migrate\CrmSsm\CheckEmailInboundCrmSsm;

class CheckEmailInboundSchedule extends Command {

    protected $signature = 'update-inbound-ssm';
    protected $description = 'Update Job Scheduler For Inbound Mailboxes Cases Crm Ssm';

    public function __construct() {
        parent::__construct();
    }

    public function handle() {
        Log::info(self::class . ' >> ' . __FUNCTION__ . ' starting ..', ['Date' => Carbon::now()]);
        $dtStartTime = Carbon::now();
        try {
            CheckEmailInboundCrmSsm::runCheckEmailInboundSchedulerCrmSsm();
            $logsdata = self::class . ' Batch execution date-time : ' . $dtStartTime . ', Completed --- Taken Time : ' . json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
            Log::info($logsdata);
        } catch (\Exception $ex) {
            \Log::error(self::class . '>> error happen!! ' . $ex->getMessage());
            $err = $ex->getTrace();
            \Log::error(self::class . '>> error happen!! ' . json_encode($err));
            \Log::error(self::class . '>> error happen!! ' . $ex->getTraceAsString());
            $this->sendErrorEmail(json_encode($ex));
            echo $ex->getTraceAsString();
        }
    }

    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server (' . env('APP_ENV') . ') - Error Check Scheduler Inbound Email Crm Ssm'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }

}
