<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ExampleScheduledTask extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'example:scheduled-task';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Example scheduled task to demonstrate Laravel 12 scheduler functionality';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Example scheduled task started at: ' . Carbon::now()->toDateTimeString());
        
        // Log the execution
        Log::info('Example scheduled task executed', [
            'timestamp' => Carbon::now(),
            'message' => 'This is an example scheduled task running successfully'
        ]);
        
        // Simulate some work
        $this->info('Processing example task...');
        sleep(2);
        
        $this->info('Example scheduled task completed successfully!');
        
        return Command::SUCCESS;
    }
}
