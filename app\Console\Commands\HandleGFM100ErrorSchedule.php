<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Mail;
use App\Migrate\MigrateUtils;
use Log;
use Config;
use App\Services\Traits\OSBService;
use App\Services\Traits\FulfilmentService;
use App\Services\Traits\SupplierService;
use SSH;
use App\EpSupportActionLog;

class HandleGFM100ErrorSchedule extends Command {
    
    use OSBService;
    use FulfilmentService;
    use SupplierService;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'error-gfm100-trigger-mminf';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Any error response Kod barang tidak wujud di 1GFMAS or Unit ukuran tidak wujud di dalam 1GFMAS bagi kod barang, will auto trigger MMINF';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        Log::info(self::class . ' starting .. '.$this->description, [
            'Date' => Carbon::now()]);
        $dtStartTime = Carbon::now();
        
        //sample 2018-04-19 00:00:00
        $dateEnd = $dtStartTime->format('Y-m-d H').':00:00';
        $dateStart = $dtStartTime->subHour(1)->format('Y-m-d H').':00:00';
        
        //$dateStart = '2018-04-21 08:00:00';
        //$dateEnd = '2018-04-21 09:00:00';

        dump('Date Start: '.$dateStart);
        dump('Date End: '.$dateEnd);
        Log::info('Start in '.self::class .'Date Start: '.$dateStart.', Date End: '.$dateEnd);
        try {
            $list = $this->getListWsErrItemCodeInGFM100ByDateRange($dateStart, $dateEnd);
            $listData = collect($list);
            
            
            /* get list request item id in PR/CR based on error item code only */
            //$listRequestItemIdAll = $this->getAllRequestItemErrorOnlyByDocNo($listData);

            /* Trigger Item to changed date */
            //foreach ($listRequestItemIdAll as $requestItemId){
            //    $this->mminfTrigger($requestItemId);
            //}
            
            
            /* get all list request item id in PR/CR  and trigger MMINF */
            $listRequestItemIdAll = $this->getAllRequestItemByDocNo($listData);
            
            
            dump('Total requestItemId: '.count($listRequestItemIdAll));
            Log::info('Total requestItemId: '.count($listRequestItemIdAll));
            
            $logsdata = self::class . ' Query Date Start : '.$dateStart.' , Query Date End : '.$dateEnd.' , Completed --- Taken Time : '.  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
            Log::info($logsdata);
            dump($logsdata);

        } catch (\Exception $exc) {

            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            $err = $exc->getTrace();
            \Log::error(self::class . '>> error happen!! ' . json_encode($err));
            \Log::error(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($err));
            echo $exc->getTraceAsString();
        }
        
    }
    
    protected function getAllRequestItemErrorOnlyByDocNo($collectionData) {
        
        $collectionTransId = $collectionData->pluck('trans_id')->unique();
        $collectsDocAndItem = collect([]);
        foreach($collectionTransId as $transId){
            $collectionFiltered = $collectionData->where('trans_id', $transId);
            $docAndItem = collect([]);
            foreach($collectionFiltered as $objData){
                if($objData->trans_type == 'IBReq'){
                    $docAndItem->put('doc_no', $objData->remarks_1);
                }
                if($objData->trans_type == 'IBRes'){
                    // Unit ukuran PAC tidak wujud di dalam 1GFMAS bagi kod barang 42151624000000002R
                    // Kod barang [60105307000000003H] tidak wujud di 1GFMAS
                    $errorDesc = $objData->status_desc;
                    
                    $checkUnitUkuran = strpos($errorDesc, 'Unit ukuran');
                    if($checkUnitUkuran !== false){
                        $itemCode = trim(substr($errorDesc, -18)); 
                        $docAndItem->put('item_code', $itemCode);
                    }
                    $checkKodBarang = strpos($errorDesc, 'Kod barang');
                    if($checkKodBarang !== false){
                        $itemCode = trim(substr($errorDesc, 12,18)); 
                        $docAndItem->put('item_code', $itemCode);
                    }
                }
                $collectsDocAndItem->push($docAndItem);
            }
        }
        $collectionsDocAndItem = $collectsDocAndItem->unique();

        Log::info('Total Doc PR (Got Error Item Code) : '.count($collectionsDocAndItem));
        dump('Total Doc PR (Got Error Item Code) : '.count($collectionsDocAndItem));
        //dd($collectsDocAndItem);

        $listRequestItemIdAll = collect([]);
        foreach ($collectionsDocAndItem as $objResult){
            $docNo = $objResult->get('doc_no');
            $itemCode = $objResult->get('item_code');
            
            dump('  doc_no : '.$docNo.'  , item_code : '.$itemCode);
            Log::info(' doc_no : '.$docNo.'  , item_code : '.$itemCode);
            
            $typeDoc   = substr($docNo, 0, 2);
            if($typeDoc == 'PO' || $typeDoc == 'CO'){
                $docObj = $this->getDocNoPRPOorCRCO($docNo);
                if($docObj){
                   $docNo =  $docObj->fr_doc_no;
                }
                $listReqItem = $this->getMminfReqItemId($docNo, $itemCode);
                $listRequestItemId = collect($listReqItem)->pluck('request_item_id');

                dump('      DocNO '.$docNo. ' Total RequestItemID : '.count($listRequestItemId));
                Log::info('     DocNO '.$docNo. ' Total RequestItemID : '.count($listRequestItemId));

                /* populate to  $listRequestItemIdAll */
                $this->populateIntoListRequestItemIdAll($docNo, $listRequestItemId, $listRequestItemIdAll);
            }

        }
        
        return $listRequestItemIdAll;
    }
    
    protected function getAllRequestItemByDocNo($listData) {
        $listResult = $listData->pluck('remarks_1')->diff(["NA"]);
            
        Log::info('Total Doc PR (Got Error Item Code) : '.count($listResult));

        $listRequestItemIdAll = collect([]);
        foreach ($listResult as $docNo){
            $typeDoc   = substr($docNo, 0, 2);
            if($typeDoc == 'PO' || $typeDoc == 'CO'){
                $docObj = $this->getDocNoPRPOorCRCO($docNo);
                if($docObj){
                   $docNo =  $docObj->fr_doc_no;
                }
                $listReqItem = $this->getMminfReqItemId($docNo, null);
                $listRequestItemId = collect($listReqItem)->pluck('request_item_id');
                
                dump('      DocNO '.$docNo. ' Total RequestItemID : '.count($listRequestItemId));
                Log::info('     DocNO '.$docNo. ' Total RequestItemID : '.count($listRequestItemId));
                
                /* Trigger Item All to changed date */
                $this->mminfTriggerByListID($listRequestItemId);
            
                /* populate to  $listRequestItemIdAll */
                $this->populateIntoListRequestItemIdAll($docNo, $listRequestItemId, $listRequestItemIdAll);
            }

        }
        
        return $listRequestItemIdAll;
    }
    
   protected function populateIntoListRequestItemIdAll($docNo,$listRequestItemId,&$listRequestItemIdAll) {
       $limitItem = 450;
        if(count($listRequestItemId) > $limitItem){
            $dataMoreItem = array(
                'Status' => 'Will not send trigger ChangeDate RequestItemID because more than 15 Items',
                'Doc No.' => $docNo,
                'Total' => count($listRequestItemId),
                'ListRequestItemID' => $listRequestItemId,
            );
            dump('          send email -- item more than '.$limitItem);
            Log::info('         send email -- item more than '.$limitItem);
            $this->sendErrorEmail(json_encode($dataMoreItem));
        }else{
            foreach ($listRequestItemId as $reqItemId){
                $listRequestItemIdAll->push($reqItemId);
                Log::info('         RequestItemID : '.$reqItemId);
            }
        }
   }
    protected function mminfTrigger($reqItemId) {
        //$reqItemId = $request->request_item_id;
        //$curChangedDate = $request->current_changed_date;

        $now = Carbon::now()->addMinute(10);
        $now = $now->toDateString() . "T" . $now->toTimeString();

        $xmlContents = ""
                . "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/'>"
                . "<soap:Header xmlns:soap='http://schemas.xmlsoap.org/soap/envelope/'></soap:Header>"
                . "<soapenv:Body>"
                    . "<trig:ScRequestItemCollection xmlns:trig='http://xmlns.oracle.com/pcbpel/adapter/db/top/TriggerMMINF'>"
                        . "<trig:ScRequestItem><trig:requestItemId>$reqItemId</trig:requestItemId>"
                        . "<trig:changedDate>$now</trig:changedDate></trig:ScRequestItem>"
                    . "</trig:ScRequestItemCollection>"
                . "</soapenv:Body></soapenv:Envelope>";
        $xmlContents = '"'.$xmlContents.'"';

        $urlIdentity = "http://192.168.63.205:7011/TriggerMMINFID/v1.0";
        $urlHeader = "'Content-Type: application/xml'";

        $commands  = [
            "curl -k ".$urlIdentity." --header  ".$urlHeader."  -d ".$xmlContents,
        ];
        
        $actionLog =  EpSupportActionLog::createActionLog('TriggerMMINFID','Web Service',$commands[0],$reqItemId,'SchedulerAdmin');

        SSH::into('osb')->run($commands, function($line){
            $line.PHP_EOL;
        });
        sleep(30);
        $resReqItems = $this->getMminfSearchDetail($reqItemId);

        $checkChangeDate = Carbon::parse($resReqItems[0]->changed_date);
        if($checkChangeDate->gt(Carbon::now())) {
            $status = 'Completed';
            EpSupportActionLog::updateActionLog($actionLog, 'Completed','SchedulerAdmin');
            
        } else {
            $status = 'Failed';
            EpSupportActionLog::updateActionLog($actionLog, 'Failed','SchedulerAdmin');
        }
        dump($reqItemId. ': '.$status);
        Log::info('         '.$reqItemId. ': '.$status);

    }
    
    protected function mminfTriggerByListID($listReqItemId) {
        //$reqItemId = $request->request_item_id;
        //$curChangedDate = $request->current_changed_date;

        $now = Carbon::now()->addMinute(10);
        $now = $now->toDateString() . "T" . $now->toTimeString();

        $ScRequestItemXML = "";
        foreach ($listReqItemId as $reqItemId){
            $ScRequestItemXML = $ScRequestItemXML.
                                "<trig:ScRequestItem>
                                    <trig:requestItemId>$reqItemId</trig:requestItemId>
                                 </trig:ScRequestItem>";
        }

        $xmlContents = ""
                . "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/'>"
                . "<soap:Header xmlns:soap='http://schemas.xmlsoap.org/soap/envelope/'></soap:Header>"
                . "<soapenv:Body>"
                    . "<trig:ScRequestItemCollection xmlns:trig='http://xmlns.oracle.com/pcbpel/adapter/db/top/TriggerMMINF'>"
                        . $ScRequestItemXML
                    . "</trig:ScRequestItemCollection>"
                . "</soapenv:Body></soapenv:Envelope>";
        $xmlContents = '"'.$xmlContents.'"';

        $urlIdentity = "http://192.168.63.205:7011/TriggerMMINFID/v1.0";
        $urlHeader = "'Content-Type: application/xml'";

        $commands  = [
            "curl -k ".$urlIdentity." --header  ".$urlHeader."  -d ".$xmlContents,
        ];
        
        $actionLog =  EpSupportActionLog::createActionLog('TriggerMMINFID','Web Service',$commands[0],json_encode($listReqItemId),'SchedulerAdmin');

        SSH::into('osb')->run($commands, function($line){$line.PHP_EOL; });
        
        sleep(10);
        
        $resReqItems = $this->getMminfSearchDetailByListReqItemId($listReqItemId);
        $reqItem = $resReqItems->first();
        $checkChangeDate = Carbon::parse($reqItem->changed_date);

        if($checkChangeDate->gt(Carbon::now())) {
            $status = 'Completed';
        }else{
            $status = 'Failed';
        }
        
        EpSupportActionLog::updateActionLog($actionLog, $status,'SchedulerAdmin');
        
        dump(__FUNCTION__. ': '.$status);
        Log::info('         '.__FUNCTION__. ': '.$status);
        
        sleep(30);

    }
    
    /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>','<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error: HandleGFM100ErrorSchedule'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }
    


}
