<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Mail;
use App\Migrate\MigrateUtils;
use Log;
use Config;
use App\Services\Traits\OSBService;
use App\Services\Traits\FulfilmentService;
use App\Services\Traits\SupplierService;
use SSH;
use DB;
use App\EpSupportActionLog;

class HandleGFM140Schedule extends Command {
    
    use OSBService;
    use FulfilmentService;
    use SupplierService;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'gfm-140';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Decrypt file contents for service GFM-140 AP511 to put temporary table';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        Log::info(self::class . ' starting .. '.$this->description, [
            'Date' => Carbon::now()]);
        $dtStartTimeLog = Carbon::now();
        $dtStartTime = Carbon::now();
        
        //sample 2018-04-19 00:00:00
        $dateEnd = $dtStartTime->format('Y-m-d H').':00:00';
        $dateStart = $dtStartTime->subHour(1)->format('Y-m-d H').':00:00';

        $dateStart = '2018-05-01 00:00:00';
        $dateEnd = '2018-06-01 00:00:00';

        dump('Date Start: '.$dateStart);
        dump('Date End: '.$dateEnd);
        Log::info('Start in '.self::class .'Date Start: '.$dateStart.', Date End: '.$dateEnd);
        try {
            $list = $this->getListFilesGFM140ByDateRange($dateStart, $dateEnd);
            $listData = collect($list);
            
            $counter=0;
            foreach ($listData as $objFile){
                $fileData = $this->wsDecryptFile1GFMAS($objFile);
                
                $count = DB::connection('mysql_ep_support')->table('ep_osb_batch_file')
                        ->where('batch_file_id',$objFile->batch_file_id)
                        ->count();
                if($count == 0){
                    DB::connection('mysql_ep_support')
                    ->insert('insert into ep_osb_batch_file 
                        (batch_file_id,trans_id,service_code,file_name,created_date,file_data) 
                        values (?, ?, ?, ?, ?, ?)', 
                        [   
                            $objFile->batch_file_id, 
                            $objFile->trans_id,
                            $objFile->service_code,
                            $objFile->file_name,
                            $objFile->created_date,
                            $fileData[0]
                            ]);
                    dump('inserted : '.$objFile->file_name);
                }
                $counter++;
                if($counter == 5){
                    sleep(1);
                    $counter = 0;
                }
            }
            
            dump('Total Files: '.count($listData));

            $logsdata = self::class . ' Query Date Start : '.$dateStart.' , Query Date End : '.$dateEnd.' , '
                    . 'Completed --- Taken Time : '.  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTimeLog)]);
            Log::info($logsdata);
            dump($logsdata);

        } catch (\Exception $exc) {

            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            $err = $exc->getTrace();
            \Log::error(self::class . '>> error happen!! ' . json_encode($err));
            \Log::error(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($err));
            echo $exc->getTraceAsString();
        }
        
    }
    
    
    
    /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>','<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error: HandleGFM100ErrorSchedule'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }
    


}
