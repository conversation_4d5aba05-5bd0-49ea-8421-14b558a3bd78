<?php

namespace App\Console\Commands\MyTV_Altel;

use Illuminate\Console\Command;
use App\Migrate\MigrateUtils;
use Carbon\Carbon;
use Log;
use App\Report\MyTvAltel\CaseDetailReport;

class ReportCaseDetailsWeekly extends Command {

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'case-detail-mytv-altel-weekly';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Report Case Detail MyTV/Altel';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

            
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        Log::info(self::class . ' starting ..', ['Date' => Carbon::now()]);
        $dtStartTime = Carbon::now();
        $dateStart = Carbon::now()->subDay(7)->format('Y-m-d');
        $dateEnd = Carbon::yesterday()->format('Y-m-d');
        
        try { 
            $report = new CaseDetailReport;
            $report->run($dateStart,$dateEnd);
        
            $logsdata = self::class . ' Query Date Start : '.$dtStartTime.' , '
                        . 'Query Date End : '.Carbon::now().' , Completed --- Taken Time : '.  
                        json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
            
            Log::info($logsdata);
        } catch (\Exception $exc) {
            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            echo $exc->getTraceAsString();
        }
        
    }

}




















