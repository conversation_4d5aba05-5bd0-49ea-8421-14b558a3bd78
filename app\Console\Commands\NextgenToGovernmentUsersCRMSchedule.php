<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Migrate\Nextgen\SyncGovernmentUsersInfo;
use App\Migrate\Nextgen\CRMService;
use Carbon\Carbon;
use Mail;
use App\Migrate\MigrateUtils;
use Log;
use Config;


class NextgenToGovernmentUsersCRMSchedule extends Command {

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'integrate-nextgen-government-users';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This will integrate data PM Modules (Government Users Info) into CRM';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    
    
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        Log::info(self::class . ' starting ..', [
            'Date' => Carbon::now()]);
        $dtStartTime = Carbon::now();
        
        $periodMinute = env('DATA_PERIOD_BY_MINUTE', '5');
        $cDateStart = MigrateUtils::getDateStartByPeriod($periodMinute);
        $cDateEnd = MigrateUtils::getDateEndByPeriod($cDateStart, $periodMinute);

        $logScheduler = CRMService::addLogScheduler(
                        $cDateStart,
                        $cDateEnd,
                        $periodMinute,
                        'UpdateGovernmentUsersInfo');
        if($logScheduler == null){
            Log::info('  '.self::class . ' Action Stop! Other Program still running. ');
            exit;
        }
        try {
            SyncGovernmentUsersInfo::runUpdateGovernmentUsersInfo($logScheduler,$logScheduler->date_from,$logScheduler->date_to);
            $logsdata = self::class . ' SUCCESS - executing Integration Nextgen (UpdateGovernmentUsersInfo) to CRM on '
                    .Carbon::now()->format('d-M-y').''
                    . '  , Completed --- Taken Time : '.  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
            Log::info($logsdata);

        } catch (\Exception $exc) {
            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            $err = $exc->getTrace();
            \Log::error(self::class . '>> error happen!! ' . json_encode($err));
            \Log::error(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            
            $logScheduler->error = json_encode($err);
            $logScheduler->remarks = $exc->getMessage();
            $logScheduler->status = 'Error';
            $logScheduler->save();
            $this->sendErrorEmail(json_encode($err));
        }
        Log::info(self::class . ' Completed --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }
    
    
    
    /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            //"to" => ['<EMAIL>'],
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') > ERROR - executing Integration Nextgen (UpdateGovernmentUsersInfo) to CRM  on '.Carbon::now()->format('d-M-y')
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }
    
    /**
     * Send an e-mail as Success Logs
     *
     * @param  Request  $logsdata
     * @return Response
     */
    protected function sendSuccessEmail($logsdata) {
        $data = array(
            //"to" => ['<EMAIL>'],
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') > SUCCESS - Integration Nextgen (UpdateGovernmentUsersInfo)  CRM on '.Carbon::now()->format('d-M-y')
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $logsdata], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }

}
