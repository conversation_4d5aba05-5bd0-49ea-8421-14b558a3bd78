<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Migrate\MigrateUtils;
use Carbon\Carbon;
use Mail;
use Log;
use Config;
use DB;
use App\Report\Crm\CaseStatisticReport;

class ReportCasesResolved extends Command {

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'crm-cases-resolved';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Report Cases (IT) Resolved By Users ';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

            
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        Log::info(self::class . ' starting ..', [
            'Date' => Carbon::now()]);
        $dtStartTime = Carbon::now();
        
        $dateReport = Carbon::yesterday();

        //$dateReport = Carbon::parse('2019-01-06');
        try { 
            
            $report = new CaseStatisticReport;
            //$report->runMyTest($dateReport);
            $report->run($dateReport);
            $report->runStatisticUsers($dateReport);
        
            $logsdata = self::class . ' Query Date Start : '.$dtStartTime.' , '
                        . 'Query Date End : '.Carbon::now().' , Completed --- Taken Time : '.  
                        json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
            
            Log::info($logsdata);
            dump($logsdata);
        } catch (\Exception $exc) {
            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            echo $exc->getTraceAsString();
        }
        
    }

     /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => 'Server ('.env('APP_ENV').') - Error executing  data fixed for field created_by and modified_user_id '
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toFormattedDateString(), 'error' => $error], function($m) use ($data) {
                $m->from('<EMAIL>', 'Pentadbir');
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }

}
