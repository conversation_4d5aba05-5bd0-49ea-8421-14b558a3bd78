<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Console\Commands;

use App\Queue\ReportMonitoringQueue;
use Illuminate\Console\Command;
use App\Migrate\MigrateUtils;
use Carbon\Carbon;
use Log;

class ReportMonitoringSchedule extends Command {

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'report-monitoring';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run command for any error send email report';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        Log::info(self::class . ' starting ..', [
            'Date' => Carbon::now()]);
        $dtStartTime = Carbon::now();

        try {
            ReportMonitoringQueue::runQueue();
            $logsdata = self::class . ' Start run command : ' . $dtStartTime . ' , '
                    . 'End run command : ' . Carbon::now() . ' , Completed --- Taken Time : ' .
                    json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);

            Log::info($logsdata);
            dump($logsdata);
        } catch (\Exception $exc) {
            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            echo $exc->getTraceAsString();
        }
    }

}
