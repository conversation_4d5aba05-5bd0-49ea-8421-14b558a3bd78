<?php

namespace App\Console\Commands;

use App\Report\Crm\statisticSupplierCasesMonitoringReport;
use Illuminate\Console\Command;
use App\Migrate\MigrateUtils;
use Carbon\Carbon;
use Mail;
use Log;
use Config;
use DB;

class ReportStatisticSupplierCasesMonitoring extends Command {

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'top-supplier-casemonitoring';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Statistic for CRM Cases Monitoring for Top 15 Supplier ';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }


    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        Log::info(self::class . ' starting ..', [
            'Date' => Carbon::now()]);
        $dtStartTime = Carbon::now();

        $dateReport = Carbon::yesterday();

        
        try {

            $report = new statisticSupplierCasesMonitoringReport();
            $report->runTest($dateReport);

            $logsdata = self::class . ' Query Date Start : '.$dtStartTime.' , '
                . 'Query Date End : '.Carbon::now().' , Completed --- Taken Time : '.
                json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);

            Log::info($logsdata);
            dump($logsdata);
        } catch (\Exception $exc) {
            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            echo $exc->getTraceAsString();
        }

    }

    /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {

        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => 'Server ('.env('APP_ENV').') - Error executing Statistic for CRM Cases Monitoring for Top 15 Supplier '
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toFormattedDateString(), 'error' => $error], function($m) use ($data) {
                $m->from('<EMAIL>', 'Pentadbir');
                $m->to($data["to"])
                    //->cc($data["cc"])
                    ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }

}
