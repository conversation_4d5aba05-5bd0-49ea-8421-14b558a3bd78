<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Report\Crm\StatisticPendingAgeingCaseIncidentReport;
use Carbon\Carbon;
use Mail;
use App\Migrate\MigrateUtils;
use Log;
use Config;

class StatisticPendingAgeingCaseIncidentReportSchedule extends Command {

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'incident-case-ageing-specialist';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This will generate Details of Statistic Case Incident with status Pending To Specialist. Include calculation Ageing.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        MigrateUtils::logDump(self::class . ' starting ..');
        $dtStartTime = Carbon::now();
        try {
            $report = new StatisticPendingAgeingCaseIncidentReport();
            $report->run();
            $logsdata = self::class . ' >> Completed --- Taken Time : '.  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
            MigrateUtils::logDump($logsdata);
        } catch (\Exception $e) {
            MigrateUtils::logErrorDump(__CLASS__.' > '.__FUNCTION__.' > '.' ERROR'. $e->getMessage());
            MigrateUtils::logErrorDump($e->getTraceAsString());
        }
        
    }

    /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error Generate CRM: Statisic Ageing Pending Case Incident'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }

}
