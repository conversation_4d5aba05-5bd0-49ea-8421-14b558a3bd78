<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Migrate\Crm\UpdateSLA;
use Carbon\Carbon;
use Mail;
use App\Migrate\MigrateUtils;
use Log;
use Config;
use App\BatchMonitoringStatistic;

class UpdateSLASchedule extends Command {

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update-sla';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This will read SLA for Cases and Tasks';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        Log::info(self::class . ' starting ..', [
            'Date' => Carbon::now()]);
        $dtStartTime = Carbon::now();
        $dateStart  = '2018-01-01'; 
        $dateEnd    = Carbon::now();
        try {
            UpdateSLA::runUpdateSLA($dateStart, $dateEnd);
            $logsdata = self::class . ' Query Date Start : '.$dateStart.' , Query Date End : '.$dateEnd.' , Completed --- Taken Time : '.  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
            Log::info($logsdata);
//            $this->sendSuccessEmail($logsdata);
                           
                $note = ' Successfully capture Acknowledge Time for SLA IT Coordinator (15 Minutes)';
                $projName = 'CDCCRM Integration';
                $batchName = 'batchAutoAcknowledgeInitialTask';
                $remarks = $note;
                $dateModified = Carbon::now();
                $status = 'Success';
                $list = BatchMonitoringStatistic::getBatchName($batchName);
                $dataList =BatchMonitoringStatistic::where('batch_name', 'batchAutoAcknowledgeInitialTask')
                    ->first();
                log::info('Masuk Success');
                if (count($list) > 0) {
                    log::info('Success Satu');
                      BatchMonitoringStatistic::updateBatchMonitoring($dataList,$status,$remarks,$dateModified);
                } else {
                      BatchMonitoringStatistic::createBatchMonitoring($projName,$batchName,$status,$remarks,$dateModified);
                    log::info('Success Dua');
                }
            
        } catch (\Exception $exc) {

            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            $err = $exc->getTrace();
            \Log::error(self::class . '>> error happen!! ' . json_encode($err));
            \Log::error(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($err));
            
                $projName = 'CDCCRM Integration';
                $batchName = 'batchAutoAcknowledgeInitialTask';
                $remarks = $exc->getMessage();
                $dateModified = Carbon::now();
                $status = 'Failed';
                $list = BatchMonitoringStatistic::getBatchName($batchName);
                $dataList =BatchMonitoringStatistic::where('batch_name', 'batchAutoAcknowledgeInitialTask')
                    ->first();
                log::info('Masuk Error');
                if (count($list) > 0) {
                    log::info('Error Satu');
                      BatchMonitoringStatistic::updateBatchMonitoring($dataList,$status,$remarks,$dateModified);
                } else {
                       BatchMonitoringStatistic::createBatchMonitoring($projName,$batchName,$status,$remarks,$dateModified);
                    log::info('Error Dua');
                }
            echo $exc->getTraceAsString();
        }
        
    }

    /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error reading SLA for Cases and Tasks'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }
    
    /**
     * Send an e-mail as Success Logs
     *
     * @param  Request  $logsdata
     * @return Response
     */
    protected function sendSuccessEmail($logsdata) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') > SUCCESS reading SLA for Cases and Tasks'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $logsdata], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }

}
