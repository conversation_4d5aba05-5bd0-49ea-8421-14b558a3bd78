<?php

namespace App\Document;

use Illuminate\Support\Facades\Storage;
use Barryvdh\DomPDF\Facade as PDF;
use Carbon\Carbon;
// use Log;
use App\Migrate\MigrateUtils;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class LmsTrainingCertGenerator
{
    public static function manualCertGenerator($dateStart = null, $dateEnd = null)
    {

        Log::debug(self::class . ' Starting... checking Training Complete', ['Query Start Date' => $dateStart, 'Query End Date' => $dateEnd]);
        $dtStartTime = Carbon::now();

        self::checkDataTrainingComplete();

        Log::info(self::class . ' Completed.. Generating LMS Training Certificate --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    public static function certGenByEventCode($eventCode)
    {
        Log::debug(self::class . ' Starting... checking Training Complete by event code');
        $dtStartTime = Carbon::now();

        self::checkDataTrainingComplete($eventCode);

        Log::info(self::class . ' Completed.. Generating LMS Training Certificate --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime), 'Event Code' => $eventCode]);
    }
    
    public static function certGenByEventCodeEmail($eventCode,$email)
    {
        Log::debug(self::class . ' Starting... checking Training Complete by event code and email address');
        $dtStartTime = Carbon::now();

        self::checkDataTrainingComplete($eventCode,$email);

        Log::info(self::class . ' Completed.. Generating LMS Training Certificate --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime), 'Event Code' => $eventCode]);
    }

    private static function checkDataTrainingComplete($eventCode = null,$email = null)
    {
        $take = 1000;
        $yesterday = Carbon::yesterday()->format('Y-m-d');

        $query = "SELECT
                    t.trn_status AS training_status,
                    CASE t.trn_status
                        WHEN NULL THEN 'N/A'
                        WHEN 'C' THEN 'Completed'
                        WHEN 'E' THEN 'Enrolled'
                        WHEN 'N' THEN 'No Show'
                        WHEN 'X' THEN 'Cancelled'
                        ELSE t.trn_status END training_status_desc,
                    c.crs_name AS course_name,
                    c.crs_code AS course_code,
                    c.status AS course_status,
                    ct.name AS course_type,
                    e.event_start,
                    e.event_name AS event_name,
                    e.event_venue AS venue_type,
                    e.in_house_price AS event_in_house_price,
                    e.event_start AS startDate,
                    e.event_end AS endDate,
                    e.code AS event_code,
                    u.first_name AS user_name,
                    u.user_email AS userEmail,
                    u.opu AS user_opu
                FROM
                    training t
                    JOIN course c ON t.crs_id = c.id
                    JOIN user_ u ON t.user_id = u.user_id
                    JOIN course_type ct ON c.crs_type_id = ct.id
                    LEFT JOIN event e ON t.event_id = e.id
                WHERE t.trn_status = 'C' 
                ";

        if ($eventCode) {
            $query .= "AND e.code = ? ";
            $params[] = $eventCode;
            
            if($email){
                $query .= "AND u.user_email = ? ";
                $params[] = $email;
            }
            
        }else {
            $query .= "AND convert(varchar, e.event_start, 23) = ? ";
            $params[] = $yesterday;
        }

        $query .= "ORDER BY e.event_start DESC";

        $resultTask = [];
        do {
            $resultTask = DB::connection('sqlsrv_cms')->select($query, $params);
            $total = count($resultTask);
            dump('Total certificate: ' . $total);
            Log::info(self::class . ' Total Record checkDataTrainingComplete for ' . $yesterday . ' : ' . $total);
            if ($total > 0) {
                self::filterData($resultTask);
            }
        } while (count($resultTask) == $take);
    }

    private static function filterData($data)
    {

        $listArray = array();
        foreach ($data as $row) {
            $dateStart = date("d-M-Y", strtotime($row->startDate));
            $dateEnd = date("d-M-Y", strtotime($row->endDate));

            $listArray['participantName'] = $row->user_name;
            $listArray['courseName'] = $row->course_name;
            $listArray['courseDateStart'] = $dateStart;
            $listArray['courseDateEnd'] = $dateEnd;
            $listArray['eventName'] = $row->event_name;
            $listArray['participantEmail'] = $row->userEmail;

            self::htmlToPdf($listArray);

            $courseStart = date("d M Y", strtotime($row->startDate));
            $courseEnd = date("d M Y", strtotime($row->endDate));

            $dataArray = collect([]);
            $dataArray->put("participantName",  $row->user_name);
            $dataArray->put("courseName", $row->course_name);
            $dataArray->put("courseDateStart", $courseStart);
            $dataArray->put("courseDateEnd", $courseEnd);
            $dataArray->put("eventName", $row->event_name);
            $dataArray['participantEmail'] = $row->userEmail; 
            self::sendEmail($dataArray);
        }
    }

    public static function htmlToPdf($data)
    {
        $participantName = strtolower($data['participantName']);
        dump('Generating cert for... ' . $participantName);
        Log::info(self::class . 'Generating cert for... ' . $participantName . ' For attending course : ' . $data['courseName'] . ' Email : ' . $data['participantEmail'] . ' , On : ' . $data['courseDateStart'] . ' Until ' . $data['courseDateEnd']);
        $participantName = preg_replace("/[\s_]/", "_", $participantName);

        $pdf = PDF::loadView('emails.lms_softcert', compact('data'))->setPaper('a4', 'portrait');
        $pdf->setOptions(['dpi' => 72]);
        Storage::put('cert/sijil_' . $participantName . '.pdf', $pdf->output());
        return $pdf->download('invoice.pdf');
    }
    
    public static function sendEmail($dataArray)
    {

        $participantName = strtolower($dataArray['participantName']);
        $participantNames = preg_replace("/[\s_]/", "_", $participantName);
        $filename = 'sijil_' . $participantNames . ".pdf";

        $file_path = storage_path() . '/app/cert/' . $filename;
        $data = array(
            // "to" => ['<EMAIL>'],
            "to" => $dataArray['participantEmail'],
            "subject" => $dataArray['eventName'] . ' - ' . $dataArray['participantName'],
            "att" => $file_path
        );
        $dataArray['subject'] = $data["subject"];
        try { 
            self::sendEmailFunction($dataArray,$data);
            
        } catch (\Exception $e) {
            echo $e->getMessage();
            Log::error('{CLASS => ' . self::class  . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]); 
        }
    }
    
    public static function sendEmailFunction($dataArray,$data) {
        Mail::send(
                'emails.lms_softcert_email',
                ['data' => $dataArray],
                function ($m) use ($data) {
                    $m->from('<EMAIL>', 'Pentadbir');
                    $m->to($data["to"]);
                    $m->bcc(['<EMAIL>','<EMAIL>']);
                    $m->subject($data["subject"]);
                    $m->attach($data["att"]);
                }
            );
    }
}
