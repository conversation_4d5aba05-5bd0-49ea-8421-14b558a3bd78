<?php

namespace App\Document;

use Illuminate\Support\Facades\Storage;
use Barryvdh\DomPDF\Facade as PDF;
use Carbon\Carbon;
use Log;
use DB;
use Mail;
use App\Migrate\MigrateUtils;

class LmsTrainingNotes
{    
     public static function AutoLMSNotes($dateStart = null, $dateEnd = null) {

        Log::debug(self::class . ' Starting... checking LMS Training Thanks You Notes Complete', ['Query Start Date' => $dateStart, 'Query End Date' => $dateEnd]);
        $dtStartTime = Carbon::now();

        self::checkLMSDataTraining();

        Log::info(self::class . ' Completed.. Sending LMS Training Thanks You Notes --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }  
    
     private static function checkLMSDataTraining() {
         $take = 1000;
          $now  = Carbon::now()->format('Y-m-d');
                
        do {            
             $result = "SELECT                          
                            t.trn_status AS training_status,
                            CASE t.trn_status
                                WHEN NULL THEN 'N/A'
                                WHEN 'C' THEN 'Completed'
                                WHEN 'E' THEN 'Enrolled'
                                WHEN 'N' THEN 'No Show'
                                WHEN 'X' THEN 'Cancelled'
                                ELSE t.trn_status END training_status_desc,
                            c.crs_name AS course_name,
                            c.crs_code AS course_code,
                            c.status AS course_status,
                            ct.name AS course_type,
                            e.event_start,
                            e.event_name AS event_name,
                            e.event_venue AS venue_type,
                            e.in_house_price AS event_in_house_price,
                            e.event_start AS startDate,
                            e.event_end AS endDate,
                            e.code AS event_code,
                            u.first_name AS user_name,
                            u.user_email AS userEmail,
                            u.opu AS user_opu                            
                    FROM
                            training t                             
                            JOIN
                                    course c 
                                    ON t.crs_id = c.id 
                            JOIN
                                    user_ u 
                                    ON t.user_id = u.user_id 
                            JOIN
                                    course_type ct 
                                    ON c.crs_type_id = ct.id 
                            LEFT JOIN
                                    event e 
                                    ON t.event_id = e.id
                            WHERE t.trn_status in ('C','E','N')
                            AND convert(varchar, e.event_start, 23) = ?
                                    order by  e.event_start DESC";
     
            $resultTask = DB::connection('sqlsrv_cms')->select($result,array($now));
            $total = count($resultTask);
            log::info('Total Email Sent : '.$total);
            if ($total > 0) {
                self::filterData($resultTask);
            }
        } while (count($resultTask) == $take);
     }
     
     private static function filterData($data) {

        foreach ($data as $row) {
            log::info('LMS Participant Name : '.$row->user_name.' - '.$row->endDate);
            $now = Carbon::now();
            $dateEnd = new Carbon($row->endDate);
            $diff3minutes = $now->diffInMinutes($dateEnd);
                
                $courseStart = date("d M Y", strtotime($row->startDate));
                $dataArray = collect([]);
                $dataArray->put("participantName",  $row->user_name);
                $dataArray->put("courseName", $row->course_name);
                $dataArray->put("courseDate", $courseStart);
                $dataArray->put("eventName", $row->event_name);
                $dataArray['participantEmail'] = $row->userEmail;
                
                if($diff3minutes > 3){
                self::sendEmail($dataArray);
                }
        }
    }
    
    public static function sendEmail($dataArray){
        $data = array(
            //"to" => ['<EMAIL>'], // ,'<EMAIL>','<EMAIL>'
           "to" => $dataArray['participantEmail'],
            "subject" => 'Terima Kasih Kerana Sertai Latihan ePerolehan'
        );
        $dataArray['subject'] = $data["subject"];
        try {
            Mail::send('emails.lms_note_email', ['data' => $dataArray],
            function($m) use ($data) {
            $m->from('<EMAIL>', 'Pentadbir');
            $m->to($data["to"]);
            $m->subject($data["subject"]);          
            });
        } catch (\Exception $e) {
            echo $e;
            Log::error('{CLASS => ' . self::class  . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
        
    }
    
}
