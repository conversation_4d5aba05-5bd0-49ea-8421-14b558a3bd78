<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Font;
use PhpOffice\PhpSpreadsheet\Style\Border;
use Carbon\Carbon;

class AspectDailyCallDetailExport implements FromCollection, WithHeadings, WithStyles, WithColumnFormatting, WithEvents, WithTitle
{
    protected $data;
    protected $fileName;

    public function __construct($data, $fileName)
    {
        $this->data = $data;
        $this->fileName = $fileName;
    }

    public function collection()
    {
        return $this->data->map(function ($obj) {
            return [
                Carbon::parse($obj->calltime)->format('d/m/Y H:i:s A'),
                $obj->service_id,
                $obj->user_id,
                $obj->callactionid,
                $obj->callactionreasonid,
                $obj->dnis,
                $obj->origserviceid,
                $obj->ani,
            ];
        });
    }

    public function headings(): array
    {
        return [
            'CALLTIME',
            'SERVICE_ID',
            'USER_ID',
            'CALLACTIONID',
            'CALLACTIONREASONID',
            'DNIS',
            'ORIGSERVICEID',
            'ANI'
        ];
    }

    public function title(): string
    {
        return $this->fileName;
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the title row (row 1)
            1 => [
                'font' => [
                    'bold' => true,
                    'name' => 'Calibri',
                    'size' => 11,
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'c5ff8f'],
                ],
            ],
            // Style the header row (row 3)
            3 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                    'name' => 'Calibri',
                    'size' => 11,
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '1c1c1c'],
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                    ],
                ],
            ],
        ];
    }

    public function columnFormats(): array
    {
        return [
            'A' => NumberFormat::FORMAT_TEXT,
            'B' => NumberFormat::FORMAT_TEXT,
            'C' => NumberFormat::FORMAT_TEXT,
            'D' => NumberFormat::FORMAT_TEXT,
            'E' => NumberFormat::FORMAT_TEXT,
            'F' => NumberFormat::FORMAT_TEXT,
            'G' => NumberFormat::FORMAT_TEXT,
            'H' => NumberFormat::FORMAT_TEXT,
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                // Set orientation to landscape
                $event->sheet->getPageSetup()->setOrientation(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::ORIENTATION_LANDSCAPE);
                
                // Set auto size for all columns
                foreach (range('A', 'H') as $column) {
                    $event->sheet->getColumnDimension($column)->setAutoSize(true);
                }
                
                // Set default font for the entire sheet
                $event->sheet->getDefaultStyle()->getFont()
                    ->setName('Calibri')
                    ->setSize(11);

                // Add title in row 1
                $event->sheet->setCellValue('A1', 'Call time within 12am - 12am');
                
                // Move headers to row 3 (Laravel Excel will put them in row 1 by default)
                // We need to insert empty row and move headers
                $event->sheet->insertNewRowBefore(1, 2);
                
                // Set the title in row 1
                $event->sheet->setCellValue('A1', 'Call time within 12am - 12am');
                $event->sheet->getStyle('A1')->applyFromArray([
                    'fill' => [
                        'fillType' => Fill::FILL_SOLID,
                        'startColor' => ['rgb' => 'c5ff8f'],
                    ],
                    'font' => [
                        'name' => 'Calibri',
                        'size' => 11,
                    ]
                ]);
                
                // Style the header row (now in row 3)
                $event->sheet->getStyle('A3:H3')->applyFromArray([
                    'font' => [
                        'bold' => true,
                        'color' => ['rgb' => 'FFFFFF'],
                        'name' => 'Calibri',
                        'size' => 11,
                    ],
                    'fill' => [
                        'fillType' => Fill::FILL_SOLID,
                        'startColor' => ['rgb' => '1c1c1c'],
                    ],
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => Border::BORDER_THIN,
                        ],
                    ],
                ]);
            },
        ];
    }
}
