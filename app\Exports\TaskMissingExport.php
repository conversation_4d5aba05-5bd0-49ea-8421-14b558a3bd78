<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Font;
use PhpOffice\PhpSpreadsheet\Style\Border;
use Illuminate\Support\Facades\DB;
use App\Services\EPService;

class TaskMissingExport implements FromCollection, WithHeadings, WithStyles, WithColumnFormatting, WithEvents, WithTitle
{
    public function collection()
    {
        $list = DB::connection('mysql_ep_support')->table('ep_task_missing')->get();
        
        return $list->map(function ($obj) {
            return [
                $obj->batch,
                $obj->case_no,
                $obj->case_status,
                $obj->doc_no,
                $obj->module,
                $obj->problem,
                EPService::$TASK_MISSING_STATUS[$obj->process_status],
                $obj->composite_instance_id,
                str_replace("&#13;&#10;", " ", $obj->resolution),
            ];
        });
    }

    public function headings(): array
    {
        return [
            'BATCH',
            'CRM CASE NO',
            'STATUS CRM',
            'DOC NO',
            'MODULE',
            'PROBLEM',
            'STATUS',
            'COMPOSITE INSTANCE ID',
            'REMARKS'
        ];
    }

    public function title(): string
    {
        return 'TaskMissing';
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                    'name' => 'Calibri',
                    'size' => 11,
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '684E49'],
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                    ],
                ],
            ],
        ];
    }

    public function columnFormats(): array
    {
        return [
            'A' => NumberFormat::FORMAT_TEXT,
            'B' => NumberFormat::FORMAT_TEXT,
            'C' => NumberFormat::FORMAT_TEXT,
            'D' => NumberFormat::FORMAT_TEXT,
            'E' => NumberFormat::FORMAT_TEXT,
            'F' => NumberFormat::FORMAT_TEXT,
            'G' => NumberFormat::FORMAT_TEXT,
            'H' => NumberFormat::FORMAT_TEXT,
            'I' => NumberFormat::FORMAT_TEXT,
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                // Set orientation to landscape
                $event->sheet->getPageSetup()->setOrientation(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::ORIENTATION_LANDSCAPE);
                
                // Set auto size for all columns
                foreach (range('A', 'I') as $column) {
                    $event->sheet->getColumnDimension($column)->setAutoSize(true);
                }
                
                // Set default font for the entire sheet
                $event->sheet->getDefaultStyle()->getFont()
                    ->setName('Calibri')
                    ->setSize(11);
            },
        ];
    }
}
