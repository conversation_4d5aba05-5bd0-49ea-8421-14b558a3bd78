<?php

namespace App\Http\Controllers;

use App\Migrate\Nextgen\PMService;
use App\Migrate\Nextgen\SMService;
use App\Migrate\Nextgen\CRMService;
use App\Migrate\Nextgen\SyncGovernmentInfo;
use Log;
use DB;
use Response;

class BatchController extends Controller {

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('guest');
    }

    /**
     * Manual request to Sync LoginID from Nextgen to CRM
     *
     * @param  array  $data
     * @return User
     */

    public function syncUser($loginID) {
        $ObjSupplierUser = $this->syncSupplierUser($loginID);
        if($ObjSupplierUser == NULL){
            return $ObjOrganizationUser = $this->syncOrganizationUser($loginID);
        }
        return $ObjSupplierUser;
    }
    
    /**
     * Manual request to Migrate LoginID from Nextgen to CRM
     *
     * @param  array  $data
     * @return User
     */
    public function syncSupplierUser($loginID) {
        $results = SMService::getSMSupplierUsersActiveByLoginID($loginID);

        if (count($results) > 0) {
            foreach ($results as $obj) {

                Log::info(' Check Company Name :- ' . $obj->company_name);
                Log::info('      eP No. :- ' . $obj->ep_no);
                $account = CRMService::getSupplierCrm($obj);
               
                if ($account != null){
                    $supplierObj = SMService::getSMSuppliersDetail($obj->supplier_id);
                    
                    if($account->mof_no != $supplierObj->mof_no){
                        Log::info('checking crm mof no : '.$account->mof_no .' not same with in eP Mof No '.$supplierObj->mof_no );
                        Log::info(json_encode($supplierObj));
                        $account = CRMService::saveAccountSupplierPrimary($supplierObj,$account->id);
                        CRMService::saveAccountSupplierAddress($supplierObj, $account->id);
                    }
                    //if($supplierObj->)
                }
                if ($account == null ) {
                    //Create Account
                    Log::info('     CREATE ACCOUNT :- ' . $obj->ep_no);
                    /* Need get Details */
                    $supplierObj = SMService::getSMSuppliersDetail($obj->supplier_id);
                    Log::info(json_encode($supplierObj));
                    $account = CRMService::saveAccountSupplierPrimary($supplierObj);
                    CRMService::saveAccountSupplierAddress($supplierObj, $account->id);
                }

                

                /** FIND account -> contact * */
                $query = DB::table('accounts as a')
                        ->join('accounts_contacts as ac', 'a.id', '=', 'ac.account_id')
                        ->join('contacts as c', 'ac.contact_id', '=', 'c.id')
                        ->where('ac.account_id', $account->id)
                        ->where('c.identity_no_nextgen', trim($obj->identification_no))
                        ->select('a.name as acc_name', 'a.id as acc_id')
                        ->addSelect('c.first_name as contact_name', 'c.id as contact_id', 'c.identity_no_nextgen', 'c.user_id_nextgen');
                //var_dump($query->toSql());
                $contact = $query->first();
                if ($contact == null) {

                    if ($obj->p_record_status == '1') {
                        $contactObj = CRMService::createContactSupplier($account, $obj);
                        Log::info('        :: ->  success create ' . $contactObj->id . ' RECORD_STATUS: ' . $contactObj->record_status_nextgen);
                    } else {
                        Log::info('        :: ->  No need create this user . This user is InActive and not in record CRM');
                    }
                } else {
                    //update it
                    $contactObj = CRMService::saveContactSupplier($obj, $contact);
                    CRMService::saveEmailContact($obj->p_email, $contact->contact_id);
                    Log::info('        :: ->  success update ' . $contactObj->id . ' RECORD_STATUS: ' . $contactObj->record_status_nextgen);
                }
            }

            $contacts = DB::table('contacts')->where('login_id_nextgen', $loginID)->first();
            return Response::json($contacts);
        }
        return null;
    }

    /**
     * Manual request to Migrate LoginID from Nextgen to CRM
     *
     * @param  array  $data
     * @return User
     */
    public function syncOrganizationUser($loginID) {
        $results = PMService::getPMOrgGovUsersDetailsByLoginID($loginID);
        if (count($results) == 0){
            $results = PMService::getPMOrgFactoringUsersDetailsByLoginID($loginID);
        }
        if (count($results) > 0) {
            foreach ($results as $obj) {
                $orgcode = $obj->org_code;
                $orgTypeDesc = PMService::getOrgProfileType($obj->op_org_type_id);
                Log::info('     ' . $orgTypeDesc . ' -> ' . $orgcode);
                Log::info('     ' . json_encode($obj));
                Log::info('    Name :- ' . $obj->fullname);
                Log::info('     Identity No. :- ' . $obj->identification_no);
                Log::info('     Login ID :- ' . $obj->login_id);
                Log::info('     Status  :- ' . $obj->uo_record_status);
                $roles = PMService::getPMOrgGovUserRolesDetails($obj->user_org_id);
                $roles_name = null;
                foreach ($roles as $objRole) {
                    if ($roles_name == null) {
                        $roles_name = $objRole->role_code;
                    } else {
                        $roles_name = $roles_name . ', ' . $objRole->role_code;
                    }
                }
                Log::info('     Roles  :- ' . $roles_name);
                //Check Data from CRM
                $account = CRMService::getGovProfileCrm($obj->org_code, $obj->op_org_type_id);
                if ($account == null) {
                    $account = SyncGovernmentInfo::prepareCreateAccount($obj);
                }

                if ($account != null) {
                    
                    //Checking if Account is deleted,must set as Active.
                    if($account->deleted == 1){
                        Log::info('     Checking Account is Inactive. Set as Active :- ' . $account->org_gov_code. ' ID : '.$account->id);
                        DB::table('accounts')
                                ->where('id', $account->id)
                                ->update([
                                    'deleted' => 0
                        ]);
                    }
                    
                    Log::info('     Checking Account ORG CODE  :- ' . $account->org_gov_code. ' ID : '.$account->id);
                    Log::info($account);
                    /** Let find and check in CRM * */
                    /** FIND account -> contact * */
                    $contact = DB::table('accounts as a')
                            ->join('accounts_contacts as ac', 'a.id', '=', 'ac.account_id')
                            ->join('contacts as c', 'ac.contact_id', '=', 'c.id')
                            ->where('a.id', $account->id)
                            ->where('c.identity_no_nextgen', trim($obj->identification_no))
                            ->select('a.name as acc_name', 'a.id as acc_id', 'a.org_gov_code', 'a.org_gov_type')
                            ->addSelect('c.first_name as contact_name', 'c.id as contact_id', 'c.identity_no_nextgen', 'c.user_id_nextgen')
                            ->first();
                    if ($contact == null) {
                        
                        if ($account != null) {
                            
                            //Checking old record user in CRM, try to sync it
                            $query = DB::table('accounts as a')
                                ->join('accounts_contacts as ac', 'a.id', '=', 'ac.account_id')
                                ->join('contacts as c', 'ac.contact_id', '=', 'c.id')
                                ->join('contacts_cstm as cc', 'c.id', '=', 'cc.id_c')
                                ->where('ac.account_id', $account->id)
                                ->where('cc.ic_no_c', trim($obj->identification_no))
                                ->select('a.name as acc_name', 'a.id as acc_id')
                                ->addSelect('c.first_name as contact_name', 'c.id as contact_id', 'c.identity_no_nextgen', 'c.user_id_nextgen');
                            $contactOldRecord = $query->first();
                            
                            if ($contactOldRecord == null) {
                                Log::info('     Checking No Contact Found as Existing');
                    
                                if ($obj->uo_record_status == '1') {
                                    $contactObj = CRMService::createContactGovernment($account, $obj, $roles_name);
                                    Log::info('          :: ->  success create ' . $contactObj->id . ' RECORD_STATUS: ' . $contactObj->record_status_nextgen);
                                } else {
                                    Log::info('          :: ->  No need create this user . This user is belong Organization InActive');
                                }
                            }else{
                                Log::info('     Checking Contact Found as Existing :: '.$contactOldRecord->contact_id);
                                $contactObj = CRMService::saveContactGovernment($obj, $roles_name, $contactOldRecord);
                                CRMService::saveEmailContact($obj->email, $contactOldRecord->contact_id);
                            }
                            
                        }
                    } else {
                        //update it
                        Log::info('     found contact  :: '.$contact->contact_id);
                        $contactObj = CRMService::saveContactGovernment($obj, $roles_name, $contact);
                        CRMService::saveEmailContact($obj->email, $contact->contact_id);
                    }
                }
            }
            $contacts = DB::table('contacts')->where('login_id_nextgen', $loginID)->first();
            if($contacts == null){
                $contacts = DB::table('contacts')->where('identity_no_nextgen', $loginID)->first();
            }
            return Response::json($contacts);
        }
        
        return null;
    }

    /**
     * Manual request to Migrate Org from Nextgen to CRM
     *
     * @param  array  $data
     * @return User
     */
    public function syncOrganization($orgcode) {
        Log::info(self::class . ' Start '.__FUNCTION__.'     ->>syncOrganization ... OrgCode: '.$orgcode );
        var_dump(self::class . ' Start '.__FUNCTION__.'     ->>syncOrganization ... OrgCode: '.$orgcode );
       

        $obj = PMService::getPMOrgProfileDetailsByOrgCode($orgcode);

        if($obj){
            
           var_dump('Profile Ep Found.' .PMService::getOrgProfileType($obj->org_type_id) . ' -> ' . $orgcode);
            //var_dump('  '.json_encode($obj));
            Log::info('     ' . PMService::getOrgProfileType($obj->org_type_id) . ' -> ' . $orgcode);
            self::checkDataCrm($obj);
             
        }else{
            var_dump('Profile Ep Not Found. Try Search Profile With Inactive Status');
                //account might be inactive in ep..search again using record status inactive
            $obj = PMService::getPMOrgProfileDetailsByOrgCode($orgcode,true);
            self::checkDataCrm($obj);
        }
        
    }
    
    public function syncOrganizationByName($orgName){
        Log::info(self::class . ' Start '.__FUNCTION__.'     ->>syncOrganizationByName ... OrgName: '.$orgName );
        var_dump(self::class . ' Start '.__FUNCTION__.'     ->>syncOrganizationByName ... OrgName: '.$orgName );
       

        $obj = PMService::getPMOrgProfileDetailsByOrgName($orgName);

        if($obj){
            
           var_dump('Profile Ep Found.' .PMService::getOrgProfileType($obj->org_type_id) . ' -> ' . $orgName);
            //var_dump('  '.json_encode($obj));
            Log::info('     ' . PMService::getOrgProfileType($obj->org_type_id) . ' -> ' . $orgName);
            self::checkDataCrm($obj);
             
        }else{
            var_dump('Profile Ep Not Found. Try Search Profile With Inactive Status');
                //account might be inactive in ep..search again using record status inactive
            $obj = PMService::getPMOrgProfileDetailsByOrgName($orgName,true);
            self::checkDataCrm($obj);
        }
    }
    
    public function checkDataCrm($obj){
        $account = CRMService::getGovProfileCrm($obj->org_code, $obj->org_type_id);
            
            if ($account != null) {
                var_dump('Sync Account ' .$account->id);
                SyncGovernmentInfo::saveAccountPrimary($obj, $account->id);
                SyncGovernmentInfo::saveAccountAddress($obj->org_profile_id, $account->id);
            } else {
                var_dump('Account Not Found. Try Search By Account Name');
                $account = CRMService::getGovProfileCrmByName($obj->org_name);
                if ($account != null) {
                    var_dump('Account Search By Account Name Found.');
                    SyncGovernmentInfo::saveAccountPrimary($obj, $account->id);
                    SyncGovernmentInfo::saveAccountAddress($obj->org_profile_id, $account->id);
                }else{
                    var_dump('Account Search By Account Name Not Found.Create New.');
                    SyncGovernmentInfo::prepareCreateAccount($obj);
                }
                
            }
    }
}   