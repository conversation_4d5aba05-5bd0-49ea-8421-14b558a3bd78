<?php

namespace App\Http\Controllers\Common;

use App\Http\Controllers\Controller;
use App\Services\CrmGamudaService;
use App\Services\CrmSsmService;
use App\Services\CasbService;
use App\Services\CrmJbalService;
use App\Services\CRMService;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class UserCRM extends Controller
{
    protected $services;
    protected $crmGamudaService;
    protected $crmSsmService;
    protected $casbService;
    protected $crmJbalService;
    protected $crmService;

    public function __construct(
        CrmGamudaService $crmGamudaService,
        CrmSsmService $crmSsmService,
        CasbService $casbService,
        CrmJbalService $crmJbalService,
        CRMService $crmService
    ) {
        $this->crmGamudaService = $crmGamudaService;
        $this->crmSsmService = $crmSsmService;
        $this->casbService = $casbService;
        $this->crmJbalService = $crmJbalService;
        $this->crmService = $crmService;
    }

    public function getFromAllCRM(Request $request, $category, $searchQuery)
    {
        $apiKey = $request->header('Authorization');

        if (Str::startsWith($apiKey, 'Bearer ')) {
            $apiKey = Str::substr($apiKey, 7);
        }

        if (!$this->validateApiKey($apiKey)) {
            return response()->json(['error' => 'Invalid API key'], 401);
        }

        $services = [
            'CRMGamuda' => $this->crmGamudaService,
            'CRMSSM' => $this->crmSsmService,
            'CRMCASB' => $this->casbService,
            'CRMJBAL' => $this->crmJbalService,
            'CRMeP' => $this->crmService
        ];

        $results = [];

        foreach ($services as $connection => $service) {
            switch ($category) {
                case 'username':
                    // Search by username
                    $result = $service->getDetailUserCRMByUsername($searchQuery);
                    break;
                case 'name':
                    // Search by name
                    $result = $service->getDetailUserCRMByName($searchQuery);
                    break;
                case 'email':
                    // Search by email
                    $result = $service->getDetailUserCRMByEmail($searchQuery);
                    break;
                default:
                    throw new Exception("Invalid search category: $category");
            }

            if ($result) {
                $results[$connection] = $result;
            }
        }

        return response()->json($results);
    }

    public function deactivateUser(Request $request)
    {
        $apiKey = $request->header('Authorization');

        if (Str::startsWith($apiKey, 'Bearer ')) {
            $apiKey = Str::substr($apiKey, 7);
        }

        if (!$this->validateApiKey($apiKey)) {
            return response()->json(['error' => 'Invalid API key'], 401);
        }

        $services = [
            'CRMGamuda' => $this->crmGamudaService,
            'CRMSSM' => $this->crmSsmService,
            'CRMCASB' => $this->casbService,
            'CRMJBAL' => $this->crmJbalService,
            'CRMeP' => $this->crmService
        ];

        $crm = $request->input('crm');
        $userId = $request->input('userId');
        $ticketHelpdesk = $request->input('ticketHelpdesk');
        $remark = $request->input('remark');
        $authUserName = $request->input('authUserName');

        if (!array_key_exists($crm, $services)) {
            return response()->json(['error' => 'Invalid CRM'], 400);
        }

        $service = $services[$crm];
        $result = $service->deactivateUserEPSS($userId, $ticketHelpdesk, $remark, $authUserName);

        return response()->json(['success' => $result]);
    }

    private function validateApiKey($apiKey)
    {
        $validApiKeys = env('EPSS_USER_CRM_API_KEYS');

        return $apiKey === $validApiKeys;
    }
}
