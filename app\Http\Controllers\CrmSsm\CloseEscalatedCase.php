<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Http\Controllers\CrmSsm;

use App\Http\Controllers\Controller;
use App\Services\CrmSsmService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class CloseEscalatedCase extends Controller {

    public function __construct() {
        $this->middleware('auth');
    }

    public static function CrmSsmService() {
        return new CrmSsmService;
    }

    public static function closeEscalatedCase($dateStart, $dateEnd) {
        $start = 0;
        $skip = 500;
        $take = 500;
        $totalRecords = 0;

        $dtStartTimeOP = Carbon::now();

        do {
            $nextSkip = $start++ * $skip;

            $result = self::CrmSsmService()->getAssignedTask($dateStart, $dateEnd, $take, $nextSkip);
            $totalRecords = $totalRecords + count($result);

            $dtStartTimeEachLoop = Carbon::now();

            dump(self::class . ' current totalrecords ' . count($result));
            Log::info(self::class . ' current totalrecords ' . count($result));
            
            foreach ($result as $obj) {
                dump('Update Case Number: ' .$obj->case_number);
                self::CrmSsmService()->closeAssignedCase($obj->caseId);
                
            }
            $takentimeeachLoop = [
                'Counter' => $start,
                'Taken Time per Minutes' => $dtStartTimeEachLoop->diffInMinutes(Carbon::now()),
                'Taken Time per Seconds' => $dtStartTimeEachLoop->diffInSeconds(Carbon::now())
            ];
            dump(self::class . '    :: LoopTakenTime >> Time   :   ', [$takentimeeachLoop]);
            dump(self::class . '    :: sum total current  :   ' . $totalRecords);
            Log::info(self::class . '    :: LoopTakenTime >> Time   :   ', [$takentimeeachLoop]);
            Log::info(self::class . '    :: sum total current  :   ' . $totalRecords);
        } while (count($result) > 0 && count($result) == 500);

        $takentimeOP = [
            'Counter' => $start,
            'Taken Time per Minutes' => $dtStartTimeOP->diffInMinutes(Carbon::now()),
            'Taken Time per Seconds' => $dtStartTimeOP->diffInSeconds(Carbon::now())
        ];
        dump(self::class . '    :: AllLoopTakenTime >> Time   :   ', [$takentimeOP]);
        dump(self::class . ' queryReport. Total All :  ' . $totalRecords);
        dump(self::class . '--------------------------------------------'); 
        Log::info(self::class . '    :: AllLoopTakenTime >> Time   :   ', [$takentimeOP]);
        Log::info(self::class . ' queryReport. Total All :  ' . $totalRecords);
        Log::info(self::class . '--------------------------------------------'); 
    }

}
