<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Http\Controllers\CrmSsm;

use App\Http\Controllers\Controller;
use App\Services\CrmSsmService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class CrmSsmController extends Controller {

    public function __construct() {
        $this->middleware('guest');
    }

    public static function CrmSsmService() {
        return new CrmSsmService;
    }

    public static function updateCase($caseNumber) {
        $cases = self::CrmSsmService()->getCaseDetails($caseNumber);

        if (isset($cases)) {

            //update cases
            $updateCase = self::CrmSsmService()->updateCaseDetails($cases->id);

            //check tasks exist
            $tasks = self::CrmSsmService()->getTaskDetails($cases->id);

            if (isset($tasks)) {
                //update tasks
                self::CrmSsmService()->updateTaskDetails($tasks->id);
            }

            if ($updateCase == 1) {
                return "Success Update Case";
            }
        }
    }

}
