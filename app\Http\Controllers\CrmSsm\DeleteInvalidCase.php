<?php

namespace App\Http\Controllers\CrmSsm;

use App\Http\Controllers\Controller;
use App\Services\CrmSsmService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class DeleteInvalidCase extends Controller {

    public function __construct() {
        $this->middleware('auth');
    }

    public static function CrmSsmService() {
        return new CrmSsmService;
    }

    public static function deleteInvalidCase($dateStart, $dateEnd) {
        $start = 0;
        $skip = 500;
        $take = 500;
        $totalRecords = 0;

        $dtStartTimeOP = Carbon::now();

        do {
            $nextSkip = $start++ * $skip;

            $result = self::CrmSsmService()->getInvalidInboundCase($dateStart, $dateEnd, $take, $nextSkip);
            $totalRecords = $totalRecords + count($result);

            $dtStartTimeEachLoop = Carbon::now();

            dump(self::class . ' > '. __FUNCTION__ . ' current totalrecords ' . count($result));
            Log::info(self::class . ' > '. __FUNCTION__ . ' current totalrecords ' . count($result));

            foreach ($result as $obj) {
                dump(self::class . ' > '. __FUNCTION__ . ' Update Case Number: ' .$obj->case_number);
                $res = DB::connection('mysql_crm_ssm')
                        ->table('cases')
                        ->where('id', $obj->id)
                        ->update([
                            'deleted' => 1, 
                ]);
                if($res == 0){
                    dump(self::class . ' > '. __FUNCTION__ . ' Success Delete Case Number: ' .$obj->case_number);
                }
            }

            $takentimeeachLoop = [
                'Counter' => $start,
                'Taken Time per Minutes' => $dtStartTimeEachLoop->diffInMinutes(Carbon::now()),
                'Taken Time per Seconds' => $dtStartTimeEachLoop->diffInSeconds(Carbon::now())
            ];
            dump(self::class . ' > '. __FUNCTION__ . '    :: LoopTakenTime >> Time   :   ', [$takentimeeachLoop]);
            dump(self::class . ' > '. __FUNCTION__ . '    :: sum total current  :   ' . $totalRecords);
            Log::info(self::class . ' > '. __FUNCTION__ . '    :: LoopTakenTime >> Time   :   ', [$takentimeeachLoop]);
            Log::info(self::class . ' > '. __FUNCTION__ . '    :: sum total current  :   ' . $totalRecords);
        } while (count($result) > 0 && count($result) == 500);

        $takentimeOP = [
            'Counter' => $start,
            'Taken Time per Minutes' => $dtStartTimeOP->diffInMinutes(Carbon::now()),
            'Taken Time per Seconds' => $dtStartTimeOP->diffInSeconds(Carbon::now())
        ];
        dump(self::class . ' > '. __FUNCTION__ . '    :: AllLoopTakenTime >> Time   :   ', [$takentimeOP]);
        dump(self::class . ' > '. __FUNCTION__ . ' queryReport. Total All :  ' . $totalRecords);
        dump(self::class . ' > '. __FUNCTION__ . '--------------------------------------------'); 
        Log::info(self::class . ' > '. __FUNCTION__ . '    :: AllLoopTakenTime >> Time   :   ', [$takentimeOP]);
        Log::info(self::class . ' > '. __FUNCTION__ . ' queryReport. Total All :  ' . $totalRecords);
        Log::info(self::class . ' > '. __FUNCTION__ . '--------------------------------------------'); 
    }

    public static function deleteEmptyDescCase($dateStart, $dateEnd) {
        $start = 0;
        $skip = 500;
        $take = 500;
        $totalRecords = 0;

        $dtStartTimeOP = Carbon::now();
        dump(self::class . ' > ' . __FUNCTION__ .' Date Start: ' .$dateStart .', Date End: '.$dateEnd);
        Log::info(self::class . ' > ' . __FUNCTION__ .' Date Start: ' .$dateStart .', Date End: '.$dateEnd);
        do {
            $nextSkip = $start++ * $skip;

            $result = self::CrmSsmService()->getEmptyDescCase($dateStart, $dateEnd, $take, $nextSkip);
            $totalRecords = $totalRecords + count($result);

            $dtStartTimeEachLoop = Carbon::now();

            dump(self::class . ' > '. __FUNCTION__ . ' current totalrecords ' . count($result));
            Log::info(self::class . ' > '. __FUNCTION__ . ' current totalrecords ' . count($result));

            foreach ($result as $obj) {
                dump(self::class . ' > '. __FUNCTION__ . ' Update Case Number: ' .$obj->case_number);
                $res = DB::connection('mysql_crm_ssm')
                        ->table('cases')
                        ->where('id', $obj->id)
                        ->update([
                            'deleted' => 1, 
                ]);
                if($res == 0){
                    dump(self::class . ' > '. __FUNCTION__ . ' Success Delete Case Number: ' .$obj->case_number);
                }
            }

            $takentimeeachLoop = [
                'Counter' => $start,
                'Taken Time per Minutes' => $dtStartTimeEachLoop->diffInMinutes(Carbon::now()),
                'Taken Time per Seconds' => $dtStartTimeEachLoop->diffInSeconds(Carbon::now())
            ];
            dump(self::class . ' > '. __FUNCTION__ . '    :: LoopTakenTime >> Time   :   ', [$takentimeeachLoop]);
            dump(self::class . ' > '. __FUNCTION__ . '    :: sum total current  :   ' . $totalRecords);
            Log::info(self::class . ' > '. __FUNCTION__ . '    :: LoopTakenTime >> Time   :   ', [$takentimeeachLoop]);
            Log::info(self::class . ' > '. __FUNCTION__ . '    :: sum total current  :   ' . $totalRecords);
        } while (count($result) > 0 && count($result) == 500);

        $takentimeOP = [
            'Counter' => $start,
            'Taken Time per Minutes' => $dtStartTimeOP->diffInMinutes(Carbon::now()),
            'Taken Time per Seconds' => $dtStartTimeOP->diffInSeconds(Carbon::now())
        ];
        dump(self::class . ' > '. __FUNCTION__ . '    :: AllLoopTakenTime >> Time   :   ', [$takentimeOP]);
        dump(self::class . ' > '. __FUNCTION__ . ' queryReport. Total All :  ' . $totalRecords);
        dump(self::class . ' > '. __FUNCTION__ . '--------------------------------------------'); 
        Log::info(self::class . ' > '. __FUNCTION__ . '    :: AllLoopTakenTime >> Time   :   ', [$takentimeOP]);
        Log::info(self::class . ' > '. __FUNCTION__ . ' queryReport. Total All :  ' . $totalRecords);
        Log::info(self::class . ' > '. __FUNCTION__ . '--------------------------------------------'); 
    }

    public static function openInvalidCase($dateStart, $dateEnd) {
        $start = 0;
        $skip = 500;
        $take = 500;
        $totalRecords = 0;

        $dtStartTimeOP = Carbon::now();
        dump(self::class . ' > ' . __FUNCTION__ .' Date Start: ' .$dateStart .', Date End: '.$dateEnd);
        Log::info(self::class . ' > ' . __FUNCTION__ .' Date Start: ' .$dateStart .', Date End: '.$dateEnd);
        do {
            $nextSkip = $start++ * $skip;

            $result = self::CrmSsmService()->getOpenStateWithCloseStatusCase($dateStart, $dateEnd, $take, $nextSkip);
            $totalRecords = $totalRecords + count($result);

            $dtStartTimeEachLoop = Carbon::now();

            dump(self::class . ' > '. __FUNCTION__ . ' current totalrecords ' . count($result));
            Log::info(self::class . ' > '. __FUNCTION__ . ' current totalrecords ' . count($result));

            foreach ($result as $obj) {
                dump(self::class . ' > '. __FUNCTION__ . ' Update Case Number: ' .$obj->case_number);
                $res = DB::connection('mysql_crm_ssm')
                        ->table('cases')
                        ->where('id', $obj->id)
                        ->update([
                            'status' => 'Open_New', 
                ]);
                if($res == 0){
                    dump(self::class . ' > '. __FUNCTION__ . ' Success Update Case Number: ' .$obj->case_number);
                }
            }

            $takentimeeachLoop = [
                'Counter' => $start,
                'Taken Time per Minutes' => $dtStartTimeEachLoop->diffInMinutes(Carbon::now()),
                'Taken Time per Seconds' => $dtStartTimeEachLoop->diffInSeconds(Carbon::now())
            ];
            dump(self::class . ' > '. __FUNCTION__ . '    :: LoopTakenTime >> Time   :   ', [$takentimeeachLoop]);
            dump(self::class . ' > '. __FUNCTION__ . '    :: sum total current  :   ' . $totalRecords);
            Log::info(self::class . ' > '. __FUNCTION__ . '    :: LoopTakenTime >> Time   :   ', [$takentimeeachLoop]);
            Log::info(self::class . ' > '. __FUNCTION__ . '    :: sum total current  :   ' . $totalRecords);
        } while (count($result) > 0 && count($result) == 500);

        $takentimeOP = [
            'Counter' => $start,
            'Taken Time per Minutes' => $dtStartTimeOP->diffInMinutes(Carbon::now()),
            'Taken Time per Seconds' => $dtStartTimeOP->diffInSeconds(Carbon::now())
        ];
        dump(self::class . ' > '. __FUNCTION__ . '    :: AllLoopTakenTime >> Time   :   ', [$takentimeOP]);
        dump(self::class . ' > '. __FUNCTION__ . ' queryReport. Total All :  ' . $totalRecords);
        dump(self::class . ' > '. __FUNCTION__ . '--------------------------------------------'); 
        Log::info(self::class . ' > '. __FUNCTION__ . '    :: AllLoopTakenTime >> Time   :   ', [$takentimeOP]);
        Log::info(self::class . ' > '. __FUNCTION__ . ' queryReport. Total All :  ' . $totalRecords);
        Log::info(self::class . ' > '. __FUNCTION__ . '--------------------------------------------'); 
    }
}
