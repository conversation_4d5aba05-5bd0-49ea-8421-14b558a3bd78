<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Http\Controllers\CrmSsm;

use App\Http\Controllers\Controller;
use App\Services\CrmSsmService;
use Carbon\Carbon;
use DateTime;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class EmailListener extends Controller {

    public static function CrmSsmService() {
        return new CrmSsmService;
    }

    public static function checkEmailListener() {
        $inbound = self::CrmSsmService()->getLatestJobQue('Check Inbound Mailboxes');
        $current = Carbon::now();
        $executeTime = Carbon::parse($inbound->execute_time)->addHour(8)->format("Y-m-d H:i:s");
        $dateDiff = $current->diff(new DateTime($executeTime));
        if ($dateDiff->i > 15) {
            Log::info(self::class . ' >> ' . __FUNCTION__ . ' .. Restart Email Scheduler.. Job Id : ' . $inbound->id);
            self::CrmSsmService()->updateJobQueue($inbound->id);
            self::sendNotification();
        }
    }

    public static function sendNotification() {
        $msg = '[ALERT] Error Inbound Email Listener CRM SSM';
        $receiverGroup = 'INBOUND_CRM_SSM';
        DB::connection('mysql_ep_notify')
                ->insert('insert into notification  
                    (message,receiver_group,process,status,sent,date_entered,retry,source_app,source_class,source_remark) 
                    values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', [
                    $msg,
                    $receiverGroup,
                    'notify personal',
                    0, 0, Carbon::now(), 0,
                    'CRM INTEGRATION',
                    __CLASS__,
                    'Monitoring inbound email listener for CRM SSM'
        ]);
    }

}
