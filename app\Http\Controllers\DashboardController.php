<?php

namespace App\Http\Controllers;

use App\Services\DashboardService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class DashboardController extends Controller
{
    protected $dashboardService;

    public function __construct(DashboardService $dashboardService)
    {
        $this->dashboardService = $dashboardService;
    }

    public function index()
    {
        $systemMetrics = $this->dashboardService->getSystemMetrics();
        //$databaseConnections = $this->dashboardService->testAllDatabaseConnections();
        $databaseConnections = [];
        
        return view('dashboard', compact('systemMetrics', 'databaseConnections'));
    }

    public function getSystemStatus(): JsonResponse
    {
        $metrics = $this->dashboardService->getSystemMetrics();
        
        return response()->json([
            'status' => 'success',
            'data' => $metrics,
            'timestamp' => now()->toDateTimeString()
        ]);
    }

    public function testDatabaseConnections(): JsonResponse
    {
        $connections = $this->dashboardService->testAllDatabaseConnections();
        
        return response()->json([
            'status' => 'success',
            'data' => $connections,
            'timestamp' => now()->toDateTimeString()
        ]);
    }

    public function getScheduledTasks(): JsonResponse
    {
        $tasks = $this->dashboardService->getScheduledTasksInfo();
        
        return response()->json([
            'status' => 'success',
            'data' => $tasks,
            'timestamp' => now()->toDateTimeString()
        ]);
    }

    public function getConsoleCommands(): JsonResponse
    {
        $commands = $this->dashboardService->getArtisanCommands();
        
        return response()->json([
            'status' => 'success',
            'data' => $commands,
            'timestamp' => now()->toDateTimeString()
        ]);
    }

    public function getRecentLogs(): JsonResponse
    {
        $logs = $this->dashboardService->getRecentLogs();
        
        return response()->json([
            'status' => 'success',
            'data' => $logs,
            'timestamp' => now()->toDateTimeString()
        ]);
    }
}