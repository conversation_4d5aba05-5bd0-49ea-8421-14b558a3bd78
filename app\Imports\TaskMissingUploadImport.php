<?php

namespace App\Imports;

use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use App\Services\EPService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class TaskMissingUploadImport implements ToCollection, WithHeadingRow
{
    private $invalidCounter = 0;
    private $successCounter = 0;
    private $duplicateCounter = 0;
    private $updateCounter = 0;

    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {
            $this->processRow($row);
        }
    }

    private function processRow($row)
    {
        $caseNumber = intval($row['crm_case_no']);
        $case = DB::table('cases')->where('case_number', $caseNumber)->first();

        if ($case) {
            $problem = $case->description;

            $statusTask = trim($row['status']);
            $resolution = trim($row['remarks']);

            foreach (EPService::$TASK_MISSING_STATUS as $key => $value) {
                if ($value == strtoupper($statusTask)) {
                    $statusTask = $key;
                    break;
                }
            }

            if ($statusTask == null) {
                $statusTask = '00'; //Pending In Action
            }

            $is_case_closed = 0;
            if ($case->status == 'Closed_Closed' || $case->status == 'Open_Resolved') {
                $is_case_closed = 1;
                if ($statusTask != '55') {
                    $statusTask = '55'; //Done
                    $resolution = $resolution . '&#13;&#10;Updated &#13;&#10;Case CRM resolved on ' . $case->date_modified;
                }
            }

            $check = DB::connection('mysql_ep_support')->table('ep_task_missing')
                    ->where('case_no', $row['crm_case_no'])->count();

            if ($check == 0) {
                DB::connection('mysql_ep_support')
                ->insert('insert into ep_task_missing
                (   case_no,
                    case_status,
                    case_created,
                    batch,
                    doc_no,
                    module,
                    process_status,
                    composite_instance_id,
                    resolution,
                    problem,
                    is_case_closed,
                    created_at,
                    created_by )
                values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
                [
                    $row['crm_case_no'],
                    $case->status,
                    $case->date_entered,
                    $row['batch'],
                    $row['document_number'],
                    trim($row['module']),
                    $statusTask,
                    $row['composite_instance_id'],
                    $resolution,
                    $problem,
                    $is_case_closed,
                    date('Y-m-d H:i:s'),
                    'system'
                ]);
                $this->successCounter++;
            } else {
                // Check if task missing exists and handle update logic
                $taskMissObj = DB::connection('mysql_ep_support')->table('ep_task_missing')
                    ->where('case_no', $row['crm_case_no'])->first();

                if ($taskMissObj && $taskMissObj->process_status != '55') {
                    $module = $taskMissObj->module;
                    if ($module == '') {
                        $module = trim($row['module']);
                    }

                    $batch = $taskMissObj->batch;
                    if ($batch == '') {
                        $batch = trim($row['batch']);
                    }

                    DB::connection('mysql_ep_support')
                    ->table('ep_task_missing')
                    ->where('task_id', $taskMissObj->task_id)
                    ->update([
                        'module' => $module,
                        'batch' => $batch,
                        'case_status' => $case->status,
                        'is_case_closed' => $is_case_closed,
                        'resolution' => $resolution,
                        'process_status' => $statusTask,
                        'completed_at' => date('Y-m-d H:i:s'),
                        'completed_by' => 'system'
                    ]);

                    $this->updateCounter++;
                } else {
                    $this->duplicateCounter++;
                }
            }
        } else {
            $this->invalidCounter++;
        }
    }

    public function getCounters()
    {
        return [
            'invalid' => $this->invalidCounter,
            'success' => $this->successCounter,
            'duplicate' => $this->duplicateCounter,
            'update' => $this->updateCounter
        ];
    }
}
