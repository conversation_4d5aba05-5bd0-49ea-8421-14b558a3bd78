<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\Casb;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Facades\DB;
use App\Migrate\MigrateUtils;
use Maatwebsite\Excel\Facades\Excel;
use App\Services\CasbService;

class MigrateAccount {

    public static function casbService() {
        return new CasbService;
    }

    public static $FULL_PATH_FILE_NAME = '/app/Migrate/Casb/data/';

    public static function runMigrate($type) {
        Log::debug(self::class . ' Starting ... runMigrate ' . $type);
        $dtStartTime = Carbon::now();

        self::migrateAccountContact($type);

        Log::info(self::class . ' Completed runMigrate --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    public static function migrateAccountContact($type) {

        DB::connection('mysql_casb')->transaction(function() use ($type) {
            $newPath = self::$FULL_PATH_FILE_NAME . $type . '.xlsx';

            Excel::load($newPath, function($reader) use($type) {

                $reader->each(function($row) use($type) {
                    $nowDb = Carbon::now()->subHour(8);
                    $accId = Uuid::uuid4()->toString();
                    $contactId = Uuid::uuid4()->toString();
                    $salutation = $row->salutation;
                    $newSalutation = null;
                    $newState = null;

                    if (in_array($salutation, CasbService::$salutationList)) {
                        $newSalutation = $row->salutation;
                    } else {
                        if (in_array($salutation, CasbService::$enArray)) {
                            $newSalutation = 'Mr.';
                        } else if (in_array($salutation, CasbService::$cikArray)) {
                            $newSalutation = 'Ms.';
                        } else if (in_array($salutation, CasbService::$pnArray)) {
                            $newSalutation = 'Mrs.';
                        }
                    }

                    if (in_array($row->state, CasbService::$stateList)) {
                        $mystate = CasbService::getStateCode($row->state);
                        if (isset($mystate)) {
                            $newState = $mystate->value_code;
                        }
                    }

                    if ($row->contact_no !== '-' && $row->customer_name !== '-') {
                        $checkExist = CasbService::checkAccount($row->customer_name, $row->contact_no, $type);

                        if (isset($checkExist)) {
                            var_dump('Skip.. Account Already Exist ' . $checkExist->id . ' ' . $checkExist->name . ' ' . $row->contact_no);
                            Log::info('Skip.. Account Already Exist ' . $checkExist->id . ' ' . $checkExist->name . ' ' . $row->contact_no);
                        } else {
                            var_dump('Insert New Account ' . $row->customer_name . ' ' . $row->contact_no);
                            DB::connection('mysql_casb')->table('accounts')
                                    ->insertGetId([
                                        'id' => $accId,
                                        'name' => $row->customer_name,
                                        'date_entered' => $nowDb,
                                        'date_modified' => $nowDb,
                                        'modified_user_id' => 1,
                                        'created_by' => 1,
                                        'description' => null,
                                        'deleted' => 0,
                                        'assigned_user_id' => 1,
                                        'account_type' => $type,
                                        'phone_office' => $row->contact_no,
                                        'phone_alternate' => $row->alternate_contact_no,
                                        'billing_address_street' => $row->address_line1 . ' ' . $row->address_line2 . ' ' . $row->address_line3,
                                        'billing_address_city' => $row->city,
                                        'billing_address_state' => $newState,
                                        'billing_address_postalcode' => $row->postcode,
                                        'billing_address_country' => 60,
                            ]);

                            DB::connection('mysql_casb')->table('accounts_cstm')
                                    ->insertGetId([
                                        'id_c' => $accId
                            ]);

                            DB::connection('mysql_casb')->table('accounts_contacts')
                                    ->insertGetId([
                                        'id' => Uuid::uuid4()->toString(),
                                        'contact_id' => $contactId,
                                        'account_id' => $accId,
                                        'date_modified' => $nowDb,
                                        'deleted' => 0
                            ]);

                            DB::connection('mysql_casb')->table('contacts')
                                    ->insertGetId([
                                        'id' => $contactId,
                                        'date_entered' => $nowDb,
                                        'date_modified' => $nowDb,
                                        'modified_user_id' => 1,
                                        'created_by' => 1,
                                        'description' => null,
                                        'deleted' => 0,
                                        'assigned_user_id' => 1,
                                        'salutation' => $newSalutation,
                                        'first_name' => $row->customer_name,
                                        'last_name' => '.',
                                        'phone_mobile' => $row->contact_no,
                                        'phone_work' => $row->alternate_contact_no,
                                        'primary_address_street' => $row->address_line1 . ' ' . $row->address_line2 . ' ' . $row->address_line3,
                                        'primary_address_city' => $row->city,
                                        'primary_address_state' => $newState,
                                        'primary_address_postalcode' => $row->postcode,
                                        'primary_address_country' => 60,
                                        'portal_user_type' => 'Single',
                                        'identification_no' => $row->id_no,
                                        'contact_type_c' => null,
                            ]);

                            DB::connection('mysql_casb')->table('contacts_cstm')
                                    ->insertGetId([
                                        'id_c' => $contactId
                            ]);

                            if (!filter_var($row->email, FILTER_VALIDATE_EMAIL) == false) {
                                $emailId = Uuid::uuid4()->toString();
                                DB::connection('mysql_casb')->table('email_addresses')
                                        ->insertGetId([
                                            'id' => $emailId,
                                            'email_address' => trim(strtolower($row->email)),
                                            'email_address_caps' => trim(strtoupper($row->email)),
                                            'date_created' => $nowDb,
                                            'date_modified' => $nowDb,
                                            'invalid_email' => null,
                                            'opt_out' => null,
                                            'deleted' => 0,
                                ]);

                                DB::connection('mysql_casb')->table('email_addr_bean_rel')
                                        ->insertGetId([
                                            'id' => Uuid::uuid4()->toString(),
                                            'email_address_id' => $emailId,
                                            'bean_id' => $contactId,
                                            'bean_module' => 'Contacts',
                                            'primary_address' => null,
                                            'reply_to_address' => null,
                                            'date_created' => $nowDb,
                                            'date_modified' => $nowDb,
                                            'deleted' => 0,
                                ]);
                            }
                        }
                    }
                });
            });
        });
    }

}
