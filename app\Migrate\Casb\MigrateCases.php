<?php

namespace App\Migrate\Casb;

use Carbon\Carbon;
use Ramsey\Uuid\Uuid;
use App\Migrate\MigrateUtils;
use App\Services\CasbService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MigrateCases
{

    public static function casbService()
    {
        return new CasbService;
    }

    public static $FULL_PATH_FILE_NAME = '/app/Migrate/Casb/data/';

    public static function runMigrate($type)
    {
        Log::debug(self::class . ' Starting ... runMigrate ' . $type);
        $dtStartTime = Carbon::now();

        self::migrateCases($type);

        Log::info(self::class . ' Completed runMigrate --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    public static function migrateCases($type)
    {

        DB::connection('mysql_casb')->transaction(function () use ($type) {

            $tableName = '';
            if ($type === 'Altel') {
                $tableName = 'altel_cases';
            } elseif ($type === 'Mytv') {
                $tableName = 'mytv_cases';
            }

            $counter = 0;

            $casesList = DB::connection('mysql_casb_migration')->table($tableName)
                ->where('CustomerStatus', 'C')
                ->whereNotNull('CustomerName')
                ->where('is_migrated', 0)
                ->get();

            if ($casesList) {
                foreach ($casesList as $case) {
                    $counter++;
                    $nowDb = Carbon::now()->subHour(8);
                    $caseId = Uuid::uuid4()->toString();
                    $domainId = CasbService::$domainIdList[$type]['id'];
                    $productType = null;
                    $altPhoneNo = null;
                    $addressStreet = null;
                    $addressCity = null;
                    $addressState = null;
                    $postcode = null;
                    $salutation = null;
                    $custIdNumber = null;
                    $accountId = null;
                    $contactId = null;
                    $stbSerialNo = null;

                    if ($type === 'Altel') {
                        $caseCreated = Carbon::parse($case->CustomerCreatedDate)->subHour(8);
                        $caseModified = Carbon::parse($case->CustomerModifiedDate)->subHour(8);
                        $caseCategory = CasbService::getListCode($case->CallerTypeName, 'category_list');
                        $caseSubCategory = CasbService::getListCode($case->Outcome1, 'sub_category_list');
                        $caseSubSubCategory = CasbService::getListCode($case->Outcome2, 'sub_sub_category_list');
                        $caseSubSubSubCategory = CasbService::getListCode($case->Outcome3, 'sub_sub_sub_category_list');
                        $productType = $case->TicketProductType;
                        $altPhoneNo = $case->CustomerAlternateContactNo;
                        if(strlen($case->CustomerIDNumber) <= 20) $custIdNumber = $case->CustomerIDNumber;
                    } elseif ($type === 'Mytv') {
                        $caseCreated = Carbon::parse($case->DateCreated2)->subHour(8);
                        $caseModified = Carbon::parse($case->DateModified2)->subHour(8);
                        $caseCategory = CasbService::getListCode('N/A', 'category_list');
                        $caseSubCategory = CasbService::getListCode($case->TicketCategoryID, 'sub_category_list');
                        $caseSubSubCategory = null;
                        $caseSubSubSubCategory = null;
                        $addressStreet = $case->CustomerAddLine1 . ' ' . $case->CustomerAddLine2 . ' ' . $case->CustomerAddLine3;
                        $addressCity = $case->CustomerAddCity;
                        $postcode = $case->CustomerAddPostcode;
                        if(strlen($case->Sum_CustomerIDNumber) <= 20) $custIdNumber = $case->Sum_CustomerIDNumber;
                        $stbSerialNo = $case->CustomerSTBSerialNo;

                        if (in_array($case->CustomerAddState, CasbService::$stateList)) {
                            $mystate = CasbService::getStateCode($case->CustomerAddState);
                            if (isset($mystate)) {
                                $addressState = $mystate->value_code;
                            }
                        }

                        if (in_array($case->CustomerSalutation, CasbService::$salutationList)) {
                            $salutation = $case->CustomerSalutation;
                        } else {
                            if (in_array($case->CustomerSalutation, CasbService::$enArray)) {
                                $salutation = 'Mr.';
                            } else if (in_array($case->CustomerSalutation, CasbService::$cikArray)) {
                                $salutation = 'Ms.';
                            } else if (in_array($case->CustomerSalutation, CasbService::$pnArray)) {
                                $salutation = 'Mrs.';
                            }
                        }
                    }

                    $checkExist = CasbService::checkCases($case->TicketID, $caseCategory, $caseSubCategory, $domainId);

                    if (isset($checkExist)) {
                        var_dump($counter . ') Skip.. Case Already Exist ' . $checkExist->id . ' ' . $checkExist->name);
                        Log::info('Skip.. Case Already Exist ' . $checkExist->id . ' ' . $checkExist->name);
                    } else {
                        var_dump($counter . ') Insert New Case ' . $case->TicketID . ' > ' . $caseId);
                        Log::info($counter . ') Insert New Case ' . $case->TicketID . ' > ' . $caseId);

                        $account = CasbService::checkAccount($case->CustomerName, $case->CustomerContactNo, $type);
                        if ($account) {
                            var_dump('Account exist > ' . $account->id);
                            $accountId = $account->id;

                            $contact = CasbService::findContact($case->CustomerName, $case->CustomerContactNo);
                            if ($contact) $contactId = $contact->id;
                        } else {
                            $accId = Uuid::uuid4()->toString();
                            $newContactId = Uuid::uuid4()->toString();

                            var_dump('Insert new Account > ' . $accId . ' > ' . $case->CustomerName);

                            DB::connection('mysql_casb')->table('accounts')
                                ->insertGetId([
                                    'id' => $accId,
                                    'name' => $case->CustomerName,
                                    'date_entered' => $nowDb,
                                    'date_modified' => $nowDb,
                                    'modified_user_id' => 1,
                                    'created_by' => 1,
                                    'description' => null,
                                    'deleted' => 0,
                                    'assigned_user_id' => 1,
                                    'account_type' => $type,
                                    'phone_office' => $case->CustomerContactNo,
                                    'phone_alternate' => $altPhoneNo,
                                    'billing_address_street' => $addressStreet,
                                    'billing_address_city' => $addressCity,
                                    'billing_address_state' => $addressState,
                                    'billing_address_postalcode' => $postcode,
                                    'billing_address_country' => 60,
                                    'company_reg_no' => $stbSerialNo
                                ]);

                            DB::connection('mysql_casb')->table('accounts_cstm')
                                ->insertGetId([
                                    'id_c' => $accId
                                ]);

                            DB::connection('mysql_casb')->table('accounts_contacts')
                                ->insertGetId([
                                    'id' => Uuid::uuid4()->toString(),
                                    'contact_id' => $newContactId,
                                    'account_id' => $accId,
                                    'date_modified' => $nowDb,
                                    'deleted' => 0
                                ]);

                            DB::connection('mysql_casb')->table('contacts')
                                ->insertGetId([
                                    'id' => $newContactId,
                                    'date_entered' => $nowDb,
                                    'date_modified' => $nowDb,
                                    'modified_user_id' => 1,
                                    'created_by' => 1,
                                    'description' => null,
                                    'deleted' => 0,
                                    'assigned_user_id' => 1,
                                    'salutation' => $salutation,
                                    'first_name' => $case->CustomerName,
                                    'last_name' => '.',
                                    'phone_mobile' => $case->CustomerContactNo,
                                    'phone_work' => $altPhoneNo,
                                    'primary_address_street' => $addressStreet,
                                    'primary_address_city' => $addressCity,
                                    'primary_address_state' => $addressState,
                                    'primary_address_postalcode' => $postcode,
                                    'primary_address_country' => 60,
                                    'portal_user_type' => 'Single',
                                    'identification_no' => $custIdNumber,
                                    'contact_type_c' => null,
                                ]);

                            DB::connection('mysql_casb')->table('contacts_cstm')
                                ->insertGetId([
                                    'id_c' => $newContactId
                                ]);

                            if (!filter_var($case->CustomerEmail, FILTER_VALIDATE_EMAIL) == false) {
                                $emailId = Uuid::uuid4()->toString();
                                DB::connection('mysql_casb')->table('email_addresses')
                                    ->insertGetId([
                                        'id' => $emailId,
                                        'email_address' => trim(strtolower($case->CustomerEmail)),
                                        'email_address_caps' => trim(strtoupper($case->CustomerEmail)),
                                        'date_created' => $nowDb,
                                        'date_modified' => $nowDb,
                                        'invalid_email' => null,
                                        'opt_out' => null,
                                        'deleted' => 0,
                                    ]);

                                DB::connection('mysql_casb')->table('email_addr_bean_rel')
                                    ->insertGetId([
                                        'id' => Uuid::uuid4()->toString(),
                                        'email_address_id' => $emailId,
                                        'bean_id' => $newContactId,
                                        'bean_module' => 'Contacts',
                                        'primary_address' => null,
                                        'reply_to_address' => null,
                                        'date_created' => $nowDb,
                                        'date_modified' => $nowDb,
                                        'deleted' => 0,
                                    ]);
                            }

                            $accountId = $accId;
                            $contactId = $newContactId;
                        }

                        DB::connection('mysql_casb')->table('cases')
                            ->insertGetId([
                                'id' => $caseId,
                                'name' => $case->TicketID,
                                'date_entered' => $caseCreated,
                                'date_modified' => $caseModified,
                                'modified_user_id' => 1,
                                'created_by' => 1,
                                'deleted' => 0,
                                'description' => $case->TicketDescription,
                                // 'assigned_user_id' => $case->TicketEscalateTo,
                                'status' => 'Closed_Closed',
                                'state' => 'Closed',
                                'priority' => 'P3',
                                'urgency' => 'P3_Low',
                                'resolve_case' => 'Yes',
                                'account_id' => $accountId,
                                'contact_id' => $contactId,
                                'contact_mode' => $case->TicketChannelType,
                                'account_type' => $type,
                                'category' => $caseCategory,
                                'sub_category' => $caseSubCategory,
                                'sub_sub_category' => $caseSubSubCategory,
                                'sub_sub_sub_category' => $caseSubSubSubCategory,
                                'domain_id' => $domainId,
                                'product_type' => $productType
                            ]);

                        DB::connection('mysql_casb')->table('cases_cstm')
                            ->insertGetId([
                                'id_c' => $caseId
                            ]);

                        if ($contactId) {
                            DB::connection('mysql_casb')->table('contacts_cases')
                                ->insertGetId([
                                    'id' => Uuid::uuid4()->toString(),
                                    'contact_id' => $contactId,
                                    'case_id' => $caseId,
                                    'date_modified' => $caseCreated,
                                    'deleted' => 0
                                ]);
                        }

                        DB::connection('mysql_casb_migration')->table($tableName)
                            ->where('id', $case->id)
                            ->update(['is_migrated' => 1, 'migrated_date' => Carbon::now()]);
                    }
                }
            }
        });
    }
}
