<?php

namespace App\Migrate\Common;

use Illuminate\Support\Facades\DB;
use App\Services\CrmGamudaService;
use App\Services\CrmSsmService;
use App\Services\CasbService;
use App\Services\CrmJbalService;
use App\Services\CRMService;
use App\Models\EpSupportActionLog;
use Exception;

class SetInactiveUserCRM
{
    protected $services;
    protected $log;

    public function __construct(
        CrmGamudaService $crmGamudaService,
        CrmSsmService $crmSsmService,
        CasbService $casbService,
        CrmJbalService $crmJbalService,
        CRMService $crmService,
        EpSupportActionLog $log
    ) {
        $this->services = [
            $crmGamudaService,
            $crmSsmService,
            $casbService,
            $crmJbalService,
            $crmService
        ];
        $this->log = $log;
    }

    public function run($userEmail, $domainId)
    {
        $results = [];

        foreach ($this->services as $service) {
            try {
                // Get the database connection from the service
                $connection = $service::$DB_CONNECTION;

                // Get the ID from the email_addresses table
                $emailId = DB::connection($connection)->table('email_addresses')
                    ->where('email_address', $userEmail)
                    ->value('id');

                if (!$emailId) {
                    $results[] = [
                        'userEmail' => $userEmail,
                        'status' => 'NOT RELEVANT',
                        'DB_CONNECTION' => $connection
                    ];
                    continue;
                }

                // Get the bean_id from the email_addr_bean_rel table
                $beanId = DB::connection($connection)->table('email_addr_bean_rel')
                    ->where('email_address_id', $emailId)
                    ->where('bean_module', 'Users')
                    ->value('bean_id');

                if (!$beanId) {
                    $results[] = [
                        'userEmail' => $userEmail,
                        'status' => 'NOT RELEVANT',
                        'DB_CONNECTION' => $connection
                    ];
                    continue;
                }

                // Get the user status from the users table
                $userStatus = DB::connection($connection)->table('users')
                    ->where('id', $beanId)
                    ->value('status');

                if ($userStatus == 'Inactive') {
                    $results[] = [
                        'userEmail' => $userEmail,
                        'status' => 'ALREADY INACTIVE',
                        'DB_CONNECTION' => $connection
                    ];
                    continue;
                }

                // Update the user status in the users table
                $affectedRows = DB::connection($connection)->table('users')
                    ->where('id', $beanId)
                    ->update(['status' => 'Inactive']);

                // Add the result to the results array
                $results[] = [
                    'userEmail' => $userEmail,
                    'status' => $affectedRows > 0 ? 'success' : 'failure',
                    'DB_CONNECTION' => $connection
                ];
            } catch (Exception $e) {
                // Display the error message in the console
                $actionLog = $this->log->createActionLog('SetInactiveUserCRM', 'CRM', $e->getMessage(), $userEmail, $domainId);
                $this->log->updateActionLog($actionLog, 'Failed', $domainId);
                error_log("Error setting user as inactive: " . $e->getMessage());
            }
        }
        $actionLog = $this->log->createActionLog('SetInactiveUserCRM', 'CRM', json_encode($results), $userEmail, $domainId);
        $this->log->updateActionLog($actionLog, 'Success', $domainId);
        return $results;
    }

    public function reverse($userEmail, $domainId)
    {
        $results = [];

        foreach ($this->services as $service) {
            try {
                // Get the database connection from the service
                $connection = $service::$DB_CONNECTION;

                // Get the ID from the email_addresses table
                $emailId = DB::connection($connection)->table('email_addresses')
                    ->where('email_address', $userEmail)
                    ->value('id');

                if (!$emailId) {
                    $results[] = [
                        'userEmail' => $userEmail,
                        'status' => 'NOT RELEVANT',
                        'DB_CONNECTION' => $connection
                    ];
                    continue;
                }

                // Get the beanId from the email_addr_bean_rel table
                $beanId = DB::connection($connection)->table('email_addr_bean_rel')
                    ->where('email_address_id', $emailId)
                    ->where('bean_module', 'Users')
                    ->value('bean_id');

                // Get the user from the users table
                $user = DB::connection($connection)->table('users')
                    ->where('id', $beanId)
                    ->first();

                if (!$user) {
                    $results[] = [
                        'userEmail' => $userEmail,
                        'status' => 'NOT RELEVANT',
                        'DB_CONNECTION' => $connection
                    ];
                    continue;
                }

                if ($user->deleted != 0) {
                    $results[] = [
                        'userEmail' => $userEmail,
                        'status' => 'ALREADY DELETED',
                        'DB_CONNECTION' => $connection
                    ];
                    continue;
                }

                if ($user->employee_status == 'Terminated') {
                    $results[] = [
                        'userEmail' => $userEmail,
                        'status' => 'ALREADY TERMINATED',
                        'DB_CONNECTION' => $connection
                    ];
                    continue;
                }

                // Update the user status in the users table
                $affectedRows = DB::connection($connection)->table('users')
                    ->where('id', $beanId)
                    ->where('users.deleted', 0)
                    ->where('users.employee_status', '!=', 'Terminated')
                    ->update(['status' => 'Active']);

                // Add the result to the results array
                $results[] = [
                    'userEmail' => $userEmail,
                    'status' => $affectedRows > 0 ? 'success' : 'failure',
                    'DB_CONNECTION' => $connection
                ];
            } catch (Exception $e) {
                // Display the error message in the console
                $actionLog = $this->log->createActionLog('ReverseSetInactiveUserCRM', 'CRM', $e->getMessage(), $userEmail, $domainId);
                $this->log->updateActionLog($actionLog, 'Failed', $domainId);
                error_log("Error setting user as active: " . $e->getMessage());
            }
        }
        $actionLog = $this->log->createActionLog('ReverseSetInactiveUserCRM', 'CRM', json_encode($results), $userEmail, $domainId);
        $this->log->updateActionLog($actionLog, 'Success', $domainId);
        return $results;
    }
}
