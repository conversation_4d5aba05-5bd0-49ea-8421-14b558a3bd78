<?php

namespace App\Migrate\CrmSsm;

use Carbon\Carbon;
use App\Migrate\MigrateUtils;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use SSH;

class CheckEmailInboundCrmSsm {

    public static function runCheckEmailInboundSchedulerCrmSsm() {

        MigrateUtils::logDump(__METHOD__.' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();

        self::checkEmailInboundJobQue();

        MigrateUtils::logDump(__METHOD__.' Completed '. __FUNCTION__ .' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function checkEmailInboundJobQue() {
        $dtStartTime = Carbon::now();
        MigrateUtils::logDump(__METHOD__.' Start Check Email Inbound Job Queue [checkEmailInboundJobQue]' . __FUNCTION__ . ' Execution DateTime: ' . $dtStartTime);

        //check for stuck job queue
        $stuckJobList = DB::connection('mysql_crm_ssm')->select(
                        "SELECT id,NAME, STATUS, resolution, execute_time, message,
                        CONVERT_TZ(execute_time,'+00:00','+08:00') AS gmt8_execute_time 
                        FROM job_queue
                        WHERE NAME = 'Check Inbound Mailboxes'
                        AND STATUS = 'running'
                        AND `resolution` = 'queued'
                        ORDER BY `execute_time` DESC LIMIT 1");

        MigrateUtils::logDump(__METHOD__.' Total : ' . count($stuckJobList));

        if (count($stuckJobList) > 0) {
            foreach ($stuckJobList as $stuckJob) {

                $now = Carbon::now();
                $executionTime = new Carbon($stuckJob->execute_time);
                $timeExecute = $executionTime->addHour(8);
                $dateDiff = $now->diffInMinutes($timeExecute);

                MigrateUtils::logDump(__METHOD__.' Time Diff : ' . $dateDiff);
                /* update job queue if stuck more than 3 min */
                if ($dateDiff > 3) {
                    $timeExecute = Carbon::parse($stuckJob->gmt8_execute_time)->format('H:i');
                    self::updateJob($stuckJob);
                    //self::killCronProcess($timeExecute);
                }
            }
        } else {
            MigrateUtils::logDump(__METHOD__.' No stuck job found.');
        }
 
        MigrateUtils::logDump(__METHOD__.' Finished Check Email Inbound Job Queue [checkEmailInboundJobQue], Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function updateJob($job) {

        MigrateUtils::logDump(__METHOD__.' Entering updateJob()..... ' . $job->id);

        $dateModified = Carbon::now()->subHour(8);

        DB::connection('mysql_crm_ssm')->table('job_queue')
                ->where('id', $job->id)
                ->update([
                    'status' => 'done',
                    'resolution' => 'failed',
                    'date_modified' => $dateModified,
                    'message' => 'Update by CRM Scheduler'
        ]);

        MigrateUtils::logDump(__METHOD__.' successfully update ' . $job->id);

        MigrateUtils::logDump(__METHOD__.' Finished updateJob()..... ' . $job->id);
    }

    private static function killCronProcess($timeExecute)
    {
        MigrateUtils::logDump(__METHOD__.' Entering to kill cron process..... ');
        try {
            //$currentTime = Carbon::now()->subHour(8);
            $command = "ps aux | grep '{$timeExecute}'.*.'cron.php' | awk '{print $2}' | xargs kill -9";
            // $command = "ps aux | grep '{$currentTime->format('H:i')}.*cron.php' | awk '{print $2}' | xargs kill -9";
            MigrateUtils::logDump(__METHOD__.' >> run command in CRM SSM Server  : '.$command);
            SSH::into('crm-ssm')->run($command);

            MigrateUtils::logDump(__METHOD__.' Successfully kill cron process.');
        } catch (Exception $exception) {
            Log::error('Failed to kill cron process: ' . $exception->getMessage());
        }
    }
}
