<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\CrmSsm;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Migrate\MigrateUtils;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;

class CheckMissingTask {

    public static function run() {
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();

        self::checkMissingTask();

        Log::info(self::class . ' > ' . __FUNCTION__ . ' --- Completed. Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        var_dump(self::class . ' > ' . __FUNCTION__ . ' --- Completed. Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function checkMissingTask() {

        $missingTask = DB::connection('mysql_crm_ssm')
                ->select("select c.* from cases c 
                            left join tasks t on t.parent_id = c.id
                            where c.status = 'Open_Assigned'
                            and t.name is null");

        if (count($missingTask) > 0) {
            foreach ($missingTask as $data) {
                var_dump(self::class . __FUNCTION__ . ' Missing Task For Case ' . $data->case_number);
                Log::info(self::class . __FUNCTION__ . ' Missing Task For Case ' . $data->case_number);

                $taskName = '';
                if ($data->assigned_user_id === '3946a5ba-bbf2-841b-6525-620606b53fbf') {
                    $taskName = 'Escalate To : Group Ssm Email Escalation';
                } else if ($data->assigned_user_id === '9c2c7e26-810d-0079-489f-62060612050d') {
                    $taskName = 'Escalate To : Group Ssm Follow Up';
                } else if ($data->assigned_user_id === 'c310e672-ca05-08e7-dd81-620606dc7f72') {
                    $taskName = 'Escalate To : Group Ssm Urgent';
                }

                if ($taskName != '') {
                    $ss = DB::connection('mysql_crm_ssm')
                            ->insert('insert into tasks  
                                (id, name, date_entered, date_modified, modified_user_id, created_by, description,
                                assigned_user_id, status, date_due, date_start, parent_type, parent_id,  priority)
                                values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', [
                        Uuid::uuid4()->toString(),
                        $taskName,
                        $data->date_modified,
                        $data->date_modified,
                        $data->modified_user_id,
                        $data->modified_user_id,
                        Str::limit($data->description, 10000),
                        $data->assigned_user_id,
                        'Pending_Acknowledgement',
                        Carbon::parse($data->date_modified)->addMinutes(15),
                        $data->date_modified,
                        'Cases',
                        $data->id,
                        $data->priority
                    ]);
                    var_dump(self::class . __FUNCTION__ . ' Complete Create Task For Case ' . $data->case_number);
                    Log::info(self::class . __FUNCTION__ . ' Complete Create Task For Case ' . $data->case_number);
                }
            }
        }
    }

}
