<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\CrmSsm;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Migrate\MigrateUtils;

class DeleteDuplicateCaseSsm {

    public static function runCheckingDuplicateCase() {
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();

        self::checkDuplicateCase();
        self::checkInvalidAccount();
        self::checkCaseCloseWithOpenTasks();

        Log::info(self::class . ' Completed runCheckingDuplicateCase --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        var_dump(self::class . ' Completed runCheckingDuplicateCase --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function checkDuplicateCase() {

        $duplicateCase = DB::connection('mysql_crm_ssm')
                ->select("SELECT a.name, a.description, a.account_id FROM cases a, 
                    (SELECT NAME,description,account_id, COUNT(*) AS total FROM cases 
                    WHERE STATUS = 'Open_New' 
                    AND account_id <> ''
                    AND deleted = 0  
                    GROUP BY NAME,description,account_id 
                    HAVING total > 1) AS b 
                    WHERE a.name = b.name 
                    AND a.account_id = b.account_id 
                    AND a.description = b.description 
                    GROUP BY 1,2,3");

        if (count($duplicateCase) > 0) {
            foreach ($duplicateCase as $case) {
                $subject = $case->name;
                $desc = $case->description;
                $accId = $case->account_id;
                $allCases = self::getCases($subject, $desc, $accId, 'All');

                var_dump(self::class . __FUNCTION__ . ' Total Duplicate Case For Subject ' . $subject . ': ' . count($allCases) . '. Delete 1 case');
                Log::info(self::class . __FUNCTION__ . ' Total Duplicate Case For Subject ' . $subject . ': ' . count($allCases) . '. Delete 1 case');
                
                $latestCases = self::getCases($subject, $desc, $accId, 'Latest');
                
                var_dump(self::class . __FUNCTION__ . ' Delete Case ' . $latestCases->case_number);
                Log::info(self::class . __FUNCTION__ . ' Delete Case ' . $latestCases->case_number);
                
//                update case status to deleted
                DB::connection('mysql_crm_ssm')->table('cases')
                        ->where('id', $latestCases->id)
                        ->update([
                            'deleted' => 1
                ]);
            }
        }
    }

    private static function checkInvalidAccount() {
        $query = DB::connection('mysql_crm_ssm')
                ->select("select a.id,a.name,a.phone_office,a.phone_alternate,a.date_entered,a.created_by,e.bean_id,e.email_address_id 
                            from accounts a left join email_addr_bean_rel e on e.bean_id = a.id and e.bean_module = 'Accounts'
                            where a.name = '' and a.deleted = 0 and a.phone_office is null and a.phone_alternate is null and e.email_address_id is null and e.bean_id is null");

        if (count($query) > 0) {
            foreach ($query as $data) {   
                var_dump(self::class . __FUNCTION__ . ' Delete Account Id ' . $data->id);
                Log::info(self::class . __FUNCTION__ . ' Delete Account Id ' . $data->id);
                
                //update account
                DB::connection('mysql_crm_ssm')->table('accounts')
                        ->where('id', $data->id)
                        ->update([
                            'deleted' => 1
                ]);
            }
        }
    }
    
    /*
     * Scenario : Case status closed but task status still open
     *          : Usually happen because two agent update same case at same time
     *          : One agent update case to close, the other set to assigned > create task
     *          : Case modified_user_id and task created_by/modified_user_id usually different for this scenario
     */
    private static function checkCaseCloseWithOpenTasks() {
        $query = DB::connection('mysql_crm_ssm')
                ->select("SELECT c.case_number,c.date_entered,c.date_modified,c.status,c.modified_user_id,t.id,
                                t.name,t.date_entered,t.date_modified,t.created_by,t.modified_user_id
                        FROM cases c
                        JOIN tasks t ON t.parent_id = c.id
                        WHERE c.state = 'Closed' AND t.status <> 'Completed' and t.deleted = 0");

        if (count($query) > 0) {
            foreach ($query as $data) {   
                var_dump(self::class . __FUNCTION__ . ' Delete Task ' . $data->id);
                Log::info(self::class . __FUNCTION__ . ' Delete Task ' . $data->id);
                
                //update account
                DB::connection('mysql_crm_ssm')->table('tasks')
                        ->where('id', $data->id)
                        ->update([
                            'deleted' => 1
                ]);
            }
        }
    }
    
    private static function getCases($subject, $desc, $accId, $type) {

        if ($type == 'All') {
            $query = DB::connection('mysql_crm_ssm')->table('cases')
                            ->where('name', $subject)->where('description', $desc)
                            ->where('deleted', 0)
                            ->where('account_id', $accId)->get();
        } else {
            $query = DB::connection('mysql_crm_ssm')->table('cases')
                            ->where('name', $subject)->where('description', $desc)
                            ->where('deleted', 0)
                            ->where('account_id', $accId)->first();
        }

        return $query;
    }

}
