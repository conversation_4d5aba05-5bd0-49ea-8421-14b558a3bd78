<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\CrmSsm;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Facades\DB;
use App\Migrate\MigrateUtils;
use Excel;
use App\Services\CrmSsmService;

class MigrateCase {

    public static function crmService() {
        return new CrmSsmService;
    }

    public static $FULL_PATH_FILE_NAME = '/app/Migrate/CrmSsm/data/CrmSsm.xlsx';
    public static $DB_CONNECTION = 'mysql_crm_ssm'; // mysql_crm_ssm_dev/mysql_crm_ssm
    public static $QUERY_SKIP = 5000;
    public static $QUERY_TAKE = 5000;

    public static function runMigrateCase() {
        Log::debug(self::class . ' Starting Migrate Ssm Cases. method >> runMigrateCase ');
        var_dump(self::class . ' Starting Migrate Ssm Cases. method >> runMigrateCase ');
        $dtStartTime = Carbon::now();

        self::migrateCase();
        var_dump(self::class . ' Completed Migrate Ssm Cases. method >> runMigrate --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info(self::class . ' Completed Migrate SSM Cases. method >> runMigrateCase --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    public static function migrateCase() {

        $start = 0;
        $skip = self::$QUERY_SKIP;
        $take = self::$QUERY_TAKE;
        $count = 0;
        $total = 0;
        $arrayStatusClosed = array('Closed Case', 'Close for Customer to Email', 'Not SSM Related');
        $arrayStatusClosedSsm = array('Closed by SSM');
        $arrayStatusOpen = array('Escalation to SSM');

        do {
            $nextSkip = $start++ * $skip;
            $results = CrmSsmService::getListCases($nextSkip, $take);
            Log::info(' Count Total Query :- ' . count($results));
            var_dump(' Count Total Query :- ' . count($results));
            $total = $total + count($results);

            foreach ($results as $row) {
                $nowDb = Carbon::now()->subHour(8);
                $caseId = Uuid::uuid4()->toString();

                $oldCaseNo = $row->case_no;
                $oldCustomerId = $row->customer_id;
                $description = null;
                if ($row->description !== 'NULL') {
                    $description = $row->description;
                }

                $caseType = $row->case_type;
                $caseTypeValueCode = null;
                $caseTypeList = CrmSsmService::getValueCode($caseType, 'case_type_dom');
                if (isset($caseTypeList)) {
                    $caseTypeValueCode = $caseTypeList->value_code;
                }

                $category1 = $row->category_1;
                $category1ValueCode = null;
                $category1List = CrmSsmService::getValueCode($category1, 'category_list');
                if (isset($category1List)) {
                    $category1ValueCode = $category1List->value_code;
                }

                $category2 = $row->category_2;
                $category2ValueCode = null;
                $category2List = CrmSsmService::getValueCode($category2, 'category_2_list');
                if (isset($category2List)) {
                    $category2ValueCode = $category2List->value_code;
                }

                $category3 = $row->category_3;
                $category3ValueCode = null;
                $category3List = CrmSsmService::getValueCode($category3, 'category_3_list');
                if (isset($category3List)) {
                    $category3ValueCode = $category3List->value_code;
                }

                $category4 = $row->category_4;
                $category4ValueCode = null;
                $category4List = CrmSsmService::getValueCode($category4, 'category_4_list');
                if (isset($category4List)) {
                    $category4ValueCode = $category4List->value_code;
                }

                $category5 = $row->category_5;
                $category5ValueCode = null;
                $category5List = CrmSsmService::getValueCode($category5, 'category_5_list');
                if (isset($category5List)) {
                    $category5ValueCode = $category5List->value_code;
                }

                $category6 = $row->category_6;
                $category6ValueCode = null;
                $category6List = CrmSsmService::getValueCode($category6, 'category_6_list');
                if (isset($category6List)) {
                    $category6ValueCode = $category6List->value_code;
                }

                $category7 = $row->category_7;
                $category7ValueCode = null;
                $category7List = CrmSsmService::getValueCode($category7, 'category_7_list');
                if (isset($category7List)) {
                    $category7ValueCode = $category7List->value_code;
                }

                $severity = $row->severity;
                $severityValueCode = null;
                $severityList = CrmSsmService::getValueCode($severity, 'severity_list');
                if (isset($severityList)) {
                    $severityValueCode = $severityList->value_code;
                }

                $channel = $row->channel;
                $channelValueCode = null;
                $channelList = CrmSsmService::getValueCode($channel, 'channel_list');
                if (isset($channelList)) {
                    $channelValueCode = $channelList->value_code;
                }

                $priority = $row->priority;
                $priorityValueCode = null;
                $priorityList = CrmSsmService::getValueCode($priority, 'case_priority_dom');
                if (isset($priorityList)) {
                    $priorityValueCode = $priorityList->value_code;
                }

                $caseStatus = $row->case_status;
                $caseStatusValueCode = null;
                $caseState = null;
                if (in_array($caseStatus, $arrayStatusClosed)) {
                    $caseStatusValueCode = 'Closed_Closed';
                    $caseState = 'Closed';
                } else if (in_array($caseStatus, $arrayStatusClosedSsm)) {
                    $caseStatusValueCode = 'Closed_Closed_By_Ssm';
                    $caseState = 'Closed';
                } else {
                    $caseStatusValueCode = 'Open_Assigned';
                    $caseState = 'Open';
                }
//                $caseStatusList = CrmSsmService::getValueCode($caseStatus, 'case_status_dom');
//                if (isset($caseStatusList)) {
//                    $caseStatusValueCode = $caseStatusList->value_code;
//                    if ($caseStatusValueCode === 'Closed_Closed_By_Ssm' || $caseStatusValueCode === 'Closed_Closed') {
//                        $caseState = 'Closed';
//                    }
//                }

                $remark = null;
                if ($row->remark !== 'NULL') {
                    $remark = $row->remark;
                }

                $agent = 1;
                if ($row->agent_id !== 'NULL') {
                    $agentData = CrmSsmService::findAgent($row->agent_id);
                    if (count($agentData) === 1) {
                        $agent = $agentData[0]->id;
                    }
                }

                $dateCreated = Carbon::parse($row->date_created)->subHour(8);
                $dateModified = Carbon::parse($row->date_created)->subHour(8);

                $checkCaseExist = CrmSsmService::getParentCase($oldCaseNo);
                
                $accountId = null;
                $accountName = null;
                
                if (count($checkCaseExist) === 0) {
                    $accountData = CrmSsmService::getAccountId($oldCustomerId);
                    if (isset($accountData)) {
                        $accountId = $accountData->id;
                        $accountName = $accountData->name;
                    } else {
                        $res = CrmSsmService::findCustomerProfile($oldCustomerId);
                        if (isset($res)) {
                            $data = CrmSsmService::getAccountName($res[0]->customer_name, $res[0]->main_contact);
                            if (isset($data)) {
                                $accountId = $data->id;
                                $accountName = $data->name;
                            }
                        }else{
                            $dataEmail = CrmSsmService::getEmail($res[0]->customer_name, $res[0]->email);
                            if (isset($dataEmail)) {
                                $accountId = $dataEmail->id;
                                $accountName = $dataEmail->name;
                            }
                        }
                    }
                    if ($accountId !== null) {
                        $counter = $count++;
                        var_dump(' ' . $counter . '). Case No :- ' . $oldCaseNo . ' Old Customer Id :- ' . $oldCustomerId);

                        DB::connection(self::$DB_CONNECTION)->table('cases')
                                ->insertGetId([
                                    'id' => $caseId,
                                    'name' => $oldCaseNo,
                                    'date_entered' => $dateCreated,
                                    'date_modified' => $dateModified,
                                    'modified_user_id' => 1,
                                    'created_by' => 1,
                                    'deleted' => 0,
                                    'description' => $description,
                                    'assigned_user_id' => $agent,
                                    'type' => $caseTypeValueCode,
                                    'status' => $caseStatusValueCode,
                                    'priority' => $priorityValueCode,
                                    'account_id' => $accountId,
                                    'state' => $caseState,
                                    'category' => $category1ValueCode,
                                    'category_2' => $category2ValueCode,
                                    'category_3' => $category3ValueCode,
                                    'category_4' => $category4ValueCode,
                                    'category_5' => $category5ValueCode,
                                    'category_6' => $category6ValueCode,
                                    'category_7' => $category7ValueCode,
                                    'channel' => $channelValueCode,
                                    'severity' => $severityValueCode
                        ]);

                        DB::connection(self::$DB_CONNECTION)->table('cases_cstm')
                                ->insertGetId([
                                    'id_c' => $caseId
                        ]);
                    } else {
                        Log::info('Skip.. Account Id Not Found > ' . $row->case_no . '. Old Customer Id > ' . $row->customer_id);
                        var_dump('Skip.. Account Id Not Found > ' . $row->case_no . '. Old Customer Id > ' . $row->customer_id);
                    }
                }else{
                    var_dump('Skip.. Case Exist Already. > ' . $row->case_no);
                }
            }
        } while (count($results) > 0 && count($results) == $take);
        var_dump('     Total Results :- ' . $total);
        Log::info('     Total Results :- ' . $total);
    }

    public static function copyDescription() {
        var_dump('     Method copyDescription :- ');
        Log::info('    Method copyDescription :- ');

        $total = 0;
        $start = 0;
        $skip = self::$QUERY_SKIP;
        $take = self::$QUERY_TAKE;
        $count = 0;
        do {
            $nextSkip = $start++ * $skip;
            $res = DB::connection(self::$DB_CONNECTION)
                ->table('cases')->join('tasks', 'cases.id', '=', 'tasks.parent_id')
                ->whereDate('cases.date_entered', '<=', '2022-01-01')
                ->select('cases.name as subject', 'cases.name as casename','cases.date_entered')
                ->addSelect('cases.resolution as caseresolution', 'tasks.description as taskdescription')
                ->skip($nextSkip)->take($take)
                ->orderBy('tasks.date_entered', 'desc')
                ->get();
            Log::info(' Count Total Query :- ' . count($res));
            var_dump(' Count Total Query :- ' . count($res));
            $total = $total + count($res);

            foreach ($res as $row) {
                if ($row->caseresolution === null && $row->taskdescription !== null) {
                    $counter = $count++;
                    var_dump(' ' . $counter . '). Update description case :- ' . $row->casename);
                    DB::connection(self::$DB_CONNECTION)->table('cases')
                            ->where('name', $row->casename)
                            ->update(['resolution' => $row->taskdescription]);
                }
            }
        } while (count($res) > 0 && count($res) == $take);
        var_dump('     Total Results :- ' . $total);
        Log::info('     Total Results :- ' . $total);
    }

}
