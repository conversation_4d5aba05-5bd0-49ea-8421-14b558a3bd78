<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\CrmSsm;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Facades\DB;
use App\Migrate\MigrateUtils;
use Excel;
use App\Services\CrmSsmService;

class MigrateCaseHistory {

    public static function crmService() {
        return new CrmSsmService;
    }

    public static $FULL_PATH_FILE_NAME = '/app/Migrate/CrmSsm/data/CrmSsm.xlsx';
    public static $DB_CONNECTION = 'mysql_crm_ssm'; // mysql_crm_ssm_dev/mysql_crm_ssm
    public static $QUERY_SKIP = 5000;
    public static $QUERY_TAKE = 5000;

    public static function runMigrateCaseHistory() {
        Log::debug(self::class . ' Starting Migrate Ssm Cases History. method >> runMigrateCase ');
        var_dump(self::class . ' Starting Migrate Ssm Cases History. method >> runMigrateCase ');
        $dtStartTime = Carbon::now();

        self::migrateCaseHistory();
        var_dump(self::class . ' Completed Migrate Ssm Cases History. method >> runMigrate --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info(self::class . ' Completed Migrate SSM Cases History. method >> runMigrateCase --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    public static function migrateCaseHistory() {

        $start = 0;
        $skip = self::$QUERY_SKIP;
        $take = self::$QUERY_TAKE;
        $count = 0;
        $total = 0;

        do {
            $nextSkip = $start++ * $skip;
            $results = CrmSsmService::getListCaseHistory($nextSkip, $take);
            Log::info(' Count Total Query :- ' . count($results));
            var_dump(' Count Total Query :- ' . count($results));
            $total = $total + count($results);

            foreach ($results as $row) {
                $nowDb = Carbon::now()->subHour(8);
                $taskId = Uuid::uuid4()->toString();
                $oldCaseNo = $row->case_no;
                $status = $row->status;
                $createdBy = $row->created_by;
                $notes = $row->notes;

                $trimnotes = preg_replace('!\s+!', ' ', trim($notes));
                $description = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($trimnotes));

                $dateCreated = Carbon::parse($row->date_created)->subHour(8);
                $findParentCase = CrmSsmService::getParentCase($oldCaseNo);

                $agent = 1;
                if ($createdBy !== 'NULL') {
                    $agentData = CrmSsmService::findAgent($createdBy);
                    if (count($agentData) === 1) {
                        $agent = $agentData[0]->id;
                    }
                }

                if (isset($findParentCase)) {
                    $counter = $count++;
                    $caseId = $findParentCase->id;
                    $casePriority = $findParentCase->priority;
                    $caseAssignTo = $findParentCase->assigned_user_id;
                    var_dump(' ' . $counter . '). Case No :- ' . $oldCaseNo . ' Case Id :- ' . $caseId);

                    DB::connection(self::$DB_CONNECTION)->table('tasks')
                            ->insertGetId([
                                'id' => $taskId,
                                'name' => $status,
                                'date_entered' => $dateCreated,
                                'date_modified' => $dateCreated,
                                'modified_user_id' => $agent,
                                'created_by' => $agent,
                                'description' => $notes,
                                'deleted' => 0,
                                'assigned_user_id' => $caseAssignTo,
                                'status' => 'Completed',
                                'parent_type' => 'Cases',
                                'parent_id' => $caseId,
                                'priority' => $casePriority
                    ]);
                } else {
                    var_dump('Skip. Parent Case Id Not Found. Case No >  ' . $oldCaseNo);
                    Log::info('Skip. Parent Case Id Not Found. Case No >  ' . $oldCaseNo);
                }
            }
        } while (count($results) > 0 && count($results) == $take);
        var_dump('     Total Results :- ' . $total);
        Log::info('     Total Results :- ' . $total);
    }

    public static function insertCase() {
        
    }

}
