<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\CrmSsm;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Facades\DB;
use App\Migrate\MigrateUtils;
use Excel;
use App\Services\CrmSsmService;

class MigrateCustomerProfile {

    public static function crmService() {
        return new CrmSsmService;
    }

    public static $DB_CONNECTION = 'mysql_crm_ssm'; // mysql_crm_ssm_dev/mysql_crm_ssm
    public static $QUERY_SKIP = 5000;
    public static $QUERY_TAKE = 5000;

    public static function runMigrate() {
        Log::debug(self::class . ' Starting Migrate Customer Profile. method >> runMigrate ');
        var_dump(self::class . ' Starting Migrate Customer Profile. method >> runMigrate ');
        $dtStartTime = Carbon::now();

        self::migrateCustomerProfile();
        var_dump(self::class . ' Completed Migrate Customer Profile. method >> runMigrate --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info(self::class . ' Completed Migrate Customer Profile. method >> runMigrate --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    public static function migrateCustomerProfile() {

        $start = 0;
        $skip = self::$QUERY_SKIP;
        $take = self::$QUERY_TAKE;
        $count = 0;
        $total = 0;

        do {
            $nextSkip = $start++ * $skip;
            $listProfiles = CrmSsmService::getListProfiles($nextSkip, $take);
            Log::info(' Count Total Query :- ' . count($listProfiles));
            var_dump(' Count Total Query :- ' . count($listProfiles));
            $total = $total + count($listProfiles);

            foreach ($listProfiles as $row) {
                $nowDb = Carbon::now()->subHour(8);
                $accId = Uuid::uuid4()->toString();
                $oldCustomerId = $row->customer_id;
                $customerName = $row->customer_name;
                $emailAddress = null;
                if ($row->email !== null) {
                    $emailAddress = $row->email;
                }
                $registrationNo = null;
                if ($row->reg_no !== null) {
                    $registrationNo = $row->reg_no;
                }

                $profileCategory = $row->profile_category;
                $contactNo = null;
                if ($row->main_contact !== null) {
                    $contactNo = $row->main_contact;
                    if(is_numeric($contactNo) && $contactNo[0] !== '0'){
                        $contactNo = '0'.$contactNo;
                    }
                }
                $fax = null;
                if ($row->fax !== null) {
                    $fax = $row->fax;
                }

                $profileCategoryValueCode = null;

                $profileCategoryList = CrmSsmService::getValueCode($profileCategory, 'account_type_dom');
                if (isset($profileCategoryList)) {
                    $profileCategoryValueCode = $profileCategoryList->value_code;
                }
//                Log::info($oldCustomerId .' > ' .$contactNo);
                if (isset($customerName)) {
                    $checkExist = CrmSsmService::checkAccount($customerName, $registrationNo, $emailAddress, $contactNo);
                    if (isset($checkExist)) {
                        var_dump('Skip.. Account Already Exist ' . $checkExist->id . '. Existing Name > ' . $checkExist->name . '. Migrated Name > ' . $customerName . ' Email :- ' . $emailAddress . ' Phone :- ' . $contactNo);
                        Log::info('Skip.. Account Already Exist ' . $checkExist->id . '. Existing Name > ' . $checkExist->name . '. Migrated Name > ' . $customerName. ' Email :- ' . $emailAddress . ' Phone :- ' . $contactNo);
                    } else {
                        $counter = $count++;
                        var_dump(' ' . $counter . '). Customer Id :- ' . $oldCustomerId . ' Customer Name :- ' . $customerName . ' Email :- ' . $emailAddress);

                        DB::connection(self::$DB_CONNECTION)->table('accounts')
                                ->insertGetId([
                                    'id' => $accId,
                                    'name' => $customerName,
                                    'date_entered' => $nowDb,
                                    'date_modified' => $nowDb,
                                    'modified_user_id' => 1,
                                    'created_by' => 1,
                                    'description' => null,
                                    'deleted' => 0,
                                    'assigned_user_id' => 1,
                                    'account_type' => $profileCategoryValueCode,
                                    'phone_office' => $contactNo,
                                    'phone_fax' => $fax,
                                    'company_reg_no' => $registrationNo,
                                    'company_old_id' => $oldCustomerId
                        ]);

                        DB::connection(self::$DB_CONNECTION)->table('accounts_cstm')
                                ->insertGetId([
                                    'id_c' => $accId
                        ]);


                        if (!filter_var($emailAddress, FILTER_VALIDATE_EMAIL) == false) {
                            $emailId = Uuid::uuid4()->toString();
                            DB::connection(self::$DB_CONNECTION)->table('email_addr_bean_rel')
                                    ->insertGetId([
                                        'id' => Uuid::uuid4()->toString(),
                                        'email_address_id' => $emailId,
                                        'bean_id' => $accId,
                                        'bean_module' => 'Accounts',
                                        'primary_address' => 1,
                                        'reply_to_address' => 0,
                                        'date_created' => $nowDb,
                                        'date_modified' => $nowDb,
                                        'deleted' => 0,
                            ]);

                            DB::connection(self::$DB_CONNECTION)->table('email_addresses')
                                    ->insertGetId([
                                        'id' => $emailId,
                                        'email_address' => trim(strtolower($emailAddress)),
                                        'email_address_caps' => trim(strtoupper($emailAddress)),
                                        'date_created' => $nowDb,
                                        'date_modified' => $nowDb,
                                        'invalid_email' => 0,
                                        'opt_out' => 0,
                                        'deleted' => 0,
                            ]);
                        }
                    }
                }
            }
        } while (count($listProfiles) > 0 && count($listProfiles) == $take);
        var_dump('     Total Results :- ' . $total);
        Log::info('     Total Results :- ' . $total);
    }
}
