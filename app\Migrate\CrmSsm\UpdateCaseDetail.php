<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\CrmSsm;

use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use Illuminate\Support\Facades\Log;

class UpdateCaseDetail
{

    public static function run($fileName)
    {

        $file = 'app/Migrate/CrmSsm/data' . "/$fileName";
        $reader = new Csv;
        $spreadsheet = $reader->load($file);
        $sheetData = $spreadsheet->getActiveSheet()->toArray();
        if (!empty($sheetData)) {
            for ($i = 1; $i < count($sheetData); $i++) {
                $caseNumber = $sheetData[$i][0];
                $caseType = $sheetData[$i][1];
                $category = $sheetData[$i][2];
                $category2 = $sheetData[$i][3];
                $category3 = $sheetData[$i][4];
                $category4 = $sheetData[$i][5];
                $category5 = $sheetData[$i][6];
                $category6 = $sheetData[$i][7];

                $categoryDropdown = self::categoryListAppData($caseType, $category, $category2, $category3, $category4, $category5, $category6);

                if (count($categoryDropdown) > 0) {
                    DB::connection('mysql_crm_ssm')
                        ->table('cases')
                        ->where('cases.case_number', $caseNumber)
                        ->update([
                            'type' => $categoryDropdown[0]->casetype_code,
                            'category' => $categoryDropdown[0]->cat_code,
                            'category_2' => $categoryDropdown[0]->cat2_code,
                            'category_3' => $categoryDropdown[0]->cat3_code,
                            'category_4' => $categoryDropdown[0]->cat4_code,
                            'category_5' => $categoryDropdown[0]->cat5_code,
                            'category_6' => $categoryDropdown[0]->cat6_code,
                        ]);
                    Log::info(self::class . ' > ' . __FUNCTION__ . ' > Category Updated! Case: ' . $caseNumber);
                } else {
                    dump('Category Match Not Found! Case: ' . $caseNumber);
                    Log::info(self::class . ' > ' . __FUNCTION__ . ' > Category Match Not Found! Case: ' . $caseNumber);
                }
            }
        }
        dump('Completed!');
    }

    public static function closeCase($dateStart, $dateEnd)
    {
        dump(self::class . ' > ' . __FUNCTION__ . ' > Date Start: ' . $dateStart . ' > Date End: ' . $dateEnd);
        Log::info(self::class . ' > ' . __FUNCTION__ . ' > Date Start: ' . $dateStart . ' > Date End: ' . $dateEnd);

        $query = DB::connection('mysql_crm_ssm')->table('cases')
            ->where('deleted', 0)
            ->whereIn('status', ['Open_New', 'Open_Assigned'])
            ->whereBetween(DB::raw("DATE(CONVERT_TZ(date_entered,'+00:00','+08:00'))"), [$dateStart, $dateEnd])
            ->get();

        if (count($query) > 0) {
            foreach ($query as $data) {
                $resolution = $data->resolution;
                $newResolution =  $resolution . "\n\n Bulk close request - 24 July 2023.";
                $dateNowDb = Carbon::now()->subHour(8)->format('Y-m-d H:i:s');
                DB::connection('mysql_crm_ssm')
                    ->table('cases')
                    ->where('id', $data->id)
                    ->update([
                        'state' => 'Closed',
                        'status' => 'Closed_Closed_By_Ssm_No_Email',
                        'date_modified' => $dateNowDb,
                        'modified_user_id' => 1,
                        'resolution' => $newResolution,
                    ]);

                dump(self::class . ' > ' . __FUNCTION__ . ' > Update Case: ' . $data->case_number);
                Log::info(self::class . ' > ' . __FUNCTION__ . ' > Update Case: ' . $data->case_number);

                $task = DB::connection('mysql_crm_ssm')->table('tasks')
                    ->where('deleted', 0)->where('parent_id', $data->id)
                    ->whereIn('status', ['Acknowledge', 'Pending_Acknowledgement'])
                    ->get();

                if (count($task) > 0) {
                    foreach ($task as $row) {
                        $taskReso = $row->resolution;
                        $newTaskReso = $taskReso . "\n\n Bulk close request - 24 July 2023.";
                        DB::connection('mysql_crm_ssm')
                            ->table('tasks')
                            ->where('id', $row->id)
                            ->update([
                                'status' => 'Completed',
                                'date_modified' => $dateNowDb,
                                'modified_user_id' => 1,
                                'resolution' => $newTaskReso,
                            ]);
                        dump(self::class . ' > ' . __FUNCTION__ . ' > Update Task Id: ' . $row->id);
                    }
                }
            }
        }
    }

    public static function closeCaseBulk($limit)
    {
        $caseNumbersAfter = array();
        $caseNumbersBefore = DB::connection('mysql_crm_ssm')->table('cases_bulk_closed')
            ->where('status', 'Pending')
            ->limit($limit)
            ->get([
                'case_number',
            ]);

        foreach($caseNumbersBefore as $eachNumber) {
            array_push($caseNumbersAfter, $eachNumber->case_number);
        }

        $query = DB::connection('mysql_crm_ssm')->table('cases')
            ->where('deleted', 0)
            ->whereIn('case_number', $caseNumbersAfter)
            ->get();

        if (count($query) > 0) {
            foreach ($query as $data) {
                // Scuffed way of getting append resolution
                $append_resolution_query = DB::connection('mysql_crm_ssm')->table('cases_bulk_closed')
                ->where('case_number', $data->case_number)
                ->get([
                    'append_resolution'
                ]);;
                $append_resolution = '';
                if(count($append_resolution_query) == 1) {
                    foreach($append_resolution_query as $new_resolution_query) {
                        $append_resolution = $new_resolution_query->append_resolution;
                    }
                }
                $resolution = $data->resolution;
                $newResolution =  $resolution . "\n\n " . $append_resolution;
                $dateNowDb = Carbon::now()->subHour(8)->format('Y-m-d H:i:s');
                DB::connection('mysql_crm_ssm')
                    ->table('cases')
                    ->where('id', $data->id)
                    ->update([
                        'state' => 'Closed',
                        'status' => 'Closed_Closed_By_Ssm_No_Email',
                        'date_modified' => $dateNowDb,
                        'modified_user_id' => 1,
                        'resolution' => $newResolution,
                    ]);

                dump(self::class . ' > ' . __FUNCTION__ . ' > Update Case: ' . $data->case_number);
                Log::info(self::class . ' > ' . __FUNCTION__ . ' > Update Case: ' . $data->case_number);

                $task = DB::connection('mysql_crm_ssm')->table('tasks')
                    ->where('deleted', 0)->where('parent_id', $data->id)
                    ->whereIn('status', ['Acknowledge', 'Pending_Acknowledgement'])
                    ->get();

                if (count($task) > 0) {
                    foreach ($task as $row) {
                        $taskReso = $row->resolution;
                        $newTaskReso = $taskReso . "\n\n Bulk close request - 14 September 2023.";
                        DB::connection('mysql_crm_ssm')
                            ->table('tasks')
                            ->where('id', $row->id)
                            ->update([
                                'status' => 'Completed',
                                'date_modified' => $dateNowDb,
                                'modified_user_id' => 1,
                                'resolution' => $newTaskReso,
                            ]);
                        dump(self::class . ' > ' . __FUNCTION__ . ' > Update Task Id: ' . $row->id);
                    }
                }

                DB::connection('mysql_crm_ssm')->table('cases_bulk_closed')
                    ->where('case_number', $data->case_number)
                    ->update([
                        'status' => 'Completed'
                    ]);
            }
        }
        dump(self::class . ' > ' . __FUNCTION__ . ' > CASE CLOSE BY BULK COMPLETED: ' . Carbon::now()->format("Y-m-d H:i:s"));
        Log::info(self::class . ' > ' . __FUNCTION__ . ' > CASE CLOSE BY BULK COMPLETED: ' . Carbon::now()->format("Y-m-d H:i:s"));
    }

    public static function categoryListAppData($caseType, $category, $category2, $category3, $category4, $category5, $category6)
    {
        return DB::connection('mysql_crm_ssm')->select("SELECT * FROM (
                        SELECT value_name, value_code AS casetype_code, value_name AS casetype_name
                        FROM cstm_list_app casetype WHERE casetype.type_code = 'case_type_dom' AND casetype.status = 1) a

                        LEFT JOIN

                        (SELECT value_name, value_code AS cat_code, value_name AS cat_name 
                        FROM cstm_list_app cat WHERE cat.type_code = 'category_list' AND cat.status = 1) b ON b.cat_code LIKE CONCAT(a.casetype_code,'_%')
                        AND a.casetype_code <> ''

                        LEFT JOIN 

                        (SELECT value_name, value_code AS cat2_code, value_name AS cat2_name 
                        FROM cstm_list_app cat2 WHERE cat2.type_code = 'category_2_list' AND cat2.status = 1) c ON c.cat2_code LIKE CONCAT(b.cat_code,'_%')
                        AND b.cat_code <> ''

                        LEFT JOIN 

                        (SELECT value_name, value_code AS cat3_code, value_name AS cat3_name 
                        FROM cstm_list_app cat3 WHERE cat3.type_code = 'category_3_list' AND cat3.status = 1) d ON d.cat3_code LIKE CONCAT(c.cat2_code,'_%')
                        AND c.cat2_code <> ''

                        LEFT JOIN 

                        (SELECT value_name, value_code AS cat4_code, value_name AS cat4_name 
                        FROM cstm_list_app cat4 WHERE cat4.type_code = 'category_4_list' AND cat4.status = 1) e ON e.cat4_code LIKE CONCAT(d.cat3_code,'_%')
                        AND d.cat3_code <> ''

                        LEFT JOIN 

                        (SELECT value_name, value_code AS cat5_code, value_name AS cat5_name 
                        FROM cstm_list_app cat5 WHERE cat5.type_code = 'category_5_list' AND cat5.status = 1) f ON f.cat5_code LIKE CONCAT(e.cat4_code,'_%')
                        AND e.cat4_code <> ''

                        LEFT JOIN 

                        (SELECT value_name, value_code AS cat6_code, value_name AS cat6_name 
                        FROM cstm_list_app cat6 WHERE cat6.type_code = 'category_6_list' AND cat6.status = 1) g ON g.cat6_code LIKE CONCAT(f.cat5_code,'_%')
                        AND f.cat5_code <> ''

                        WHERE a.value_name = '$caseType' AND b.value_name = '$category'  AND c.value_name = '$category2' AND d.value_name = '$category3'
                        AND e.value_name = '$category4' AND f.value_name = '$category5' AND g.value_name = '$category6'");
    }

    public static function statusListAppData($state, $status)
    {
        return DB::connection('mysql_crm_ssm')->select("SELECT * FROM (
            SELECT value_code AS case_state_code, value_name AS case_state_name
            FROM cstm_list_app casetype WHERE casetype.type_code = 'case_state_dom' AND casetype.status = 1) a

            LEFT JOIN

            (SELECT value_code AS case_status_code, value_name AS case_status_name 
            FROM cstm_list_app cat WHERE cat.type_code = 'case_status_dom' AND cat.status = 1) b ON b.case_status_code LIKE CONCAT(a.case_state_code,'_%')
            AND a.case_state_code <> '' 

            WHERE a.case_state_name = '$state' AND a.case_status_name = '$status'");
    }

    public static function readCaseNumberFromExcel($fileName)
    {
        // File path of the local Excel file
        $filePath = 'app/Migrate/CrmSsm/data' . "/$fileName";

        // Load the Excel file and get the "Case Number" column data as an array
        $spreadsheet = IOFactory::load($filePath);
        $worksheet = $spreadsheet->getActiveSheet();

        // Find the column index based on the header "Case Number"
        $caseHeaderCoordinate = Coordinate::stringFromColumnIndex(1) . '1'; // Assuming the header is in the first row (row 1)
        $caseHeaderCell = $worksheet->getCell($caseHeaderCoordinate);
        $caseHeaderColumnIndex = Coordinate::columnIndexFromString($caseHeaderCell->getColumn());

        // Find the column index based on the header "Remarks Combi"
        $remarksHeaderCoordinate = Coordinate::stringFromColumnIndex(2) . '1'; // Assuming the header is in the first row (row 1)
        $remarksHeaderCell = $worksheet->getCell($remarksHeaderCoordinate);
        $remarksHeaderColumnIndex = Coordinate::columnIndexFromString($remarksHeaderCell->getColumn());

        // Assuming data starts from row 2
        $highestRow = $worksheet->getHighestRow();
        $cases = [];

        // Loop through the rows, starting from the second row (skip the header row)
        for ($row = 2; $row <= $highestRow; $row++) {
            $caseCellCoordinate = Coordinate::stringFromColumnIndex($caseHeaderColumnIndex) . $row;
            $remarksCellCoordinate = Coordinate::stringFromColumnIndex($remarksHeaderColumnIndex) . $row;
            $cases[] = array(
                'case_number' => $worksheet->getCell($caseCellCoordinate)->getValue(),
                'append_resolution' => $worksheet->getCell($remarksCellCoordinate)->getValue()
            );
        }

        // Insert each case numbers as a new row in cases_bulk_closed with column status 'Pending'
        foreach ($cases as $case) {
            $isCaseInserted = DB::connection("mysql_crm_ssm")->table('cases_bulk_closed')
                ->where('case_number', $case['case_number'])
                ->get();

            if (count($isCaseInserted) == 0) {
                DB::connection('mysql_crm_ssm')
                    ->table('cases_bulk_closed')
                    ->insert([
                        'case_number' => $case['case_number'],
                        'status' => 'Pending',
                        'append_resolution' => $case['append_resolution']
                    ]);
                dump(self::class . " > " . __FUNCTION__ . " Inserting Case Number > " . $case['case_number']);
                Log::info(self::class . " > " . __FUNCTION__ . " Inserting Case Number > " . $case['case_number']);
            } else {
                dump(self::class . " > " . __FUNCTION__ . " Case Number > " . $case['case_number'] . " already exist");
                Log::info(self::class . " > " . __FUNCTION__ . " Case Number > " . $case['case_number'] . " already exist");
            }
        }
        dump(self::class . " > " . __FUNCTION__ . " Cases to be closed in bulk have been inserted in cases_bulk_closed table. Now awaiting to execution");
        Log::info(self::class . " > " . __FUNCTION__ . " Cases to be closed in bulk have been inserted in cases_bulk_closed table. Now awaiting to execution");
    }
}
