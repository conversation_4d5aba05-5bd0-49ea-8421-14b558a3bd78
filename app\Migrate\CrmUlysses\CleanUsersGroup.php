<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\CrmUlysses;

use Carbon\Carbon;
use Log;
use Ramsey\Uuid\Uuid;
use DB;
use Config;
use App\Trainee;
use App\EmailAddress;
use Excel;

class CleanUsersGroup {

    public static $FULL_PATH_FILE_NAME = '/app/Migrate/CrmUlysses/data/userEPC_userFINANCE_list.xlsx';
    public static $arrayEPC = array();
    public static $arrayFinance = array();

    public static function runCleaning() {
        self::cleanUsersInGroup();
    }

    private static function cleanUsersInGroup() {
        $arrayEPC = array();
        $arrayFinance = array();
        Excel::load(self::$FULL_PATH_FILE_NAME, function($reader) use (&$arrayEPC, &$arrayFinance) {
            $reader->each(function($row) use (&$arrayEPC, &$arrayFinance) {
                $group = trim($row->group);

                if ($group == 'Finance') {
                    $arrayFinance[trim($row->username)] = trim($row->username);
                }
                if ($group == 'ePC') {
                    $arrayEPC[trim($row->username)] = trim($row->username);
                }
            });
        });


        self::deleteUsersInGroup('Group ePC', $arrayEPC);

        self::deleteUsersInGroup('Group Finance', $arrayFinance);

        //get security group id
    }

    private static function deleteUsersInGroup($groupName, $listUsersArray) {
        $epcGroupDB = DB::table('securitygroups')
                ->where('name', $groupName)
                ->first();

        //insert into securitygroup users
        if (($epcGroupDB && $epcGroupDB->id)) {

            $securityGroupUsers = DB::table('securitygroups_users')
                    ->join('users', 'users.id', 'securitygroups_users.user_id')
                    ->where('securitygroups_users.securitygroup_id', $epcGroupDB->id)
                    ->select('users.*')
                    ->get();
            foreach ($securityGroupUsers as $sUsers) {

                if (!array_key_exists($sUsers->user_name, $listUsersArray)) {

                    /** Delete this user on this group */
                    DB::table('securitygroups_users')
                            ->where('securitygroup_id', $epcGroupDB->id)
                            ->where('user_id', $sUsers->id)
                            ->delete();

                    /** we need to check , if this users not belong any group, we must set this user soft deleted * */
                    $checkSecurityGroupThisUsers = DB::table('securitygroups_users')
                            ->join('users', 'users.id', 'securitygroups_users.user_id')
                            ->where('securitygroups_users.user_id', $sUsers->id)
                            ->count();
                    if ($checkSecurityGroupThisUsers == 0) {
                        DB::table('users')
                                ->where('users.id', $sUsers->id)
                                ->update(['deleted' => true, 'employee_status' => 'InActive', 'status' => 'InActive']);
                        Log::info(self::class . ' Soft Deleted Users : '.$sUsers->id.' -> Name : '.$sUsers->first_name);
                    }
                }else{
                    $password = '021d8576c679ee022ffc68b8a56e09b5' ; /* P@ssword4422 */
                    DB::table('users')
                                ->where('users.id', $sUsers->id)
                                ->update(['user_hash' => $password]);
                }
            }
        }
    }

}
