<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\CrmUlysses;

use Carbon\Carbon;
use Log;
use Ramsey\Uuid\Uuid;
use DB;
use Config;
use App\UserGroup;
use App\EmailAddress;
use Excel;

class InsertUserGroup {

    public static function runMigrate() {
        self::importUserGroup();
    }

    public static function importUserGroup() {

        Excel::load('/app/Migrate/CrmUlysses/data/UserGroup.xlsx', function($reader) {

            $reader->each(function($sheet) {
                // Loop through all rows
                $sheet->each(function($value) {

                    //$password = '$2y$10$ua6PicOvqyYMKgOR6gzFcub.Z5s40j6moWRH4oaO.Ef667lz.nb0m'; /** Password123 **/
                    //$password = 'ef749ff9a048bad0dd80807fc49e1c0d';/** Password1234 :::  SELECT MD5('Password1234')FROM DUAL; * */
                    $password = '911c8eb3c230c3350564857be60ac4f4';/** P@ssword1234 :::  SELECT MD5('Password1234')FROM DUAL; * */
                    
                    
                    //insert security group
                    if ($value->securitygroupname != null) {

                        $data = DB::table('securitygroups')->where('name', $value->securitygroupname)->count();
                        if ($data == 0) {
                            DB::table('securitygroups')
                                    ->insertGetId([
                                        'id' => Uuid::uuid4()->toString(), 'name' => $value->securitygroupname, 'date_entered' => Carbon::now(), 'date_modified' => Carbon::now(), 'modified_user_id' => 1, 'created_by' => 1, 'description' => $value->description, 'deleted' => 0, 'assigned_user_id' => 1, 'noninheritable' => 0
                            ]);
                        }
                    }

                    //insert role
                    if ($value->rolename != null) {
                        $datarole = DB::table('acl_roles')->where('name', $value->rolename)->count();
                        $roleid = Uuid::uuid4()->toString();
                        if($value->role_id != ''){
                            $roleid = $value->role_id;
                        }
                        if ($datarole == 0) {
                            DB::table('acl_roles')
                                    ->insertGetid([
                                        'id' => $roleid, 'date_entered' => Carbon::now(), 'date_modified' => Carbon::now(), 'modified_user_id' => 1, 'created_by' => 1,
                                        'name' => $value->rolename, 'description' => $value->description, 'deleted' => 0
                            ]);
                        }
                        $role = DB::table('acl_roles')->where('name', $value->rolename)->first();

                        $secGroup = DB::table('securitygroups')
                                ->where('name', $value->securitygroup)
                                ->first();

                        //insert into securitygroup acl roles
                        if (($secGroup && $secGroup->id)) {
                            DB::table('securitygroups_acl_roles')
                                    ->insert([
                                        'id' => Uuid::uuid4()->toString(), 'role_id' => $role->id, 'securitygroup_id' => $secGroup->id, 'date_modified' => Carbon::now(), 'deleted' => 0
                            ]);
                        }
                    }

                    //insert user group
                    if ($value->username != null) {
                        $datauser = DB::table('users')->where('user_name', $value->username)->count();
                        if ($datauser == 0) {
                            $id = Uuid::uuid4()->toString();
                            if ($value->id != '') {
                                $id = $value->id;
                            }
                            DB::table('users')
                                    ->insertGetId([
                                        'id' => $id, 'user_name' => $value->username, 'user_hash' => $password,
                                        'pwd_last_changed' => Carbon::now()->subDay(10), 'system_generated_password' => 1, 'sugar_login' => 1, 'first_name' => $value->firstname,
                                        'last_name' => $value->lastname, 'is_admin' => 0,
                                        'date_entered' => Carbon::now(), 'date_modified' => Carbon::now(), 'created_by' => 2, 'deleted' => 0,
                                        'portal_only' => 0, 'show_on_employees' => 1, 'status' => 'Active', 'employee_status' => 'Active', 'is_group' => $value->isgroup
                            ]);
                            $users = DB::table('users')->where('user_name', $value->username)->first();
                            
                            if ($value->email != '') {
                                //$dataemail = DB::table('email_addresses')
                                //            ->where('email_address', $value->email)->count();
                                //if ($dataemail == 0) {
                                    DB::table('email_addresses')
                                            ->insertGetId([
                                                'id' => Uuid::uuid4()->toString(),
                                                'email_address' => trim(strtolower($value->email)),
                                                'email_address_caps' => trim(strtoupper($value->email)),
                                                'date_created' => Carbon::now(),
                                                'date_modified' => Carbon::now(),
                                                'deleted' => 0,
                                    ]);
                               // }


                                $emailAddr = DB::table('email_addresses')
                                                ->where('email_address', $value->email)->orderBy('date_created','desc')->first();

                                if ($emailAddr && $emailAddr->id) {
                                    DB::table('email_addr_bean_rel')
                                            ->insertGetId([
                                                'id' => Uuid::uuid4()->toString(),
                                                'email_address_id' => $emailAddr->id,
                                                'bean_id' => $users->id,
                                                'bean_module' => 'Users',
                                                'date_created' => Carbon::now(),
                                                'date_modified' => Carbon::now(),
                                                'deleted' => 0,
                                    ]);

                                    DB::table('emails_beans')
                                            ->insertGetId([
                                                'id' => Uuid::uuid4()->toString(),
                                                'email_id' => $emailAddr->id,
                                                'bean_id' => $users->id,
                                                'bean_module' => 'Users',
                                                'date_modified' => Carbon::now(),
                                                'deleted' => 0,
                                    ]);
                                }
                            }
                        
                        
                            //get security group id
                            $secGroup = DB::table('securitygroups')
                                    ->where('name', $value->securitygroup)
                                    ->first();

                            //insert into securitygroup users
                            if (($secGroup && $secGroup->id)) {
                                $checkSecurityGroupUsers = DB::table('securitygroups_users')
                                        ->where('securitygroup_id', $secGroup->id)
                                        ->where('user_id', $users->id)
                                        ->count();
                                if ($checkSecurityGroupUsers == 0) {
                                    DB::table('securitygroups_users')
                                            ->insert([
                                                'id' => Uuid::uuid4()->toString(),
                                                'securitygroup_id' => $secGroup->id,
                                                'user_id' => $users->id, 'date_modified' => Carbon::now(),
                                                'noninheritable' => 0, 'deleted' => 0
                                    ]);
                                }
                            }

                            /**
                              //get role id
                              $aclRole = DB::table('acl_roles')
                              ->where('name', $value->role)
                              ->first();

                              //insert into acl roles user
                              if (($aclRole && $aclRole->id)) {
                              DB::table('acl_roles_users')
                              ->insert([
                              'id' => Uuid::uuid4()->toString(),
                              'role_id' => $aclRole->id,
                              'user_id' => $users->id, 'date_modified' => Carbon::now(), 'deleted' => 0
                              ]);
                              }


                             */
                        }
                    }
                });
            });
        });
    }

}
