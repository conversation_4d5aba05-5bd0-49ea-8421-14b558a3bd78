<?php

namespace App\Migrate\CrmUlysses;

use Carbon\Carbon;
use Log;
use Ramsey\Uuid\Uuid;
use DB;
use Config;
use App\Invoices;
use App\LineItem;
use App\LineItemGroup;
use App\Product;
use App\LineItemCustom;
use App\InvoiceCustom;
use App\Migrate\MigrateUtils;
use App\LineItemProductStaff;
use App\Migrate\CrmUlysses\MigrateProduct;

class MigrateInvoice {

    /**
     * Format date : yyyy-mm-dd eg: 2014-12-25 
     * @param type $dateStart
     * @param type $dateEnd
     */
    public static function runMigrate($dateStart = null,$dateEnd = null) {
        
        MigrateProduct::runMigrate();
        
        Log::debug(self::class . ' Starting ... runMigrate ',['Query Start Date' => $dateStart,'Query End Date' => $dateEnd]);
        $dtStartTime = Carbon::now();

        self::saveInvoice($dateStart, $dateEnd);

        Log::info(self::class . ' Completed runMigrate --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }
 
    public static function getStatusClosed(){
        /*
         *  0   Pending Payment
         *  1   Paid
         *  2   Invoice Canceled
         *  3   Invoice Not Exist   (latest check data on year 2012)
         *  4   Pending Cancel      (latest check data on year 2012)
         *  5   Transferred         (latest check data on year 2012)
         *  6   Pending Refund      (latest check data on year 1/9/2016)
         *  7   Partial Refund      (latest check data on year 1/9/2016)
         *  8   Refunded 
         */
        return $invoice_status = array (1,2,8);
    }
    
    public static function saveInvoice($dateStart,$dateEnd) {

//        $status = array('Pending Payment', 'Paid', 'Invoice Cancelled');
//        $invno = array(203399,203411,234240);
        //$date = array('2014-01-01', Carbon::now()->format('Y-m-d')); 
        $date = array('2017-03-27', '2017-03-29');
        if($dateStart != null && $dateEnd != null){
           $date = array($dateStart, $dateEnd);   
        }
        var_dump($date);
        $take = 50000;
        $skip = 50000;
        $count = 0;
        $kira = 0;
        $num = 0;
        do {
            $dtStartTime = Carbon::now();
            Log::info(self::class . ' Query Invoice: counter ' . $count);
            
            /**
             *
             *  SELECT
                    *
                  FROM invoice i,
                       crm_payment_transaction pt,
                       crm_payment p
                  WHERE i.invoice_id = pt.invoice_id
                  AND pt.crm_payment_id = p.crm_payment_id
                  AND p.crm_payment_status = 'Complete paid'
                  AND i.created_by = 'SR'
                  AND i.AMENDED_BY IS NULL
                  AND i.CREATED_DATETIME BETWEEN '2017-03-01' AND '2017-04-01'
                  ORDER BY i.invoice_id;
             * 
             * 
             SELECT i.invoice_id,
                i.invoice_no,
                i.invoice_date,
                i.reference,
                i.total,
                i.created_by,
                i.amended_by,
                i.created_datetime,
                i.amended_datetime,
                i.IsPaid,
                i.CRM_INVOICE_STATUS,
                pt.crm_payment_transaction_id,
                pt.crm_payment_id,
                pt.invoiceitem_id,
                pt.created_by,
                pt.created_datetime,
                p.payment_date,
                p.short_description,
                p.crm_receipt_no,
                p.crm_payment_status,
                p.crm_reference_no
         FROM invoice i,
              crm_payment_transaction pt,
              crm_payment p
         WHERE i.invoice_id = pt.invoice_id
           AND pt.crm_payment_id = p.crm_payment_id
           AND p.crm_payment_status = 'Complete paid'
           AND i.created_by = 'SR'
           AND i.AMENDED_BY IS NULL
           AND i.CREATED_DATETIME BETWEEN '2017-03-01' AND '2017-04-01'
         ORDER BY i.invoice_id;
             * 
             */
            
            //This case for SR paid but something happen AMENDED_BY is null
            $queryNew = DB::connection('sqlsrv')
                ->table('CRM_PAYMENT')
                ->join('CRM_PAYMENT_TRANSACTION', 'CRM_PAYMENT.CRM_PAYMENT_ID', 'CRM_PAYMENT_TRANSACTION.CRM_PAYMENT_ID')
                ->join('INVOICE', 'CRM_PAYMENT_TRANSACTION.INVOICE_ID', 'INVOICE.INVOICE_ID')
                ->where('CRM_PAYMENT.CRM_PAYMENT_STATUS','Complete paid')
                ->where('INVOICE.CREATED_BY','SR')
                ->whereBetween('INVOICE.CREATED_DATETIME', $date)
                ->whereNull('INVOICE.AMENDED_BY')
                ->where('INVOICE.INVOICE_ID','499943')    //505083//499943//504490
                ->select('INVOICE.*','CRM_PAYMENT.CRM_PAYMENT_STATUS as status_payment','CRM_PAYMENT.PAYMENT_DATE','CRM_PAYMENT.AMENDED_BY as p_amendby','CRM_PAYMENT.AMENDED_DATETIME as p_amenddate')
                ->take($take)
                ->skip($skip * $count++);
            //var_dump($queryNew->toSql());
            $result   = $queryNew->get();
            
//            $resultNew = DB::connection('sqlsrv')
//                    ->table('INVOICE')
////                    ->whereIn('INVOICE_ID', $invno)
//                    ->whereBetween('CREATED_DATETIME', $date)
//                    //->whereBetween('AMENDED_DATETIME', $date)
//                    //->whereIn('INVOICE_STATUS',self::getStatusClosed())
//                    ->where('CRM_INVOICE_STATUS','Paid')
//                    ->where('created_by','SR')
//                    ->whereNotNull('AMENDED_BY')
//                    //->select('INVOICE_ID','INVOICE_NO','INVOICE_DATE','REFERENCE','STATUS','CREATED_BY','CREATED_DATETIME','AMENDED_DATETIME','AMENDED_BY','CRM_INVOICE_STATUS')
//                    ->take($take)
//                    ->skip($skip * $count++)
//                    ->orderBy('INVOICE.INVOICE_ID')
//                    ->get();
            Log::info(self::class . ' Total Invoices : ' . count($result));
            var_dump(self::class . ' Total Invoices : ' . count($result));
            foreach ($result as $row) {
                //Log::info(self::class . ' INVOICE ID ' . $row->INVOICE_ID);
                $data = Invoices::where('id', trim($row->INVOICE_ID))->count();
                //Log::info(self::class . ' GET RESULT INVOICE ' . $data);
                //Log::info('Data' .$data);
                
                if ($data == 0) {
                    $num = $kira++;
                    
                    //var_dump(json_encode($row));
                    if(trim($row->INVOICE_ID) == '505083'){
                        var_dump('');
                        var_dump($num.'). We found it: there is no record in CRM : '.$row->INVOICE_ID);
                        var_dump('#####################################  :: '.$row->INVOICE_ID);
                        //exit;
                    }
                    
                    
                    Log::info('');
                    Log::info($num.'). We found it: there is no record in CRM : '.$row->INVOICE_ID);
                    Log::info('     INVOICE_ID: '.  $row->INVOICE_ID);
                    Log::info('     INVOICE_NO: '.  $row->INVOICE_NO);
                    Log::info('     CREATED_DATETIME: '.  $row->CREATED_DATETIME);
                    Log::info('     AMENDED_DATETIME: '.  $row->AMENDED_DATETIME);
                    Log::info('     AMENDED_BY: '.  $row->AMENDED_BY);
                    Log::info('     Status_payment: '. $row->status_payment);
                    Log::info('     Invoice Status: '. $row->CRM_INVOICE_STATUS);
                    Log::info('     PAYMENT_DATE: '. $row->CRM_INVOICE_STATUS);
                    Log::info('     AMENDED_BY Payment: '. $row->p_amendby);
                    Log::info('     AMENDED_DATETIME Payment: '. $row->p_amenddate);
             
                    $invoice = new Invoices;
                    $invoice->id = $row->INVOICE_ID;
                    $invoice->name = trim($row->INVOICE_NO);

//                    Log::info(self::class . ' Raw Date ' . $row->CREATED_DATETIME);
                    //sync date : minus 8 hour
                    $dateEntered = new Carbon($row->CREATED_DATETIME);
                    $dateModified = new Carbon($row->AMENDED_DATETIME);
                    $dateEntered->subHour(8);
                    $dateModified->subHour(8);
//                    Log::info(self::class . ' Sync Date ' . $dateEntered->format('Y-m-d H:i:s'));

                    $invoice->date_entered = $dateEntered->format('Y-m-d H:i:s');
                    $invoice->date_modified = $dateModified->format('Y-m-d H:i:s');
                    $invoice->modified_user_id = trim($row->AMENDED_BY);
                    $invoice->created_by = trim($row->CREATED_BY);
                    $invoice->description = trim($row->SHORT_DESCRIPTION);
                    $invoice->billing_account_id = $row->CUSTOMER_ID;
                    $invoice->billing_contact_id = $row->CONTACT_ID;

                    $addr = self::getAddressDetail($row->CUSTOMER_ID);

                    if ($addr && $addr->billing_address_street) {
                        $invoice->billing_address_street = $addr->billing_address_street;
                        $invoice->billing_address_city = $addr->billing_address_city;
                        $invoice->billing_address_state = $addr->billing_address_state;
                        $invoice->billing_address_postalcode = $addr->billing_address_postalcode;
                        $invoice->billing_address_country = $addr->billing_address_country;
                    }

                    $invoice->number = $row->INVOICE_ID;
                    $invoice->total_amt = $row->TOTAL;
                    $invoice->subtotal_amount = $row->TOTAL;
                    $invoice->discount_amount = 0;
                    $invoice->tax_amount = $row->CRM_GST_AMOUNT;
                    $invoice->total_amount = $row->CRM_TOTAL_INCL_TAX;
                    $invoice->invoice_date = $row->INVOICE_DATE;
                    $invoice->status = trim($row->CRM_INVOICE_STATUS);
                    $invoice->save();

                    self::saveInvoicePayment($invoice, $row);
                   
                    $lineitemgroup = self ::saveLineItemGroup($row);

                    //create lineItem
                    self::MigrateInvoiceItem($invoice, $lineitemgroup);
                } else {
                    //Log::info('Invoice already exists. ');
                }
            }
            
            Log::info(self::class . ' Completed records : '.count($result).' -> Counter : '.$count.' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
            var_dump(self::class . ' Completed '.$num.'  records : '.count($result).' -> Counter : '.$count.' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
   
        } while (count($result) == $take);
    }

    /**
     * 
     * @param Invoices $invoice
     * @param type $rowData
     */
    public static function saveInvoicePayment(Invoices $invoice, $rowData) {
        Log::info(self::class . ' '.__FUNCTION__.' -> Invoice No.  '.$invoice->name);
        Log::info(self::class . ' '.__FUNCTION__.' -> Invoice ID.  '.$invoice->id);
        $invoiceCstm = new InvoiceCustom;
        $invoiceCstm->id_c = ''.$invoice->id;
        $invoiceCstm->invoice_generate_no_c = $invoice->name;
        
        $invoiceCstm->rounding_price_c  = $rowData->CRM_SALES_ROUNDING_ADJUSTMENT;
        //SR 
        $invoiceCstm->ep_appl_no_c = trim($rowData->REFERENCE);

        $epAppl = DB::connection('sqlsrv')
                ->table('CRM_Application_Header')
                ->where('APPL_NO', $invoiceCstm->ep_appl_no_c)
                ->first();

        if ($epAppl && $epAppl->APPL_NO) {
            $invoiceCstm->ep_appl_status_c = trim($epAppl->status);
            $invoiceCstm->ep_appl_status_desc_c = trim($epAppl->status);
            $invoiceCstm->ep_appl_date_c = trim($epAppl->APPL_DATE);
            if(trim($epAppl->status) == 'Pending Payment'){
                $invoiceCstm->ep_appl_status_c = '906'; 
            }
        }
        
        $invoiceCstm->save();
        
        Log::info(self::class . ' '.__FUNCTION__.' -> APPL_NO.  '.$invoiceCstm->ep_appl_no_c);
        
        //Query Payment and Payment Transaction :- Reupdate Invoice

        $payment = DB::connection('sqlsrv')
                ->table('CRM_PAYMENT')
                ->join('CRM_PAYMENT_TRANSACTION', 'CRM_PAYMENT.CRM_PAYMENT_ID', 'CRM_PAYMENT_TRANSACTION.CRM_PAYMENT_ID')
                ->where('CRM_PAYMENT_TRANSACTION.INVOICE_ID', $invoice->id)
                ->select('CRM_PAYMENT.*')
                ->first();

        if ($payment && $payment->CRM_PAYMENT_ID) {
            /** Remove special Character in name fields * */
            $celReason = preg_replace('/[\x00-\x08\x0B-\x1F]/', '', trim($payment->CANCEL_REASON));
            $celReason = preg_replace('/[\xA0]/', '', $celReason);
            $celReason = preg_replace('/[\xC2]/', '', $celReason);
            $celReason = preg_replace('/[\xC3]/', '', $celReason);
            $celReason = preg_replace('/[\x92]/', '', $celReason);
            
            $datePayment = new Carbon($payment->PAYMENT_DATE);
            $datePayment->subHour(8);
                    
            $invoiceCstm->bank_c = $payment->CRM_BANK_ID;
            $invoiceCstm->bank_location_c = $payment->CRM_BANK_LOCATION;
            $invoiceCstm->document_date_c = $payment->doc_date;
            $invoiceCstm->epc_location_c = $payment->CRM_EPC_LOCATION_ID;
            $invoiceCstm->payment_channel_c = $payment->CRM_EPC_LOCATION_ID;
            $invoiceCstm->payment_date_c = $datePayment->format('Y-m-d H:i:s');
            $invoiceCstm->payment_type_c = $payment->CRM_PAYMENT_TYPE_ID;
            $invoiceCstm->receipt_generate_no_c = $payment->CRM_RECEIPT_NO;
            $invoiceCstm->payment_reference_no_c = trim($payment->CRM_REFERENCE_NO);
            $invoiceCstm->payment_remarks_c = trim($payment->SHORT_DESCRIPTION);
            $invoiceCstm->payment_cancel_reason_c = $celReason;
            if ($invoiceCstm->payment_cancel_reason_c != '') {
                $invoiceCstm->payment_remarks_c = $invoiceCstm->payment_remarks_c. ' Cancel Reason : '.$invoiceCstm->payment_cancel_reason_c;
            }
            $invoiceCstm->save();
            
            /** Reupdate latest update payment on invoice **/
            $dateModified = new Carbon($payment->AMENDED_DATETIME);
            $dateModified->subHour(8);

            $userModifiedBy = trim($payment->AMENDED_BY);
                        
            $invoice->date_modified = $dateModified->format('Y-m-d H:i:s');
            $invoice->modified_user_id = $userModifiedBy;
            $invoice->save();
            
        }else{

            //Query Payment History and Payment Transaction History :- Reupdate Invoice

            $paymentHistory = DB::connection('sqlsrv')
                    ->table('CRM_PAYMENT_HISTORY')
                    ->join('CRM_PAYMENT_TRANSACTION_HISTORY', 'CRM_PAYMENT_HISTORY.CRM_PAYMENT_ID', 'CRM_PAYMENT_TRANSACTION_HISTORY.CRM_PAYMENT_ID')
                    ->where('CRM_PAYMENT_TRANSACTION_HISTORY.INVOICE_ID', $invoice->id)
                    ->select('CRM_PAYMENT_HISTORY.*')
                    ->first();

            if ($paymentHistory && $paymentHistory->CRM_PAYMENT_ID) {
                $invoiceCstm->bank_c = $paymentHistory->CRM_BANK_ID;
                $invoiceCstm->bank_location_c = $paymentHistory->CRM_BANK_LOCATION;
                $invoiceCstm->document_date_c = $paymentHistory->DOC_DATE;

                /** Remove special Character in name fields * */
                $cancelReason = preg_replace('/[\x00-\x08\x0B-\x1F]/', '', trim($paymentHistory->CANCEL_REASON));
                $cancelReason = preg_replace('/[\xA0]/', '', $cancelReason);
                $cancelReason = preg_replace('/[\xC2]/', '', $cancelReason);
                $cancelReason = preg_replace('/[\xC3]/', '', $cancelReason);
                $cancelReason = preg_replace('/[\x92]/', '', $cancelReason);

                $datePayment = new Carbon($paymentHistory->PAYMENT_DATE);
                $datePayment->subHour(8);
            
                $invoiceCstm->epc_location_c = $paymentHistory->CRM_EPC_LOCATION_ID;
                $invoiceCstm->payment_channel_c = $paymentHistory->CRM_EPC_LOCATION_ID;
                $invoiceCstm->payment_date_c = $datePayment->format('Y-m-d H:i:s');
                $invoiceCstm->payment_type_c = $paymentHistory->CRM_PAYMENT_TYPE_ID;
                $invoiceCstm->receipt_generate_no_c = $paymentHistory->CRM_RECEIPT_NO;
                $invoiceCstm->payment_reference_no_c = trim($paymentHistory->CRM_REFERENCE_NO);
                $invoiceCstm->payment_remarks_c = trim($paymentHistory->SHORT_DESCRIPTION);
                $invoiceCstm->payment_cancel_reason_c = $cancelReason;
                if ($invoiceCstm->payment_cancel_reason_c != '') {
                    $invoiceCstm->payment_remarks_c = $invoiceCstm->payment_remarks_c. ' Cancel Reason : '.$invoiceCstm->payment_cancel_reason_c;
                }

                $invoiceCstm->save();
                
                
                /** Reupdate latest update payment on invoice **/
                $dateModified = new Carbon($paymentHistory->AMENDED_DATETIME);
                $dateModified->subHour(8);

                $userModifiedBy = trim($paymentHistory->AMENDED_BY);

                $invoice->date_modified = $dateModified->format('Y-m-d H:i:s');
                $invoice->modified_user_id = $userModifiedBy;
                $invoice->save();
            }
        }
        
        $invoiceReferenceId = DB::connection('sqlsrv')
                ->table('INVOICE')
                ->where('CRM_PARENT_INVOICE_ID','not like','%null%')
                ->where('INVOICE_ID',$invoice->id)
                ->first();
        
        if ($invoiceReferenceId && $invoiceReferenceId->CRM_PARENT_INVOICE_ID)
        {
            $data = array(
                'ID' => trim($invoiceReferenceId->CRM_PARENT_INVOICE_ID)
            );
            
            $inv = Invoices::where('id',trim($invoiceReferenceId->CRM_PARENT_INVOICE_ID))->first();
            if($inv && $inv->number){
                $data['INVOICE_ID'] = $inv->number;
            }
            if($inv && $inv->name){
                $data['INVOICE_NO'] = $inv->name;
            }
            
            $invoiceCstm->reference_invoice_id_c = ''.json_encode($data);
            $invoiceCstm->save();
        }
        
        Log::info(self::class . ' '.__FUNCTION__.' -> Completed ');
    }

    /**
     * 
     * @param type $rowData
     * @return LineItemGroup
     */
    public static function saveLineItemGroup($rowData) {

        $lineitemgroup = new LineItemGroup;
        $lineitemgroup->id = Uuid::uuid4()->toString();
        $lineitemgroup->name = '';
        $lineitemgroup->date_entered = $rowData->CREATED_DATETIME;
        $lineitemgroup->date_modified = $rowData->AMENDED_DATETIME;
        //$lineitemgroup->modified_user_id = 2;
        //$lineitemgroup->created_by = 2;
        $lineitemgroup->modified_user_id = trim($rowData->AMENDED_BY);
        $lineitemgroup->created_by = trim($rowData->CREATED_BY);
        $lineitemgroup->description = '';
        $lineitemgroup->deleted = 0;
        $lineitemgroup->assigned_user_id = 2;
        $lineitemgroup->total_amt = $rowData->TOTAL;
        $lineitemgroup->discount_amount = 0;
        $lineitemgroup->subtotal_amount = $rowData->TOTAL;
        $lineitemgroup->tax_amount = $rowData->CRM_GST_AMOUNT;
        $lineitemgroup->subtotal_tax_amount = 0;
        $lineitemgroup->total_amount = $rowData->TOTAL;
        $lineitemgroup->parent_type = 'AOS_Invoices';
        $lineitemgroup->parent_id = $rowData->INVOICE_ID;
        //$lineitemgroup->increment('number', 1);
        $lineitemgroup->number = 1;
        $lineitemgroup->currency_id = -99;
        $lineitemgroup->save();

        return $lineitemgroup;
    }

    /**
     * 
     * @param Invoices $invoice
     * @param LineItemGroup $lineitemgroup
     */
    public static function MigrateInvoiceItem(Invoices $invoice, LineItemGroup $lineitemgroup) {

        $result = DB::connection('sqlsrv')
                ->table('INVOICEITEM')
                ->where('INVOICE_ID', $invoice->id)
                ->get();
        $integer = 0;
        foreach ($result as $rowdata) {

//            $product = Product::where('name', trim($rowdata->ITEM_REF))->first();
            
            $product = DB::table('aos_products')
                    ->join('aos_products_cstm','aos_products_cstm.id_c','aos_products.id')
                    ->where('aos_products.name',trim($rowdata->ITEM_REF))
                    ->orWhere('aos_products_cstm.name_bm_c',trim($rowdata->ITEM_REF))
                    ->orWhere('aos_products_cstm.name_en_c',trim($rowdata->ITEM_REF))
                    ->first();

            if ($product && $product->id) {
                $invoiceitem = new LineItem;
                $invoiceitem->id = $rowdata->INVOICEITEM_ID;
                $invoiceitem->name = trim($product->name);
                $invoiceitem->date_entered = $rowdata->CREATED_DATETIME;
                $invoiceitem->date_modified = $rowdata->AMENDED_DATETIME;
                $invoiceitem->modified_user_id = $invoice->modified_user_id;
                $invoiceitem->created_by = $invoice->created_by;
                $invoiceitem->description = trim($rowdata->SHORT_DESCRIPTION);
                $invoiceitem->deleted = 0;
                $invoiceitem->assigned_user_id = 2;
                $invoiceitem->currency_id = '-99';
                $invoiceitem->part_number = '';
                $invoiceitem->item_description = trim($product->description);
                //$invoiceitem->increment('number', 1);
                $invoiceitem->number = $integer++;
                $invoiceitem->product_qty = $rowdata->QUANTITY;
                $invoiceitem->product_cost_price = $rowdata->UNIT_PRICE;
                $invoiceitem->product_list_price = $rowdata->UNIT_PRICE;
                $invoiceitem->product_discount = 0;
                $invoiceitem->product_discount_amount = 0;
                $invoiceitem->discount = 'Percentage';
                $invoiceitem->product_unit_price = $rowdata->UNIT_PRICE;
                $invoiceitem->vat_amt = $rowdata->CRM_GST_AMOUNT;
                $invoiceitem->product_total_price = $rowdata->CRM_TOTAL_INCL_TAX;
                $invoiceitem->vat = $rowdata->CRM_GST_PERCENT;
                $invoiceitem->parent_type = 'AOS_Invoices';
                $invoiceitem->parent_id = $rowdata->INVOICE_ID;
                $invoiceitem->product_id = $product->id;
                $invoiceitem->group_id = $lineitemgroup->id;
                $invoiceitem->save();

                //This information for SR
                if ($rowdata->ITEM_ID != '') {
                    $invoiceItemCstm = new LineItemCustom;
                    $invoiceItemCstm->id_c = $invoiceitem->id;
                    $invoiceItemCstm->ep_item_id_c = $rowdata->crm_sr_item_id;
                    $invoiceItemCstm->ep_product_group_id_c = $rowdata->PRODUCT_GROUP_ID;
                    $invoiceItemCstm->save();
                }
                /*
                $resultAppl = DB::connection('sqlsrv')->table('CRM_Application_Header')
                ->join('CRM_Application_Line_Items', 'CRM_Application_Header.CRM_APPLICATION_HEADER_ID', 'CRM_Application_Line_Items.CRM_APPLICATION_HEADER_ID')
                ->join('crm_application_line_subitem', 'CRM_Application_Header.CRM_APPLICATION_LINE_ITEMS_ID', 'CRM_Application_Line_Items.CRM_APPLICATION_LINE_ITEMS_ID')
                ->join('crm_invoice_subitem', 'crm_invoice_subitem.CRM_SUBITEM_ID', 'crm_application_line_subitem.CRM_SUBITEM_ID')
                ->where('crm_invoice_subitem.INVOICEITEM_ID', $invoiceitem->id)
                ->first();
                
                if($resultAppl && $resultAppl->APPL_NO != ''){  
                    $invoice->ep_appl_date_c = $resultAppl->APPL_DATE;
                    $invoice->ep_appl_no_c = $resultAppl->APPL_NO;
                    
                    $invoice->ep_appl_type_c = '';
                    $invoice->ep_appl_type_desc_c = '';
                    $invoice->ep_appl_status_desc_c = $resultAppl->status;
                    if($resultAppl->status == 'Pending Payment'){
                       $invoice->ep_appl_status_c = '906'; 
                    }
                    $invoice->save();
                }
                */
                self::saveProductStaff($invoiceitem);
            }
        }
    }

    /**
     * 
     * @param LineItem $invoiceItem
     */
    public static function saveProductStaff(LineItem $invoiceItem) {
        $result = DB::connection('sqlsrv')
                ->table('CRM_APPLICATION_LINE_SUBITEM')
                ->join('CRM_INVOICE_SUBITEM', 'CRM_INVOICE_SUBITEM.CRM_SUBITEM_ID', 'CRM_APPLICATION_LINE_SUBITEM.CRM_SUBITEM_ID')
                ->where('CRM_INVOICE_SUBITEM.INVOICEITEM_ID', $invoiceItem->id)
                ->first();

        if ($result && $result->INVOICEITEM_ID) {
            $productStaff = new LineItemProductStaff;
            $productStaff->id = Uuid::uuid4()->toString();
            $productStaff->name = $result->CRM_SUBITEM_NAME;
            $productStaff->date_entered = $result->created_datetime;
            $productStaff->date_modified = $result->amended_datetime;
            $productStaff->modified_user_id = 2;
            $productStaff->created_by = $result->created_by;
            $productStaff->description = '';
            $productStaff->identification_no = $result->CRM_SUBITEM_ID_NO;
            $productStaff->reference_staff_id_ep = $result->CRM_SUBITEM_ID;
            $productStaff->quantity = $result->CRM_QUANTITY;
            $productStaff->products_quotes_id = $invoiceItem->id;
            $productStaff->save();
        }
    
    }

    /**
     * 
     * @param type $id
     * @return type
     */
    public static function getAddressDetail($id) {
        return DB::table('accounts')
                        ->where('id', $id)
                        ->select('billing_address_street')
                        ->addSelect('billing_address_city')
                        ->addSelect('billing_address_state')
                        ->addSelect('billing_address_postalcode')
                        ->addSelect('billing_address_country')
                        ->first();
    }

}
