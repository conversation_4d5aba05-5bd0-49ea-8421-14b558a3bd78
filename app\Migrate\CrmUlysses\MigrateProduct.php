<?php

namespace App\Migrate\CrmUlysses;

use Carbon\Carbon;
use Log;
use Ramsey\Uuid\Uuid;
use DB;
use Config;

class MigrateProduct {

    public static function runMigrate() {
        self::migrateCategoryProduct();
        self::migrateProduct();
    }

    public static function migrateCategoryProduct() {
        $results = DB::connection('sqlsrv')
                ->table('product_group')
                ->get();
        foreach ($results as $row) {
            $countCheck = DB::table('aos_product_categories')->where('name', trim($row->PRODUCT_GROUP))->count();
            if ($countCheck == 0) {
                DB::table('aos_product_categories')->insert(
                        [
                            'id' => $row->PRODUCT_GROUP_ID,
                            'name' => trim($row->PRODUCT_GROUP),
                            'date_entered' => Carbon::now(),
                            'date_entered' => Carbon::now(),
                            'modified_user_id' => 1,
                            'created_by' => 1,
                            'description' => '',
                            'deleted' => false,
                            'assigned_user_id' => 1,
                            'is_parent' => true
                        ]
                );
            }
            
        }
        //Log::info(self::class . ' migrateCategoryProduct completed. Total data:: ' . count($results));
    }

    public static function migrateProduct() {

        $results = DB::connection('sqlsrv')
                ->table('spare_part')
                ->get();
        foreach ($results as $row) {
            
            /** Remove special Character in name fields * */
            $itemName1 = preg_replace('/[\x00-\x08\x0B-\x1F]/', '', trim($row->SPARE_PART));
            $itemName2 = preg_replace('/[\xA0]/', '', $itemName1);
            $itemName = preg_replace('/[\xC2]/', '', $itemName2);
               
            $countCheck = DB::table('aos_products')->where('name', $itemName)->count();
            //Log::info(self::class . ' migrateProduct product name ::: ' . $itemName . ' is existed : '.$countCheck); 
            if ($countCheck == 0) {
                

                //check status
                $status = 1;
                $deleted = false;
                if (trim($row->HOLD) == 'Y') {
                    $status = 0;
                    $deleted = true;
                }

                DB::table('aos_products')->insertGetId(
                        [
                            'id' => trim($row->SPARE_PART_ID),
                            'name' => $itemName,
                            'date_entered' => Carbon::now(),
                            'date_modified' => Carbon::now(),
                            'modified_user_id' => 1,
                            'created_by' => 1,
                            'description' => trim($row->SHORT_DESCRIPTION),
                            'deleted' => $deleted,
                            'assigned_user_id' => 1,
                            'type' => 'Good',
                            'cost' => $row->SELLING_PRICE1,
                            'currency_id' => '-99',
                            'price' => $row->SELLING_PRICE1,
                            'aos_product_category_id' => $row->PRODUCT_GROUP_ID
                        ]
                );
                $item = DB::table('aos_products')->where('name', $itemName)->where('description', trim($row->SHORT_DESCRIPTION))->orderBy('date_entered', 'desc')->first();



                /** All item name in OLD CRM put English words. Selected Item , define Malay word for reference * */
                if (array_key_exists($itemName, self::$productsSyncSupplierEp)) {
                    DB::table('aos_products_cstm')->insert(
                            [
                                'id_c' => $item->id,
                                'tax_code_c' => 'SR', //Standard Rate 6% tax
                                'name_bm_c' => self::$productsSyncSupplierEp[trim($row->SPARE_PART)],
                                'name_en_c' => trim($row->SPARE_PART),
                            ]
                    );
                }

                /**
                 * SRN : NEW APPLICATION
                 * SRR : RE NEW APPLICATION
                 */ else if ($itemName == 'SRN' || $itemName == 'SRR') {
                    DB::table('aos_products_cstm')->insert(
                            [
                                'id_c' => $item->id,
                                //'tax_code_c' => 'RS', //Exemption / Relief Supply
                                'tax_code_c' => 'SR', //Standard Rate 6% tax : on 1/4/2017 all in GST
                                'name_bm_c' => 'Yuran Pendaftaran (Mandatori)',
                                'name_en_c' => 'Registration Fee(Mandatory)',
                                'status_c' => $status,
                            ]
                    );
                }

                /**
                 * SRA : Additional Category Application
                 */ else if ($itemName == 'SRA') {
                    DB::table('aos_products_cstm')->insert(
                            [
                                'id_c' => $item->id,
                                //'tax_code_c' => 'RS', //Exemption / Relief Supply
                                'tax_code_c' => 'SR', //Standard Rate 6% tax : on 1/4/2017 all in GST
                                'name_bm_c' => 'Bidang Tambahan (Mandatori)',
                                'name_en_c' => 'Additional Category(Mandatory)',
                                'status_c' => $status,
                            ]
                    );
                } else {
                    DB::table('aos_products_cstm')->insert(
                            [
                                'id_c' => $item->id,
                                'tax_code_c' => 'SR', //Standard Rate 6% tax
                                'status_c' => $status,
                            ]
                    );
                }
            }

           
        }
        //Log::debug(self::class . ' migrateProduct completed. Total data:: ' . count($results));
    }

    public static $productsSyncSupplierEp = array(
        'ePXS (1 Year Validity)' => 'ePXS (Sahlaku 1 Tahun)',
        'ePXS (3 Years Validity)' => 'ePXS (Sahlaku Tiga Tahun)',
        'MyeP Card (3 Year Validity)' => 'Kad MyeP (Sahlaku Tiga Tahun)',
        'MyKad (3 Year Validity)' => 'MyKad (Sahlaku Tiga Tahun)',
        'Smart Card Reader' => 'Pembaca Kad Pintar'
    );

}
