<?php

namespace App\Migrate\CrmUlysses;

use Carbon\Carbon;
use Log;
use Ramsey\Uuid\Uuid;
use DB;
use Config;
use App\RegisteredUser;
use App\EmailAddress;
use App\Roles;

class MigrateStaff {

    public static function runMigrate() {
        self::userStaff();
    }

    //migrate from table staff to table users
    public static function userStaff() {
//        $countUser = DB::table('users')->count();
//        if ($countUser < 2) {

        $groupGmMapping = array(
            'GM Borneo' => 'Group GM Borneo',
            'GM Sector 1' => 'Group GM Sector 1',
            'GM Sector 2' => 'Group GM Sector 2',
            'GM Sector 3' => 'Group GM Sector 3',
            'GM Sector 4' => 'Group GM Sector 4',
            'GM Selatan' => 'Group GM Selatan',
            'GM Timur' => 'Group GM Timur',
            'GM Utara' => 'Group GM Utara'
        );

        $resultstaff = DB::connection('sqlsrv')
                ->table('staff')
                //->where('HOLD', 'N')
                ->get();

        foreach ($resultstaff as $row) {
            $groupUser = array(null, 'C', 'N');
            $trimggroupUser = array_map('trim', $groupUser);
            $loginName = trim($row->LOGIN);
            $isExist = RegisteredUser::where('user_name', $loginName)->count();
            if ($isExist == 0) {
                if (in_array($row->USER_0, $trimggroupUser)) {

                    //$password = '$2y$10$ua6PicOvqyYMKgOR6gzFcub.Z5s40j6moWRH4oaO.Ef667lz.nb0m'; /** Password123 **/
                    //$password = 'ef749ff9a048bad0dd80807fc49e1c0d';/** Password1234 :::  SELECT MD5('Password1234')FROM DUAL; * */
                    $password = '911c8eb3c230c3350564857be60ac4f4';/** P@ssword1234 :::  SELECT MD5('Password1234')FROM DUAL; * */
                    
                    $firstname = preg_replace('/[\x00-\x08\x0B-\x1F-\xA0-\xC2]/', '', trim($row->FIRSTNAME));
                    $users = new RegisteredUser;
                    $users->id = Uuid::uuid4()->toString();
                    $users->user_name = $loginName;
                    $users->user_hash = $password;
                    $users->system_generated_password = 1;
                    $users->pwd_last_changed = Carbon::now()->subDay(10);
                    $users->sugar_login = 1;
                    $users->first_name = $firstname;
                    $users->last_name = '';
                    $users->is_admin = 0;
                    $users->external_auth_only = 0;
                    $users->description = trim($row->SHORT_DESCRIPTION);
                    $users->date_entered = Carbon::now();
                    $users->date_modified = Carbon::now();
                    $users->created_by = 2;
                    $users->title = trim($row->TITLE);
                    $users->phone_home = trim($row->PHONE);
                    $users->phone_mobile = trim($row->MOBILE_PHONE);
                    $users->status = 'Active';
                    $users->deleted = 0;
                    $users->employee_status = 'Active';
                    $users->is_group = 0;
                    if(trim($row->HOLD) == 'Y'){
                        $users->employee_status = 'InActive';
                        $users->status = 'InActive';
                        $users->deleted = 1;
                    }
                    $users->save();

                    self ::saveEmailAddress($users, $row);
                }
            }
            $users = RegisteredUser::where('user_name', $loginName)->first();
            if ($users && $users->id != '') {
                if (($row->USER_0 == null && $row->USER_1 == 'CS') || ($row->USER_0 == 'C' && $row->USER_1 == 'CS') || ($row->USER_0 == 'N' && $row->USER_1 == 'CS')) {
//                    self::saveRole($users, 'Customer Service');
                    self::saveGroup($users, 'Group Customer Service');
                } else if (($row->USER_0 == null && $row->USER_1 == 'ePC') || ($row->USER_0 == 'C' && $row->USER_1 == 'ePC') || ($row->USER_0 == 'N' && $row->USER_1 == 'ePC')) {
//                    self::saveRole($users, 'ePC');
                    if ($row->STAFF_ID != '20055') { //Suliza Zulkifli is not in group ePC
                        self::saveGroup($users, 'Group ePC');
                    }
                } else if (($row->USER_0 == null && $row->USER_1 == 'Finance') || ($row->USER_0 == 'C' && $row->USER_1 == 'Finance') || ($row->USER_0 == 'N' && $row->USER_1 == 'Finance')) {
//                    self::saveRole($users, 'Finance');
                    self::saveGroup($users, 'Group Finance');
                } else if (($row->USER_0 == null && trim($row->USER_1) == 'GM') || (trim($row->USER_0) == 'C' && trim($row->USER_1) == 'GM') || (trim($row->USER_0) == 'N' && trim($row->USER_1) == 'GM')) {
//                    self::saveRole($users, 'Internal User');
                    self::saveGroup($users, 'Group Government Management(GM)');
                    if ($row->USER_2 != '') {
                        //Log::debug(self::class . ' GM  '.$row->USER_2 );
                        if (array_key_exists(trim($row->USER_2), $groupGmMapping)) {
                            self::saveGroup($users, $groupGmMapping[trim($row->USER_2)]);
                        }
                    }
                    if ($row->USER_3 != '') {
                        //Log::debug(self::class . ' GM  '.$row->USER_3 );
                        if (array_key_exists(trim($row->USER_3), $groupGmMapping)) {
                            self::saveGroup($users, $groupGmMapping[trim($row->USER_3)]);
                        }
                    }
                } else if (($row->USER_0 == null && $row->USER_1 == 'SM') || ($row->USER_0 == 'C' && $row->USER_1 == 'SM')) {
//                    self::saveRole($users, 'Internal User');
                    self::saveGroup($users, 'Group Supplier Management');
                } else if (($row->USER_0 == null && $row->USER_1 == 'OFU') || ($row->USER_0 == 'C' && $row->USER_1 == 'OFU')) {
                    if ($row->STAFF_ID != '20410') { //maziah is in group crm admin
//                    self::saveRole($users, 'OFU');
                        self::saveGroup($users, 'Group Operation Management(OM)');
                    }
                } else if (($row->USER_0 == null && $row->USER_1 == 'Unit eP') || ($row->USER_0 == 'C' && $row->USER_1 == 'Unit eP') || ($row->USER_0 == 'N' && $row->USER_1 == 'Unit eP')) {
//                    self::saveRole($users, 'Unit eP');
                    self::saveGroup($users, 'Approver');
                } else if (($row->USER_0 == 'C' && $row->USER_1 == 'Administrator') || ($row->USER_0 == 'N' && $row->USER_1 == 'Administrator') || ($row->USER_0 == null && $row->USER_1 == 'Administrator')) {
//                    self::saveRole($users, 'Unit eP');
                    if ($loginName != 'support_scsb') {
                        self::saveGroup($users, 'Group CRM Administrator');
                    }
                } else if (($row->USER_0 == 'C' && $row->USER_1 == 'CF') || ($row->USER_0 == null && $row->USER_1 == 'CF')) {
                    self::saveGroup($users, 'Group Operation Management(OM)');
                } else if (($row->USER_0 == 'C' && $row->USER_1 == 'DB-Admin')) {
                    self::saveGroup($users, 'Group IT Specialist(Database Admin)');
                } else if ($row->USER_0 == 'C' && $row->USER_1 == 'DBA') {
                    self::saveGroup($users, 'Group IT Specialist(Database Management)');
                } 
//                else if ($row->USER_0 == 'C' && $row->USER_1 == 'IT-Support') {
//                    self::saveGroup($users, 'Group IT-Support');
//                } 
                else if (($row->USER_0 == 'C' && $row->USER_1 == 'Network')) {
                    self::saveGroup($users, 'Group IT Specialist(Network Admin)');
                } else if (($row->USER_0 == 'C' && $row->USER_1 == 'Server-Admin') || ($row->USER_0 == null && $row->USER_1 == 'Server-Admin')) {
                    if ($row->STAFF_ID != '20188') {//remove hendra
                        self::saveGroup($users, 'Group IT Specialist(Server Admin)');
                    }
                } 
//                else if (($row->USER_0 == 'C' && $row->USER_1 == 'Support') || ($row->USER_0 == null && $row->USER_1 == 'Support')) {
//                    self::saveGroup($users, 'Group Support');
//                } 
                else if (($row->USER_0 == 'C' && $row->USER_1 == 'Training') || ($row->USER_0 == null && $row->USER_1 == 'Training') || ($row->USER_0 == 'N' && $row->USER_1 == 'Training')) {
                    self::saveGroup($users, 'Group Training');
                } else {
                    Log::debug(self::class . ' No role and group specified : ' . $users->first_name);
                    /** Any users found but not in any group related. We attached it this user under group Internal* */
                    self::saveGroup($users, 'Group Internal User');
                }
                
                if ($row->STAFF_ID == '20407') { //add support_scsb
                    self::saveGroup($users, 'Group Internal User');
                }
                
                if ($row->STAFF_ID == '20410') { //add maziah to group crm admin
                    self::saveGroup($users, 'Group CRM Administrator');
                }
                if ($row->STAFF_ID == '20188') { //add hendra
                    self::saveGroup($users, 'Group IT Specialist(Network Admin)');
                }
//                if ($row->STAFF_ID == '20181') { //group coo - pn.norhisah
//                    self::saveGroup($users, 'Group SVP'); //not finalized 
//                }
            }
        }
    }

    public static function saveEmailAddress(RegisteredUser $user, $rowData) {

        $emailraw = trim($rowData->EMAIL);
        $email = filter_var($emailraw, FILTER_SANITIZE_EMAIL);

        if (!filter_var($email, FILTER_VALIDATE_EMAIL) == false) {
            $emailAddress = new EmailAddress;
            $emailAddress->id = Uuid::uuid4()->toString();
            $emailAddress->email_address = trim(strtolower($email));
            $emailAddress->email_address_caps = trim(strtoupper($email));
            $emailAddress->date_created = Carbon::now();
            $emailAddress->date_modified = Carbon::now();
            $emailAddress->deleted = 0;
            $emailAddress->save();

            //Add relationship with email
            $user->emailsUser()->attach($emailAddress, [
                'id' => Uuid::uuid4()->toString(),
                'bean_module' => 'Users',
                'date_modified' => Carbon::now(),
                'deleted' => 0
            ]);
            $user->emailsUserRelation()->attach($emailAddress, [
                'id' => Uuid::uuid4()->toString(),
                'bean_module' => 'Users',
                'primary_address' => 1,
                'reply_to_address' => 0,
                'date_created' => Carbon::now(),
                'date_modified' => Carbon::now(),
                'deleted' => 0
            ]);
        } else {
            Log::debug(self::class . ' Incomplete emails ' . $email . '  Name:' . $user->first_name);
        }
    }

    public static function saveRole(RegisteredUser $user, $rolename) {

        $roleId = self::getRoleId($rolename);

        $user->rolesUser()->attach($roleId, [
            'id' => Uuid::uuid4()->toString(),
            'date_modified' => Carbon::now(),
            'deleted' => 0
        ]);
    }

    public static function getRoleId($name) {
        return DB::table('acl_roles')
                        ->where('name', $name)
                        ->select('id')
                        ->first();
    }

    public static function saveGroup(RegisteredUser $user, $groupname) {

        $groupId = self::getGroupId($groupname);
        $checkSecurityGroupUsers = DB::table('securitygroups_users')
                ->where('securitygroup_id', $groupId->id)
                ->where('user_id', $user->id)
                ->count();
        if ($checkSecurityGroupUsers == 0) {
            $user->groupUser()->attach($groupId, [
                'id' => Uuid::uuid4()->toString(),
                'date_modified' => Carbon::now(),
                'deleted' => 0,
                'primary_group' => 0,
                'noninheritable' => 0
            ]);
        }
    }

    public static function getGroupId($name) {
        return DB::table('securitygroups')
                        ->where('name', $name)
                        ->select('id')
                        ->first();
    }

}
