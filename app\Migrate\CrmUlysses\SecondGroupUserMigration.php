<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\CrmUlysses;

use Carbon\Carbon;
use Ramsey\Uuid\Uuid;
use DB;
use Excel;
use App\RegisteredUser;

class SecondGroupUserMigration {

    public static function runMigrate() {
        self::insertGroup();
    }

    public static function insertGroup() {

        Excel::load('/app/Migrate/CrmUlysses/data/SecondGroupUserMigration.xlsx', function($reader) {

            $reader->each(function($sheet) {
                // Loop through all rows
                $sheet->each(function($value) {

                    //insert user
                    if ($value->username != null) {

                        $username = trim(strtolower($value->username));
                        //$password = '$2y$10$ua6PicOvqyYMKgOR6gzFcub.Z5s40j6moWRH4oaO.Ef667lz.nb0m'; /** Password123 **/
                        //$password = 'ef749ff9a048bad0dd80807fc49e1c0d';/** Password1234 :::  SELECT MD5('Password1234')FROM DUAL; * */
                        $password = '911c8eb3c230c3350564857be60ac4f4';/** P@ssword1234 :::  SELECT MD5('Password1234')FROM DUAL; * */
                    
                        $datauser = DB::table('users')->where('user_name', $username)->count();
                        if ($datauser == 0) {

                            DB::table('users')
                                    ->insertGetId([
                                        'id' => Uuid::uuid4()->toString(),
                                        'user_name' => $username,
                                        'user_hash' => $password,
                                        'pwd_last_changed' => Carbon::now()->subDay(10),
                                        'system_generated_password' => 1,
                                        'sugar_login' => 1,
                                        'first_name' => trim($value->name),
                                        'is_admin' => 0,
                                        'date_entered' => Carbon::now(),
                                        'date_modified' => Carbon::now(),
                                        'created_by' => 2,
                                        'deleted' => 0,
                                        'portal_only' => 0,
                                        'show_on_employees' => 1,
                                        'status' => 'Active',
                                        'employee_status' => 'Active',
                                        'is_group' => 0
                            ]);
                        }

                       
                        
                        $user = RegisteredUser::where('user_name', $value->username) ->first();
                        $user->employee_status = 'Active';
                        $user->status = 'Active';
                        $user->deleted = 0;
                        $user->save();
                        

                        if ($user && $user->id) {

                            $dataemail = DB::table('email_addresses')
                                            ->where('email_address', trim(strtolower($value->email)))->count();
                            if ($dataemail == 0) {
                                DB::table('email_addresses')
                                        ->insertGetId([
                                            'id' => Uuid::uuid4()->toString(),
                                            'email_address' => trim(strtolower($value->email)),
                                            'email_address_caps' => trim(strtoupper($value->email)),
                                            'date_created' => Carbon::now(),
                                            'date_modified' => Carbon::now(),
                                            'deleted' => 0,
                                ]);
                            }


                            $emailAddr = DB::table('email_addresses')
                                            ->where('email_address', trim(strtolower($value->email)))->first();

                            if ($emailAddr && $emailAddr->id) {

                                $checkAddrEA = DB::table('email_addr_bean_rel')
                                        ->where('email_address_id', $emailAddr->id)
                                        ->where('bean_id', $user->id)
                                        ->where('bean_module', 'Users')
                                        ->count();
                                if ($checkAddrEA == 0) {
                                    DB::table('email_addr_bean_rel')
                                            ->insertGetId([
                                                'id' => Uuid::uuid4()->toString(),
                                                'email_address_id' => $emailAddr->id,
                                                'bean_id' => $user->id,
                                                'bean_module' => 'Users',
                                                'date_created' => Carbon::now(),
                                                'date_modified' => Carbon::now(),
                                                'deleted' => 0,
                                    ]);
                                }
                                $checkAddrEB = DB::table('emails_beans')
                                        ->where('email_id', $emailAddr->id)
                                        ->where('bean_id', $user->id)
                                        ->where('bean_module', 'Users')
                                        ->count();
                                if ($checkAddrEB == 0) {
                                    DB::table('emails_beans')
                                            ->insertGetId([
                                                'id' => Uuid::uuid4()->toString(),
                                                'email_id' => $emailAddr->id,
                                                'bean_id' => $user->id,
                                                'bean_module' => 'Users',
                                                'date_modified' => Carbon::now(),
                                                'deleted' => 0,
                                    ]);
                                }
                            }

                            $securityGroup = DB::table('securitygroups')
                                    ->where('name', $value->securitygroups)
                                    ->first();

                            if ($securityGroup && $securityGroup->id) {

                                $checkSecurityGroupUsers = DB::table('securitygroups_users')
                                        ->where('securitygroup_id', $securityGroup->id)
                                        ->where('user_id', $user->id)
                                        ->count();
                                if ($checkSecurityGroupUsers == 0) {
                                    DB::table('securitygroups_users')
                                            ->insertGetId([
                                                'id' => Uuid::uuid4()->toString(),
                                                'date_modified' => Carbon::now(),
                                                'deleted' => 0,
                                                'securitygroup_id' => $securityGroup->id,
                                                'user_id' => $user->id,
                                    ]);
                                }
                            }
                            /*
                              $role = DB::table('acl_roles')
                              ->where('name', $value->roles)
                              ->first();

                              if ($role && $role->id) {
                              DB::table('acl_roles_users')
                              ->insertGetId([
                              'id' => Uuid::uuid4()->toString(),
                              'role_id' => $role->id,
                              'user_id' => $user->id,
                              'date_modified' => Carbon::now(),
                              'deleted' => 0,
                              ]);
                              }

                             */
                        }
                    }
                });
            });
        });
    }

}
