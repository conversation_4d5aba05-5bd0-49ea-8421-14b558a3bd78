<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\CrmUlysses;

use Carbon\Carbon;
use Log;
use Ramsey\Uuid\Uuid;
use DB;
use Config;
use App\Trainee;
use App\EmailAddress;
use Excel;

class TraineeListCrm {

    public static function runMigrate() {
        self::insertTrainee();
    }

    public static function insertTrainee() {

        Excel::load('/app/Migrate/CrmUlysses/data/TraineeCrm.xlsx', function($reader) {

            $reader->each(function($sheet) {
                // Loop through all rows
                $sheet->each(function($value) {
                    
                    //$password = '$2y$10$ua6PicOvqyYMKgOR6gzFcub.Z5s40j6moWRH4oaO.Ef667lz.nb0m'; /** Password123 **/
                    //$password = 'ef749ff9a048bad0dd80807fc49e1c0d';/** Password1234 :::  SELECT MD5('Password1234')FROM DUAL; * */
                    $password = '911c8eb3c230c3350564857be60ac4f4';/** P@ssword1234 :::  SELECT MD5('Password1234')FROM DUAL; * */
                    
                    //insert user
                    if ($value->username != null) {
                        $username = trim(strtolower($value->username));
                        
                        $datauser = DB::table('users')->where('user_name', $username)->count();
                        if ($datauser == 0) {

                            DB::table('users')
                                    ->insertGetId([
                                        'id' => Uuid::uuid4()->toString(),
                                        'user_name' => $username,
                                        'user_hash' => $password,
                                        'pwd_last_changed' => Carbon::now()->subDay(10),
                                        'system_generated_password' => 1,
                                        'sugar_login' => 1,
                                        'first_name' => trim($value->firstname),
                                        'is_admin' => 0,
                                        'date_entered' => Carbon::now(),
                                        'date_modified' => Carbon::now(),
                                        'created_by' => 2,
                                        'deleted' => 0,
                                        'portal_only' => 0,
                                        'show_on_employees' => 1,
                                        'status' => 'Active',
                                        'employee_status' => 'Active',
                                        'is_group' => 0
                            ]);
                        }else{
                            /**For update user to be first time login **/
                            // To fixed database on development. 
                            DB::table('users')
                            ->where('user_name', $username)
                            ->update([
                                    'user_hash' => $password,
                                    'pwd_last_changed' => Carbon::now()->subDay(10),
                                    'system_generated_password' => 1,
                                    ]);
                        }
                        $users = DB::table('users')->where('user_name', $username)->first();
                        
                        $dataemail = DB::table('email_addresses')
                                        ->where('email_address', $value->email)->count();
                        if ($dataemail == 0) {
                            DB::table('email_addresses')
                                    ->insertGetId([
                                        'id' => Uuid::uuid4()->toString(),
                                        'email_address' => trim(strtolower($value->email)),
                                        'email_address_caps' => trim(strtoupper($value->email)),
                                        'date_created' => Carbon::now(),
                                        'date_modified' => Carbon::now(),
                                        'deleted' => 0,
                            ]);
                        }
                        

                        $emailAddr = DB::table('email_addresses')
                                        ->where('email_address', $value->email)->first();

                        if ($emailAddr && $emailAddr->id) {
                            DB::table('email_addr_bean_rel')
                                    ->insertGetId([
                                        'id' => Uuid::uuid4()->toString(),
                                        'email_address_id' => $emailAddr->id,
                                        'bean_id' => $users->id,
                                        'bean_module' => 'Users',
                                        'date_created' => Carbon::now(),
                                        'date_modified' => Carbon::now(),
                                        'deleted' => 0,
                            ]);

                            DB::table('emails_beans')
                                    ->insertGetId([
                                        'id' => Uuid::uuid4()->toString(),
                                        'email_id' => $emailAddr->id,
                                        'bean_id' => $users->id,
                                        'bean_module' => 'Users',
                                        'date_modified' => Carbon::now(),
                                        'deleted' => 0,
                            ]);
                        }


                        //get security group id
                        $secGroup = DB::table('securitygroups')
                                ->where('name', $value->securitygroups)
                                ->first();

                        //insert into securitygroup users
                        if (($secGroup && $secGroup->id)) {
                            DB::table('securitygroups_users')
                                    ->insert([
                                        'id' => Uuid::uuid4()->toString(),
                                        'securitygroup_id' => $secGroup->id,
                                        'user_id' => $users->id, 'date_modified' => Carbon::now(),
                                        'noninheritable' => 0, 'deleted' => 0
                            ]);
                        }
                        
                        /*
                        //get role id
                        $aclRole = DB::table('acl_roles')
                                ->where('name', $value->roles)
                                ->first();

                        //insert into acl roles user
                        if (($aclRole && $aclRole->id)) {
                            DB::table('acl_roles_users')
                                    ->insert([
                                        'id' => Uuid::uuid4()->toString(),
                                        'role_id' => $aclRole->id,
                                        'user_id' => $users->id, 'date_modified' => Carbon::now(), 'deleted' => 0
                            ]);
                        }
                         
                        */
                    }
                });
            });
        });
    }

}
