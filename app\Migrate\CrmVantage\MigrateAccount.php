<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\CrmVantage;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Facades\DB;
use App\Migrate\MigrateUtils;
use App\Services\CrmVantageService;
use Exception;

class MigrateAccount {

    public static function crmService() {
        return new CrmVantageService;
    }

    public static function runMigrate() {
        Log::debug(self::class . ' Starting ... runMigrate ' );
        $dtStartTime = Carbon::now();

        self::migrateAccountContact();

        Log::info(self::class . ' Completed runMigrate --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);

    }

    public static function migrateAccountContact() {

        $start = 0;
        $skip = 500;
        $take = 500;
        $count = 0;
        $total = 0;

        do {
            $nextSkip = $start++ * $skip;
            $listAccounts = CrmVantageService::getMigrateAccount($nextSkip, $take);
            $totalListAccounts = count($listAccounts);
            Log::info(' Count Total Query :- ' . $totalListAccounts);
            var_dump(' Count Total Query :- ' . $totalListAccounts);
            $total = $total + $totalListAccounts;

            foreach ($listAccounts as $row) {
                $nowDb = Carbon::now()->subHour(8);
                $accId = Uuid::uuid4()->toString();
                $contactId = Uuid::uuid4()->toString();
                $emailId = Uuid::uuid4()->toString();

                $companyName = trim($row->company_name);
                $companyRegNo = $row->company_registration_no;
                $companyPhone = $row->company_phone_no;
                $companyAddress = $row->company_address;
                $companyCity = $row->company_city;
                $companyState = $row->company_state;
                $contactName = $row->contact_name;
                $contactEmail = $row->contact_email;
                $contactPhone = $row->contact_phone_no; 
                $contactName = $row->contact_name;
                $contactEmail = $row->contact_email;
                $contactPhone = $row->contact_phone_no;


                //set date create account earlier than case created. earliest case created on jan 2023
                $dateCreated = '2023-01-02 08:00';
                $accountDateCreated = Carbon::parse($dateCreated)->subHour(8);

                $dateCreated2 = '2023-01-02 08:30';
                $contactDateCreated = Carbon::parse($dateCreated2)->subHour(8);
 
                $checkAccount = CrmVantageService::checkAccount($companyName,$companyRegNo,$companyPhone);

                if (isset($checkAccount)) {
                    $isInsert=false;
                    $accId = $checkAccount->id;
                    var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > Account Already Exist. Do Update > ' . $accId . ' > ' . $checkAccount->name);
                    Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > Account Already Exist. Do Update >' . $accId . ' > ' . $checkAccount->name);

                } else {
                    $isInsert=true;
                    var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > Insert New Account ' . $companyName); 
                    Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > Insert New Account ' . $companyName); 
                }

                $sqlInsertAccount = CrmVantageService::insertAccount($isInsert, $accId, $accountDateCreated, $companyName, $companyPhone, $companyAddress, $companyCity, $companyState, $companyRegNo);
                var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > sqlInsertAccount ' . $sqlInsertAccount); 
                Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > sqlInsertAccount ' . $sqlInsertAccount); 
                if($sqlInsertAccount !== 'Error'){
                    $sqlInsertAccountCstm = CrmVantageService::insertAccountCstm($isInsert,$accId);
                    var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > sqlInsertAccountCstm ' . $sqlInsertAccountCstm); 
                    Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > sqlInsertAccountCstm ' . $sqlInsertAccountCstm); 
                    if($sqlInsertAccountCstm !== 'Error'){
                        var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > Complete Migrate Account > ' . $contactName);
                        Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > Complete Migrate Account > ' . $contactName);  
                    }else{
                        var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > Error Insert Into Table Account Cstm >  ' . $companyName . ' > ' . $sqlInsertAccountCstm);
                        Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > Error Insert Into Table Account Cstm >  ' . $companyName . ' > ' . $sqlInsertAccountCstm);                       
                    }
                }else{
                    var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > Error Insert Into Table Account >  ' . $companyName . ' > ' . $sqlInsertAccount);
                    Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > Error Insert Into Table Account >  ' . $companyName . ' > ' . $sqlInsertAccount);            
                } 
                //check contact 
                $checkContact = CrmVantageService::checkContact($contactEmail, $contactPhone, $contactName);

                if(isset($checkContact)){
                    $isInsertContact=false; 
                    $contactId = $checkContact->id;   
                    var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > Contact Already Exist. Do Update > ' . $contactId);
                    Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > Contact Already Exist. Do Update > ' . $contactId);
                }else{
                    $isInsertContact=true; 
                    var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > Insert New Contact  > ' . $contactName);
                    Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > Insert New Contact  > ' . $contactName);
               }

                $sqlInsertContact = CrmVantageService::insertContact($isInsertContact, $contactId, $contactDateCreated, $contactName, $contactPhone, '');
                var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > sqlInsertContact ' . $sqlInsertContact); 
                Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > sqlInsertContact ' . $sqlInsertContact); 
    
                if($sqlInsertContact !== 'Error'){
                    $sqlInsertContactCstm = CrmVantageService::insertContactCstm($isInsertContact, $contactId);
                    var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > sqlInsertContactCstm ' . $sqlInsertContactCstm); 
                    Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > sqlInsertContactCstm ' . $sqlInsertContactCstm); 
                    
                    if($sqlInsertContactCstm !== 'Error'){
                        $sqlInsertAccountContact = CrmVantageService::insertAccountContact($isInsertContact, $accId, $contactId, $contactDateCreated);
                        var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > sqlInsertAccountContact ' . $sqlInsertAccountContact); 
                        Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > sqlInsertAccountContact ' . $sqlInsertAccountContact); 

                        if($sqlInsertAccountContact !== 'Error'){
                            if (!filter_var($contactEmail, FILTER_VALIDATE_EMAIL) == false) {
                                $checkEmail = CrmVantageService::checkEmail($contactEmail,'Contacts');

                                if(isset($checkEmail)){
                                    Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > Email Already Exist. Skip  > ' . $contactEmail);
                                    Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > Email Already Exist. Skip  > ' . $contactEmail);
                                }else{
                                    $sqlInsertEmail = CrmVantageService::insertEmail($emailId,$contactEmail,$contactId,'Contacts',$contactDateCreated);
                                    Log::info('$sqlInsertEmail > ' .$sqlInsertEmail);
                                
                                    if($sqlInsertEmail !== 'Error'){
                                        var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > Complete Insert Email For Contact > ' . $contactName);
                                        Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > Complete Insert Email For Contact > ' . $contactName);                    
                                    }else{
                                        var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > Error Insert Into Table Email >  ' . $contactName . ' >> ' . $sqlInsertEmail);
                                        Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > Error Insert Into Table Email >  ' . $contactName . ' >> ' . $sqlInsertEmail);
                                    }
                                } 
                            }else{
                                var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > Email address not valid >  ' . $contactEmail);
                                Log::info(__CLASS__ . ' > ' .__FUNCTION__ . ' > Email address not valid >  ' . $contactEmail);
                            }
                        }else{
                            var_dump(__CLASS__ . ' > ' .__FUNCTION__. ' > Error Insert Into Table Account Contact Relationship >  ' . $contactName . ' >> ' . $sqlInsertAccountContact);
                            Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > Error Insert Into Table Account Contact Relationship >  ' . $contactName . ' >> ' . $sqlInsertAccountContact);
                        }
                    }else{
                        var_dump(__CLASS__ . ' > ' .__FUNCTION__. ' > Error Insert Into Table Contact Cstm >  ' . $contactName . ' >> ' . $sqlInsertContactCstm);
                        Log::info(__CLASS__ . ' > ' .__FUNCTION__. ' > Error Insert Into Table Contact Cstm >  ' . $contactName . ' >> ' . $sqlInsertContactCstm);
                    }
                }else{
                    var_dump(__CLASS__ . ' > ' .__FUNCTION__. ' > Error Insert Into Table Contact >  ' . $contactName . ' >> ' . $sqlInsertContact);
                    Log::info(__CLASS__ . ' > ' .__FUNCTION__. ' > Error Insert Into Table Contact >  ' . $contactName . ' >> ' . $sqlInsertContact);
                }
            }
        } while ($totalListAccounts > 0 && $totalListAccounts == $take);
       
    } 
     
}
