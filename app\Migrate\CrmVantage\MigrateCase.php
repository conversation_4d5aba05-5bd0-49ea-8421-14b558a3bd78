<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\CrmVantage;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Facades\DB;
use App\Migrate\MigrateUtils;
use App\Services\CrmVantageService;
use Exception;

class MigrateCase {

    public static function crmService() {
        return new CrmVantageService;
    }

    public static function runMigrate() {
        Log::debug(self::class . ' Starting ... runMigrate ' );
        $dtStartTime = Carbon::now();

        self::migrateCase();

        Log::info(self::class . ' Completed runMigrate --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);

    }

    public static function migrateCase() {

        $start = 0;
        $skip = 500;
        $take = 500;
        $count = 0;
        $total = 0;

        do {
            $nextSkip = $start++ * $skip;
            $listCases = CrmVantageService::getMigrateCase($nextSkip, $take);
            $totallistCases = count($listCases);
            Log::info(' Count Total Query :- ' . $totallistCases);
            var_dump(' Count Total Query :- ' . $totallistCases);
            $total = $total + $totallistCases;

            foreach ($listCases as $row) {
                $nowDb = Carbon::now()->subHour(8);
                $accountId = null;
                $caseId = Uuid::uuid4()->toString();
                $taskId = Uuid::uuid4()->toString();
                $contactId = null;
                $assignUserId = 1;
                $createdBy = 1;

                $caseTitle = trim($row->case_title);
                $caseDescription = $row->case_description;
                $caseCategory = $row->case_category;
                $caseSubCategory = $row->case_sub_category;
                $caseCreatedDate = $row->case_created_date;
                $caseCreatedBy = $row->case_created_by;
                $caseCompletedDate = $row->case_completed_date;
                $status = $row->case_status;
                $caseResolution = $row->case_resolution; 
                $caseAccount = trim($row->case_account); 
                $taskTitle = $row->escalate_task_title; 
                $taskUserAssigned = $row->escalate_task_user_assigned; 
                $taskResolution = $row->escalate_task_resolution; 
                $taskStatus = $row->escalate_task_status; 
                $taskCreatedDate = $row->escalate_task_created_date; 
                $taskCompletedDate = $row->escalate_task_completed_date; 

                if($taskStatus == 'Closed'){
                    $taskStatus = 'Completed';
                }
                $caseStatus = CrmVantageService::getValueLookupByNameCRM('case_status_dom',$status);
                $newTaskStatus = CrmVantageService::getValueLookupByNameCRM('task_status_dom',$taskStatus);
                $caseCategoryValue = CrmVantageService::getValueLookupByNameCRM('category_list',$caseCategory);
                $caseSubCategoryValue = CrmVantageService::getValueLookupByNameCRM('category2_list',$caseSubCategory);
              
                if($status == 'Closed'){
                    $caseState = 'Closed';
                }else {
                    $caseState = 'Open';
                }

                $checkAccount = CrmVantageService::checkAccount($caseAccount,null,null);

                if (isset($checkAccount)) {
                    $accountId = $checkAccount->id; 

                } else {
                    var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > Account Not Found > ' . $accountId);
                    Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > Account Not Found > ' . $accountId); 
                }
                
                $caseCreated = strtotime($caseCreatedDate);
                $newFormatCaseCreated = Carbon::parse(date('Y-m-d H:i:s',$caseCreated))->subHour(8);
                
                if($caseCompletedDate != ''){
                    $caseCompleted = strtotime($caseCompletedDate);
                    $newFormataseCompleted = Carbon::parse(date('Y-m-d H:i:s',$caseCompleted))->subHour(8);
                }else{
                    $newFormataseCompleted = $newFormatCaseCreated;
                } 

                $taskCreated = strtotime($taskCreatedDate);
                $newFormatTaskCreated = Carbon::parse(date('Y-m-d H:i:s',$taskCreated))->subHour(8);

                if($taskCompletedDate != ''){
                    $taskCompleted = strtotime($taskCompletedDate);
                    $newFormatTaskCompleted = Carbon::parse(date('Y-m-d H:i:s',$taskCompleted))->subHour(8);
                }else{
                    $newFormatTaskCompleted = $newFormatTaskCreated;
                }
                
                $checkContact = CrmVantageService::checkContact(null, null, $caseCreatedBy);
                if(isset($checkContact)){
                    $contactId = $checkContact->id;
                } 

                $getInboundUser = CrmVantageService::getUserDetail('inbound_email');
                if(isset($getInboundUser)){
                    $createdBy = $getInboundUser->id;
                }
                
                $checkUser = CrmVantageService::checkUser(null, null, $taskUserAssigned);
                if(isset($checkUser)){
                    $assignUserId = $checkUser->id;
                }else{
                    $assignUserId = $createdBy;
                }
                
                $checkCase = CrmVantageService::checkCase($caseTitle,$accountId,$caseDescription);
                if(isset($checkCase)){
                    $isInsert = false;
                    $caseId = $checkCase->id;
                    
                    $checkTask = CrmVantageService::checkTask($caseId);
                    
                    if(isset($checkTask)){
                        $taskId = $checkTask->id;
                    }
                    var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > Case Exist. Do Update > ' .$caseId);
                    Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > Case Exist. Do Update > ' .$caseId);
                }else{
                    $isInsert = true;
                    var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > Insert Case > ' .$caseTitle);
                    Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > sqlInsertCase > ' .$caseTitle);    
                }

                $sqlInsertCase = CrmVantageService::insertCase($isInsert, $caseId, $caseTitle, $newFormatCaseCreated, $newFormataseCompleted,
                                $assignUserId,$createdBy,$contactId,$caseDescription,$caseStatus,$caseResolution,
                                $accountId,$caseState,$caseCategoryValue,$caseSubCategoryValue);
                var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > sqlInsertCase > ' .$sqlInsertCase);
                Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > sqlInsertCase > ' .$sqlInsertCase);
                
                if($sqlInsertCase !== 'Error'){
                    $sqlInsertCaseCstm = CrmVantageService::insertCaseCstm($isInsert, $caseId);
                    var_dump(__CLASS__ . ' > ' .__FUNCTION__ . ' > sqlInsertCaseCstm > ' .$sqlInsertCaseCstm);
                    Log::info(__CLASS__ . ' > ' .__FUNCTION__ . ' > sqlInsertCaseCstm > ' .$sqlInsertCaseCstm);
                    
                    if($sqlInsertCaseCstm !== 'Error'){
                        $sqlInsertTask = CrmVantageService::insertTask($isInsert, $caseId,$taskId,$newFormatTaskCreated,$newFormatTaskCompleted,$assignUserId,
                        $caseDescription,$newTaskStatus,$caseResolution,$contactId);
                        var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > sqlInsertTask > ' .$sqlInsertTask);
                        Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > sqlInsertTask > ' .$sqlInsertTask);
                        
                        if($sqlInsertTask !== 'Error'){
                            var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > Complete Migrate Case and Task > ' . $caseTitle);
                            Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > Complete Migrate Case and Task > ' . $caseTitle); 
                        }else{
                            var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > Error Insert Into Table Task >  ' . $caseTitle);
                            Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > Error Insert Into Table Task >  ' . $caseTitle);                    
                        }
                    }else{
                        var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > Error Insert Into Table Case Custom >  ' . $caseTitle);
                        Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > Error Insert Into Table Case Custom >  ' . $caseTitle);            
                    }
                }else{
                    var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > Error Insert Into Table Case >  ' . $caseTitle);
                    Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > Error Insert Into Table Case >  ' . $caseTitle);
                }    
            }
        } while ($totallistCases > 0 && $totallistCases == $take);
       
    } 
     
}
