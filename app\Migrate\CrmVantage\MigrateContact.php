<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\CrmVantage;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Facades\DB;
use App\Migrate\MigrateUtils;
use App\Services\CrmVantageService;
use Exception;

class MigrateContact {

    public static function crmService() {
        return new CrmVantageService;
    }

    public static function runMigrate() {
        Log::debug(self::class . ' Starting ... runMigrate ' );
        $dtStartTime = Carbon::now();

        self::migrateContact();

        Log::info(self::class . ' Completed runMigrate --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);

    }

    public static function migrateContact() {

        $start = 0;
        $skip = 500;
        $take = 500;
        $count = 0;
        $total = 0;

        do {
            $nextSkip = $start++ * $skip;
            $listContacts = CrmVantageService::getMigrateContact($nextSkip, $take);
            $totallistContacts = count($listContacts);
            Log::info(' Count Total Query :- ' . $totallistContacts);
            var_dump(' Count Total Query :- ' . $totallistContacts);
            $total = $total + $totallistContacts;

            foreach ($listContacts as $row) {
                $nowDb = Carbon::now()->subHour(8);
                $contactId = Uuid::uuid4()->toString();
                $emailId = Uuid::uuid4()->toString();

                $fullName = $row->fullname;
                $email = $row->email;
                $phoneNo = $row->phone_no;
                $userType = $row->user_type;
                $accountName = trim($row->account_name); 

                $dateCreated2 = '2023-01-02 08:30';
                $contactDateCreated = Carbon::parse($dateCreated2)->subHour(8);

                //check contact 
                $checkContact = CrmVantageService::checkContact($email, $phoneNo, $fullName);

                if(!isset($checkContact)){ 
                    $isInsert = true;       
                    var_dump(__CLASS__ . ' > ' .__FUNCTION__ . ' > Contact ' . $fullName . ' not exist. Create New');
                    Log::info(__CLASS__ . ' > ' .__FUNCTION__ . ' > Contact ' . $fullName . ' not exist. Create New');
         
                }else{
                    $isInsert = false;
                    $contactId = $checkContact->id;
                    var_dump(__CLASS__ . ' > ' .__FUNCTION__ . ' > Contact Already Exist.Do Update > ' .$contactId .' > ' .$fullName);
                    Log::info(__CLASS__ . ' > ' .__FUNCTION__ . '> Contact Already Exist.Do Update > ' .$contactId .' > ' .$fullName);
                }
                
                $sqlInsertContact = CrmVantageService::insertContact($isInsert, $contactId, $contactDateCreated, $fullName, $phoneNo, $userType);
                    Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > sqlInsertContact' .$sqlInsertContact);
                    if($sqlInsertContact !== 'Error'){
                        $sqlInsertContactCstm = CrmVantageService::insertContactCstm($isInsert, $contactId);
                        var_dump(__CLASS__ . ' > ' .__FUNCTION__  . ' > sqlInsertContactCstm' .$sqlInsertContactCstm);
                        Log::info(__CLASS__ . ' > ' .__FUNCTION__  . ' > sqlInsertContactCstm' .$sqlInsertContactCstm);
                        if($sqlInsertContactCstm !== 'Error'){
                            $checkAccount = CrmVantageService::checkAccount($accountName, null ,null);
                            
                            if (isset($checkAccount)) {
                                //insert account contact relationship
                                $sqlInsertAccountContact = CrmVantageService::insertAccountContact($isInsert, $checkAccount->id, $contactId, $contactDateCreated);
                                var_dump(__CLASS__ . ' > ' .__FUNCTION__  .' > sqlInsertAccountContact > ' .$sqlInsertAccountContact);
                                Log::info(__CLASS__ . ' > ' .__FUNCTION__  .' > sqlInsertAccountContact > ' .$sqlInsertAccountContact);
                                
                                if($sqlInsertAccountContact !== 'Error'){
                                    if (!filter_var($email, FILTER_VALIDATE_EMAIL) == false) {  
                                        $checkEmail = CrmVantageService::checkEmail($email,'Contacts');

                                        if(isset($checkEmail)){
                                            Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > Email Already Exist. Skip  > ' . $email);
                                            Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > Email Already Exist. Skip  > ' . $email);
                                        }else{
                                            $sqlInsertEmail = CrmVantageService::insertEmail($emailId,$email,$contactId,'Contacts',$contactDateCreated);
                                            var_dump(__CLASS__ . ' > ' .__FUNCTION__  . ' > sqlInsertEmail > ' .$sqlInsertEmail);
                                            Log::info(__CLASS__ . ' > ' .__FUNCTION__  . ' > sqlInsertEmail > ' .$sqlInsertEmail);
                                            if($sqlInsertEmail != 'Error'){
                                                var_dump(__CLASS__ . ' > ' .__FUNCTION__ . ' > Complete Migrate Contact ' . $fullName);
                                                Log::info(__CLASS__ . ' > ' .__FUNCTION__ . ' > Complete Migrate Contact ' . $fullName);                    
                                            }else{
                                                var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > Error Insert Into Table Email >  ' . $fullName . ' >> ' . $sqlInsertEmail);
                                                Log::info(__CLASS__ . ' > ' .__FUNCTION__ . ' > Error Insert Into Table Email >  ' . $fullName . ' >> ' . $sqlInsertEmail);
                                            }
                                        }  
                                    }else{
                                        var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > Email address not valid >  ' . $email);
                                        Log::info(__CLASS__ . ' > ' .__FUNCTION__ . ' > Email address not valid >  ' . $email);
                                    }
                                    
                                } else{
                                    var_dump(__CLASS__ . ' > ' .__FUNCTION__  . ' > Error Insert Into Table Account Contact Relationship >  ' . $fullName . ' >> ' . $sqlInsertAccountContact);
                                    Log::info(__CLASS__ . ' > ' .__FUNCTION__ . ' > Error Insert Into Table Account Contact Relationship >  ' . $fullName . ' >> ' . $sqlInsertAccountContact);
                                }
                            }else{
                                var_dump(__CLASS__ . ' > ' .__FUNCTION__ . ' > Account Not Found > ' . $accountName);
                                Log::info(__CLASS__ . ' > ' .__FUNCTION__ . ' > Account Not Found > ' . $accountName);

                            } 
                             
                        }else{
                            var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > Error Insert Into Table Contact Cstm >  ' . $fullName . ' >> ' . $sqlInsertContactCstm);
                            Log::info(__CLASS__ . ' > ' .__FUNCTION__ . ' > Error Insert Into Table Contact Cstm >  ' . $fullName . ' >> ' . $sqlInsertContactCstm);
                        }
                    }else{
                        var_dump(__CLASS__ . ' > ' .__FUNCTION__ . ' > Error Insert Into Table Contact >  ' . $fullName . ' >> ' . $sqlInsertContact);
                        Log::info(__CLASS__ . ' > ' .__FUNCTION__ . ' > Error Insert Into Table Contact >  ' . $fullName . ' >> ' . $sqlInsertContact);
                    }
            }
        } while ($totallistContacts > 0 && $totallistContacts == $take);
       
    } 
     
}
