<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\CrmVantage;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Facades\DB;
use App\Migrate\MigrateUtils;
use App\Services\CrmVantageService;
use Exception;

class MigrateUser {

    public static function crmService() {
        return new CrmVantageService;
    }

    public static function runMigrate() {
        Log::debug(self::class . ' Starting ... runMigrate ' );
        $dtStartTime = Carbon::now();

        self::migrateUser();

        Log::info(self::class . ' Completed runMigrate --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);

    }

    public static function migrateUser() {

        $start = 0;
        $skip = 500;
        $take = 500;
        $count = 0;
        $total = 0;

        do {
            $nextSkip = $start++ * $skip;
            $listUsers = CrmVantageService::getMigrateUser($nextSkip, $take);
            $totallistUsers = count($listUsers);
            Log::info(' Count Total Query :- ' . $totallistUsers);
            var_dump(' Count Total Query :- ' . $totallistUsers);
            $total = $total + $totallistUsers;

            foreach ($listUsers as $row) {
                $nowDb = Carbon::now()->subHour(8);
                $userId = Uuid::uuid4()->toString();
                $emailId = Uuid::uuid4()->toString();
                $userName = null;

                $fullName = $row->fullname;
                $email = $row->email;
                $phoneNo = $row->phone_no;
                $userType = $row->user_type;
                $accountName = trim($row->account_name); 

                $dateCreated = '2023-01-01 08:00';
                $userDateCreated = Carbon::parse($dateCreated)->subHour(8);

                if (($pos = strpos($email, "@")) !== FALSE) {
                    $userName = ltrim(strstr($email, '@', true)); 
                }
                //check user 
                $checkUser = CrmVantageService::checkUser($email, $phoneNo, $fullName);

                if(isset($checkUser)){  
                    $isInsert = false;  
                    $userId = $checkUser->id;
                    var_dump(__CLASS__ . ' > ' .__FUNCTION__ . ' > User ' . $userId . ' Already Exist. Do Update');
                    Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > User ' . $userId . ' Already Exist. Do Update'); 
         
                }else{
                    $isInsert = true;
                    var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > Insert User > ' . '. ' .$fullName );
                    Log::info(__CLASS__ . ' > ' .__FUNCTION__ . ' > Insert User > ' . '. ' .$fullName );
                } 
                $sqlInsertUser = CrmVantageService::insertUser($isInsert, $userId, $userDateCreated, $fullName, $phoneNo, $userType,$userName);
                    if($sqlInsertUser !== 'Error'){
                        var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > Complete Migrate User ' . $fullName);
                        Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > Complete Migrate User ' . $fullName); 
                        
                        if (!filter_var($email, FILTER_VALIDATE_EMAIL) == false) {   
                            
                            $checkEmail = CrmVantageService::checkEmail($email,'Users');

                                if(isset($checkEmail)){
                                    Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > Email Already Exist. Skip  > ' . $email);
                                    Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > Email Already Exist. Skip  > ' . $email);
                                }else{
                                    $sqlInsertEmail = CrmVantageService::insertEmail($emailId,$email,$userId,'Users',$userDateCreated);
                                    if($sqlInsertEmail !== 'Error'){
                                        var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > Complete Migrate Email User ' . $fullName);
                                        Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > Complete Migrate Email User ' . $fullName);                    
                                    }else{
                                        var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > Error Insert Into Table Email >  ' . $fullName . ' >> ' . $sqlInsertEmail);
                                        Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > Error Insert Into Table Email >  ' . $fullName . ' >> ' . $sqlInsertEmail);
                                    }
                                }
                            
                        }else{
                            var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > Email address not valid >  ' . $email);
                            Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > Email address not valid >  ' . $email);
                        }

                    }else{
                        var_dump(__CLASS__ . ' > ' .__FUNCTION__ .' > Error Insert Into Table User >  ' . $fullName . ' >> ' . $sqlInsertUser);
                        Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > Error Insert Into Table User >  ' . $fullName . ' >> ' . $sqlInsertUser);
                    }
            }
        } while ($totallistUsers > 0 && $totallistUsers == $take);
       
    } 
     
}
