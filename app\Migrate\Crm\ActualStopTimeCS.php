<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\Crm;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Migrate\MigrateUtils;

class ActualStopTimeCS {

    public static function runCheckingActualStopTimeCS($dateStart = null, $dateEnd = null) {

        Log::debug(self::class . ' Starting... Scheduler to empty the value of SLA Stop for CS ', ['Query Start Date' => $dateStart, 'Query End Date' => $dateEnd]);
        $dtStartTime = Carbon::now();

        self::ActualStopTimeCS($dateStart, $dateEnd);

        Log::info(self::class . ' Completed.. Scheduler to empty the value of SLA Stop for CS --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    /* Case Pending response too long*/

    private static function ActualStopTimeCS($dateStart, $dateEnd) {

        $take = 1000;

        $dtStartTime = Carbon::now();
        $getCurr = strtotime($dtStartTime);
        $processDate = date('Y-m-d', $getCurr);
        
        //check for cases still pending response & SLA Stop by scheduler
        do {
            
             $result = "SELECT DISTINCT  c.id     AS case_id,
                 c.case_number     AS case_number,
                 c.name            AS case_name,
                 u.first_name     AS csName,
                 us.first_name AS csNama,
                 cc.`contact_mode_c`,
                 c.pickup_datetime AS case_pickupdate,
                 CONVERT_TZ(c.date_entered,'+00:00','+08:00') AS created_date,
                 cc.request_type_c                            AS request_type,
                 cc.incident_service_type_c                   AS type_of_incident,
                 c.state                                     AS case_state,
                 c.status                                    AS case_status,
                 c.sla_flag                                  AS cs_sla_flag,
                 cmode.value_name                             AS contact_mode,
                 cc.case_info_c                              AS case_info,
                 CONVERT_TZ(c.cntc_mode_sla_startdate,'+00:00','+08:00') AS cs_start_datetime,
                 CONVERT_TZ(c.cntc_mode_sla_duedate,'+00:00','+08:00')   AS cs_due_datetime,
                 CONVERT_TZ(c.cntc_mode_sla_startdate,'+00:00','+08:00') AS cs_actual_start_datetime,
                 CONVERT_TZ(c.cntc_mode_executiondate,'+00:00','+08:00') AS cs_completed_datetime,
                 NOW() AS curr_time,
                 (CASE WHEN (c.cntc_mode_sla_startdate IS NOT NULL) THEN TIMESTAMPDIFF(SECOND,CONVERT_TZ(c.cntc_mode_sla_startdate,'+00:00','+08:00'),CONVERT_TZ(c.cntc_mode_sla_duedate,'+00:00','+08:00')) END)   AS cs_available_duration,
                 (CASE WHEN (c.cntc_mode_sla_startdate IS NOT NULL) THEN TIMESTAMPDIFF(SECOND,CONVERT_TZ(c.cntc_mode_sla_startdate,'+00:00','+08:00'),CONVERT_TZ(c.cntc_mode_executiondate,'+00:00','+08:00')) END) AS cs_actual_duration,
                 (CASE WHEN (c.cntc_mode_executiondate IS NOT NULL) THEN TIMESTAMPDIFF(SECOND,CONVERT_TZ(c.cntc_mode_executiondate,'+00:00','+08:00'),NOW()) END) AS duration  
                FROM (cases c
                JOIN cases_cstm cc ON (c.id = cc.id_c)
                LEFT JOIN cstm_list_app cmode ON (cc.contact_mode_c = cmode.value_code)
                LEFT JOIN users u ON (u.`id`= c.`pickupby_id`)
                LEFT JOIN users us ON (us.`id`= c.`modified_user_id`))
                WHERE( (STR_TO_DATE(CONVERT_TZ(`c`.`date_entered`,'+00:00','+08:00'),'%Y-%m-%d') = ?)
                AND ( cmode.`value_name` IN ('Open Portal','Email'))    
                AND (c.cntc_mode_executiondate IS NOT NULL)    
                AND (c.`status` = 'Open_Pending Input') 
                AND (cc.`request_type_c` = '')
                AND (cc.`case_info_c` = '')  
                AND (`c`.`deleted` = 0))
                ORDER BY cs_completed_datetime ASC";
     
            $resultTask = DB::connection('mysql_crm')->select($result, array($processDate));
            $total = count($resultTask);
            if ($total > 0) {
                self::UpdateActualSLAEnd($resultTask);
            }
        } while (count($resultTask) == $take);
    }

    private static function UpdateActualSLAEnd($data) {

        Log::info(self::class . '(Scheduler -> NULL SLA Stop) Total Case to be update : ' . count($data));
        foreach ($data as $row) {

            $now = Carbon::now();
            $date_sla_stop = new Carbon($row->cs_completed_datetime);
            $dateStopSLA = $date_sla_stop;
            $diffInMinutes = $now->diffInMinutes($dateStopSLA);

            // update null after 2 hour scheduler stop the SLA for case pending response
            if ($diffInMinutes >= 120) {
                
                Log::info(self::class . ' Case Number to be update : ' . $row->case_number);
                
                $nullvalue = null;
                
                DB::table('cases')
                            ->where('id', $row->case_id)
                            ->update(['cntc_mode_executiondate' => $nullvalue]);     
            }
        }
    }
}
