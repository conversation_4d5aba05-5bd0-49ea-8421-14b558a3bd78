<?php

namespace App\Migrate\Crm;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Models\TaskCustom;
use App\Migrate\MigrateUtils;

class AutoAck_Severity { // Auto Acknowledge SLA Severity

    public static function runAutoAck_Severity($dateStart = null, $dateEnd = null) {

        Log::debug(self::class . ' Starting ... runAutoAck_Severity ', ['Query Start Date' => $dateStart, 'Query End Date' => $dateEnd]);
        $dtStartTime = Carbon::now();

        self::checkAutoAckSeverityTask($dateStart, $dateEnd);

        Log::info(self::class . ' Completed runAutoAck_Severity --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function checkAutoAckSeverityTask($dateStart, $dateEnd) {

        Log::info(self::class . ' Start Check SLA for Severity Task' . __FUNCTION__ . '     ->> Date Start: ' . $dateStart . ', Date End: ' . $dateEnd);
        var_dump('Date Start: ' . $dateStart);
        var_dump('Date End: ' . $dateEnd);

        $take = 1000;
        $skip = 1000;
        $countTask = 0;

        $dtStartTime = Carbon::now();

        //check for severity tasks 
        do {
            $taskStatus = DB::table('cases as a')
                    ->join('cases_cstm as b', 'b.id_c', '=', 'a.id')
                    ->join('tasks as c', 'c.parent_id', '=', 'a.id')
                    ->join('tasks_cstm as d', 'd.id_c', '=', 'c.id')
                    ->where('c.status', 'Pending Acknowledgement')
                    ->where('c.task_severity', 'like', 's3')
                    ->where('b.request_type_c', 'incident')
                    ->where('a.deleted', 0)
                    ->where('c.deleted', 0)
                    ->whereNotIn('c.assigned_user_id', ['5dac7b92-18d0-4beb-b600-413957aa4c26'])
                    ->where('b.incident_service_type_c', 'incident_it')
                 //   ->whereIn ('a.case_number' , [2691172,2690836,2691156,2691160,2691167])
                    ->select('c.*', 'd.*', 'a.case_number as casenumber', 'a.id as caseId')
                    ->take($take)
                    ->skip($skip * $countTask++);

            $resultTask = $taskStatus->get();
            $total = count($resultTask);
            if ($total > 0) {
                self::checkTaskSeverity($resultTask);
            }
        } while (count($resultTask) == $take); 
        
        Log::info(self::class . ' AutoAck_Severity for Severity Task , Counter :' . $countTask . ' Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        }

    private static function checkTaskSeverity($data) {

        $counter = 0;
        Log::info(self::class . ' Task Count ' . count($data));
        foreach ($data as $row) {
            $count = $counter++;
            $now = Carbon::now();
            $currTime = Carbon::now()->subHour(8);
            sleep(3);
            $date_start = new Carbon($row->date_start);
            $dateStart = $date_start->addHour(8);
            $diff3minutes = $now->diffInMinutes($dateStart);

            $date_due = new Carbon($row->date_due);
            $datedue = $date_due->addHour(8);
            if ($diff3minutes >= 120) {
                if ($now > $datedue) {
//                    $minSlaStart = strtotime($date_start);
//                    $maxSlaStart = strtotime($date_due);
//                    $slaVal = mt_rand($minSlaStart, $maxSlaStart);
//                    $random = date('Y-m-d H:i:s', $slaVal);
//                    $slaRandom = new Carbon($random);
//                    $slaRandomDate = $slaRandom->subHour(8);
                    Log::info( self::class . ' Time Now > than Due Time...Pending already..... Need to update status, acknowledge time and sla start');
                    Log::info(' Case Number >> '.$row->casenumber);
                    Log::info(' SLA Start >> '.$dateStart);
                    Log::info(' SLA Stop >> '.$date_due);
                    Log::info(' Acknowledge by Batch >> '.$currTime);                          
                    self::AcknowledgeTaskSeverity($row, $count, $currTime);
                } else {
//                    $minSlaStart = strtotime($date_start);
//                    $maxSlaStart = strtotime($now);
//                    $slaVal = mt_rand($minSlaStart, $maxSlaStart);
//                    $random = date('Y-m-d H:i:s', $slaVal);
//                    $slaRandom = new Carbon($random);
//                    $slaRandomDate = $slaRandom->subHour(8);
                    Log::info(self::class . ' Time Now < than Due Time...Task created after 2 hour..... Need to update status, acknowledge time and sla start');
                    Log::info(' Case Number >> '.$row->casenumber);
                    Log::info(' SLA Start >> '.$dateStart);
                    Log::info(' SLA Stop >> '.$date_due);
                    Log::info(' Acknowledge by Batch >> '.$currTime);
                    self::AcknowledgeTaskSeverity($row, $count, $currTime);
                }
            } 
        }
    }
    
    private static function AcknowledgeTaskSeverity($row, $count, $randomDate) {

        Log::info(self::class . ' Entering AcknowledgeTaskSeverity()..... ' . $row->casenumber);
        
        $updateCaseDetails = [
            'date_modified' => $randomDate
        ];

        $updateTasksDetails = [
            'status' => 'Acknowledge',
            'date_modified' => $randomDate
        ];
        
        $updateTasksCstmDetails = [
            'acknowledge_time_c' => $randomDate,
            'sla_start_4hr_c' => $randomDate,
            'sla_task_flag_c' => 's3'
        ];
        
        DB::table('cases')
                ->where('id', $row->caseId)
                ->update($updateCaseDetails);

        DB::table('tasks')
                ->where('id', $row->id)
                ->update($updateTasksDetails);
        
         DB::table('tasks_cstm')
                ->where('id_c', $row->id)
                ->update($updateTasksCstmDetails);
         
        self::copySlaStop($row, $count);
        
        
    }

    private static function copySlaStop($data, $count) {
        $taskCstm = TaskCustom::find($data->id);
        if ($taskCstm->sla_start_4hr_c != $taskCstm->acknowledge_time_c) {
            DB::table('tasks_cstm')
                    ->where('id_c', $taskCstm->id_c)
                    ->update(['acknowledge_time_c' => $taskCstm->sla_start_4hr_c]);
            Log::info(self::class . ' Success , Counter :' . $count . ' Task Id :' . $taskCstm->id_c);
        }
    }
}
