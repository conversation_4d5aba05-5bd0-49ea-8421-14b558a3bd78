<?php

namespace App\Migrate\Crm;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use App\Models\Tasks;
use App\Migrate\MigrateUtils;
use App\Models\TaskCustom;

class AutoApprovalS4 { 
 
    public static function run() {
        Log::debug(self::class . ' Starting ... run ');
        $dtStartTime = Carbon::now();

        self::queryApprovalS4();

        Log::info(self::class . ' Completed runAutoAck_Severity --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
   
    }

    public static function queryApprovalS4 (){
        Log::info(self::class . ' Find Pending Approver Task With Implementation Issue No > ' . __FUNCTION__);

        $take = 1000;
        $skip = 1000;
        $count = 0;

        $dtStartTime = Carbon::now();
 
        do {
            $query = DB::table('cases as c')
                    ->join('cases_cstm as cc', 'cc.id_c', '=', 'c.id')
                    ->join('tasks as t', 't.parent_id', '=', 'c.id')
                    ->join('tasks_cstm as tc', 'tc.id_c', '=', 't.id')
                    ->where('c.state', 'open')
                    ->where('c.status', 'Open_Assigned')
                    ->where('cc.request_type_c', 'incident')
                    ->where('cc.incident_service_type_c','incident_it')
                    ->where('c.deleted', 0)
                    ->where('t.deleted', 0)
                    ->whereIn('t.status', ['Pending Acknowledgement','Acknowledge'])
                    ->where('t.name','Assigned to Approver')
                    ->where('t.implementation_issue','no')
                    ->whereNotNull('t.complexity')
                    ->select('t.*', 'tc.*', 'c.case_number','c.id as case_id')
                    ->take($take)
                    ->skip($skip * $count++);

            $result= $query->get(); 
            $total = count($result); 
            if ($total > 0) { 
                self::checkData($result);
            }
        } while (count($result) == $take); 
        
        Log::info(self::class . ' Counter :' . $count . ' Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        
    }

    public static function checkData($data) {

        $counter = 0;
        Log::info(self::class . ' Task Count ' . count($data));
        foreach ($data as $row) {
            $count = $counter++;
            $now = Carbon::now(); 
            $date_start = new Carbon($row->date_entered);
            $dateStart = $date_start->addHour(8);
            $diffInDays = $now->diffInDays($dateStart);
            $durationDays = $row->task_duration_c;

            Log::info(self::class . ' Auto Approve Case ' . $row->case_number .' Diff in Days :' .$diffInDays);
            if ($diffInDays >= '1') {

                //auto approve task
                DB::table('tasks')
                ->where('id', $row->id)
                ->update([  
                    'status' => 'Completed',
                    'date_modified' => (new Carbon($row->date_entered))->addDays(1), 
                ]); 
                DB::table('tasks_cstm')
                ->where('id_c', $row->id)
                ->update([
                    'sla_stop_approver_c' => (new Carbon($row->date_entered))->addDays(1), 
                    'acknowledge_time_c' => (new Carbon($row->date_entered))->addDays(1), 
                    'acknowledge_by_userid_c' => $row->modified_user_id,
                    'resolution_c' => $row->resolution_c
                ]); 

                //create PMO task
                $newTask = new Tasks();
                $newTask->id = Str::uuid()->toString();
                $newTask->name = 'Assigned to Group PMO';
                $newTask->date_entered = (new Carbon($row->date_entered))->addDays(1);
                $newTask->date_modified = (new Carbon($row->date_entered))->addDays(1);
                $newTask->deleted = 0;
                $newTask->modified_user_id = $row->modified_user_id;
                $newTask->created_by = $row->modified_user_id;
                $newTask->description = $row->description;
                $newTask->assigned_user_id = '5dac7b92-18d0-4beb-b600-413957aa4c26'; //pmo
                $newTask->status = 'Pending Acknowledgement';
                $newTask->date_due = (new Carbon($row->date_entered))->addDays(1)->addDays($durationDays);
                $newTask->date_start = (new Carbon($row->date_entered))->addDays(1);
                $newTask->parent_type = 'Cases';
                $newTask->parent_id = $row->parent_id;
                $newTask->priority = $row->priority;
                $newTask->case_redmine_number = $row->case_redmine_number;
                $newTask->task_justification = $row->task_justification;
                $newTask->resolution_category_c = $row->resolution_category_c; 
                $newTask->redmine_solution = $row->redmine_solution;
                $newTask->contact_id = $row->contact_id;
                $newTask->implementation_issue = $row->implementation_issue;
                $newTask->child_parent_redmine = $row->child_parent_redmine;
                $newTask->incident_classification = $row->incident_classification;
                $newTask->complexity = $row->complexity;
                $newTask->save();

                $newTaskCustom = new TaskCustom();
                $newTaskCustom->id_c = $newTask->id;
                $newTaskCustom->assign_group_c = 'Group PMO';
                $newTaskCustom->resolution_c = $row->resolution_c;
                $newTaskCustom->sla_flag_c = 0;
                $newTaskCustom->checkbox_add_day_c = 0;
                $newTaskCustom->category_factor_c = $row->category_factor_c;  
                $newTaskCustom->sla_task_flag_c = '4';
                $newTaskCustom->task_duration_c = $row->task_duration_c;
                $newTaskCustom->tasks_combine_c = $row->tasks_combine_c;
                $newTaskCustom->save();
            }
        }
    }
}
