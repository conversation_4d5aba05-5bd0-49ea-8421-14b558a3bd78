<?php

namespace App\Migrate\Crm;

use Carbon\Carbon;
use App\Migrate\MigrateUtils;
use App\Services\CRMService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class AutoFillCategoryEaduan
{

    public static function crmService()
    {
        return new CRMService;
    }

    public static function runUpdateCaseCatgories()
    {
        Log::debug(self::class . ' Starting ... runUpdateCaseCatgories');
        $dtStartTime = Carbon::now();

        try {
            self::checkCaseStatus();
        } catch (Exception $e) {
            Log::error(self::class . ' Error runUpdateCaseCatgories :', ['Error' => $e->getMessage()]);
        }

        Log::info(self::class . ' Completed runUpdateCaseCatgories --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        var_dump(self::class . ' Completed runUpdateCaseCatgories --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function checkCaseStatus()
    {
        $cases = DB::table('cases')
            ->select([
                'id',
                'case_number',
                'request_type_c',
                'category_c',
                'sub_category_c',
                'sub_category_2_c',
                'state',
                'status',
                'date_entered',
                'date_modified',
            ])
            ->join('cases_cstm', 'cases.id', '=', 'cases_cstm.id_c')
            ->where('cases.status', '=', 'Closed_Cancelled_Eaduan')
            ->where('cases.deleted', '=', 0)
            ->where(function ($query) {
                $query->whereNull('cases_cstm.request_type_c')
                    ->orWhere('cases_cstm.request_type_c', '=', '')
                    ->whereNull('cases_cstm.category_c')
                    ->orWhere('cases_cstm.category_c', '=', '')
                    ->whereNull('cases_cstm.sub_category_c')
                    ->orWhere('cases_cstm.sub_category_c', '=', '')
                    ->whereNull('cases_cstm.sub_category_2_c')
                    ->orWhere('cases_cstm.sub_category_2_c', '=', '');
            })
            ->orderBy('cases.date_entered', 'desc')
            ->get();

        Log::info(self::class . ' Total Cases to be updated : ' . count($cases));
        Log::info(self::class . ' List of Case Number : ' . implode(', ', $cases->pluck('case_number')->toArray()));

        // dd($cases);

        foreach ($cases as $case) {
            try {
                var_dump(' Case Number : ' . $case->case_number);
                self::updateCase($case);
            } catch (Exception $e) {
                Log::error(self::class . ' Error updateCase :', ['CaseNumber' => $case->case_number, 'Error' => $e->getMessage()]);
            }
        }
    }

    private static function updateCase($case)
    {
        DB::connection('mysql')
            ->table('cases_cstm')
            ->where('id_c', $case->id)
            ->update([
                'category_c' => '10714',
                'sub_category_c' => '10714_15834',
                'sub_category_2_c' => '10714_15834_16942',
                'request_type_c' => 'enquiry',
            ]);

        DB::connection('mysql')
            ->table('cases')
            ->where('id', $case->id)
            ->update([
                'date_modified' => Carbon::now('UTC'),
            ]);
    }
}
