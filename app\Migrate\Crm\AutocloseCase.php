<?php

namespace App\Migrate\Crm;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Migrate\MigrateUtils;
use App\Services\CRMService;
use Illuminate\Support\Facades\Mail;

/*
 * This will update PTJ as Active base don Excel file. IF not found in excel, data will set inactive
 * PTJ Information
 * 
 * If data in eP not exist in CRM, System will create to CRM.
 * 
 */

class AutocloseCase {

    public static function crmService() {
        return new CRMService;
    }

    public static function runCloseCase() {
        Log::debug(self::class . ' Starting ... runCloseCase');
        $dtStartTime = Carbon::now();

        self::checkCaseStatus();

        Log::info(self::class . ' Completed runCloseCase --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        var_dump(self::class . ' Completed runCloseCase --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function checkCaseStatus() {

        $dtStartTime = Carbon::now();

        $case3Days = DB::select("SELECT 
            *
            FROM cases c
            JOIN cases_cstm cc ON cc.id_c = c.id
            WHERE DATE_ADD(DATE(c.`date_modified`), INTERVAL 3 DAY) = CURDATE()
            AND c.status = 'Pending_User_Verification'
            AND c.`state` = 'Open'");

        $case7Days = DB::select("SELECT 
            *
            FROM cases c
            JOIN cases_cstm cc ON cc.id_c = c.id
            WHERE DATE(c.`date_modified`) < NOW() - INTERVAL 1 WEEK
            AND c.status = 'Pending_User_Verification'
            AND c.`state` = 'Open'");

        if (count($case3Days) > 0) {
            $count3days = 0;
            self::checkCase3Days($case3Days, $count3days);
            Log::info(self::class . ' Count 3 days : ' . count($case3Days) . ' , Taken Time Case 3 Days :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
//            var_dump(self::class . ' Count 3 days : ' . count($case3Days) . ' , Taken Time Case 3 Days :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        }

        if (count($case7Days) > 0) {
            $count7days = 0;
            self::checkCase7Days($case7Days, $count7days);
            Log::info(self::class . ' Count 7 days : ' . count($case7Days) . ' , Taken Time Case 7 Days :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
//            var_dump(self::class . ' Count 7 days : ' . count($case7Days) . ' , Taken Time Case 7 Days :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        }
    }

    private static function checkCase3Days($case3Days, $count3days) {

        foreach ($case3Days as $obj) {
            $count3days++;
            $emailAddress = self::crmService()->getEmailDetail('Contacts', $obj->contact_id_c);

            if ($emailAddress && $emailAddress != '') {
                $dataCase = collect([]);
                $dataCase->put("case_number", $obj->case_number);
                $dataCase->put("case_subject", $obj->name);
                $dataCase->put("email_address", $emailAddress->email_address);

                if (!filter_var($emailAddress->email_address, FILTER_VALIDATE_EMAIL) == false) {
                    self::sendEmail($dataCase);
                    Log::info(self::class . 'Case Pending User Verification 3 days already.. Send Email Reminder > COUNTER ' . $count3days . ' Case Number : ' . $obj->case_number . ' Send Email to : ' . $emailAddress->email_address);
//                var_dump(self::class . 'Case Pending User Verification 3 days already.. Send Email Reminder > COUNTER ' . $count3days . ' Case Number : ' . $obj->case_number . ' Send Email to : ' . $emailAddress->email_address);
                } else {
                    Log::info(self::class . 'Case Pending User Verification 3 days already.. Invalid email > COUNTER ' . $count3days . ' Case Number : ' . $obj->case_number . ' Invalid Email : ' . $emailAddress->email_address);
//                var_dump(self::class . 'Case Pending User Verification 3 days already.. Invalid email > COUNTER ' . $count3days . ' Case Number : ' . $obj->case_number .  ' Invalid Email : ' . $emailAddress->email_address);
                }
            } else {
                Log::info(self::class . 'No email ' . $obj->case_number);
            }
        }
    }

    private static function checkCase7Days($case7Days, $count7days) {

        foreach ($case7Days as $obj) {

            $count7days++;
            $emailContact = '';
            $emailUser = '';
            $account = '';
            $accountName = '';
            $createdBy = $obj->created_by;

            if ($obj->account_id != '') {
                $accountName = self::crmService()->getDetailAccountCRM($obj->account_id);
                $account = $accountName->name;

                $emailContacts = self::crmService()->getEmailDetail('Contacts', $obj->contact_id_c);
                if ($emailContacts) {
                    $emailContact = $emailContacts->email_address;
                }

                $emailUser = self::crmService()->getEmailDetail('Users', $createdBy);
            }
            if ($account == null) {
                $lead = self::crmService()->getDetailLeadCRM($obj->id);
                if ($lead) {
                    $account = $lead->first_name . $lead->last_name;

                    $emailLeads = self::crmService()->getEmailDetail('Leads', $lead->leads_cases_1leads_ida);
                    if ($emailLeads) {
                        $emailContact = $emailLeads->email_address;
                    }

                    $emailUser = self::crmService()->getEmailDetail('Users', $createdBy);
                }
            }


            $task = self::crmService()->getDetailTaskLatestCRM($obj->id);
            $category = self::crmService()->getValueLookupCRM('category_list', $obj->category_c);
            $subCategory = self::crmService()->getValueLookupCRM('cdc_sub_category_list', $obj->sub_category_c);
            $subSubCategory = self::crmService()->getValueLookupCRM('cdc_sub_category_2_list', $obj->sub_category_2_c);
            $requestedBy = self::crmService()->getNameContactCRM($obj->contact_id_c);
            $statusCase = self::crmService()->getValueLookupCRM('case_status_dom', $obj->status);

            //update case state to close
                DB::table('cases')
                        ->where('id', $obj->id)
                        ->update(['status' => 'Closed_Closed', 'state' => 'Closed', 'date_modified' => Carbon::now()->subHour(8)]);

                //update task to complete
                DB::table('tasks')
                        ->where('id', $task->id)
                        ->update(['status' => 'Completed']);
                
            if ($emailContact != '' && $emailUser) {
                $data = collect([]);
                $data->put("case_number", $obj->case_number);
                $data->put("case_subject", trim($obj->name));
                $data->put("email_address", $emailContact);
                $data->put("case_logged_date", $obj->date_entered);
                $data->put("case_category", trim($category));
                $data->put("case_sub_category", trim($subCategory));
                $data->put("case_sub_sub_category", trim($subSubCategory));
                $data->put("contact", $requestedBy);
                $data->put("account", $account);
                $data->put("description", trim($obj->description));
                $data->put("resolution", trim($obj->resolution));
                $data->put("case_status", $statusCase);
                $data->put("email_user", $emailUser->email_address);

                if (!filter_var($emailContact, FILTER_VALIDATE_EMAIL) == false || !filter_var($emailUser->email_address, FILTER_VALIDATE_EMAIL)) {
                    self::sendEmailContact($data);
                    if($createdBy != '96fa954b-8288-4bef-ac39-5338a5b5c9c0'){ //self portal
//                        self::sendEmailUser($data);
                    }
                    
                    Log::info('Case exceed 7 days > COUNTER ' . $count7days . ' Case Number : ' . $obj->case_number . ' email User ' . $data->get('email_user'));
                } else {
                    Log::info('Case exceed 7 days > COUNTER ' . $count7days . ' Case Number : ' . $obj->case_number . ' email User ' . $data->get('email_user'));
                }
            } else {
                Log::info("Email Not Exist : " . $obj->case_number);
            }
        }
    }

    protected static function sendEmail($dataCase) {

        $subject = 'eBantuan ePerolehan';
        try {
            Mail::send('emails.reminder', [
                'subject' => $subject,
                'date' => Carbon::now()->toDateString(),
                'data' => $dataCase
                    ], function($m) use ($dataCase, $subject) {
                $m->from('<EMAIL>', 'eAduan');
                $m->to($dataCase->get('email_address'))->subject($subject);
//                $m->to("<EMAIL>")->subject($subject);
            });
            Log::info('Success send email to : ' . $dataCase->get('email_address'));
        } catch (\Exception $e) {
            Log::error('Console :: sendEmail ', ['Email' => $dataCase->get('email_address'), 'ERROR' => $e->getMessage()]);
//            var_dump('  failed  sendEmail : ' . $dataCase->get('email_address'));
            return $e;
        }
    }

    protected static function sendEmailContact($data) {

        $subject = 'eBantuan ePerolehan : Kes Selesai';
        try {
            Mail::send('emails.emailContact', [
                'subject' => $subject,
                'date' => Carbon::now()->toDateString(),
                'data' => $data
                    ], function($m) use ($data, $subject) {
                $m->from('<EMAIL>', 'eAduan');
//                $m->to("<EMAIL>")->subject($subject);
                $m->to($data->get('email_address'))->subject($subject);
            });
            Log::info('Success send email contact to : ' . $data->get('email_address'));
        } catch (\Exception $e) {
            Log::error('Console :: sendEmailContact ', ['Email' => $data->get('email_address'), 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }

    protected static function sendEmailUser($dataUser) {

        $subject = '[Case Status] - Closed_Closed : ' . $dataUser->get('case_number');
        try {
            Mail::send('emails.emailUser', [
                'subject' => $subject,
                'date' => Carbon::now()->toDateString(),
                'data' => $dataUser
                    ], function($m) use ($dataUser, $subject) {
                $m->from('<EMAIL>', 'eAduan');
//                $m->to("<EMAIL>")->subject($subject);
                $m->to($dataUser->get('email_user'))->subject($subject);
            });
            Log::info('Success send email user to : ' . $dataUser->get('email_user'));
        } catch (\Exception $e) {
            Log::error('Console :: sendEmailUser ', ['Email' => $dataUser->get('email_user'), 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }

}
