<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\Crm;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Config;
use App\Services\CRMService;
use Intervention\Image\Facades\Image;
use App\Migrate\MigrateUtils;

class BlastEmail {

    public static function crmService() {
        return new CRMService;
    }

    public static function blast($dateStart = null, $dateEnd = null) {

        Log::debug(self::class . ' Starting ... Blast Email ', ['Query Start Date' => $dateStart, 'Query End Date' => $dateEnd]);
        $dtStartTime = Carbon::now();

        $data = self::checkCaseClose($dateStart, $dateEnd);
        $countData = count($data);

        if ($countData > 0) {

            $account = "";
            $contact = "";
            $emailContact = "";
            $statusCase = "";
            $dateCreated = "";
            $dateModified = "";
            $modifiedBy = "";

            foreach ($data as $value) {
                //account
                if ($value->account_id != '') {
                    $account = self::crmService()->getDetailAccountCRM($value->account_id);
                }

                if ($account == null) {
                    $lead = self::crmService()->getDetailLeadCRM($value->id);
                    if ($lead) {
                        $emailContact = self::crmService()->getEmailCRM("Leads", $lead->id);
                    }
                }

                $contact = self::crmService()->getDetailContactCRM($value->contact_id_c);
                if ($contact) {
                    $emailContact = self::crmService()->getEmailCRM("Contacts", $value->contact_id_c);
                }


                if ($value->status != '') {
                    $statusCase = self::crmService()->getValueLookupCRM('case_status_dom', $value->status);
                }

                $dateCreated = Carbon::parse($value->created_date);
                $dateModified = Carbon::parse($value->modified_date);
                $emailAdd = preg_replace('/[,]+/', ' ', trim($emailContact));

                if ($value->modified_user_id != '') {
                    $modifiedBy = self::crmService()->getNameUserCRM($value->modified_user_id);
                }

                dump(' Case No: ' . $value->case_number . ' State: ' . $statusCase . ' Date Created: ' . $dateCreated .
                        ' Date Modified: ' . $dateModified . ' Modified By: ' . $modifiedBy . ' Email: ' . $emailAdd);
//                self::blastEmail($emailAdd);
            }
        }
        dump('count ' . count($data));

        Log::info(self::class . ' Completed Blast Email --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        var_dump(self::class . ' Completed Blast Email --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    public static function checkCaseClose($dtStartTime, $dtEndTime) {

        dump('date start ' . $dtStartTime . ' date end ' . $dtEndTime);
        $closedStatus = array('Closed_Closed',
            'Closed_Verified_Eaduan',
            'Closed_Cancelled_Eaduan',
            'Closed_Rejected_Eaduan',
            'Closed_Rejected',
            'Closed_Duplicate',
            'Closed');

        $sql = DB::table('cases')
                ->join('cases_cstm', 'cases.id', '=', 'cases_cstm.id_c')
                ->where('cases.deleted', 0)
                ->whereIn('cases.status', $closedStatus)
                ->whereBetween('cases.date_modified', [$dtStartTime,
            $dtEndTime]);


        $sql->select('cases.*', 'cases_cstm.*');
        $sql->addSelect(DB::raw("CONVERT_TZ(cases.date_entered,'+00:00','+08:00') AS created_date"));
        $sql->addSelect(DB::raw("CONVERT_TZ(cases.date_modified,'+00:00','+08:00') AS modified_date"));

        $data = array(
            "sql" => $sql->toSql(),
            "parameter" => $sql->getBindings()
        );

//        dump(self::class . ' :: getQuery >> SQL   :   ', [$data]);
        return $result = $sql->get();
    }

    public static function blastEmail($emailAdd) {

        $what = "WHAT    |   ePerolehan Briefing";
        $when = "WHEN    |   20 March 2020";
        $whenTime = "            9 am - 5 pm";
        $where = "WHERE   |   MoF";
        $whereAt = "            Dewan Utama";
        $white = "FFFFFF";
        $yellow = "FFFF00";

        $right = 360;
        $rightTable = 220;
        $top = 440;
        $img = Image::make('public/img/blast.png');

        $img->text('ePerolehan cordially invites you to', $right, $top, function($font) use($yellow) {
            $font->file(5);
//            $font->size(12);
            $font->color($yellow);
            $font->align('center');
            $font->valign('bottom');
            $font->angle(0);
        });
        $img->text('our ePerolehan Briefing', $right, $top + 20, function($font) use($yellow) {
            $font->file(5);
//            $font->size(12);
            $font->color($yellow);
            $font->align('center');
            $font->valign('bottom');
            $font->angle(0);
        });
        $img->text('Come learn all about the excitement and growth', $right, $top + 70, function($font) use($white) {
            $font->file(3);
//            $font->size(12);
            $font->color($white);
            $font->align('center');
            $font->valign('bottom');
            $font->angle(0);
        });
        $img->text('within the ePerolehan community', $right, $top + 90, function($font) use($white) {
            $font->file(3);
//            $font->size(12);
            $font->color($white);
            $font->align('center');
            $font->valign('bottom');
            $font->angle(0);
        });
        $img->text($what, $rightTable, $top + 160, function($font) use($white) {
            $font->file(5);
//            $font->size(12);
            $font->color($white);
            $font->align('left');
            $font->valign('bottom');
            $font->angle(0);
        });
        $img->text($when, $rightTable, $top + 200, function($font) use($white) {
            $font->file(5);
//            $font->size(12);
            $font->color($white);
            $font->align('left');
            $font->valign('bottom');
            $font->angle(0);
        });
        $img->text($whenTime, $rightTable + 38, $top + 220, function($font) use($white) {
            $font->file(3);
//            $font->size(12);
            $font->color($white);
            $font->align('left');
            $font->valign('bottom');
            $font->angle(0);
        });
        $img->text($where, $rightTable, $top + 260, function($font) use($white) {
            $font->file(5);
//            $font->size(12);
            $font->color($white);
            $font->align('left');
            $font->valign('bottom');
            $font->angle(0);
        });
        $img->text($whereAt, $rightTable + 38, $top + 280, function($font) use($white) {
            $font->file(3);
//            $font->size(12);
            $font->color($white);
            $font->align('left');
            $font->valign('bottom');
            $font->angle(0);
        });

        $file_path = storage_path() . '/app/public/blast.png';
        $img->save($file_path);

        $data = array(
            "to" => [$emailAdd],
            "subject" => 'Server (' . env('APP_ENV') . ') > ePerolehan Briefing ',
            "att" => $file_path
        );
        $dataArray['subject'] = $data["subject"];

        try {
            Mail::send('emails.blast', $dataArray, function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])->subject($data["subject"]);
            });
            dump('done send');
        } catch (\Exception $e) {
            echo $e;
            Log::error(self::class . ' Error ... ' . __FUNCTION__ . ' ::  ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            dump('error' . $e);
            return $e;
        }
    }

}
