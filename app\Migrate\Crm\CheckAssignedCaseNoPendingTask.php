<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\Crm;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Migrate\MigrateUtils;
use App\Services\CRMService;

class CheckAssignedCaseNoPendingTask {

    public static function crmService() {
        return new CRMService;
    }

    public static function run() {

        Log::debug(self::class . ' > ' . __FUNCTION__ . ' > Starting.... ');
        $dtStartTime = Carbon::now(); 
        
        self::checkAssignedCaseWithNoPendingTask();
        dump(self::class . ' > ' . __FUNCTION__ . ' Completed.Taken Time : ', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info(self::class . ' > ' . __FUNCTION__ . ' Completed.Taken Time : ', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }
    /*
     * Bug scenario: Case status Assigned but no open task (Status Pending Acknowledgement/Acknowledge)
     */
    private static function checkAssignedCaseWithNoPendingTask() {
        $dtStartTime = Carbon::now();

        $query = DB::connection('mysql_crm')->select(
            "SELECT c.case_number,c.id FROM cases c, cases_cstm cc, tasks t 
                WHERE c.id = t.parent_id 
                AND cc.id_c = c.id
                AND c.status IN ('Open_Assigned','Open_Resolved')
                AND cc.request_type_c IN ('incident','service')

                AND NOT EXISTS (

                SELECT tasks.id FROM tasks,cases
                WHERE cases.id = tasks.parent_id AND tasks.status IN ('Pending Acknowledgement','Acknowledge') AND cases.id = c.id)
                GROUP BY c.case_number,c.id "
        );

        $total = count($query);
        Log::info(self::class . ' > ' . __FUNCTION__ . ' > Total Case Found : ' . $total);
        $msg = "*CASE ASSIGNED WITH NO PENDING TASK!!*";
        $totalCount = 0;
        if ($total > 0) {
            foreach ($query as $row) {
                $latestTask = DB::table('tasks')->where('parent_id',$row->id)
                        ->where('deleted',0)->orderBy('date_entered','desc')->first();
                        if($latestTask){
                            $userId = DB::table('users')->where('id',$latestTask->modified_user_id)->first();
                            if($userId){ 
                                $taskModified = Carbon::parse($latestTask->date_modified);
                                $current = Carbon::now();
                                $diff = $current->diffInDays($taskModified);
                                // Log::info($diff);
                                    
                                if($latestTask && $userId && $diff > 1) {
                                    $totalCount = $totalCount + $total;
                                $msg = $msg. "\n \n".
"Case Number: *$row->case_number* \n"
. "Please advice user to reassign task *" .$latestTask->name ."*. \n"
. "Task completed on " .Carbon::parse($latestTask->date_modified)->addHour(8)->format("Y-m-d H:i:s A") .' by *' .$userId->first_name .' ' .$userId->last_name .'*';
                                
                                }
                            }
                        }
                
            }
        }
        if($totalCount > 0){
            self::notifyWhatsapp($msg);
        }
        dump(self::class . ' > ' . __FUNCTION__ . ' > Total : ' . $total . ' Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info(self::class . ' > ' . __FUNCTION__ . ' > Total : ' . $total . ' Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    
    }
    
    private static function notifyWhatsapp($msg) {
        DB::connection('mysql_ep_notify')
                ->insert('insert into notification  
                    (message,receiver_group,process,status,sent,date_entered,retry,source_app,source_class,source_remark) 
                    values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', [
                    $msg,
                    'CRM_EP_OPEN_CASE_NO_PENDING_TASK',
                    'notify', 0, 0,
                    Carbon::now(), 0,
                    'CrmIntegration',
                    __CLASS__,
                    'This alert to reassign completed task'
        ]);
    }
}


