<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\Crm;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Migrate\MigrateUtils;
use Ramsey\Uuid\Uuid;
use App\Models\Cases;
use App\Models\CasesCustom;
use App\Models\Tasks;
use App\Models\TaskCustom;
use Config;
use App\Services\CRMService;

class CheckBugsCase {

    public static function crmService() {
        return new CRMService;
    }

    public static function checkBugs() {

        Log::debug(self::class . ' > ' . __FUNCTION__ . ' > Starting.... ');
        $dtStartTime = Carbon::now();

        self::checkCaseStatus();
        self::checkCaseSla();
        self::checkCaseOnsite();
        self::checkCaseResolveWithTaskCompleted();
        self::checkDuplicateCaseOwnerTask();
        self::checkDuplicateOnsiteTask();
        self::checkIncidentCaseWithStatusAssigned();
        self::checkCaseClosedWithOpenTask();
        self::checkEmptyCases();
        
        Log::info(self::class . ' > ' . __FUNCTION__ . ' Completed.Taken Time : ', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function checkCaseStatus() {

        $take = 1000;
        $skip = 1000;
        $countCase = 0;
        $onlineOnsite = array(10722, 10721, 10720, 10719);
        $itType = array('service_it', 'incident_it');
        $dtStartTime = Carbon::now();

        $caseStatus = DB::table('cases as c')
                ->join('cases_cstm as cc', 'cc.id_c', '=', 'c.id')
                ->join('tasks as t', 't.parent_id', '=', 'c.id')
                ->join('tasks_cstm as tc', 'tc.id_c', '=', 't.id')
                ->whereIn('c.status', ['Open_New', 'Open_Pending Input'])
                ->where('c.deleted', 0)
                ->where('t.deleted', 0)
                ->select('c.*', 'cc.*', 't.name as taskname', 'c.status as casestatus', 'c.id as caseid')
                ->orderBy('tc.task_number_c', 'desc')
                ->take($take)
                ->skip($skip * $countCase++);

        $resultCase = $caseStatus->get();
        $total = count($resultCase);
        Log::info(self::class . ' > ' . __FUNCTION__ . ' > Total Case Found : ' . $total);
        if ($total > 0) {
            foreach ($resultCase as $row) {
                //check for online/onsite case with wrong status name 
                if ($row->incident_service_type_c === 'service_business' && $row->request_type_c === 'service' && in_array($row->category_c, $onlineOnsite) && $row->taskname === 'Pending Approval - Onsite Support') {
                    Log::info(self::class . ' > ' . __FUNCTION__ . ' > Invalid Status For Online/Onsite Case : ' . $row->case_number . ' > Status : ' . $row->casestatus);
                    $updateCase = DB::table('cases')
                            ->where('id', $row->id)
                            ->update([
                        'status' => 'Open_Pending_Approval']);
                    Log::info(self::class . ' > ' . __FUNCTION__ . ' > Invalid Status For Online/Onsite Case : ' . $row->case_number . ' > Success Update : ' . $updateCase);
                } else if (!in_array($row->category_c, $onlineOnsite)) {
                    $checkLatestTask = self::crmService()->getDetailTaskLatestCRM($row->caseid);
                    Log::info(self::class . ' > ' . __FUNCTION__ . ' > Invalid Status For Other Case : ' . $row->case_number . ' > Status : ' . $row->casestatus . ' > Latest Task : ' . $checkLatestTask->name . ' > Assign Group ' . $checkLatestTask->assign_group_c);
                    if ($checkLatestTask && $checkLatestTask->assign_group_c === 'Case Owner') {
                        if (in_array($row->incident_service_type_c, $itType) && $row->request_type_c !== 'enquiry' && $checkLatestTask->status === 'Pending Acknowledgement') {
                            $updateCase = DB::table('cases')
                                    ->where('id', $row->id)
                                    ->update(['status' => 'Open_Resolved']);
                        } else if (in_array($row->incident_service_type_c, $itType) && $row->request_type_c !== 'enquiry' && $checkLatestTask->status === 'Acknowledge') {
                            $updateCase = DB::table('cases')
                                    ->where('id', $row->id)
                                    ->update(['status' => 'Pending_User_Verification']);
                        } else if (!in_array($row->incident_service_type_c, $itType) && $row->request_type_c !== 'enquiry' && $checkLatestTask->status !== 'Completed') {
                            $updateCase = DB::table('cases')
                                    ->where('id', $row->id)
                                    ->update(['status' => 'Open_Assigned']);
                        } else if ($row->state === 'Closed') {
                            $updateCase = DB::table('cases')
                                    ->where('id', $row->id)
                                    ->update(['status' => 'Closed_Closed']);
                        }
                    } else {
                        $updateCase = DB::table('cases')
                                ->where('id', $row->id)
                                ->update(['status' => 'Open_Assigned']);
                    }
                }
            }
        }
        Log::info(self::class . ' > ' . __FUNCTION__ . ' > Total : ' . $total . ' Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function checkCaseSla() {
        $dtStartTime = Carbon::now();

        $query = DB::select("SELECT * FROM cases c, cases_cstm cc
                    WHERE c.`id` = cc.`id_c` AND c.`state` = 'Closed'
                    AND cc.`request_type_c` = 'incident' AND cc.`incident_service_type_c` = 'incident_it'
                    AND (c.`sla_flag` IS NULL OR c.`sla_flag` = '') 
                    AND YEAR(c.`date_entered`) = YEAR(NOW()) AND c.case_number NOT IN (4202508,4247236)");

        $total = count($query);
        Log::info(self::class . ' > ' . __FUNCTION__ . ' > Total Case Found : ' . $total);
        if ($total > 0) {
            foreach ($query as $row) {
                //check latest task with flag
                $taskLatest = self::crmService()->getDetailTaskLatestFlagCRM($row->id);
                if ($taskLatest) {
                    $taskLatestFlag = $taskLatest->sla_task_flag_c;
                    $updateCase = DB::table('cases')
                            ->where('id', $row->id)
                            ->update([
                        'sla_flag' => $taskLatestFlag]);
                    Log::info(self::class . ' > ' . __FUNCTION__ . ' > Update Case Flag For It Incident : ' . $row->case_number . ' > Latest Flag : ' . $taskLatestFlag . ' > Success Update : ' . $updateCase);
                }
            }
        }
        Log::info(self::class . ' > ' . __FUNCTION__ . ' > Total : ' . $total . ' Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function checkCaseOnsite() {
        $dtStartTime = Carbon::now();

        $query = DB::select("SELECT c.`id`, cc.`id_c`, c.case_number, c.`state`, c.`status`, cc.`category_c`, cc.`category_desc_c`,
                            cc.`sub_category_c`, cc.`sub_category_desc_c`, cc.`sub_category_2_c`, cc.`sub_category_2_desc_c`,
                            cc.`request_type_c`, cc.`incident_service_type_c`, c.date_modified, c.description, c.modified_user_id,
                            c.`date_modified` + INTERVAL 15 MINUTE as sla_due, c.resolution
                            FROM cases c, cases_cstm cc
                            WHERE c.`id` = cc.`id_c`
                            AND cc.`category_c` IN ('')
                            AND cc.`incident_service_type_c` = 'service_business'
                            AND c.`deleted` = 0");

        $total = count($query);
        Log::info(self::class . ' > ' . __FUNCTION__ . ' > Total Case Found : ' . $total);
        if ($total > 0) {
            foreach ($query as $row) {
                //update category_c
                $category = explode("_", $row->sub_category_c, 5);
                $categoryDesc = self::crmService()->getDetailLookupCRM('category_list', $category[0]);
                $updateCase = DB::table('cases')
                        ->where('id', $row->id)
                        ->update([
                    'status' => 'Open_Pending_Approval']);
                $updateCstm = DB::table('cases_cstm')
                        ->where('id_c', $row->id)
                        ->update([
                    'category_c' => $category[0],
                    'category_desc_c' => $categoryDesc->value_name]);
                Log::info(self::class . ' > ' . __FUNCTION__ . ' > Update Case Category For Onsite : ' . $row->case_number . ' > Category : ' . $category[0] . ' > Success Update : ' . $updateCase . ' ' . $updateCstm);

                //check latest task exist
                $taskLatest = self::crmService()->getDetailTaskLatestFlagCRM($row->id);
                if (!$taskLatest) {
                    $newTask = new Tasks;
                    $newTask->id = Uuid::uuid4()->toString();
                    $newTask->name = 'Pending Approval - Onsite Support';
                    $newTask->description = $row->description;
                    $newTask->date_entered = $row->date_modified;
                    $newTask->date_modified = $row->date_modified;
                    $newTask->modified_user_id = $row->modified_user_id;
                    $newTask->created_by = $row->modified_user_id;
                    $newTask->deleted = 0;
                    $newTask->assigned_user_id = '22f5826e-3c95-5a9a-be15-5c2edb2c2a21'; //tl group
                    $newTask->status = 'Pending Acknowledgement';
                    $newTask->date_due_flag = 0;
                    $newTask->date_start = $row->date_modified;
                    $newTask->date_due = $row->sla_due;
                    $newTask->date_start_flag = 0;
                    $newTask->parent_type = 'Cases';
                    $newTask->parent_id = $row->id;
                    $newTask->priority = 'Low';
                    $newTask->save();

                    $newTaskCustom = new TaskCustom;
                    $newTaskCustom->id_c = $newTask->id;
                    $newTaskCustom->assign_group_c = 'Group TL - Team';
                    $newTaskCustom->resolution_c = $row->resolution;
                    $newTaskCustom->sla_flag_c = 0;
                    $newTaskCustom->checkbox_add_day_c = 0;
                    $newTaskCustom->save();
                    Log::info(self::class . ' > ' . __FUNCTION__ . ' > Create New Task : ' . $row->case_number . ' > Task Id : ' . $newTask->id);
                }
            }
        }
        Log::info(self::class . ' > ' . __FUNCTION__ . ' > Total : ' . $total . ' Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function checkCaseResolveWithTaskCompleted() {
        /**
         * Bug scenario : Case status is open_resolved, but task status for case owner is Completed. workflow may not run properly
         * Task status case owner should be pending acknowledgement.. check on acknowledge time, if null then update status to pending acknowledgement.
         * Date entered and date modified for task case owner is similar, this shows that this task is not modified yet by case owner user
         */
        $dtStartTime = Carbon::now();

        $query = DB::select("SELECT c.id,c.`case_number`, c.`state`, c.`status`, c.`date_entered`, c.`date_modified`, 
                       t.`name`, t.`status`, t.`date_entered`, t.`date_modified`, t.`task_batch_indicator` 
                       FROM cases c, cases_cstm cc, tasks t, tasks_cstm tc
                       WHERE c.`id` = cc.`id_c`
                       AND t.`parent_id` = c.`id` AND tc.`id_c` = t.`id`
                       AND c.`deleted` = 0 AND t.`deleted` = 0  
                       AND c.`status` IN ('Open_Resolved','Open_Assigned')
                       AND t.`name` IN ('Assigned to Case Owner')
                       AND t.`status` = 'Completed'");

        $total = count($query);
        Log::info(self::class . ' > ' . __FUNCTION__ . ' > Total Case Found : ' . $total);
        if ($total > 0) {
            foreach ($query as $row) {
                //check latest task with flag
                $taskLatest = self::crmService()->getDetailTaskLatestCRM($row->id);
                if ($taskLatest) {
                    $taskAcknowledgeTime = $taskLatest->acknowledge_time_c;
                    if ($taskAcknowledgeTime == null) {
                        $updateTask = DB::table('tasks')
                                ->where('id', $taskLatest->id)
                                ->update(['status' => 'Pending Acknowledgement']);
                    }
                    Log::info(self::class . ' > ' . __FUNCTION__ . ' > Update Task Status For Task : ' . $taskLatest->name . ' > Case Number : ' . $row->case_number . ' > Success Update : ' . $updateTask);
                }
            }
        }
        Log::info(self::class . ' > ' . __FUNCTION__ . ' > Total : ' . $total . ' Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function checkDuplicateCaseOwnerTask() {
        /**
         * Bug scenario : Cases with status open has duplicate case owner task. 
         * 1. Check case status, if Resolved then case owner task must have one record only with status pending acknowledgement
         * 2. If case status Pending Verification, then case owner task must have one record only with status Acknowledge
         */
        $dtStartTime = Carbon::now();

        $query = DB::select("SELECT a.id, a.`status`,a.`date_entered`,b.`request_type_c`,b.`incident_service_type_c`,a.`case_number`, COUNT(c.name) AS cnt
                                FROM cases a
                                LEFT JOIN `cases_cstm` b ON a.id=b.`id_c`
                                LEFT JOIN tasks c ON c.`parent_id`=a.`id`
                                LEFT JOIN tasks_cstm d ON d.`id_c`=c.`id`
                                WHERE c.`name` LIKE '%owner%' -- %nitial%
                                AND YEAR(a.`date_entered`) = YEAR(NOW())
                                AND a.`deleted` =0
                                AND c.`deleted` = 0
                                AND a.`state` = 'Open'
                                AND c.`status` <> 'Completed'
                                GROUP BY a.`case_number`
                                HAVING cnt > 1");

        $total = count($query);
        Log::info(self::class . ' > ' . __FUNCTION__ . ' > Total Case Found : ' . $total);
        if ($total > 0) {
            foreach ($query as $row) {
                if ($row->status == 'Open_Resolved' || $row->status == 'Open_Assigned') {
                    $task = self::crmService()->getDetailTaskLatestCRM($row->id);
                    if ($task) {
                        $updateTask = DB::table('tasks')
                            ->where('id', $task->id)
                            ->update(['deleted' => 1]);
                    }
                    Log::info(self::class . ' > ' . __FUNCTION__ . ' > Update Task Status For Task Id : ' . $task->id . ' > Case Number : ' . $row->case_number . ' > Success Update : ' . $updateTask);
                }
            }
        }
        Log::info(self::class . ' > ' . __FUNCTION__ . ' > Total : ' . $total . ' Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }
    
    private static function checkDuplicateOnsiteTask() {
        /*
         * Bug scenario : Onsite case must have one task only. Delete duplicate task
         */
        $dtStartTime = Carbon::now();

        $query = DB::select("SELECT a.id,a.case_number,a.status,a.doc_no,a.name,a.account_id,a.date_entered,a.date_modified AS case_date_modified,
                            a.deleted, cc.`case_info_c`, t.id AS task_id, t.name, t.date_modified AS task_date_modified,
                            t.date_entered AS task_date_entered,t.status AS tstatus FROM cases a, cases_cstm cc, tasks t,
                                    (SELECT cases.name,cases.`case_number`,doc_no,cases.description,account_id, case_info_c, t.`name` tname, COUNT(t.`name`) AS total FROM cases, cases_cstm, tasks t
                                     WHERE cases.`id` = cases_cstm.`id_c`
                                     AND t.`parent_id` = cases.`id`
                                     AND t.`deleted` = 0  
                                     AND cases.deleted = 0 
                                     AND t.`deleted` = 0
                                     AND YEAR(cases.date_entered) = YEAR(NOW())
                                     AND MONTH(cases.date_entered) = MONTH(NOW())
                                     AND t.`name` = 'Pending Approval - Onsite Support' 
                                     GROUP BY cases.name,cases.`case_number`,cases.description,doc_no,account_id, case_info_c, tname
                                     HAVING total > 1) AS b 
                             WHERE   a.`id` = cc.`id_c` AND t.`parent_id` = a.`id`
                             AND b.case_number = a.`case_number`  ; ");

        $total = count($query);
        Log::info(self::class . ' > ' . __FUNCTION__ . ' > Total Case Found : ' . $total);
        if ($total > 0) {
            foreach ($query as $row) {
                if ($row->status == 'Closed_Approved' && $row->case_date_modified != $row->task_date_modified) {
                        $updateTask = DB::table('tasks')
                            ->where('id', $row->task_id)
                            ->update(['deleted' => 1]);
                    Log::info(self::class . ' > ' . __FUNCTION__ . ' > Update Task Status For Task Id (Scenario 1):  ' . $row->task_id . ' > Case Number : ' . $row->case_number . ' > Success Update : ' . $updateTask);
                }else if($row->status == 'Closed_Approved' && $row->tstatus == 'Approved' && $row->task_date_entered == $row->task_date_modified){
                    $updateTask = DB::table('tasks')
                            ->where('id', $row->task_id)
                            ->update(['deleted' => 1]);
                    Log::info(self::class . ' > ' . __FUNCTION__ . ' > Update Task Status For Task Id (Scenario 2): ' . $row->task_id . ' > Case Number : ' . $row->case_number . ' > Success Update : ' . $updateTask);
                }
            }
        }
        Log::info(self::class . ' > ' . __FUNCTION__ . ' > Total : ' . $total . ' Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function checkIncidentCaseWithStatusAssigned() {
       /*
         * Bug scenario : Case with incident type it_incident, task assigned to case owner but case status assigned.
        * Case status should be resolved when task case owner status is pending acknowledgement
        * Case status should be pending verification when task case owner status is acknowledge
         */
        $dtStartTime = Carbon::now();

        $query = DB::select("SELECT a.`case_number`,a.id as caseId, c.id as taskId, 
                                a.status as caseStatus, c.status as taskStatus
                                FROM cases a
                                JOIN cases_cstm b ON a.`id`=b.`id_c`
                                JOIN tasks c ON c.`parent_id`=a.`id`
                                JOIN tasks_cstm d ON c.`id`=d.`id_c`
                                WHERE b.`request_type_c` = 'incident'
                                AND b.`incident_service_type_c` = 'incident_it'
                                AND c.`name` LIKE '%wner%'
                                AND c.`status` = 'Pending Acknowledgement'
                                AND a.state = 'Open'
                                AND c.`deleted` = 0
                                AND a.`status` = 'Open_Assigned'");

        $total = count($query);
        Log::info(self::class . ' > ' . __FUNCTION__ . ' > Total Case Found : ' . $total);
        if ($total > 0) {
            foreach ($query as $row) {
                if ($row->taskStatus == 'Pending Acknowledgement') {
                        $updateTask = DB::table('cases')
                            ->where('id', $row->caseId)
                            ->update(['status' => 'Open_Resolved']);
                    Log::info(self::class . ' > ' . __FUNCTION__ . ' > Update Task Status For Task Id : ' . $row->taskId . ' > Case Number : ' . $row->case_number . ' > Success Update : ' . $updateTask);
                }else if ($row->taskStatus == 'Acknowledge') {
                        $updateTask = DB::table('cases')
                            ->where('id', $row->caseId)
                            ->update(['status' => 'Pending_User_Verification']);
                    Log::info(self::class . ' > ' . __FUNCTION__ . ' > Update Task Status For Task Id : ' . $row->taskId . ' > Case Number : ' . $row->case_number . ' > Success Update : ' . $updateTask);
                }
            }
        }
        Log::info(self::class . ' > ' . __FUNCTION__ . ' > Total : ' . $total . ' Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
     
    }
    
    private static function checkCaseClosedWithOpenTask() {
        /*
         * Bug scenario : Case status closed, but task status acknowledge/pending acknowledgement
         */

        $dtStartTime = Carbon::now();

        $query = DB::table('cases')->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id')
                ->join('tasks', 'tasks.parent_id', '=', 'cases.id')
                ->where('cases.state', 'Closed')
                ->whereIn('tasks.status', ['Pending Acknowledgement', 'Acknowledge'])
                ->select('cases.case_number', 'cases.status as caseStatus', 'cases_cstm.request_type_c', 'cases_cstm.incident_service_type_c', 'tasks.name', 'tasks.status', 'tasks.id')
                ->get();


        $total = count($query);
        Log::info(self::class . ' > ' . __FUNCTION__ . ' > Total Case Found : ' . $total);
        if ($total > 0) {
            foreach ($query as $row) {
                if ($row->caseStatus == 'Closed_Closed') {
                    $updateTask = DB::table('tasks')
                            ->where('id', $row->id)
                            ->update(['status' => 'Completed']);
                    Log::info(self::class . ' > ' . __FUNCTION__ . ' > Update Task Status For Task Id : ' . $row->id . ' > Case Number : ' . $row->case_number . ' > Success Update : ' . $updateTask);
                } else if ($row->caseStatus == 'Closed_Approved') {
                    $updateTask = DB::table('tasks')
                            ->where('id', $row->id)
                            ->update(['status' => 'Approved']);
                    Log::info(self::class . ' > ' . __FUNCTION__ . ' > Update Task Status For Task Id : ' . $row->id . ' > Case Number : ' . $row->case_number . ' > Success Update : ' . $updateTask);
                }
            }
        }
        Log::info(self::class . ' > ' . __FUNCTION__ . ' > Total : ' . $total . ' Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function checkEmptyCases() {
        /*
         * Bug scenario : Case created but other required field are empty
         */

         $dtStartTime = Carbon::now();

         $query = DB::table('cases')->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id')
                 ->where('cases.state', 'Open')
                 ->where('cases.status', 'Open_New')
                 ->where('cases.name','')
                 ->where('cases.deleted',0)
                 ->select('cases.case_number', 'cases.status as caseStatus', 'cases.description', 
                 'cases.name','cases.id')
                 ->addSelect(DB::raw("CONVERT_TZ(cases.date_entered,'+00:00','+08:00') AS created_date"))
                 ->get();
 
 
         $total = count($query);
         Log::info(self::class . ' > ' . __FUNCTION__ . ' > Total Case Found : ' . $total);

         if ($total > 0) {
             foreach ($query as $row) {
                $now = Carbon::now();
                $caseCreated = new Carbon($row->created_date);
                $dateCaseCreated = $caseCreated;
                $diffInMinutes = $now->diffInMinutes($dateCaseCreated);
                
                if($diffInMinutes > 5){
                    $updateTask = DB::table('cases')
                        ->where('id', $row->id)
                        ->update(['deleted' => 1]);
                    Log::info(self::class . ' > ' . __FUNCTION__ . ' > Delete Case Number : ' . $row->case_number );
                }
             }
        }
    }

}
