<?php

namespace App\Migrate\Crm;

use Carbon\Carbon;
use Carbon\CarbonInterval;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Migrate\MigrateUtils;
use Config;

class CheckCaseExecutionDate {

    public static function run() {

        Log::debug(self::class . ' Starting ... run ');
        $dtStartTime = Carbon::now();

        self::checkExecutionDate();

        Log::info(self::class . ' Completed checkExecutionDate --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    public static function checkExecutionDate() {
        $dtStartTime = Carbon::now();
        Log::info(self::class . ' Start checkExecutionDate' . __FUNCTION__ . ' Execution DateTime: ' . $dtStartTime);

        var_dump('Execution datetime: ' . $dtStartTime); 
        //check for stuck job queue
        $result = DB::select(
                        "SELECT c.`id`,c.`case_number`, c.`date_entered`, c.`date_modified`, 
                            c.`modified_user_id`, c.`cntc_mode_executiondate`, 
                            c.`cntc_mode_sla_startdate`, c.`cntc_mode_sla_duedate`, 
                            c.`pickup_datetime`, c.`pickupby_id`
                            FROM cases c, cases_cstm cc, users u
                            WHERE c.`id` = cc.`id_c`
                            AND u.`id` = c.`created_by`
                            AND cc.`contact_mode_c` = 'Call-in'
                            -- and u.`first_name` = 'Hairul Fatin Nabila Azlan'
                            AND c.`deleted` = 0
                            AND cc.`sub_category_c` NOT IN ('10715_15858')
                            AND c.`cntc_mode_executiondate` IS NULL 
                            AND YEAR(c.`date_entered`) = YEAR(NOW())");

        var_dump('Total : ' . count($result));
        Log::info('Total : ' . count($result));
        if (count($result) > 0) {
            foreach ($result as $data) { 
                
                $dateEntered = $data->date_entered;
                $dateExecution = (new Carbon($dateEntered))->addMinute(3);
                $dateSlaDue = (new Carbon($dateEntered))->addMinute(15);
                Log::info('Case Number ' . $data->case_number);
                Log::info('Date Entered ' . $dateEntered);
                Log::info('Date Execution ' . $dateExecution);
                Log::info('Date Sla Due ' . $dateSlaDue);

                //update case 
                DB::table('cases')
                        ->where('case_number', $data->case_number)
                        ->where('id', $data->id)
                        ->update([
                            'cntc_mode_executiondate' => $dateExecution,
                            'cntc_mode_sla_startdate' => $dateEntered,
                            'cntc_mode_sla_duedate' => $dateSlaDue,
                            'pickup_datetime' => $dateEntered,
                            'pickupby_id' => $data->modified_user_id
                ]);
                
                var_dump('Successfully update case ' . $data->case_number);
                var_dump('Date Entered ' . $dateEntered);
                var_dump('Date Execution ' . $dateEntered);
                var_dump('Date Sla Due ' . $dateEntered);
            }
        }
    }

}
