<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\Crm;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Migrate\MigrateUtils;
use Carbon\CarbonInterval;
use Ramsey\Uuid\Uuid;
use App\Models\Cases;
use App\Models\CasesCustom;
use App\Models\Tasks;
use App\Models\TaskCustom;
use Config;

class CreateRITTask {

    public static function runCheckingCreateRITTask($dateStart = null, $dateEnd = null) {

        Log::debug(self::class . ' Starting ... CreateRITTask ');
        $dtStartTime = Carbon::now();

        self::CreateRITTask($dateStart, $dateEnd);

        Log::info(self::class . ' Completed CreateRITTask --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        var_dump(self::class . ' Completed CreateRITTask --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    /* This Batch will Complete Task Pending too long for status Acknowledge for 15 Minutes (Flag 2) and Create New Task with Flag 3 (4 Hour) */

    private static function CreateRITTask($dateStart, $dateEnd) {

        Log::info(self::class . ' Start Checking Acknowledge Task for Task 15Minutes >> ' . __FUNCTION__);
        var_dump('Date Start: ' . $dateStart);
        var_dump('Date End: ' . $dateEnd);

        $take = 1000;
        $skip = 1000;
        $countTask = 0;

        $dtStartTime = Carbon::now();
        $dateYesterday = Carbon::yesterday()->format('Y-m-d');
        
        //check for 15 minutes >> flag 2
        do {
            $taskStatus = DB::table('cases as a')
                    ->join('cases_cstm as b', 'b.id_c', '=', 'a.id')
                    ->join('tasks as c', 'c.parent_id', '=', 'a.id')
                    ->join('tasks_cstm as d', 'd.id_c', '=', 'c.id')
                    ->where('c.status', 'Acknowledge')
                    ->where('c.assigned_user_id', 'like', 'bd305f97-902e-e186-f506-58997eeecc12')
                    ->where('b.request_type_c', 'incident')
                    ->where('b.incident_service_type_c', 'incident_it')
                    ->where('d.sla_task_flag_c', 2)
                    ->where('a.deleted', 0)
                    ->where('c.deleted', 0)
                    ->where(DB::raw("(DATE_FORMAT(a.date_entered,'%Y-%m-%d'))"),"<=",$dateYesterday)
                  //  ->where('a.case_number', 3894550)
                    ->where('a.state', 'Open')
                    ->where('a.status', 'Open_Assigned')
                    ->select('c.*', 'd.*', 'a.case_number as casenumber', 'a.name as caseName ','c.name as taskName', 'a.name as casesubject', 'd.task_number_c as tasknumber', 'c.parent_id as case_id', 'c.created_by as createdBy', 'c.status as taskStatus', 'c.date_entered as task_dateStart')
                    ->take($take)
                    ->skip($skip * $countTask++);

            $resultTask = $taskStatus->get();
            $total = count($resultTask);
            if ($total > 0) {
               // log::info('Task : '.$total);
                self::checkFlag2Task($resultTask);
            }
        } while (count($resultTask) == $take);
        Log::info(self::class . ' Complete Update Pending for 4 Hour Task , Counter :' . $countTask . ' Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function checkFlag2Task($data) {

        foreach ($data as $row) {
             
            sleep(5);
            $now = Carbon::now()->subHour(8);
            $dateStart = new Carbon($row->task_dateStart);            
            $AdiffInHours = $now->diffInHours($dateStart);
            
               if ($AdiffInHours > 24) {
                    $TaskDateTime = date('Y-m-d H:i:s', strtotime($row->task_dateStart . ' +24 hour')); 
                    self::updateSLA4HourTaskPendingAcknowledge($row, $now);                    
                    log::info(' Masuk checkFlag2Task : '.$row->casenumber);
                    log::info(' Masuk checkFlag2Task : '.$row->tasknumber.' : '.$row->taskName);
                    log::info('Task Lebih 24 Jam');
                    log::info(' Task created Date : '.$row->task_dateStart );                                       
                    log::info(' Complete Date After 24H : '.$TaskDateTime . ' Date Random Now : '.$now);     
               }else{
                   log::info(' Masuk checkFlag2Task Not More Than 24 Hour: '.$row->casenumber);
                   log::info(' Masuk checkFlag2Task Not More Than 24 Hour: '.$now . '> ' .$dateStart .' > ' .$AdiffInHours);
               }
        }
    }
    
    private static function updateSLA4HourTaskPendingAcknowledge($data, $slaRandomDateTime) {
        
        if($data->resolution_c == null || $data->resolution_c == ''){
            $resolutionTask = 'Task assign to Production Support for further action. Tq';
        }else{
            $resolutionTask = $data->resolution_c;
        }

        $updateTaskcstmDetails = [
            'resolution_c' => $resolutionTask,
        ];

        $updateTaskDetails = [
            'status' => 'Completed',
            'date_modified' => $slaRandomDateTime,
            'task_batch_indicator' => 1
        ];

        $updateCaseDetails = [
            'sla_flag' => 2,
            'date_modified' => $slaRandomDateTime
        ];

        DB::table('tasks')
                ->where('id', $data->id)
                ->update($updateTaskDetails);

        DB::table('tasks_cstm')
                ->where('id_c', $data->id)
                ->update($updateTaskcstmDetails);

        DB::table('cases')
                ->where('id', $data->case_id)
                ->update($updateCaseDetails);

        $newTask = new Tasks;
        $newTask->id = Uuid::uuid4()->toString();
        $newTask->name = 'Assigned to Group IT Specialist(Production Support)';
        $newTask->description = $data->description;
        $newTask->date_entered = $slaRandomDateTime;
        $newTask->date_modified = $slaRandomDateTime;
        $newTask->modified_user_id = $data->createdBy;
        $newTask->created_by = $data->createdBy;
        $newTask->deleted = 0;
        $newTask->assigned_user_id = 'd3bf216c-122b-4ce5-9410-899317762b60';
        $newTask->status = 'Pending Acknowledgement';
        $newTask->date_due_flag = 0;
        $newTask->date_start = $slaRandomDateTime;
        $newTask->date_due = date('Y-m-d H:i:s', strtotime($slaRandomDateTime . "+ 4 hour")); 
        $newTask->date_start_flag = 0;
        $newTask->parent_type = 'Cases';
        $newTask->parent_id = $data->case_id;
        $newTask->priority = 'Low';
        $newTask->task_justification = 'technical';
        $newTask->task_severity = null;
        $newTask->task_batch_indicator = 1;
        $newTask->resolution_category_c = 'not_applicable';
        $newTask->save();

        $newTaskCustom = new TaskCustom;
        $newTaskCustom->id_c = $newTask->id;
        $newTaskCustom->assign_group_c = 'Group IT Specialist(Production Support)';
        $newTaskCustom->resolution_c = 'Assigned to Production Support for further action. Tq';
        $newTaskCustom->sla_flag_c = 0;
        $newTaskCustom->checkbox_add_day_c = 0;
        $newTaskCustom->category_factor_c = 'external_factor';  
        $newTaskCustom->sla_task_flag_c = '3';
        $newTaskCustom->save();

        Log::info(self::class . ' Create New Task Assigned for Severity for current Task status Pending Acknowledge :'. ' Task Id :' . $data->id);
    }

}
