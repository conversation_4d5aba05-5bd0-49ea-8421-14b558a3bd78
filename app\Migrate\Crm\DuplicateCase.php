<?php

/* 
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\Crm;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Migrate\MigrateUtils;
use App\Services\CRMService;

class DuplicateCase {
    
    public static function crmService() {
        return new CRMService;
    }
    
    public static function runCheckingDuplicateCase() {
        Log::debug(self::class . ' Starting ... runCheckingDuplicateCase');
        $dtStartTime = Carbon::now();

        self::checkDuplicateCase();

        Log::info(self::class . ' Completed runCheckingDuplicateCase --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        var_dump(self::class . ' Completed runCheckingDuplicateCase --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        
    }
    
    private static function checkDuplicateCase() {
        
        $duplicateCase = DB::select("SELECT a.id,a.case_number,a.status,a.doc_no,a.name,a.account_id,a.date_entered,a.deleted, cc.`case_info_c` FROM cases a, cases_cstm cc, 
                        (SELECT NAME,doc_no,description,account_id, case_info_c, COUNT(*) AS total FROM cases, cases_cstm 
                         WHERE cases.`id` = cases_cstm.`id_c`
                         AND STATUS = 'Open_Pending Input' 
                         AND account_id <> ''
                         AND deleted = 0 
                         AND date_entered > '2017-12-31' 
                         AND (cases_cstm.`case_info_c` = '' OR cases_cstm.`case_info_c` IS NULL)
                         GROUP BY NAME,description,doc_no,account_id, case_info_c 
                         HAVING total > 1) AS b 
                         WHERE a.name = b.name 
                         AND a.`id` = cc.`id_c`
                         AND   a.account_id = b.account_id 
                         AND a.description = b.description
                         AND a.doc_no = b.doc_no
                         AND cc.`case_info_c` = b.case_info_c
                         AND a.status = 'Open_Pending Input'");
        
        $totalDuplicate = count($duplicateCase);
        
        if ($totalDuplicate > 0) {
            
            $counter = 0;
            while($counter < $totalDuplicate){
                
                
            foreach($duplicateCase as $case) {
                
                $counter++;
                
                if ($counter < $totalDuplicate) {
                    
                    // update case status to deleted
                    
                    DB::table('cases')
                            ->where('id', $case->id)
                            ->update([
                                'deleted' => 1
                    ]);
                    
                    var_dump(self::class . __FUNCTION__ . ' Total Duplicate Case ' .$totalDuplicate);
                    Log::info(self::class . __FUNCTION__ . ' Total Duplicate Case ' .$totalDuplicate);
                    var_dump(self::class . __FUNCTION__  .' Delete Case ' .$case->case_number);
                    Log::info(self::class . __FUNCTION__ . ' Delete Case ' .$case->case_number);
                    var_dump(self::class . __FUNCTION__  . ' Total Update ' .$counter);
                    Log::info(self::class . __FUNCTION__ .  ' Total Update ' .$counter);
                    
                } else {
                    //tinggalkan 1 rekod yang berstatus aktif
                    var_dump(self::class . __FUNCTION__ . ' Duplicate Case with Status Active ' .$case->case_number);
                    Log::info(self::class . __FUNCTION__ .  ' Duplicate Case with Status Active ' .$case->case_number);
                }
            }
            }
        }
            
    }
    
}