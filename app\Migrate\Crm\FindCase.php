<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\Crm;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Migrate\MigrateUtils;
use App\Services\CRMService;

class FindCase {

    static $query_skip = 500;
    static $query_take = 500;

    public static function crmService() {
        return new CRMService;
    }

    public static function runCheckingCase($documentNo) {
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();

        $start = 0;
        $skip = self::$query_skip;
        $take = self::$query_take;
        $totalRecords = 0;

        $CsvData = array('Case No,Date Entered, Date Modified, Document Number, Subject, Description, Resolution, Request Type, Incident/Service Type, Category, Sub Category, Sub Category 2');

        do {
            $nextSkip = $start++ * $skip;
            foreach ($documentNo as $value) {
                $data = self::getQuery($value, $take, $nextSkip);
                $totalRecords = $totalRecords + count($data);

                dump(self::class . ' current totalrecords ' . count($data));

                foreach ($data as $obj) {

                    $docNo = preg_replace('/[,"]+/', ' ', trim($obj->doc_no));
                    $subject = preg_replace('/[,]+/', ' ', trim($obj->name));
                    $trimdescription = preg_replace('!\s+!', ' ', trim($obj->description));
                    $trimdescriptionLen = '';
                    if ((strlen($trimdescription)) > 40000) {
                        $trimdescriptionLen = substr($trimdescription, 0, -10000);
                    } else {
                        $trimdescriptionLen = $trimdescription;
                    }
                    $description = preg_replace('/[,]+/', ' ', trim($trimdescriptionLen));
                    $strLen = (strlen($obj->resolution));
                    if ($strLen > 40000) {
                        $trimResolution = substr($obj->resolution, 0, -10000);
                    } else {
                        $trimResolution = $obj->resolution;
                    }
                    $reqType = '';
                    if ($obj->request_type_c != '') {
                        $reqType = self::crmService()->getValueLookupCRM('request_type_list', $obj->request_type_c);
                    }
                    $incType = '';
                    if ($obj->incident_service_type_c != '') {
                        $incType = self::crmService()->getValueLookupCRM('incident_service_type_list', $obj->incident_service_type_c);
                    }
                    $category = '';
                    if ($obj->category_c != '') {
                        $category = self::crmService()->getValueLookupCRM('category_list', $obj->category_c);
                    }

                    $subCategory = '';
                    if ($obj->sub_category_c != '') {
                        $subCategory = self::crmService()->getValueLookupCRM('cdc_sub_category_list', $obj->sub_category_c);
                    }

                    $subSubCategory = '';
                    if ($obj->sub_category_2_c != '') {
                        $subSubCategory = self::crmService()->getValueLookupCRM('cdc_sub_category_2_list', $obj->sub_category_2_c);
                    }
                    $resolution = preg_replace('/[^a-zA-Z0-9]/', ' ', trim($trimResolution));
                    $CsvData[] = (
                            $obj->case_number . ',' .
                            Carbon::parse($obj->date_entered)->addHour(8) . ',' .
                            Carbon::parse($obj->date_modified)->addHour(8) . ',' .
                            $docNo . ',' .
                            $subject .',' .
                            $description . ',' .
                            $resolution .',' .
                            $reqType .','.
                            $incType .','.
                            $category .',' .
                            $subCategory .',' .
                            $subSubCategory 
                            );
                }
            }
        } while (count($data) > 0 && count($data) == self::$query_take);

        $filename = "FindCase.csv";
        $file_path = storage_path() . '/app/exports/cases/' . $filename;
        $file = fopen($file_path, "w+");
        foreach ($CsvData as $exp_data) {
            fputcsv($file, explode(',', $exp_data));
        }
        fclose($file);

        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        var_dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function getQuery($documentNo, $take, $nextSkip) {

        $query = DB::table('cases')
                ->join('cases_cstm', 'cases.id', '=', 'cases_cstm.id_c')
                ->where('cases.doc_no', 'like', '%' . $documentNo . '%')
                ->orWhere('cases.name', 'like', '%' . $documentNo . '%')
                ->orWhere('cases.description', 'like', '%' . $documentNo . '%')
                ->orWhere('cases.resolution', 'like', '%' . $documentNo . '%')
                ->select('cases.*', 'cases_cstm.*')
                ->skip($nextSkip)->take($take)
                ->get();

        Log::info(__FUNCTION__ . 'Document No : ' .$documentNo . ' Total ' .count($query));
        var_dump(__FUNCTION__ . 'Document No : ' .$documentNo . ' Total ' .count($query));
        return $query;
    }

}
