<?php

namespace App\Migrate\Crm;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Facades\DB;
use App\Models\Account;
use App\Models\Contact;
use App\Models\AccountCustom;
use App\Models\ContactCustom;
use App\Models\LogIntegration;
use App\Models\EmailAddress;
use App\Migrate\MigrateUtils;
use Config;
use Excel;

/*
 * This will update PTJ as Active base don Excel file. IF not found in excel, data will set inactive
 * PTJ Information
 * 
 * If data in eP not exist in CRM, System will create to CRM.
 * 
 */

class FixedCRMData {

    public static function runFixedData() {
        Log::debug(self::class . ' Starting ... runFixedData');
        $dtStartTime = Carbon::now();

        //self::fixedInvoicesCreatedOrModifiedBy();
        //self::fixedCasesCreatedOrModifiedBy();
        //self::fixedTasksCreatedOrModifiedBy();
        //self::fixedInvoicesCreatedOrModifiedByHumanMistake();
        //self::reSyncInvoicesCreatedOrModifiedBy();
        //self::fixedInvoicesModifiedByAndPaymentDate();
        //fixed address_state in accounts 
        //self::fixedAddressState();
        // self::fixIDNextgenLoginRemoveInAccountDeleted();
        self::fixedCasesResolutionForStatusCancelledEaduan();

        Log::info(self::class . ' Completed runFixedData --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    public static function  fixIDNextgenLoginRemoveInAccountDeleted(){
        $results = DB::select(
                " SELECT 
                    c.name,
                    c.`deleted`,
                    c.`account_type`,
                    c.`org_gov_code`,
                    a.id AS contact_id,
                    a.`first_name`,
                    a.`login_id_nextgen`,
                    a.`deleted`  AS ContactDeleted
                  FROM
                    contacts a,
                    accounts_contacts b,
                    accounts c 
                  WHERE a.id = b.contact_id 
                  AND a.`account_reference_id` = c.id 
                  AND c.deleted = 1");
        var_dump('Total : '.count($results));
        $total = 0;
        if(count($results) > 0){
        foreach ($results as $obj){
            DB::table('contacts')
                            ->where('id', $obj->contact_id)
                            ->update([
                                'login_id_nextgen'=>'',
                                'record_status_nextgen'=> 0,
                                'deleted' => 1]);
            DB::table('accounts_contacts')
                            ->where('contact_id', $obj->contact_id)
                            ->update([
                                'deleted' => 1]);
            
            $objRes = DB::table('contacts')->where('id',$obj->contact_id)->first();
            $total++;
            Log::info('SOFT DELETE  CONTACT_ID :  ' . $objRes->id . ' LOGINIDNEXTGEN :  ' . $objRes->login_id_nextgen . 'DELETED AS : ' . $objRes->deleted);
            var_dump('SOFT DELETE  CONTACT_ID :  ' . $objRes->id . ' LOGINIDNEXTGEN :  ' . $objRes->login_id_nextgen . 'DELETED AS : ' . $objRes->deleted);
            
        }
        }
        var_dump('TOTAL DELETED:  ' .$total);
        Log::info('TOTAL DELETED:  ' .$total);
    }
    
    
    public static function  fixDuplicateIdNextgenLogin(){
        $results = DB::select(
                " SELECT 
                    a.id AS contact_id,
                    b.`deleted` AS ACCOUNT_DELETED,
                    a.login_id_nextgen,
                    a.`record_status_nextgen`,
                    a.`deleted` AS contact_deleted,
                    b.`org_gov_code`,
                    c.deleted AS ACC_CTC_DELETED,
                    a.`account_reference_id`,
                    c.`account_id` AS ACC_CTC_ACCOUNT_ID,
                    c.`contact_id` AS ACC_CTC_CONTACT_D,
                    c.`id` AS ACC_CTC_ID 
                  FROM
                    contacts a,
                    accounts b,
                    accounts_contacts c 
                  WHERE a.`account_reference_id` = b.`id` 
                    AND a.id = c.`contact_id` 
                    AND b.id = c.`account_id` 
                    AND login_id_nextgen IN (SELECT 
                    login_id_nextgen 
                  FROM
                    contacts 
                  WHERE LENGTH(login_id_nextgen) > 1 
                  GROUP BY login_id_nextgen 
                  HAVING COUNT(login_id_nextgen) > 1 )
                    AND b.deleted = 1");
        var_dump('Total : '.count($results));
        $total = 0;
        if(count($results) > 0){
        foreach ($results as $obj){
            
            DB::table('contacts')
                            ->where('id', $obj->contact_id)
                            ->update([
                                'login_id_nextgen'=>'',
                                'record_status_nextgen'=> 0,
                                'deleted' => 1]);
            DB::table('accounts_contacts')
                            ->where('contact_id', $obj->contact_id)
                            ->update([
                                'deleted' => 1]);
            
            $objRes = DB::table('contacts')->where('id',$obj->contact_id)->first();
            $total++;
            Log::info('SOFT DELETE  CONTACT_ID :  ' . $objRes->id . ' LOGINIDNEXTGEN :  ' . $objRes->login_id_nextgen . 'DELETED AS : ' . $objRes->deleted);
            var_dump('SOFT DELETE  CONTACT_ID :  ' . $objRes->id . ' LOGINIDNEXTGEN :  ' . $objRes->login_id_nextgen . 'DELETED AS : ' . $objRes->deleted);
            
        }
        }
        var_dump('TOTAL DELETED:  ' .$total);
        Log::info('TOTAL DELETED:  ' .$total);
    }
    
    public  static function fixAccount(){
        
        $results = DB::select(
                " SELECT a.id, a.deleted,a.org_gov_code, a.name  FROM accounts a
                WHERE 
                a.org_gov_code IN ( SELECT org_gov_code FROM accounts 
                WHERE deleted = 0 
                GROUP BY org_gov_code ,org_gov_type
                HAVING  COUNT(org_gov_code) > 1) 
                AND  a.deleted = 0 
                AND  NOT EXISTS (SELECT *  FROM cases c WHERE c.account_id  = a.id )
                ORDER BY a.org_gov_code");

        var_dump('Total : '.count($results));
        $total = 0;
        foreach ($results as $obj){
            
            DB::table('accounts')
                            ->where('id', $obj->id)
                            ->update(['deleted' => 1]);
            
            $acc = DB::table('accounts')->where('id',$obj->id)->first();
            $total++;
            Log::info('SOFT DELETE  ACCOUNTID :  ' . $acc->id . ' DELETED AS : ' . $acc->deleted);
            var_dump('SOFT DELETE  ACCOUNTID :  ' . $acc->id . ' DELETED AS : ' . $acc->deleted);
        }
         var_dump('TOTAL DELETED:  ' .$total);
         Log::info('TOTAL DELETED:  ' .$total);
    }
    /**
     * Fixed update modified_by and modified_date based on payment table at ulysses
     * Fixed update payment_date - 8hour
     */
    private static function fixedInvoicesModifiedByAndPaymentDate() {
        $dtStartTime = Carbon::now();
        $take = 50;
        $skip = 50;
        $count = 0;
        do {
            $dtStartTimeSave = Carbon::now();
            Log::info(self::class . '  ' . __FUNCTION__ . ' Query Invoice: counter ' . $count);
            $sql = DB::table('aos_invoices')
                    ->where('date_entered', '<', '2017-04-01')
                    ->take($take)
                    ->skip($skip * $count++);
            $result = $sql->get();
            Log::info(self::class . '   ' . __FUNCTION__ . ' : Total : ' . count($result));


            foreach ($result as $row) {
                $payment = DB::connection('sqlsrv')
                        ->table('CRM_PAYMENT')
                        ->join('CRM_PAYMENT_TRANSACTION', 'CRM_PAYMENT.CRM_PAYMENT_ID', 'CRM_PAYMENT_TRANSACTION.CRM_PAYMENT_ID')
                        ->where('CRM_PAYMENT_TRANSACTION.INVOICE_ID', $row->id)
                        ->select('CRM_PAYMENT.*')
                        ->first();

                if ($payment && $payment->CRM_PAYMENT_ID) {
                    $datePayment = new Carbon($payment->PAYMENT_DATE);
                    $datePayment->subHour(8);

                    $dateModified = new Carbon($payment->AMENDED_DATETIME);
                    $dateModified->subHour(8);

                    $userModifiedBy = trim($payment->AMENDED_BY);
                    $userModelModify = DB::table('users')->where('user_name', $userModifiedBy)->first();
                    if ($userModelModify && $userModelModify->id != '') {
                        $userModifiedBy = $userModelModify->id;
                    }

                    DB::table('aos_invoices')
                            ->where('id', $row->id)
                            ->update(['date_modified' => $dateModified->format('Y-m-d H:i:s'), 'modified_user_id' => $userModifiedBy]);
                    DB::table('aos_invoices_cstm')
                            ->where('id_c', $row->id)
                            ->update(['payment_date_c' => $datePayment->format('Y-m-d H:i:s')]);
                } else {
                    $paymentHistory = DB::connection('sqlsrv')
                            ->table('CRM_PAYMENT_HISTORY')
                            ->join('CRM_PAYMENT_TRANSACTION_HISTORY', 'CRM_PAYMENT_HISTORY.CRM_PAYMENT_ID', 'CRM_PAYMENT_TRANSACTION_HISTORY.CRM_PAYMENT_ID')
                            ->where('CRM_PAYMENT_TRANSACTION_HISTORY.INVOICE_ID', $row->id)
                            ->select('CRM_PAYMENT_HISTORY.*')
                            ->first();

                    if ($paymentHistory && $paymentHistory->CRM_PAYMENT_ID) {
                        $datePayment = new Carbon($paymentHistory->PAYMENT_DATE);
                        $datePayment->subHour(8);

                        $dateModified = new Carbon($paymentHistory->AMENDED_DATETIME);
                        $dateModified->subHour(8);

                        $userModifiedBy = trim($paymentHistory->AMENDED_BY);
                        $userModelModify = DB::table('users')->where('user_name', $userModifiedBy)->first();
                        if ($userModelModify && $userModelModify->id != '') {
                            $userModifiedBy = $userModelModify->id;
                        }

                        DB::table('aos_invoices')
                                ->where('id', $row->id)
                                ->update(['date_modified' => $dateModified->format('Y-m-d H:i:s'), 'modified_user_id' => $userModifiedBy]);
                        DB::table('aos_invoices_cstm')
                                ->where('id_c', $row->id)
                                ->update(['payment_date_c' => $datePayment->format('Y-m-d H:i:s')]);
                    }
                }
            }
            Log::info(self::class . '   ' . __FUNCTION__ . ' , COUNTER ' . $count . ' , Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTimeSave)]);
        } while (count($result) == $take);

        Log::info(self::class . '   ' . __FUNCTION__ . ' COMPLETED, Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        dd('Total Update ' . count($result));
    }

    /**
     * Reupdate invoice production start 1/4/2017 to set modified same with created
     */
    private static function fixedInvoicesCreatedOrModifiedByHumanMistake() {
        $dtStartTime = Carbon::now();
        $take = 50;
        $skip = 50;
        $count = 0;
        do {
            $dtStartTimeSave = Carbon::now();
            Log::info(self::class . ' Query Invoice: counter ' . $count);
            $sql = DB::table('aos_invoices')
                    ->where('date_entered', '>', '2017-04-01')
                    ->whereNotIn('name', ['INV/550098/01', 'INV/550085/01', 'INV/550040/01'])
                    ->take($take)
                    ->skip($skip * $count++);
            $result = $sql->get();
            Log::info(self::class . '   fixedInvoicesCreatedOrModifiedByHumanMistake : Total : ' . count($result));


            foreach ($result as $row) {
                DB::table('aos_invoices')
                        ->where('id', $row->id)
                        ->update(['date_modified' => $row->date_entered, 'modified_user_id' => $row->created_by]);
            }
            Log::info(self::class . '   fixedInvoicesCreatedOrModifiedByHumanMistake , COUNTER ' . $count . ' , Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTimeSave)]);
        } while (count($result) == $take);

        Log::info(self::class . '   fixedInvoicesCreatedOrModifiedByHumanMistake COMPLETED, Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        dd('Total Update ' . count($result));
    }

    /**
     * reupdate modified info same with ulysses data
     */
    private static function reSyncInvoicesCreatedOrModifiedBy() {
        $dtStartTime = Carbon::now();
        $take = 50;
        $skip = 50;
        $count = 0;
        do {
            $dtStartTimeSave = Carbon::now();
            Log::info(self::class . ' Query Invoice: counter ' . $count);
            $sql = DB::table('aos_invoices')
                    ->where('date_entered', '<', '2017-04-01')
                    ->take($take)
                    ->skip($skip * $count++);
            $result = $sql->get();
            foreach ($result as $row) {
                /** kesilapan ..adoi!!! * */
                $userCreatedBy = $row->created_by;
                if ($row->modified_user_id == '1a0401f0-7d1a-4ca2-866c-70b814259f7c') {
                    $dataInv = DB::connection('sqlsrv')
                            ->table('INVOICE')
                            ->where('INVOICE_ID', $row->id)
                            ->first();
                    if ($dataInv && $dataInv->INVOICE_ID != '') {
                        $dateModified = new Carbon($dataInv->AMENDED_DATETIME);
                        $dateModified->subHour(8);

                        $userModifiedBy = trim($dataInv->AMENDED_BY);
                        $userModelModify = DB::table('users')->where('user_name', $userModifiedBy)->first();
                        if ($userModelModify && $userModelModify->id != '') {
                            $userModifiedBy = $userModelModify->id;
                        }

                        if ($userModifiedBy == 'SR') {
                            $userModifiedBy = '29cc3de4-1519-40be-a9ed-59952858c49e';
                        }
                        if ($userCreatedBy == '3') {
                            $userCreatedBy = '29cc3de4-1519-40be-a9ed-59952858c49e';
                        }

                        DB::table('aos_invoices')
                                ->where('id', $row->id)
                                ->update(['date_modified' => $dateModified->format('Y-m-d H:i:s'), 'created_by' => $userCreatedBy, 'modified_user_id' => $userModifiedBy]);
                    } else {
                        Log::info(self::class . '   reSyncInvoicesCreatedOrModifiedBy tak jumpa ' . $row->id);
                    }
                }
            }
            Log::info(self::class . '   reSyncInvoicesCreatedOrModifiedBy , COUNTER ' . $count . ' , Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTimeSave)]);
        } while (count($result) == $take);

        Log::info(self::class . '   reSyncInvoicesCreatedOrModifiedBy COMPLETED, Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    /**
     * After migration , need to find id modified_by and created_by to re-update at modules
     */
    private static function fixedInvoicesCreatedOrModifiedBy() {
        $dtStartTime = Carbon::now();
        $take = 150;
        $skip = 150;
        $count = 0;
        do {
            $dtStartTimeSave = Carbon::now();
            Log::info(self::class . ' ' . __FUNCTION__ . ' > Query Invoice: counter ' . $count);
            $sql = DB::table('aos_invoices')
                    //->where('date_modified', '>', '2017-04-01')
                    ->where('name', 'INV/505873/01')
                    ->take($take)
                    ->skip($skip * $count++);
            $result = $sql->get();
            foreach ($result as $row) {
                $userCreatedBy = $row->created_by;
                $userModifiedBy = $row->modified_user_id;

                if ($userCreatedBy && $userCreatedBy != '3' && strlen($userCreatedBy) < 35) {
                    $userModelCreated = DB::table('users')->where('user_name', $row->created_by)->first();
                    if ($userModelCreated && $userModelCreated->id != '') {
                        $userCreatedBy = $userModelCreated->id;
                    }
                    $userModelModify = DB::table('users')->where('user_name', $row->modified_user_id)->first();
                    if ($userModelModify && $userModelModify->id != '') {
                        $userModifiedBy = $userModelModify->id;
                    }

                    if ($userCreatedBy == 'SR') {
                        $userCreatedBy = '3';
                    }
                    if ($userModifiedBy == 'SR') {
                        $userModifiedBy = '3';
                    }

                    if ($userCreatedBy != $row->created_by || $userModifiedBy != $row->modified_user_id) {
                        DB::table('aos_invoices')
                                ->where('id', $row->id)
                                ->update(['created_by' => $userCreatedBy, 'modified_user_id' => $userModifiedBy]);
                        var_dump('Invoice : ' . $row->name . ' , $userCreatedBy : ' . $userCreatedBy . ' , $userModifiedBy: ' . $userModifiedBy);
                        DB::table('aos_line_item_groups')
                                ->where('parent_id', $row->id)
                                ->where('parent_type', 'AOS_Invoices')
                                ->update(['created_by' => $userCreatedBy, 'modified_user_id' => $userModifiedBy]);

                        DB::table('aos_products_quotes')
                                ->where('parent_id', $row->id)
                                ->where('parent_type', 'AOS_Invoices')
                                ->update(['created_by' => $userCreatedBy, 'modified_user_id' => $userModifiedBy]);
                    }
                }
            }
            Log::info(self::class . '   ' . __FUNCTION__ . ' , COUNTER ' . $count . ' , Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTimeSave)]);
        } while (count($result) == $take);

        Log::info(self::class . '   ' . __FUNCTION__ . ' COMPLETED, Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    /**
     * After migration , need to find id modified_by and created_by to re-update at modules
     */
    private static function fixedCasesCreatedOrModifiedBy() {
        $dtStartTime = Carbon::now();
        $take = 500;
        $skip = 500;
        $count = 0;
        do {
            $dtStartTimeSave = Carbon::now();
            Log::info(self::class . ' Query Cases: counter ' . $count);
            $sql = DB::table('cases')
                    ->where('date_modified', '>', '2017-04-01')
                    ->take($take)
                    ->skip($skip * $count++);
            $result = $sql->get();
            foreach ($result as $row) {
                $userCreatedBy = $row->created_by;
                $userModifiedBy = $row->modified_user_id;
                if ($userCreatedBy && strlen($userCreatedBy) < 35) {
                    $userModelCreated = DB::table('users')->where('user_name', $row->created_by)->first();
                    if ($userModelCreated && $userModelCreated->id != '') {
                        $userCreatedBy = $userModelCreated->id;
                    }
                    $userModelModify = DB::table('users')->where('user_name', $row->modified_user_id)->first();
                    if ($userModelModify && $userModelModify->id != '') {
                        $userModifiedBy = $userModelModify->id;
                    }
                }
                /** check to skip if already fixed it * */
                if ($userCreatedBy != $row->created_by || $userModifiedBy != $row->modified_user_id) {
                    DB::table('cases')
                            ->where('id', $row->id)
                            ->update(['created_by' => $userCreatedBy, 'modified_user_id' => $userModifiedBy]);
                    Log::info(self::class . ' Query Cases: updated ' . $userModifiedBy);
                }
            }
            Log::info(self::class . '   fixedCasesCreatedOrModifiedBy , COUNTER ' . $count . ' , Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTimeSave)]);
        } while (count($result) == $take);

        Log::info(self::class . '   fixedCasesCreatedOrModifiedBy COMPLETED, Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    /**
     * After migration , need to find id modified_by and created_by to re-update at modules
     */
    private static function fixedTasksCreatedOrModifiedBy() {
        $dtStartTime = Carbon::now();
        $take = 500;
        $skip = 500;
        $count = 0;
        do {
            $dtStartTimeSave = Carbon::now();
            Log::info(self::class . ' Query Tasks: counter ' . $count);
            $sql = DB::table('tasks')
                    ->where('date_modified', '>', '2017-04-01')
                    ->take($take)
                    ->skip($skip * $count++);
            $result = $sql->get();
            foreach ($result as $row) {

                $userCreatedBy = $row->created_by;
                $userModifiedBy = $row->modified_user_id;
                if ($userCreatedBy && strlen($userCreatedBy) < 35) {
                    $userModelCreated = DB::table('users')->where('user_name', $row->created_by)->first();
                    if ($userModelCreated && $userModelCreated->id != '') {
                        $userCreatedBy = $userModelCreated->id;
                    }
                    $userModelModify = DB::table('users')->where('user_name', $row->modified_user_id)->first();
                    if ($userModelModify && $userModelModify->id != '') {
                        $userModifiedBy = $userModelModify->id;
                    }
                }

                /** check to skip if already fixed it * */
                if ($userCreatedBy != $row->created_by || $userModifiedBy != $row->modified_user_id) {
                    DB::table('tasks')
                            ->where('id', $row->id)
                            ->update(['created_by' => $userCreatedBy, 'modified_user_id' => $userModifiedBy]);
                    Log::info(self::class . ' Query Task: updated ' . $userModifiedBy);
                }
            }
            Log::info(self::class . '   fixedTasksCreatedOrModifiedBy , COUNTER ' . $count . ' , Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTimeSave)]);
        } while (count($result) == $take);

        Log::info(self::class . '   fixedTasksCreatedOrModifiedBy COMPLETED, Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function fixedAddressState() {
        $dtStartTime = Carbon::now();

        //10001     WILAYAH PERSEKUTUAN (K.L)
        //10002     MELAKA
        //10003     JOHOR
        //10004     SELANGOR
        //10005     PULAU PINANG
        //10006     NEGERI SEMBILAN
        //10007     PAHANG
        //10008     PERAK
        //10009     SABAH
        //10010     SARAWAK 
        //10011     PERLIS 
        //10012     KEDAH 
        //10013     KELANTAN
        //10014     TERENGGANU
        //10015     WILAYAH PERSEKUTUAN (LABUAN)
        //10016     WP CYBERJAYA
        //10017     PUTRAJAYA
        //10018     LUAR NEGERI 
        //10019     NOT DEFINED

        $wilayahKL = 'WILAYAH PERSEKUTUAN (K.L)';
        $melaka = 'MELAKA';
        $johor = 'JOHOR';
        $selangor = 'SELANGOR';
        $penang = 'PULAU PINANG';
        $nsembilan = 'NEGERI SEMBILAN';
        $pahang = 'PAHANG';
        $perak = 'PERAK';
        $sabah = 'SABAH';
        $sarawak = 'SARAWAK';
        $perlis = 'PERLIS';
        $kedah = 'KEDAH';
        $kelantan = 'KELANTAN';
        $terengganu = 'TERENGGANU';
        $wplabuan = 'WILAYAH PERSEKUTUAN (LABUAN)';
        $wpcyberjaya = 'WP CYBERJAYA';
        $putrajaya = 'PUTRAJAYA';
        $luarnegeri = 'LUAR NEGERI';
        $notdefined = 'NOT DEFINED';
        $negativeone = '-1';

        $stateName = array($wilayahKL, $melaka, $johor, $selangor, $penang, $nsembilan, $pahang, $perak,
            $sabah, $sarawak, $perlis, $kedah, $kelantan, $terengganu, $wplabuan, $wpcyberjaya, $putrajaya, $luarnegeri, $notdefined, $negativeone);

        $countState = Account::whereIn('billing_address_state', $stateName)
                ->select('id')
                ->count();

        var_dump('Count State: ' . $countState);

        if ($countState > 0) {

            //check for WILAYAH PERSEKUTUAN (K.L) start
            $dataKL = Account::where('billing_address_state', $wilayahKL)
                    ->select('id', 'billing_address_state')
                    ->get();

            if (count($dataKL) > 0) {
                self::updateKL($dataKL);
                var_dump('Count State- ' . $wilayahKL . ' : ' . count($dataKL));
            }

            //check for MELAKA start
            $dataMelaka = Account::where('billing_address_state', $melaka)
                    ->select('id', 'billing_address_state')
                    ->get();

            if (count($dataMelaka) > 0) {
                self::updateMelaka($dataMelaka);
                var_dump('Count State- ' . $melaka . ' : ' . count($dataMelaka));
            }

            //check for JOHOR start
            $dataJohor = Account::where('billing_address_state', $johor)
                    ->select('id', 'billing_address_state')
                    ->get();

            if (count($dataJohor) > 0) {
                self::updateJohor($dataJohor);
                var_dump('Count State- ' . $johor . ' : ' . count($dataJohor));
            }

            //check for SELANGOR start
            $dataSelangor = Account::where('billing_address_state', $selangor)
                    ->select('id', 'billing_address_state')
                    ->get();

            if (count($dataSelangor) > 0) {
                self::updateSelangor($dataSelangor);
                var_dump('Count State- ' . $selangor . ' : ' . count($dataSelangor));
            }

            //check for PULAU PINANG start
            $dataPenang = Account::where('billing_address_state', $penang)
                    ->select('id', 'billing_address_state')
                    ->get();

            if (count($dataPenang) > 0) {
                self::updatePenang($dataPenang);
                var_dump('Count State- ' . $penang . ' : ' . count($dataPenang));
            }

            //check for NEGERI SEMBILAN start
            $dataNSembilan = Account::where('billing_address_state', $nsembilan)
                    ->select('id', 'billing_address_state')
                    ->get();

            if (count($dataNSembilan) > 0) {
                self::updateNSembilan($dataNSembilan);
                var_dump('Count State- ' . $nsembilan . ' : ' . count($dataNSembilan));
            }

            //check for PAHANG start
            $dataPahang = Account::where('billing_address_state', $pahang)
                    ->select('id', 'billing_address_state')
                    ->get();

            if (count($dataPahang) > 0) {
                self::updatePahang($dataPahang);
                var_dump('Count State- ' . $pahang . ' : ' . count($dataPahang));
            }

            //check for PERAK start
            $dataPerak = Account::where('billing_address_state', $perak)
                    ->select('id', 'billing_address_state')
                    ->get();

            if (count($dataPerak) > 0) {
                self::updatePerak($dataPerak);
                var_dump('Count State- ' . $perak . ' : ' . count($dataPerak));
            }

            //check for SABAH start
            $dataSabah = Account::where('billing_address_state', $sabah)
                    ->select('id', 'billing_address_state')
                    ->get();

            if (count($dataSabah) > 0) {
                self::updateSabah($dataSabah);
                var_dump('Count State- ' . $sabah . ' : ' . count($dataSabah));
            }

            //check for SARAWAK start
            $dataSarawak = Account::where('billing_address_state', $sarawak)
                    ->select('id', 'billing_address_state')
                    ->get();

            if (count($dataSarawak) > 0) {
                self::updateSarawak($dataSarawak);
                var_dump('Count State- ' . $sarawak . ' : ' . count($dataSarawak));
            }

            //check for PERLIS start
            $dataPerlis = Account::where('billing_address_state', $perlis)
                    ->select('id', 'billing_address_state')
                    ->get();

            if (count($dataPerlis) > 0) {
                self::updatePerlis($dataPerlis);
                var_dump('Count State- ' . $perlis . ' : ' . count($dataPerlis));
            }

            //check for KEDAH start
            $dataKedah = Account::where('billing_address_state', $kedah)
                    ->select('id', 'billing_address_state')
                    ->get();

            if (count($dataKedah) > 0) {
                self::updateKedah($dataKedah);
                var_dump('Count State- ' . $kedah . ' : ' . count($dataKedah));
            }

            //check for KELANTAN start
            $dataKelantan = Account::where('billing_address_state', $kelantan)
                    ->select('id', 'billing_address_state')
                    ->get();

            if (count($dataKelantan) > 0) {
                self::updateKelantan($dataKelantan);
                var_dump('Count State- ' . $kelantan . ' : ' . count($dataKelantan));
            }

            //check for TERENGGANU start
            $dataTerengganu = Account::where('billing_address_state', $terengganu)
                    ->select('id', 'billing_address_state')
                    ->get();

            if (count($dataTerengganu) > 0) {
                self::updateTerengganu($dataTerengganu);
                var_dump('Count State- ' . $terengganu . ' : ' . count($dataTerengganu));
            }

            //check for WILAYAH PERSEKUTUAN (LABUAN) start
            $dataWPLabuan = Account::where('billing_address_state', $wplabuan)
                    ->select('id', 'billing_address_state')
                    ->get();

            if (count($dataWPLabuan) > 0) {
                self::updateWPLabuan($dataWPLabuan);
                var_dump('Count State- ' . $wplabuan . ' : ' . count($dataWPLabuan));
            }

            //check for WP CYBERJAYA start
            $dataWPCyberjaya = Account::where('billing_address_state', $wpcyberjaya)
                    ->select('id', 'billing_address_state')
                    ->get();

            if (count($dataWPCyberjaya) > 0) {
                self::updateWPCyberjaya($dataWPCyberjaya);
                var_dump('Count State- ' . $wpcyberjaya . ' : ' . count($dataWPCyberjaya));
            }

            //check for PUTRAJAYA start
            $dataPutrajaya = Account::where('billing_address_state', $putrajaya)
                    ->select('id', 'billing_address_state')
                    ->get();

            if (count($dataPutrajaya) > 0) {
                self::updatePutrajaya($dataPutrajaya);
                var_dump('Count State- ' . $putrajaya . ' : ' . count($dataPutrajaya));
            }

            //check for LUAR NEGERI start
            $dataLuarNegeri = Account::where('billing_address_state', $luarnegeri)
                    ->select('id', 'billing_address_state')
                    ->get();

            if (count($dataLuarNegeri) > 0) {
                self::updateLuarNegeri($dataLuarNegeri);
                var_dump('Count State- ' . $luarnegeri . ' : ' . count($dataLuarNegeri));
            }

            //check for NOT DEFINED, -1 start
            $dataNotDefined = Account::whereIn('billing_address_state', [$notdefined, $negativeone])
                    ->select('id', 'billing_address_state')
                    ->get();

            if (count($dataNotDefined) > 0) {
                self::updateNotDefined($dataNotDefined);
                var_dump('Count State- ' . $notdefined . ' : ' . count($dataNotDefined));
                var_dump('Count State- ' . $negativeone . ' : ' . count($dataNotDefined));
            }
        }
        Log::info(self::class . '   fixedAddressState COMPLETED, Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function updateKL($data) {
        $counter = 0;
        foreach ($data as $row) {
            $count = $counter++;
            DB::table('accounts')
                    ->where('id', $row->id)
                    ->update(['billing_address_state' => 10001]);
            var_dump('State : Wilayah Persekutuan (K.L) - ' . $count . '). Account Id :- ' . $row->id);
            Log::info('State : Wilayah Persekutuan (K.L) - ' . $count . '). Account Id :- ' . $row->id);
        }
        Log::info("Total Update State : Wilayah Persekutuan (K.L) - " . $counter);
    }

    private static function updateMelaka($data) {
        $counter = 0;
        foreach ($data as $row) {
            $count = $counter++;
            DB::table('accounts')
                    ->where('id', $row->id)
                    ->update(['billing_address_state' => 10002]);
            var_dump('State : Melaka - ' . $count . '). Account Id :- ' . $row->id);
            Log::info('State : Melaka - ' . $count . '). Account Id :- ' . $row->id);
        }
        Log::info("Total Update State : Melaka - " . $counter);
    }

    private static function updateJohor($data) {
        $counter = 0;
        foreach ($data as $row) {
            $count = $counter++;
            DB::table('accounts')
                    ->where('id', $row->id)
                    ->update(['billing_address_state' => 10003]);
            var_dump('State : Johor - ' . $count . '). Account Id :- ' . $row->id);
            Log::info('State : Johor - ' . $count . '). Account Id :- ' . $row->id);
        }
        Log::info("Total Update State : Johor - " . $counter);
    }

    private static function updateSelangor($data) {
        $counter = 0;
        foreach ($data as $row) {
            $count = $counter++;
            DB::table('accounts')
                    ->where('id', $row->id)
                    ->update(['billing_address_state' => 10004]);
            var_dump('State : Selangor - ' . $count . '). Account Id :- ' . $row->id);
            Log::info('State : Selangor - ' . $count . '). Account Id :- ' . $row->id);
        }
        Log::info("Total Update State : Selangor - " . $counter);
    }

    private static function updatePenang($data) {
        $counter = 0;
        foreach ($data as $row) {
            $count = $counter++;
            DB::table('accounts')
                    ->where('id', $row->id)
                    ->update(['billing_address_state' => 10005]);
            var_dump('State : Pulau Pinang - ' . $count . '). Account Id :- ' . $row->id);
            Log::info('State : Pulau Pinang - ' . $count . '). Account Id :- ' . $row->id);
        }
        Log::info("Total Update State : Pulau Pinang - " . $counter);
    }

    private static function updateNSembilan($data) {
        $counter = 0;
        foreach ($data as $row) {
            $count = $counter++;
            DB::table('accounts')
                    ->where('id', $row->id)
                    ->update(['billing_address_state' => 10006]);
            var_dump('State : Negeri Sembilan - ' . $count . '). Account Id :- ' . $row->id);
            Log::info('State : Negeri Sembilan - ' . $count . '). Account Id :- ' . $row->id);
        }
        Log::info("Total Update State : Negeri Sembilan - " . $counter);
    }

    private static function updatePahang($data) {
        $counter = 0;
        foreach ($data as $row) {
            $count = $counter++;
            DB::table('accounts')
                    ->where('id', $row->id)
                    ->update(['billing_address_state' => 10007]);
            var_dump('State : Pahang - ' . $count . '). Account Id :- ' . $row->id);
            Log::info('State : Pahang - ' . $count . '). Account Id :- ' . $row->id);
        }
        Log::info("Total Update State : Pahang - " . $counter);
    }

    private static function updatePerak($data) {
        $counter = 0;
        foreach ($data as $row) {
            $count = $counter++;
            DB::table('accounts')
                    ->where('id', $row->id)
                    ->update(['billing_address_state' => 10008]);
            var_dump('State : Perak - ' . $count . '). Account Id :- ' . $row->id);
            Log::info('State : Perak - ' . $count . '). Account Id :- ' . $row->id);
        }
        Log::info("Total Update State : Perak - " . $counter);
    }

    private static function updateSabah($data) {
        $counter = 0;
        foreach ($data as $row) {
            $count = $counter++;
            DB::table('accounts')
                    ->where('id', $row->id)
                    ->update(['billing_address_state' => 10009]);
            var_dump('State : Sabah - ' . $count . '). Account Id :- ' . $row->id);
            Log::info('State : Sabah - ' . $count . '). Account Id :- ' . $row->id);
        }
        Log::info("Total Update State : Sabah - " . $counter);
    }

    private static function updateSarawak($data) {
        $counter = 0;
        foreach ($data as $row) {
            $count = $counter++;
            DB::table('accounts')
                    ->where('id', $row->id)
                    ->update(['billing_address_state' => 10010]);
            var_dump('State : Sarawak - ' . $count . '). Account Id :- ' . $row->id);
            Log::info('State : Sarawak - ' . $count . '). Account Id :- ' . $row->id);
        }
        Log::info("Total Update State : Sarawak - " . $counter);
    }

    private static function updatePerlis($data) {
        $counter = 0;
        foreach ($data as $row) {
            $count = $counter++;
            DB::table('accounts')
                    ->where('id', $row->id)
                    ->update(['billing_address_state' => 10011]);
            var_dump('State : Perlis - ' . $count . '). Account Id :- ' . $row->id);
            Log::info('State : Perlis - ' . $count . '). Account Id :- ' . $row->id);
        }
        Log::info("Total Update State : Perlis - " . $counter);
    }

    private static function updateKedah($data) {
        $counter = 0;
        foreach ($data as $row) {
            $count = $counter++;
            DB::table('accounts')
                    ->where('id', $row->id)
                    ->update(['billing_address_state' => 10012]);
            var_dump('State : Kedah - ' . $count . '). Account Id :- ' . $row->id);
            Log::info('State : Kedah - ' . $count . '). Account Id :- ' . $row->id);
        }
        Log::info("Total Update State : Kedah - " . $counter);
    }

    private static function updateKelantan($data) {
        $counter = 0;
        foreach ($data as $row) {
            $count = $counter++;
            DB::table('accounts')
                    ->where('id', $row->id)
                    ->update(['billing_address_state' => 10013]);
            var_dump('State : Kelantan - ' . $count . '). Account Id :- ' . $row->id);
            Log::info('State : Kelantan - ' . $count . '). Account Id :- ' . $row->id);
        }
        Log::info("Total Update State : Kelantan - " . $counter);
    }

    private static function updateTerengganu($data) {
        $counter = 0;
        foreach ($data as $row) {
            $count = $counter++;
            DB::table('accounts')
                    ->where('id', $row->id)
                    ->update(['billing_address_state' => 10014]);
            var_dump('State : Terengganu - ' . $count . '). Account Id :- ' . $row->id);
            Log::info('State : Terengganu - ' . $count . '). Account Id :- ' . $row->id);
        }
        Log::info("Total Update State : Terengganu - " . $counter);
    }

    private static function updateWPLabuan($data) {
        $counter = 0;
        foreach ($data as $row) {
            $count = $counter++;
            DB::table('accounts')
                    ->where('id', $row->id)
                    ->update(['billing_address_state' => 10015]);
            var_dump('State : WP Labuan - ' . $count . '). Account Id :- ' . $row->id);
            Log::info('State : WP Labuan - ' . $count . '). Account Id :- ' . $row->id);
        }
        Log::info("Total Update State : WP Labuan - " . $counter);
    }

    private static function updateWPCyberjaya($data) {
        $counter = 0;
        foreach ($data as $row) {
            $count = $counter++;
            DB::table('accounts')
                    ->where('id', $row->id)
                    ->update(['billing_address_state' => 10016]);
            var_dump('State : WP Cyberjaya - ' . $count . '). Account Id :- ' . $row->id);
            Log::info('State : WP Cyberjaya - ' . $count . '). Account Id :- ' . $row->id);
        }
        Log::info("Total Update State : WP Cyberjaya - " . $counter);
    }

    private static function updatePutrajaya($data) {
        $counter = 0;
        foreach ($data as $row) {
            $count = $counter++;
            DB::table('accounts')
                    ->where('id', $row->id)
                    ->update(['billing_address_state' => 10017]);
            var_dump('State : Putrajaya - ' . $count . '). Account Id :- ' . $row->id);
            Log::info('State : Putrajaya - ' . $count . '). Account Id :- ' . $row->id);
        }
        Log::info("Total Update State : Putrajaya - " . $counter);
    }

    private static function updateLuarNegeri($data) {
        $counter = 0;
        foreach ($data as $row) {
            $count = $counter++;
            DB::table('accounts')
                    ->where('id', $row->id)
                    ->update(['billing_address_state' => 10018]);
            var_dump('State : Luar Negeri - ' . $count . '). Account Id :- ' . $row->id);
            Log::info('State : Luar Negeri - ' . $count . '). Account Id :- ' . $row->id);
        }
        Log::info("Total Update State : Luar Negeri - " . $counter);
    }

    private static function updateNotDefined($data) {
        $counter = 0;
        foreach ($data as $row) {
            $count = $counter++;
            DB::table('accounts')
                    ->where('id', $row->id)
                    ->update(['billing_address_state' => 10019]);
            var_dump('State : Not Defined/-1 - ' . $count . '). Account Id :- ' . $row->id);
            Log::info('State : Not Defined/-1 - ' . $count . '). Account Id :- ' . $row->id);
        }
        Log::info("Total Update State : Not Defined/-1- " . $counter);
    }

    private static function fixedCasesResolutionForStatusCancelledEaduan() {
        $dtStartTime = Carbon::now();
        $take = 500;
        $skip = 500;
        $count = 0;
        do {
            $dtStartTimeSave = Carbon::now();
            Log::info(self::class . ' > ' .__FUNCTION__ . ' > Query Cases: counter ' . $count);
            $sql = DB::table('cases')
                    ->where('state', 'Closed')
                    ->where('status', 'Closed_Cancelled_Eaduan') 
                    ->where('resolution', 'not like', "%Cancel Remark%")
                   // ->where('case_number',4509066)
                    ->take($take)
                    ->skip($skip * $count++);
            $result = $sql->get();
            foreach ($result as $row) {
                //get latest task before user cancelled  
                $tasks = DB::table('tasks')
                ->join('tasks_cstm','tasks.id','=','tasks_cstm.id_c')
                ->where('created_by', '<>' ,'96fa954b-8288-4bef-ac39-5338a5b5c9c0')
                ->where('tasks.parent_id',$row->id)
                ->orderBy('date_entered','desc')->first();
                if ($tasks && $tasks->id != '') {
                    $taskResolution = $tasks->resolution_c;
                    $newReso = "$taskResolution
------
Cancel Remark:
$row->resolution";
                    DB::table('cases')
                    ->where('id', $row->id)
                    ->update(['resolution' => $newReso]); 
                    Log::info(self::class . '  > ' .__FUNCTION__ .' > Case Number > ' .$row->case_number .' > COUNTER ' . $count . ' , Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTimeSave)]);
                } 
 
            }
            Log::info(self::class . '  > ' .__FUNCTION__ .'> COUNTER ' . $count . ' , Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTimeSave)]);
        } while (count($result) == $take);

        Log::info(self::class .'  > ' .__FUNCTION__ . ' > COMPLETED, Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

}
