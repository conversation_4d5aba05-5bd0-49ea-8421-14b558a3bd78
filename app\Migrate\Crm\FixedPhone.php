<?php

namespace App\Migrate\Crm;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Facades\DB;
use App\Models\Account;
use App\Models\Contact;
use App\Models\AccountCustom;
use App\Models\ContactCustom;
use App\Models\LogIntegration;
use App\Models\EmailAddress;
use App\Migrate\MigrateUtils;
use Config;
use Excel;

class FixedPhone {

    public static function runFixedPhone() {
        Log::debug(self::class . ' Starting ... runFixedData');
        $dtStartTime = Carbon::now();

        self::fixedPhoneNumber();

        Log::info(self::class . ' Completed runFixedData --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        var_dump(self::class . ' Completed runFixedData --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function fixedPhoneNumber() {

        $take = 1000;
        $skip = 1000;
        $countAcc = 0;
        $countContacts = 0;

        $dtStartTime = Carbon::now();  
            
        do {

            //check for accounts start
            $phoneAccount = Account::whereNotIn('phone_office', ['NULL', 'N/A', 'NA'])
                    ->take($take)
                    ->skip($skip * $countAcc++);

            $resultAccount = $phoneAccount->get();

            if (count($resultAccount) > 0) {
                self::checkPhone($resultAccount);
            }
        } while (count($resultAccount) == $take);
        
        do {

            //check for accounts start
            $phoneContact = Contact::whereNotIn('phone_mobile', ['NULL', 'N/A', 'NA'])
                    ->orWhereNotIn('phone_work', ['N/A', 'NA'])
                    ->take($take)
                    ->skip($skip * $countContacts++);

            $resultContact = $phoneContact->get();

            if (count($resultContact) > 0) {
                self::checkPhone($resultContact);
            }
        } while (count($resultContact) == $take);
        
        var_dump('fixedPhoneNumber , COUNTER ACCOUNT:' . $countAcc . ' , COUNTER CONTACTS:'.$countContacts.' , Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info('fixedPhoneNumber , COUNTER ACCOUNT:' . $countAcc . ' , COUNTER CONTACTS:'.$countContacts.' , Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function checkPhone($data) {
        $counter = 0;

        foreach ($data as $row) {
            $count = $counter++;
            if ((preg_match('/\-/', $row->phone_office)) || (preg_match('/\-/', $row->phone_mobile)) || (preg_match('/\-/', $row->phone_work))) {
                self::updatePhoneDash($row, $count);
            }
            if ((preg_match('/\+/', $row->phone_office)) || (preg_match('/\+/', $row->phone_mobile)) || (preg_match('/\+/', $row->phone_work))) {
                self::updatePhonePlus($row, $count);
            }
            if ((preg_match('/\'/', $row->phone_office)) || (preg_match('/\+/', $row->phone_mobile)) || (preg_match('/\'/', $row->phone_work))) {
                self::updatePhone($row, $count);
            }
            if ((preg_match('/\ /', $row->phone_office)) || (preg_match('/\ /', $row->phone_mobile)) || (preg_match('/\ /', $row->phone_work))) {
                self::updatePhoneSpace($row, $count);
            }
            if ((preg_match('/^60/', $row->phone_office)) || (preg_match('/\+60/', $row->phone_office)) || (preg_match('/^60/', $row->phone_mobile)) || (preg_match('/\+60/', $row->phone_mobile)) || (preg_match('/\+60/', $row->phone_work)) || (preg_match('/^60/', $row->phone_work))) {
                self::updatePhoneSix($row, $count);
            }
        }
    }

    private static function updatePhoneDash($data, $counter) {
        var_dump(__FUNCTION__.' > checking..');
        $newPhone = '';
        $newPhoneWork = '';

        if ($data->phone_office) {
            $newPhone = str_replace('-', '', $data->phone_office);
            self::savePhone($data, $newPhone, $counter, $newPhoneWork);
        }

        if ($data->phone_mobile || $data->phone_work) {
            if ($data->phone_mobile) {
                $newPhone = str_replace('-', '', $data->phone_mobile);
            }
            if ($data->phone_work) {
                $newPhoneWork = str_replace('-', '', $data->phone_work);
            }
            if (($data->phone_mobile) && ($data->phone_work)) {

                $newPhone = str_replace('-', '', $data->phone_mobile);
                $newPhoneWork = str_replace('-', '', $data->phone_work);
            }

            self::savePhone($data, $newPhone, $counter, $newPhoneWork);
        }
    }
    
    private static function updatePhone($data, $counter) {
        var_dump(__FUNCTION__.' > checking.. single tanduk ');
        $newPhone = '';
        $newPhoneWork = '';

        if ($data->phone_office) {
            $newPhone = str_replace("'", '', $data->phone_office);
            self::savePhone($data, $newPhone, $counter, $newPhoneWork);
        }

        if ($data->phone_mobile || $data->phone_work) {
            if ($data->phone_mobile) {
                $newPhone = str_replace("'", '', $data->phone_mobile);
            }
            if ($data->phone_work) {
                $newPhoneWork = str_replace("'", '', $data->phone_work);
            }
            if (($data->phone_mobile) && ($data->phone_work)) {

                $newPhone = str_replace("'", '', $data->phone_mobile);
                $newPhoneWork = str_replace("'", '', $data->phone_work);
            }

            self::savePhone($data, $newPhone, $counter, $newPhoneWork);
        }
    }

    private static function updatePhonePlus($data, $counter) {
        var_dump(__FUNCTION__.' > checking..  (+) ');
        $newPhone = '';
        $newPhoneWork = '';

        if ($data->phone_office) {
            $newPhone = str_replace('+', '', $data->phone_office);
            self::savePhone($data, $newPhone, $counter, $newPhoneWork);
        }

        if ($data->phone_mobile || $data->phone_work) {
            if ($data->phone_mobile) {
                $newPhone = str_replace('+', '', $data->phone_mobile);
            }
            if ($data->phone_work) {
                $newPhoneWork = str_replace('+', '', $data->phone_work);
            }
            if (($data->phone_mobile) && ($data->phone_work)) {

                $newPhone = str_replace('+', '', $data->phone_mobile);
                $newPhoneWork = str_replace('+', '', $data->phone_work);
            }

            self::savePhone($data, $newPhone, $counter, $newPhoneWork);
        }
    }

    private static function updatePhoneSpace($data, $counter) {
        var_dump(__FUNCTION__.' > checking..');
        $newPhone = '';
        $newPhoneWork = '';

        if ($data->phone_office) {
            $newPhone = str_replace(' ', '', $data->phone_office);
            self::savePhone($data, $newPhone, $counter, $newPhoneWork);
        }

        if ($data->phone_mobile || $data->phone_work) {
            if ($data->phone_mobile) {
                $newPhone = str_replace(' ', '', $data->phone_mobile);
            }
            if ($data->phone_work) {
                $newPhoneWork = str_replace(' ', '', $data->phone_work);
            }
            if (($data->phone_mobile) && ($data->phone_work)) {

                $newPhone = str_replace(' ', '', $data->phone_mobile);
                $newPhoneWork = str_replace(' ', '', $data->phone_work);
            }

            self::savePhone($data, $newPhone, $counter, $newPhoneWork);
        }
    }

    private static function updatePhoneSix($data, $counter) {
        var_dump(__FUNCTION__.' > checking..');
        $newPhone = '';
        $newPhoneWork = '';

        if ((strlen($data->phone_office) > 8) && (strlen($data->phone_office) < 13)) {

            if ($data->phone_office) {
                $newPhone = substr($data->phone_office, 1);
                self::savePhone($data, $newPhone, $counter, $newPhoneWork);
            }
        } else {
            
            var_dump('Weird Phone Number Length : Account Id :' . $data->id . ' Phone Office :' . $data->phone_office);
        }


        if (((strlen($data->phone_mobile) > 8) && (strlen($data->phone_mobile) < 13)) || ((strlen($data->phone_work) > 8) && (strlen($data->phone_work) < 13))) {

            if ((preg_match('/^60/', $data->phone_mobile)) || (preg_match('/^60/', $data->phone_work))) {
                if ($data->phone_mobile) {
                    $newPhone = ltrim($data->phone_mobile, '6');
                }
                if ($data->phone_work) {
                    $newPhoneWork = ltrim($data->phone_work, '6');
                }
                if (($data->phone_mobile) && ($data->phone_work)) {

                    $newPhone = ltrim($data->phone_mobile, '6');
                    $newPhoneWork = ltrim($data->phone_work, '6');
                }

                self::savePhone($data, $newPhone, $counter, $newPhoneWork);
            }


            if ((preg_match('/\+60/', $data->phone_mobile)) || (preg_match('/\+60/', $data->phone_work))) {
                if ($data->phone_mobile) {
                    $newPhone = ltrim($data->phone_mobile, '+6');
                }
                if ($data->phone_work) {
                    $newPhoneWork = ltrim($data->phone_work, '+6');
                }
                if (($data->phone_mobile) && ($data->phone_work)) {

                    $newPhone = ltrim($data->phone_mobile, '+6');
                    $newPhoneWork = ltrim($data->phone_work, '+6');
                }

                self::savePhone($data, $newPhone, $counter, $newPhoneWork);
            }
        } else {
            var_dump('Weird Phone Number Length : Contact Id :' . $data->id . ' Phone Mobile :' . $data->phone_mobile);
            var_dump('Weird Phone Number Length : Contact Id :' . $data->id . ' Phone Work :' . $data->phone_work);
        }
    }

    private static function savePhone($data, $newPhone, $counter, $newPhoneWork) {
        if ($data->phone_office) {
            //update table accounts
            DB::table('accounts')
                    ->where('id', $data->id)
                    ->update(['phone_office' => $newPhone]);
            var_dump(__FUNCTION__.'> Count : ' . $counter . ' Account Id :' . $data->id . ' Old Phone Office :' . $data->phone_office . ' New Phone Office :' . $newPhone);
        }

        if ($data->phone_mobile || $data->phone_work) {
            if ($data->phone_mobile) {
                var_dump(__FUNCTION__.'> Count : ' . $counter . ' Contact Id :' . $data->id . ' Old Phone Mobile :' . $data->phone_mobile . ' New Phone Mobile :' . $newPhone);
            }
            if ($data->phone_work) {
                var_dump(__FUNCTION__.'> Count : ' . $counter . ' Contact Id :' . $data->id . ' Old Phone Work :' . $data->phone_work . ' New Phone Work :' . $newPhoneWork);
            }
            if ($data->phone_work && $data->phone_mobile) {
                var_dump(__FUNCTION__.'> Count : ' . $counter . ' Contact Id :' . $data->id . ' Old Phone Mobile :' . $data->phone_mobile . ' New Phone Mobile :' . $newPhone);
                var_dump(__FUNCTION__.'> Count : ' . $counter . ' Contact Id :' . $data->id . ' Old Phone Work :' . $data->phone_work . ' New Phone Work :' . $newPhoneWork);
            }
            //update contacts
            DB::table('contacts')
                    ->where('id', $data->id)
                    ->update(['phone_mobile' => $newPhone, 'phone_work' => $newPhoneWork]);
        }
    }

}
