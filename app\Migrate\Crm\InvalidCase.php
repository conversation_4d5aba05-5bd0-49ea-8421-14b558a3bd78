<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\Crm;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Migrate\MigrateUtils;
use App\Services\CRMService;

class InvalidCase {

    public static function crmService() {
        return new CRMService;
    }

    public static function run() {
        Log::debug(self::class . ' Starting ... run');
        $dtStartTime = Carbon::now();

        self::checkInvalidCase();

        Log::info(self::class . ' Completed run --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        var_dump(self::class . ' Completed run --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function checkInvalidCase() {
        
        $case = DB::select("SELECT c.id,c.case_number, c.date_entered, c.date_modified,
                                    c.name, c.description, c.resolution, c.account_id,c.doc_no,
                                    cc.category_c,cc.contact_id_c,cc.contact_mode_c,cc.request_type_c
                                    FROM cases c, cases_cstm cc
                                    WHERE c.id = cc.id_c
                                    AND cc.contact_mode_c = '' AND c.deleted = 0
                                    AND cc.category_c NOT IN (10719,10720,10722,10721)");

        $total = count($case);

        if ($total > 0) {

            foreach ($case as $row) {
                $caseId = $row->id;
                $caseNumber = $row->case_number;
                $caseName = $row->name;
                $caseDescription = $row->description;
                $caseResolution = $row->resolution;
                $caseCategory = $row->category_c;
                $caseRequestType = $row->request_type_c;
                $caseAccountId = $row->account_id;
                $caseContactId = $row->contact_id_c;
                $caseReference = self::crmService()->getDetailCase($caseNumber, $caseName, $caseResolution);
                if (isset($caseReference)) {
                    //check duplicate case 
                    $caseRefNumber = $caseReference->case_number;
                    $caseRefName = $caseReference->name;
                    $caseRefDescription = $caseReference->description;
                    $caseRefResolution = $caseReference->resolution;
                    $caseRefCategory = $caseReference->category_c;
                    $caseRefRequestType = $caseReference->request_type_c;
                    $caseRefAccountId = $row->account_id;
                    $caseRefContactId = $row->contact_id_c;

                    if ($caseName == $caseRefName && $caseDescription == $caseRefDescription && $caseResolution == $caseRefResolution && $caseAccountId == $caseRefAccountId && $caseContactId == $caseRefContactId) {
                        // update case status to deleted
                        DB::table('cases')
                                ->where('id', $caseId)
                                ->update([
                                    'deleted' => 1
                        ]);
                        var_dump(self::class . __FUNCTION__ . '. Delete case ' . $caseNumber . '. Auto create from ' . $caseRefNumber);
                        Log::info(self::class . __FUNCTION__ . '. Delete case ' . $caseNumber . '. Auto create from ' . $caseRefNumber);
                    }
                }
            }
        }
    }

    public static function checkSpamEmailCases() {
        Log::debug(self::class . ' > '  . __FUNCTION__ .' : Starting....run ');
        $dtStartTime = Carbon::now();
        
        $case = DB::select("SELECT * FROM cases c
                                WHERE c.name = '[Emailed] : Undeliverable: German Sex Industry Penis Ritual Leaked'
                                AND c.status = 'Open_Pending Input'
                                AND c.deleted = 0");

        $total = count($case);

        if ($total > 0) {

            foreach ($case as $row) {

                // update case status to deleted
                DB::table('cases')
                        ->where('id', $row->id)
                        ->update([
                            'deleted' => 1
                ]);
                var_dump(self::class . __FUNCTION__ . '. Delete case ' . $row->case_number);
                Log::info(self::class . __FUNCTION__ . '. Delete case ' . $row->case_number);
            }
        }
        Log::info(self::class . ' > '  . __FUNCTION__ .' Completed run --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        var_dump(self::class . ' > '  . __FUNCTION__ .' Completed run --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);

    }

}
