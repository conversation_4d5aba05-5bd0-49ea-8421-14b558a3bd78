<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\Crm;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Excel;
use App\Models\Tasks;
use App\Models\TaskCustom;
use App\Models\Cases;
use Ramsey\Uuid\Uuid;
use App\Migrate\MigrateUtils;
use App\Services\EPService;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Services\CRMService;

class ModifyCase {

    /*
    Group IT Coordinator  			bd305f97-902e-e186-f506-58997eeecc12
    Archissoft Build Team			15c7bd74-12f3-311b-9e8f-5a810702a1a5
    Group Middleware                            5f919eef-f9cc-4278-b7d1-bbc1aa87ff6e
    Group IT Specialist(Production Support)	d3bf216c-122b-4ce5-9410-899317762b60
    Group IT Specialist(Network Admin)          bb59c521-4a4d-a001-a8aa-58d09f694ae7
    Group IT Specialist(Server Admin)           825b06f5-d0b6-e6da-0d56-58d0a0bca1e4
    Group IT Specialist(Developer)		5dac7b92-18d0-4beb-b600-413957aa4c26
    Group IT Specialist(Security)		86dde498-da2d-4208-a7dd-786980e6827a
    */
    protected static $listGroupSelectedID =   "('bd305f97-902e-e186-f506-58997eeecc12','15c7bd74-12f3-311b-9e8f-5a810702a1a5',
                                        '5f919eef-f9cc-4278-b7d1-bbc1aa87ff6e',
                                        'd3bf216c-122b-4ce5-9410-899317762b60',
                                        'bb59c521-4a4d-a001-a8aa-58d09f694ae7',
                                        '825b06f5-d0b6-e6da-0d56-58d0a0bca1e4',
                                        '5dac7b92-18d0-4beb-b600-413957aa4c26',
                                        '86dde498-da2d-4208-a7dd-786980e6827a')";
    
    // Group Change & Release Mgmt 26e52e3a-5947-696f-9cea-5ab9b76c48b4
    protected static $listGroupForDBManagement = "('26e52e3a-5947-696f-9cea-5ab9b76c48b4')";
    
    protected static $groupIdItCoordinator = "('bd305f97-902e-e186-f506-58997eeecc12')";
    
    public static function crmService() {
        return new CRMService;
    }
    
    public static function run() {
        ini_set('memory_limit', '-1');
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();
        self::updateExcel();
        
        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
   
        
    }
    
    protected static function  getCasesDetail($case_no){
        $cases = DB::select("SELECT 
            a.id AS case_id,a.name,a.case_number,a.status AS case_status,a.date_entered,
            a.description AS case_description,a.resolution AS case_resolution,
            c.id AS task_id ,c.name AS task_name,
            cc.task_number_c ,cc.resolution_c ,c.status AS task_status,
            c.date_start,c.date_due,
            cc.acknowledge_time_c,cc.sla_start_15min_c,cc.sla_stop_15min_c,
            cc.sla_start_4hr_c,cc.sla_stop_4hr_c,cc.date_execution_time_c ,cc.acknowledge_by_userid_c 
            FROM cases a, cases_cstm b  , tasks c, tasks_cstm cc 
            WHERE a.id=b.id_c 
            AND c.id=cc.id_c 
            AND a.id = c.parent_id 
            AND cc.task_number_c IN ( SELECT MAX(tb.task_number_c) FROM tasks ta, tasks_cstm tb  WHERE ta.id=tb.id_c AND ta.parent_id = a.id  AND ta.deleted = 0 )                      
            AND a.case_number = ?", 
            array($case_no)) ; 
        if(count($cases) > 0){
            return $cases[0];
        }
        return null;
        
    }
    
    protected static function  updateTaskCaseToResolve($caseObj,$newResolution,$typeTask=null,$actionBy = '61795c8d-9bbf-46eb-9187-dc59f49101bf' ){
        // update case Completed
        if($typeTask=='4HOUR'){
            DB::table('tasks')
                ->where('id', $caseObj->task_id)
                ->update([
                    'status'                    => 'Completed',
                    'date_start'                => Carbon::now()->subHour(8),
                    'date_due'                  => Carbon::now()->subHour(8)->addHour(4),
                    'task_justification'        => 'technical',
                    'date_modified'     => Carbon::now()->subHour(8),
                    'modified_user_id'  => $actionBy, //mohdshamsul
                   ]);
            DB::table('tasks_cstm')
                ->where('id_c', $caseObj->task_id)
                ->update([
                    'resolution_c'              => $newResolution,
                    'sla_start_4hr_c'           => Carbon::now()->subHour(8),
                    'sla_stop_4hr_c'            => Carbon::now()->subHour(8)->addMinute(1),
                    'date_execution_time_c'     => Carbon::now()->subHour(8)->addMinute(1),
                    'sla_task_flag_c'           => '3',
                    'acknowledge_time_c'        => Carbon::now()->subHour(8),
                    'acknowledge_by_userid_c'   => $actionBy, //mohdshamsul
                    'category_factor_c'         => 'external_factor'
                   ]);
        }else{
            DB::table('tasks')
                ->where('id', $caseObj->task_id)
                ->update([
                    'status'            => 'Completed',
                    'date_modified'     => Carbon::now()->subHour(8),
                    'modified_user_id'  => $actionBy,
                   ]);
            DB::table('tasks_cstm')
                ->where('id_c', $caseObj->task_id)
                ->update([
                    'resolution_c'  => $newResolution
                   ]);
        }
        
        $newTask = new Tasks;
        $newTask->id =Uuid::uuid4()->toString();
        $newTask->name = 'Assigned to Case Owner';
        $newTask->description = $caseObj->case_description;
        $newTask->date_entered = Carbon::now()->subHour(8);
        $newTask->date_modified = Carbon::now()->subHour(8);
        $newTask->modified_user_id = $actionBy; //mohdshamsul
        $newTask->created_by = $actionBy; //mohdshamsul
        $newTask->deleted = 0;
        $newTask->assigned_user_id = '2732c16c-1fff-9e6c-bb4e-58997edd3340';  //Group Business Coordinator
        $newTask->status = 'Pending Acknowledgement';
        $newTask->date_due_flag = 0;
        $newTask->date_start = Carbon::now()->subHour(8);
        $newTask->date_due = Carbon::now()->subHour(8)->addMinutes(15); // NOW() + 15 minutes
        $newTask->date_start_flag = 0;
        $newTask->parent_type = 'Cases';
        $newTask->parent_id = $caseObj->case_id;
        $newTask->priority = 'Low';
        $newTask->task_justification = 'technical';
        $newTask->save();

        $newTaskCustom = new TaskCustom;
        $newTaskCustom->id_c = $newTask->id;
        $newTaskCustom->assign_group_c = 'Case Owner';
        $newTaskCustom->resolution_c = $newResolution;
        $newTaskCustom->sla_flag_c = 0;
        //$maxTaskNumber = DB::select("SELECT MAX(task_number_c) as max_task_number FROM tasks_cstm"); 
        //$newTaskCustom->task_number_c = (intval($maxTaskNumber[0]->max_task_number)+1); //auto increment
        $newTaskCustom->checkbox_add_day_c = 0;
        $newTaskCustom->category_factor_c = 'external_factor';
        $newTaskCustom->save();

        $caseModel = Cases::find($caseObj->case_id);
        $caseModel->date_modified =  Carbon::now()->subHour(8);
        $caseModel->modified_user_id = $actionBy; //mohdshamsul
        $caseModel->status = 'Open_Resolved';
        $caseModel->resolution = $newResolution;
        $caseModel->save();
        
        $caseResultObj = self::getCasesDetail($caseObj->case_number);
        dump('successfully task assign to case owner .. Task Number: '.$caseResultObj->task_number_c);
        dump('case Number: '.$caseResultObj->case_number.' ,status: '.$caseResultObj->case_status);
        dump($caseResultObj);
        
        Log::info('successfully task assign to case owner .. Task Number: '.$caseResultObj->task_number_c);
        Log::info('case Number: '.$caseResultObj->case_number.' ,status: '.$caseResultObj->case_status);
        Log::info(json_encode($caseResultObj));
    }

    /**
     * check Task Missing from eP Support System. Status done will set resolve task in CRM.
     */
    public static function resolveTask(){
        
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        dump(self::class . ' Starting ... ' . __FUNCTION__);
        
        $listTaskMissing = DB::connection('mysql_ep_support')
                ->table('ep_task_missing')
                ->whereIn('case_status', ['In_Progress','Open_Assigned'])
                ->where('process_status', '55')
                ->select('task_id', 'case_no', 'case_status', 'doc_no', 'resolution', 'process_status','completed_by','updated_by')
                ->orderBy('case_created', 'desc')
                ->get();
        $beginResolution = 'Results from investigation:'
                                            . '
                            ';
        $counter = 0;
        foreach($listTaskMissing as $obj){
            $newResolution = $beginResolution.trim($obj->resolution);
            $caseObj = self::getCasesDetail($obj->case_no);
            
            /* Get User ID CRM */
            $userName = $obj->completed_by;
            if($userName == null){
                $userName = $obj->updated_by;
            }
            $userCrm = DB::table('users')->where('user_name',$userName)->first();
            $userIdCRM = "61795c8d-9bbf-46eb-9187-dc59f49101bf";  //mohdshamsul
            if($userCrm != null){
               $userIdCRM = $userCrm->id;
            }
            
            
            if($caseObj && ( $caseObj->case_status == 'In_Progress' || $caseObj->case_status == 'Open_Assigned')){
                
                //Checking If Task at Initial Task 
                if (strpos($caseObj->task_name, 'Initial Task') !== false) {
                    $isNotRelated = strtoupper(trim($obj->resolution));
                    if (strpos($isNotRelated, 'NOT RELATED') !== false) {  
                    }else{
                        if( strlen(trim($obj->resolution)) > 0){
                            dump('###################### Initial Task');
                            dump($obj);
                            self::updateTaskCaseToResolve($caseObj, $newResolution, null, $userIdCRM);
                            $counter++;
                            //dd('completed');
                        }
                    }
                }else{
                    if($counter<=50){
                        $isNotRelated = strtoupper(trim($obj->resolution));
                        if (strpos($isNotRelated, 'NOT RELATED') !== false) { 
                        }else{
                            if( strlen(trim($obj->resolution)) > 0){
                                dump('###################### Assign to Specialist');
                                dump($obj);
                                self::updateTaskCaseToResolve($caseObj, $newResolution,'4HOUR', $userIdCRM);
                                $counter++;
                                //dd('completed');
                            }
                        }
                    }
                }
            }
            
        }
        dump(__FUNCTION__.' > Total: '.count($listTaskMissing));
        dump(__FUNCTION__.' > Total will updated: '.$counter);
        Log::debug(__FUNCTION__.' > Total: '.count($listTaskMissing));
        Log::debug(__FUNCTION__.' > Total will updated: '.$counter);
        
    }
    
    public static function modifyTaskAssignToCodification(){
        dump('Start initiate '.__FUNCTION__);
        $taskName = 'Assigned to Group Codification';
        $assignToCodification = 'ad116431-c5a8-f8b9-85d3-5a3d41e9e883'; //Group Codification
        $dateStart = '2018-01-01'; 
        $dateEnd= '2018-06-01';
        
        $assignTo = self::$listGroupSelectedID;
        //$assignTo = self::$groupIdItCoordinator;
        $listContractCase = DB::select("SELECT 
	a.id as case_id,a.name as case_name,a.case_number,a.date_entered,d.first_name,
        c.id as task_id , c.name as task_name, cc.task_number_c , tm.value_name 
	FROM cases a, cases_cstm b  , tasks c, tasks_cstm cc , users d  ,
	(
		SELECT 'Manual Contract' AS module,value_code,value_name FROM cstm_list_app WHERE value_name  IN (
		'Lampiran A - Contract',
		'Lampiran C – Items'
		)
	) AS tm 
	WHERE a.id=b.id_c 
        AND c.id=cc.id_c 
        AND a.status in ('Open_Assigned','In_Progress')  
	AND a.id = c.parent_id AND c.assigned_user_id = d.id 
	AND c.date_entered IN ( SELECT MAX(date_entered) FROM tasks WHERE parent_id = a.id ) 
	AND c.assigned_user_id IN $assignTo                         
	AND b.incident_service_type_c IN ('incident_it','service_it') 
	AND tm.value_code = b.sub_category_2_c  
	AND  DATE(CONVERT_TZ(a.date_entered, '+00:00', 'SYSTEM'))
                        BETWEEN STR_TO_DATE(?,'%Y-%m-%d') AND STR_TO_DATE(?,'%Y-%m-%d')", 
        array($dateStart,$dateEnd)) ; 
        
        //dd(count($listContractCase));
        $counter = 0;
        foreach ($listContractCase as $obj){
            //dd($obj);
            $caseObj = self::getCasesDetail($obj->case_number);
            if (strpos($caseObj->task_name, 'Initial Task') !== false) {
                dump('###################### Initial Task');
                dump($caseObj);
                //self::updateAndNewTaskToGroupCodification($caseObj, 'Escalate to Group Codification');
                $counter++;
            }else{
                dump('###################### Specialist Group');
                dump($caseObj);
//                DB::table('tasks')
//                    ->where('id', $obj->task_id)
//                    ->update([
//                        'name' => $taskName,
//                        'assigned_user_id' => $assignToCodification
//                       ]);
                $counter++;
            }
             
        }
        
        dump('Total: '.count($listContractCase));

    }
    
    public static function modifyTaskAssignToDataManagement(){
        dump('Start initiate '.__FUNCTION__);
        $taskName = 'Assigned to Group IT Specialist(Database Management)';
        $assignToDBManagement = '3d74f2af-f673-4263-afc0-d3f89aadf0ef'; //Group Codification
        $dateStart = '2018-01-01'; 
        $dateEnd= '2018-06-01';
        
        $assignTo = self::$listGroupForDBManagement;
        $listContractCase = DB::select("SELECT 
	a.id as case_id,a.name,a.case_number,a.date_entered,c.case_redmine_number,c.status,d.first_name,c.id as task_id ,cc.task_number_c , tm.value_name 
	FROM cases a, cases_cstm b  , tasks c, tasks_cstm cc , users d  ,
	(
		SELECT 'Manual Contract' AS module,value_code,value_name FROM cstm_list_app WHERE value_name  IN (
		'Lampiran A - Contract',
		'Lampiran C – Items'
		)
	) AS tm 
	WHERE a.id=b.id_c 
        AND c.id=cc.id_c 
        AND a.status in ('Open_Assigned','In_Progress') 
	AND a.id = c.parent_id AND c.assigned_user_id = d.id 
	AND c.date_entered IN ( SELECT MAX(date_entered) FROM tasks WHERE parent_id = a.id ) 
	AND c.assigned_user_id IN $assignTo     
	AND b.incident_service_type_c IN ('incident_it','service_it') 
	AND tm.value_code = b.sub_category_2_c  
	AND  DATE(CONVERT_TZ(a.date_entered, '+00:00', 'SYSTEM'))
                        BETWEEN STR_TO_DATE(?,'%Y-%m-%d') AND STR_TO_DATE(?,'%Y-%m-%d')", 
        array($dateStart,$dateEnd)) ; 

        $counter = 0;
        foreach ($listContractCase as $key => $obj){
            if($key < 50){
                dump($obj);
                DB::table('tasks')
                    ->where('id', $obj->task_id)
                    ->update([
                        'name' => $taskName,
                        'assigned_user_id' => $assignToDBManagement
                       ]);

                dump(' Updated  Case No: '.$obj->case_number. ' Task Number: '.$obj->task_number_c);

                $counter++;
            }
            
        }
        dump('Total Updated: '.$counter);
        dump('Total: '.count($listContractCase));

    }
    
    protected static function  updateAndNewTaskToGroupCodification($caseObj,$newResolution){
        // update case Completed

        DB::table('tasks')
            ->where('id', $caseObj->task_id)
            ->update([
                'status'            => 'Completed',
                'date_modified'     => Carbon::now()->subHour(8),
                'modified_user_id'  => '61795c8d-9bbf-46eb-9187-dc59f49101bf',
               ]);
        DB::table('tasks_cstm')
            ->where('id_c', $caseObj->task_id)
            ->update([
                'resolution_c'  => $newResolution
               ]);
        
        
        $newTask = new Tasks;
        $newTask->id =Uuid::uuid4()->toString();
        $newTask->name = 'Assigned to Group Codification';
        $newTask->description = $caseObj->case_description;
        $newTask->date_entered = Carbon::now()->subHour(8);
        $newTask->date_modified = Carbon::now()->subHour(8);
        $newTask->modified_user_id = '61795c8d-9bbf-46eb-9187-dc59f49101bf'; //mohdshamsul
        $newTask->created_by = '61795c8d-9bbf-46eb-9187-dc59f49101bf'; //mohdshamsul
        $newTask->deleted = 0;
        $newTask->assigned_user_id = 'ad116431-c5a8-f8b9-85d3-5a3d41e9e883';   // Group Codification
        $newTask->status = 'Pending Acknowledgement';
        $newTask->date_due_flag = 0;
        $newTask->date_start = Carbon::now()->subHour(8);
        $newTask->date_due = Carbon::now()->subHour(8)->addMinutes(15); // NOW() + 15 minutes
        $newTask->date_start_flag = 0;
        $newTask->parent_type = 'Cases';
        $newTask->parent_id = $caseObj->case_id;
        $newTask->priority = 'Low';
        $newTask->task_justification = 'technical';
        $newTask->save();

        $newTaskCustom = new TaskCustom;
        $newTaskCustom->id_c = $newTask->id;
        $newTaskCustom->assign_group_c = 'Group Codification';
        $newTaskCustom->resolution_c = $newResolution;
        $newTaskCustom->sla_flag_c = 0;
        $newTaskCustom->checkbox_add_day_c = 0;
        $newTaskCustom->category_factor_c = 'external_factor';
        $newTaskCustom->save();

        
        $caseResultObj = self::getCasesDetail($caseObj->case_number);
        dump('successfully task assign to case owner .. Task Number: '.$caseResultObj->task_number_c);
        dump($caseResultObj);
        
        Log::info('successfully task assign to case owner .. Task Number: '.$caseResultObj->task_number_c);
        Log::info('case Number: '.$caseResultObj->case_number.' ,status: '.$caseResultObj->case_status);
        Log::info(json_encode($caseResultObj));
    }
    
    public static function resolveTaskByExcel() {
        $filename = '/app/Migrate/Crm/data/update-withoutbatchno.xlsx';
        //$filename = '/app/Migrate/Crm/data/update-withbatchno.xlsx';
        dump('Reading file ... '.$filename);
        $counter = 0;
        Excel::load($filename, function($reader) use (&$counter) {
            
            $reader->each(function($row)  use (&$counter)  {
                //dd($row);
                $beginResolution = 'Results from investigation:'
                                            . '
                            ';
                $newResolution = $beginResolution.trim($row->remarks);
                $caseObj = self::getCasesDetail($row->crm_case_no);
                //dd($caseObj);
                if($caseObj && ($caseObj->case_status == 'In_Progress' || $caseObj->case_status == 'Open_Assigned' )){
                    if (strpos($caseObj->task_name, 'Initial Task') !== false) {
                        $isNotRelated = strtoupper(trim($row->remarks));
                        if (strpos($isNotRelated, 'NOT RELATED') !== false) {  
                        }else{
                            if( strlen(trim($row->remarks)) > 0){
                                dump('###################### Initial Task');
                                dump($row);
                                self::updateTaskCaseToResolve($caseObj, $newResolution);
                                $counter++;
                                //dd('completed');
                            }
                        }
                    }else{
                        //if($counter<=50){
                            $isNotRelated = strtoupper(trim($row->remarks));
                            if (strpos($isNotRelated, 'NOT RELATED') !== false) { 
                            }else{
                                if( strlen(trim($row->remarks)) > 0){
                                    dump('###################### Assign to Specialist');
                                    dump($row);
                                    self::updateTaskCaseToResolve($caseObj, $newResolution,'4HOUR');
                                    $counter++;
                                    //dd('completed');
                                }
                            }
                        //}
                    }
                }
            });
        });
        
        dump('Total updated: '.$counter);
    }
    
    
    /*
     * command: modify-task-dm-excel
     */
    public static function modifyTaskForDMByExcel(){
        dump('Start initiate '.__FUNCTION__);
        
        /** Update task **/
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change 01-06-2018.xlsx';
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change 04-06-2018.xlsx';
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change 05-06-2018.xlsx';
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change 06-06-2018.xlsx';
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change 07-06-2018.xlsx';
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change 09-06-2018.xlsx';
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change 10-06-2018.xlsx';
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change 12-06-2018.xlsx';
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change 12-06-2018_batch2.xlsx';
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change 21-06-2018.xlsx';
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change 09-07-2018.xlsx';
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change 10-07-2018.xlsx';
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change 18-07-2018.xlsx';
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change 19-07-2018.xlsx';
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change 20-07-2018.xlsx';
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change 23-07-2018.xlsx';
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change 24-07-2018.xlsx';
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change 26-07-2018.xlsx';
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change 04-08-2018.xlsx';
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change 07-08-2018.xlsx';
        
        /** Resolve  task **/
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed.xlsx'; // Status : Completed
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 06-06-2018.xlsx'; // Status : Completed
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 07-06-2018.xlsx'; // Status : Completed
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 09-06-2018.xlsx'; // Status : Completed
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 09-06-2018_batch2.xlsx'; // Status : Completed
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 09-06-2018_batch3.xlsx'; // Status : Completed
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 11-06-2018.xlsx'; // Status : Completed
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 12-06-2018.xlsx'; // Status : Completed
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 12-06-2018_batch2.xlsx'; // Status : Completed
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 13-06-2018.xlsx'; // Status : Completed
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 14-06-2018.xlsx'; // Status : Completed
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 18-06-2018.xlsx'; // Status : Completed
	//$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 21-06-2018.xlsx'; // Status : Completed
	//$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 22-06-2018.xlsx'; // Status : Completed
	//$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 25-06-2018.xlsx'; // Status : Completed
	//$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 02-07-2018.xlsx'; // Status : Completed
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 05-07-2018.xlsx'; // Status : Completed
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 12-07-2018.xlsx'; // Status : Completed
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 13-07-2018.xlsx'; // Status : Completed
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 14-07-2018.xlsx'; // Status : Completed
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 18-07-2018.xlsx'; // Status : Completed
	//$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 20-07-2018.xlsx'; // Status : Completed
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 24-07-2018.xlsx'; // Status : Completed
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 26-07-2018.xlsx'; // Status : Completed
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 27-07-2018.xlsx'; // Status : Completed
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 30-07-2018.xlsx'; // Status : Completed
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 01-08-2018.xlsx'; // Status : Completed
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 02-08-2018.xlsx'; // Status : Completed
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 07-08-2018.xlsx'; // Status : Completed
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 08-08-2018.xlsx'; // Status : Completed
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 13-08-2018.xlsx'; // Status : Completed
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 15-08-2018.xlsx'; // Status : Completed
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 20-08-2018.xlsx'; // Status : Completed
        $filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Deployed 28-08-2018.xlsx'; // Status : Completed
        
        
        /** Reject  task **/
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Rejected 07-06-2018.xlsx'; // Status : Rejected
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Rejected 09-06-2018.xlsx'; // Status : Rejected
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Rejected 09-06-2018_batch2.xlsx'; // Status : Rejected
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Rejected 09-06-2018_batch3.xlsx'; // Status : Rejected
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Rejected 11-06-2018.xlsx'; // Status : Rejected
	//$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Rejected 19-06-2018.xlsx'; // Status : Rejected
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Rejected 21-06-2018.xlsx'; // Status : Rejected
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Rejected 25-06-2018.xlsx'; // Status : Rejected
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Rejected 07-07-2018.xlsx'; // Status : Rejected
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Rejected 12-07-2018.xlsx'; // Status : Rejected
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Rejected 14-07-2018.xlsx'; // Status : Rejected
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Rejected 18-07-2018.xlsx'; // Status : Rejected
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Rejected 19-07-2018.xlsx'; // Status : Rejected
	//$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Rejected 20-07-2018.xlsx'; // Status : Rejected
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Rejected 24-07-2018.xlsx'; // Status : Rejected
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Rejected 26-07-2018.xlsx'; // Status : Rejected
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Rejected 30-07-2018.xlsx'; // Status : Rejected
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Rejected 01-08-2018.xlsx'; // Status : Rejected
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Rejected 13-08-2018.xlsx'; // Status : Rejected
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Rejected 15-08-2018.xlsx'; // Status : Rejected
        //$filename = '/app/Migrate/Crm/data/Manual Contract - CRM Case Bulk Status Change Rejected 20-08-2018.xlsx'; // Status : Rejected
        
        
        dump('Reading file ... '.$filename);
        $counter = 0;
        $counterNotFound = 0;
        $kira = 0;
        Excel::load($filename, function($reader) use (&$counter,&$counterNotFound,&$kira) {
            
            $reader->each(function($row)  use (&$counter,&$counterNotFound,&$kira)  {

                $caseNumber = intval($row->crm);

                if($row->status == 'In Progress'){
                    $kira++;
                    //dump($kira. ': '.$caseNumber);
                    $caseObj = self::getCasesDetail($caseNumber);
                    if($caseObj != null){
                        if($caseObj && ($caseObj->case_status == 'In_Progress' || $caseObj->case_status == 'Open_Assigned' )){
                            if (strpos($caseObj->task_name, 'Initial Task') !== false) {
                                if( strlen(trim($row->resolution)) > 0){
                                    dump('###################### Initial Task');
                                    dump($row);
                                    $counter++;
                                    dd('completed');
                                }
                            }else{
                                if( strlen(trim($row->resolution)) > 0){
                                    if($caseObj->case_resolution != trim($row->resolution) ){
                                        DB::table('cases')
                                            ->where('id', $caseObj->case_id)
                                            ->update([
                                                'status' => 'In_Progress',
                                                'modified_user_id' => '2f164c07-0f19-820d-86b8-5afe9667f3d3',
                                                'resolution' => trim($row->resolution),
                                                'date_modified' => Carbon::now()
                                               ]);

                                        DB::table('tasks')
                                            ->where('id', $caseObj->task_id)
                                            ->update([
                                                'status' => 'In Progress',
                                                'modified_user_id' => '2f164c07-0f19-820d-86b8-5afe9667f3d3',
                                                'date_modified' => Carbon::now()
                                               ]);

                                        DB::table('tasks_cstm')
                                            ->where('id_c', $caseObj->task_id)
                                            ->update([
                                                'resolution_c' => trim($row->resolution)
                                               ]);

                                        $counter++;
                                        
                                        $caseObjUpdated = self::getCasesDetail($caseNumber);
                                        dump('###################### Assign to Specialist');
                                        //dump($row);
                                        dump($caseObjUpdated);
                                        //dd('completed');
                                    }else{
                                       dump('Case Number: '.$caseNumber. ' Status Case: '.$caseObj->case_status); 
                                    }
                                }
                            }
                        }else{
                           dump('Case Number: '.$caseNumber. ' Status Case: '.$caseObj->case_status); 
                           $counterNotFound++;
                        }
                    }else{
                        dump('????????????????? -> Not Found CaseNo: '.$caseNumber);
                        $counterNotFound++;
                    }
                
                }else if($row->status == 'Completed'){
                    $kira++;
                    //dump($kira. ': '.$caseNumber);
                    $caseObj = self::getCasesDetail($caseNumber);
                    if($caseObj != null){
                        if($caseObj && ($caseObj->case_status == 'In_Progress' || $caseObj->case_status == 'Open_Assigned' )){
                            if (strpos($caseObj->task_name, 'Initial Task') !== false) {
                                if( strlen(trim($row->resolution)) > 0){
                                    dump('###################### Initial Task');
                                    dump($row);
                                    $counter++;
                                    dd('completed');
                                }
                            }else{
                                if( strlen(trim($row->resolution)) > 0){
                                    if($caseObj->case_resolution != trim($row->resolution) ){
                                        $counter++;

                                        $beginResolution = 'Template sudah disemak. Kontrak telah migrasi ke dalam sistem eP. Mohon pengguna untuk semak di sistem eP.'
                                                                        . '
                                                        ';
                                        $newResolution = $beginResolution.trim($row->resolution);

                                        self::updateTaskCaseToResolve($caseObj, $newResolution,'4HOUR');
                                        //dd('completed');
                                    }else{
                                       // dump('already completed for case: '.$caseNumber);
                                    }
                                }
                            }
                        }else{
                           dump('Case Number: '.$caseNumber. ' Status Case: '.$caseObj->case_status); 
                           $counterNotFound++;
                        }
                    }else{
                        dump('????????????????? -> Not Found CaseNo: '.$caseNumber);
                        $counterNotFound++;
                    }
                }else if(trim($row->status) == 'Rejected'){
                    $kira++;
                    //dump($kira. ': '.$caseNumber);
                    $caseObj = self::getCasesDetail($caseNumber);
                    if($caseObj != null){
                        if($caseObj && ($caseObj->case_status == 'In_Progress' || $caseObj->case_status == 'Open_Assigned' )){
                            if (strpos($caseObj->task_name, 'Initial Task') !== false) {
                                if( strlen(trim($row->resolution)) > 0){
                                    dump('###################### Initial Task');
                                    dump($row);
                                    $counter++;
                                    dd('completed');
                                }
                            }else{
                                if( strlen(trim($row->resolution)) > 0){
                                    if($caseObj->case_resolution != trim($row->resolution) ){
                                        $counter++;

                                        $beginResolution = 'Template sudah disemak. Disertakan sebab kontrak ini gagal untuk diwujudkan dalam sistem eP.'
                                                                        . '
                                                        ';
                                        $newResolution = $beginResolution.trim($row->resolution);
                                        
                                        $endResolution = '
                                                        Sekiranya perlu, sila log kes baru dengan template yang telah dilengkapkan untuk tindakan selanjutnya.'
                                                                        . '
                                                        Terima kasih.'. '
                                                        ';
                                        
                                        $rejectReason = $newResolution.$endResolution;
                                        //dump($rejectReason);
                                        self::updateTaskCaseToRejected($caseObj, $rejectReason,'4HOUR');
                                        //dd('completed');
                                     
                                    }else{
                                       // dump('already completed for case: '.$caseNumber);
                                    }
                                }
                            }
                        }else{
                           dump('Case Number: '.$caseNumber. ' Status Case: '.$caseObj->case_status); 
                           $counterNotFound++;
                        }
                    }else{
                        dump('????????????????? -> Not Found CaseNo: '.$caseNumber);
                        $counterNotFound++;
                    }
                }
                
            });
        });
        
        dump('Total updated: '.$counter);
        dump('Total invalid: '.$counterNotFound);

    }
    
    protected static function  updateTaskCaseToRejected($caseObj,$newResolution,$typeTask=null){
        // update case Completed
        if($typeTask=='4HOUR'){
            DB::table('tasks')
                ->where('id', $caseObj->task_id)
                ->update([
                    'status'                    => 'Completed',
                    'date_start'                => Carbon::now()->subHour(8),
                    'date_due'                  => Carbon::now()->subHour(8)->addHour(4),
                    'task_justification'        => 'technical',
                    'date_modified'     => Carbon::now()->subHour(8),
                    'modified_user_id'  => '61795c8d-9bbf-46eb-9187-dc59f49101bf', //mohdshamsul
                   ]);
            DB::table('tasks_cstm')
                ->where('id_c', $caseObj->task_id)
                ->update([
                    'resolution_c'              => $newResolution,
                    'sla_start_4hr_c'           => Carbon::now()->subHour(8),
                    'sla_stop_4hr_c'            => Carbon::now()->subHour(8)->addMinute(1),
                    'date_execution_time_c'     => Carbon::now()->subHour(8)->addMinute(1),
                    'sla_task_flag_c'           => '3',
                    'acknowledge_time_c'        => Carbon::now()->subHour(8),
                    'acknowledge_by_userid_c'   => '61795c8d-9bbf-46eb-9187-dc59f49101bf', //mohdshamsul
                    'category_factor_c'         => 'external_factor'
                   ]);
        }else{
            DB::table('tasks')
                ->where('id', $caseObj->task_id)
                ->update([
                    'status'            => 'Completed',
                    'date_modified'     => Carbon::now()->subHour(8),
                    'modified_user_id'  => '61795c8d-9bbf-46eb-9187-dc59f49101bf',
                   ]);
            DB::table('tasks_cstm')
                ->where('id_c', $caseObj->task_id)
                ->update([
                    'resolution_c'  => $newResolution
                   ]);
        }
        
        $newTask = new Tasks;
        $newTask->id =Uuid::uuid4()->toString();
        $newTask->name = 'Task Rejected';
        $newTask->description = $caseObj->case_description;
        $newTask->date_entered = Carbon::now()->subHour(8);
        $newTask->date_modified = Carbon::now()->subHour(8);
        $newTask->modified_user_id = '61795c8d-9bbf-46eb-9187-dc59f49101bf'; //mohdshamsul
        $newTask->created_by = '61795c8d-9bbf-46eb-9187-dc59f49101bf'; //mohdshamsul
        $newTask->deleted = 0;
        $newTask->assigned_user_id = '2732c16c-1fff-9e6c-bb4e-58997edd3340';  //Group Business Coordinator
        $newTask->status = 'Completed';
        $newTask->date_due_flag = 0;
        $newTask->date_start = Carbon::now()->subHour(8);
        $newTask->date_due = Carbon::now()->subHour(8)->addMinutes(15); // NOW() + 15 minutes
        $newTask->date_start_flag = 0;
        $newTask->parent_type = 'Cases';
        $newTask->parent_id = $caseObj->case_id;
        $newTask->priority = 'Low';
        $newTask->task_justification = 'technical';
        $newTask->save();

        $newTaskCustom = new TaskCustom;
        $newTaskCustom->id_c = $newTask->id;
        $newTaskCustom->assign_group_c = 'Case Owner';
        $newTaskCustom->resolution_c = $newResolution;
        $newTaskCustom->sla_flag_c = 0;
        //$maxTaskNumber = DB::select("SELECT MAX(task_number_c) as max_task_number FROM tasks_cstm"); 
        //$newTaskCustom->task_number_c = (intval($maxTaskNumber[0]->max_task_number)+1); //auto increment
        $newTaskCustom->checkbox_add_day_c = 0;
        $newTaskCustom->category_factor_c = 'external_factor';
        $newTaskCustom->save();

        $caseModel = Cases::find($caseObj->case_id);
        $caseModel->date_modified =  Carbon::now()->subHour(8);
        $caseModel->modified_user_id = '61795c8d-9bbf-46eb-9187-dc59f49101bf'; //mohdshamsul
        $caseModel->status = 'Closed_Rejected';
        $caseModel->state = 'Closed';
        $caseModel->resolution = $newResolution;
        $caseModel->save();
        
        $caseResultObj = self::getCasesDetail($caseObj->case_number);
        dump('successfully task assign to case owner .. Task Number: '.$caseResultObj->task_number_c);
        dump('case Number: '.$caseResultObj->case_number.' ,status: '.$caseResultObj->case_status);
        dump($caseResultObj);
        
        Log::info('successfully task assign to case owner .. Task Number: '.$caseResultObj->task_number_c);
        Log::info('case Number: '.$caseResultObj->case_number.' ,status: '.$caseResultObj->case_status);
        Log::info(json_encode($caseResultObj));
    }
    
    
}    
