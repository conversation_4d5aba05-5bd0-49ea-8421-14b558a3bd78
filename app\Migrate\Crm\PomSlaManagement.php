<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\Crm;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Migrate\MigrateUtils;
use Carbon\CarbonInterval;
use Ramsey\Uuid\Uuid;
use App\Models\Cases;
use App\Models\CasesCustom;
use App\Models\Tasks;
use App\Models\TaskCustom;
use Illuminate\Support\Facades\Config;

class PomSlaManagement {

    public static function checkingSLAFlag2($dateSlaStop) {

        Log::info(self::class . ' > '. __FUNCTION__ . ' > Starting');
        var_dump(self::class . ' > '. __FUNCTION__ . ' > Starting');
        $dtStartTime = Carbon::now();

        $resultCaseAck = DB::select("SELECT DISTINCT c.case_number AS case_number,
                            CONVERT_TZ(c.date_entered,'+00:00','+08:00') AS itcoord_case_created,
                            c.name AS itcoord_name,
                            itcoord_tc.task_number_c AS itcoord_task_number,
                            itcoord_tc.sla_task_flag_c AS itcoord_sla_flag,
                            CONVERT_TZ(itcoord_t.date_start,'+00:00','+08:00') AS itcoord_start_datetime,
                            CONVERT_TZ(itcoord_t.date_due,'+00:00','+08:00') AS itcoord_due_datetime,
                            CONVERT_TZ(itcoord_tc.sla_start_15min_c,'+00:00','+08:00') AS itcoord_actual_start_datetime,
                            CONVERT_TZ(itcoord_tc.sla_stop_15min_c,'+00:00','+08:00') AS itcoord_completed_datetime,
                            TIMESTAMPDIFF(SECOND,CONVERT_TZ(itcoord_t.date_start,'+00:00','+08:00'),CONVERT_TZ(itcoord_t.date_due,'+00:00','+08:00')) AS itcoord_available_duration,
                            TIMESTAMPDIFF(SECOND,CONVERT_TZ(itcoord_tc.sla_start_15min_c,'+00:00','+08:00'),CONVERT_TZ(itcoord_tc.sla_stop_15min_c,'+00:00','+08:00')) AS itcoord_actual_duration 
                            FROM ((((cases c JOIN cases_cstm cc ON((c.id = cc.id_c))) 
                            LEFT JOIN tasks itcoord_t ON((c.id = itcoord_t.parent_id))) 
                            LEFT JOIN tasks_cstm itcoord_tc ON(((itcoord_t.id = itcoord_tc.id_c) 
                            AND (itcoord_tc.sla_task_flag_c = '2')))) 
                            LEFT JOIN cstm_list_app subcat ON(((cc.sub_category_c = subcat.value_code) 
                            AND (subcat.type_code = 'cdc_sub_category_list') 
                            AND (TRIM(subcat.value_code) <> '') 
                            AND (subcat.value_code NOT IN ('10712_15034','10714_15842','10713_15534'))))) 
                            WHERE ((cc.incident_service_type_c = 'incident_it') 
                            AND (cc.request_type_c = 'incident') 
                            AND (c.deleted = 0) 
                            AND (itcoord_t.deleted = 0)  
                            AND (itcoord_tc.task_number_c IS NOT NULL) 
                            AND (STR_TO_DATE(CONVERT_TZ(itcoord_tc.sla_stop_15min_c,'+00:00','+08:00'),'%Y-%m-%d') = '$dateSlaStop')
                            AND (TRIM(subcat.value_code) <> '')) 
                            ORDER BY c.case_number DESC");

        $totalCase = count($resultCaseAck);

        $collect = collect();
        $msg = "*SLA TASK ACKNOWLEDGEMENT: INITIAL TASK (FLAG 2)*";
        $totalCount = 0;
        if($totalCase > 0){
            foreach($resultCaseAck as $case) { 
  
                 if($case->itcoord_actual_duration > $case->itcoord_available_duration) {
                    $totalCount = $totalCount + $totalCase;
                    Log::info(self::class . ' > '. __FUNCTION__ .' > Please check this case : ' .$case->case_number);
                    var_dump(self::class . ' > '. __FUNCTION__ .' > Please check this case : ' .$case->case_number);

                    $msg = $msg . "\n \n" .

"*Case Number :*  " .$case->case_number . "\n" .
"*Available Duration :*  " .$case->itcoord_available_duration . " sec \n" .
"*Actual Duration :*  " .$case->itcoord_actual_duration . " sec";
                 }
            }
        }
        if($totalCount > 0){
            self::notifyWhatsapp($msg);
        }
        
        Log::info(self::class . ' > '. __FUNCTION__ . ' > Completed');
        var_dump(self::class . ' > '. __FUNCTION__ . ' > Completed');
    }

    public static function checkingSLAFlag3($dateSlaStop) {

        Log::info(self::class . ' > '. __FUNCTION__ . ' > Starting');
        var_dump(self::class . ' > '. __FUNCTION__ . ' > Starting');
        $dtStartTime = Carbon::now();

        $result = DB::select("SELECT DISTINCT c.case_number AS case_number,
                            CONVERT_TZ(c.date_entered,'+00:00','+08:00') AS itspec_case_created,
                            c.name AS itspec_name,
                            itspec_tc.task_number_c AS itspec_task_number,
                            itspec_tc.sla_task_flag_c AS itspec_sla_flag,
                            CONVERT_TZ(itspec_t.date_start,'+00:00','+08:00') AS itspec_start_datetime,
                            CONVERT_TZ(itspec_t.date_due,'+00:00','+08:00') AS itspec_due_datetime,
                            CONVERT_TZ(itspec_tc.sla_start_4hr_c,'+00:00','+08:00') AS itspec_actual_start_datetime,
                            CONVERT_TZ(itspec_tc.sla_stop_4hr_c,'+00:00','+08:00') AS itspec_completed_datetime,
                            TIMESTAMPDIFF(SECOND,CONVERT_TZ(itspec_t.date_start,'+00:00','+08:00'),CONVERT_TZ(itspec_t.date_due,'+00:00','+08:00')) AS itspec_available_duration,
                            TIMESTAMPDIFF(SECOND,CONVERT_TZ(itspec_tc.sla_start_4hr_c,'+00:00','+08:00'),CONVERT_TZ(itspec_tc.sla_stop_4hr_c,'+00:00','+08:00')) AS itspec_actual_duration 
                            FROM ((((cases c 
                            JOIN cases_cstm cc ON((c.id = cc.id_c))) 
                            LEFT JOIN tasks itspec_t ON((c.id = itspec_t.parent_id))) 
                            LEFT JOIN tasks_cstm itspec_tc ON(((itspec_t.id = itspec_tc.id_c) 
                            AND (itspec_tc.sla_task_flag_c = '3')))) 
                            LEFT JOIN cstm_list_app subcat ON(((cc.sub_category_c = subcat.value_code) 
                            AND (subcat.type_code = 'cdc_sub_category_list') 
                            AND (TRIM(subcat.value_code) <> '') 
                            AND (subcat.value_code NOT IN ('10712_15034','10714_15842','10713_15534'))))) 
                            WHERE ((cc.incident_service_type_c = 'incident_it') 
                            AND (cc.request_type_c = 'incident')
                            AND (itspec_t.name IN ('Assigned to Production Support','Assigned to Group IT Specialist(Production Support)'))
                            AND (c.deleted = 0)
                            AND (itspec_t.deleted = 0) 
                            AND (c.status IN ('Pending_User_Verification', 'Open_Resolved', 'Closed_Approved', 'Closed_Cancelled_Eaduan', 'Closed_Closed', 'Closed_Rejected', 'Closed_Rejected_Eaduan', 'Closed_Verified_Eaduan'))
                            AND (itspec_t.status = 'Completed')
                            AND ( itspec_t.assigned_user_id <> '15c7bd74-12f3-311b-9e8f-5a810702a1a5')
                            AND (itspec_tc.task_number_c IS NOT NULL) 
                            AND (STR_TO_DATE(CONVERT_TZ(itspec_tc.sla_stop_4hr_c,'+00:00','+08:00'),'%Y-%m-%d') = '$dateSlaStop')
                            AND (TRIM(subcat.value_code) <> '')) 
                            ORDER BY c.case_number DESC");

        $totalCase = count($result);

        $collect = collect();
        $msg = "*SLA TASK COMPLETED: RIT TASK (FLAG 3)*";
        $totalCount = 0;

        if($totalCase > 0){
            foreach($result as $case) {  
                 if($case->itspec_actual_duration > $case->itspec_available_duration) {
                    $totalCount = $totalCount + $totalCase;

                    Log::info(self::class . ' > '. __FUNCTION__ .' > Please check this case : ' .$case->case_number);
                    var_dump(self::class . ' > '. __FUNCTION__ .' > Please check this case : ' .$case->case_number);

                    $msg = $msg . "\n \n" .

"*Case Number :*  " .$case->case_number . "\n" .
"*Available Duration :*  " .$case->itspec_available_duration . " sec \n" .
"*Actual Duration :*  " .$case->itspec_actual_duration . " sec";
                 }
            }
        }
        if($totalCount > 0){
            self::notifyWhatsapp($msg);
        }
        Log::info(self::class . ' > '. __FUNCTION__ . ' > Completed');
        var_dump(self::class . ' > '. __FUNCTION__ . ' > Completed');
    }

    public static function checkingSLASeverity($dateSlaStop) {

        Log::info(self::class . ' > '. __FUNCTION__ . ' > Starting');
        var_dump(self::class . ' > '. __FUNCTION__ . ' > Starting');
        $dtStartTime = Carbon::now();

        $result = DB::select("SELECT DISTINCT c.case_number AS case_number,
                        CONVERT_TZ(c.date_entered,'+00:00','+08:00') AS itseverity_case_created,
                        c.name AS itseverity_name,
                        itspec_tc.task_number_c AS itseverity_task_number,
                        itspec_tc.sla_task_flag_c AS itseverity_sla_flag,
                        CONVERT_TZ(itspec_t.date_start,'+00:00','+08:00') AS itseverity_start_datetime,
                        CONVERT_TZ(itspec_t.date_due,'+00:00','+08:00') AS itseverity_due_datetime,
                        CONVERT_TZ(itspec_tc.sla_start_4hr_c,'+00:00','+08:00') AS itseverity_actual_start_datetime,
                        CONVERT_TZ(itspec_tc.sla_stop_4hr_c,'+00:00','+08:00') AS itseverity_completed_datetime,
                        TIMESTAMPDIFF(SECOND,CONVERT_TZ(itspec_t.date_start,'+00:00','+08:00'),CONVERT_TZ(itspec_t.date_due,'+00:00','+08:00')) AS itseverity_available_duration,
                        TIMESTAMPDIFF(SECOND,CONVERT_TZ(itspec_tc.sla_start_4hr_c,'+00:00','+08:00'),CONVERT_TZ(itspec_tc.sla_stop_4hr_c,'+00:00','+08:00')) AS itseverity_actual_duration 
                        FROM ((((cases c 
                        JOIN cases_cstm cc ON((c.id = cc.id_c))) 
                        LEFT JOIN tasks itspec_t ON((c.id = itspec_t.parent_id))) 
                        LEFT JOIN tasks_cstm itspec_tc ON(((itspec_t.id = itspec_tc.id_c) 
                        AND (itspec_tc.sla_task_flag_c IN ('s1','s2','s3'))))) 
                        LEFT JOIN cstm_list_app subcat ON(((cc.sub_category_c = subcat.value_code) 
                        AND (subcat.type_code = 'cdc_sub_category_list') 
                        AND (TRIM(subcat.value_code) <> '') 
                        AND (subcat.value_code NOT IN ('10712_15034','10714_15842','10713_15534'))))) 
                        WHERE ((cc.incident_service_type_c = 'incident_it') 
                        AND (cc.request_type_c = 'incident')
                        AND (c.deleted = 0)
                        AND (itspec_t.deleted = 0) 
                        AND (c.status IN ('Pending_User_Verification', 'Open_Resolved', 'Closed_Approved', 'Closed_Cancelled_Eaduan', 'Closed_Closed', 'Closed_Rejected', 'Closed_Rejected_Eaduan', 'Closed_Verified_Eaduan'))
                        AND (itspec_t.status = 'Completed')
                        AND ( itspec_t.assigned_user_id NOT IN ('15c7bd74-12f3-311b-9e8f-5a810702a1a5','5dac7b92-18d0-4beb-b600-413957aa4c26'))
                        AND (itspec_tc.task_number_c IS NOT NULL)
                        AND (itspec_tc.sla_stop_4hr_c IS NOT NULL)
                        AND (STR_TO_DATE(CONVERT_TZ(itspec_tc.sla_stop_4hr_c,'+00:00','+08:00'),'%Y-%m-%d') = '$dateSlaStop')
                        AND (TRIM(subcat.value_code) <> ''))
                        ORDER BY c.case_number DESC");

        $totalCase = count($result);
        $totalCount = 0;

        $collect = collect();
        $msg = "*SLA TASK COMPLETED: SEVERITY TASK (FLAG s1/s2/s3)*";
        if($totalCase > 0){
            foreach($result as $case) {  
                 if($case->itseverity_actual_duration > $case->itseverity_available_duration) {
                    $totalCount = $totalCount + $totalCase;

                    Log::info(self::class . ' > '. __FUNCTION__ .' > Please check this case : ' .$case->case_number);
                    var_dump(self::class . ' > '. __FUNCTION__ .' > Please check this case : ' .$case->case_number);

                    $msg = $msg . "\n \n" .

"*Case Number :*  " .$case->case_number . "\n" .
"*Available Duration :*  " .$case->itseverity_available_duration . " sec \n" .
"*Actual Duration :*  " .$case->itseverity_actual_duration . " sec";
                 }
            }
        }
        if($totalCount > 0){
            self::notifyWhatsapp($msg);
        }
        Log::info(self::class . ' > '. __FUNCTION__ . ' > Completed');
        var_dump(self::class . ' > '. __FUNCTION__ . ' > Completed');
    }

    public static function checkingSLAS4($dateSlaStop) {

        Log::info(self::class . ' > '. __FUNCTION__ . ' > Starting');
        var_dump(self::class . ' > '. __FUNCTION__ . ' > Starting');
        $dtStartTime = Carbon::now();

        $result = DB::select("SELECT DISTINCT c.case_number AS case_number, 
                    c.redmine_number AS redmine_number,
                    UPPER(c.redmine_implementation_issue) AS redmine_implementation_issue,
                    UPPER(approver_t.child_parent_redmine) AS redmine_child_parent,
                    CONVERT_TZ(c.date_entered,'+00:00','+08:00') AS itapprover_case_created,
                    c.name AS itapprover_name,
                    approver_t.name AS itapprover_task_name,
                    approver_tc.task_number_c AS itapprover_task_number,
                    approver_tc.sla_task_flag_c AS itapprover_sla_flag,
                    CONVERT_TZ(approver_t.date_start,'+00:00','+08:00') AS itapprover_start_datetime,
                    CONVERT_TZ(approver_t.date_due,'+00:00','+08:00') AS itapprover_due_datetime,
                    CONVERT_TZ(approver_t.date_start,'+00:00','+08:00') AS itapprover_actual_start_datetime,
                    CONVERT_TZ(approver_t.reassign_time_c,'+00:00','+08:00') AS itapprover_completed_datetime,
                    approver_tc.task_duration_c AS itapprover_duration,
                    DATEDIFF(STR_TO_DATE(CONVERT_TZ(approver_t.date_due,'+00:00','+08:00'),'%Y-%m-%d'),STR_TO_DATE(CONVERT_TZ(approver_t.date_start,'+00:00','+08:00'),'%Y-%m-%d')) AS itapprover_available_duration,
                    DATEDIFF(STR_TO_DATE(CONVERT_TZ(approver_t.reassign_time_c,'+00:00','+08:00'),'%Y-%m-%d'),STR_TO_DATE(CONVERT_TZ(approver_t.date_start,'+00:00','+08:00'),'%Y-%m-%d')) AS itapprover_actual_duration, 
                    approver_tc.is_sent
                    FROM ((((cases c 
                    JOIN cases_cstm cc ON((c.id = cc.id_c))) 
                    LEFT JOIN tasks approver_t ON((c.id = approver_t.parent_id))) 
                    LEFT JOIN tasks_cstm approver_tc ON(((approver_t.id = approver_tc.id_c) 
                    AND (approver_tc.sla_task_flag_c = '4')))) 
                    LEFT JOIN cstm_list_app subcat ON(((cc.sub_category_c = subcat.value_code) 
                    AND (subcat.type_code = 'cdc_sub_category_list') 
                    AND (TRIM(subcat.value_code) <> '') 
                    AND (subcat.value_code NOT IN ('10712_15034','10714_15842','10713_15534'))))) 
                    WHERE ((cc.incident_service_type_c = 'incident_it') 
                    AND (c.status IN ('Pending_User_Verification', 'Open_Resolved', 'Closed_Approved', 'Closed_Cancelled_Eaduan', 'Closed_Closed', 'Closed_Rejected', 'Closed_Rejected_Eaduan', 'Closed_Verified_Eaduan'))
                    AND (approver_t.status = 'Completed') 
                    AND (cc.request_type_c = 'incident')
                    AND (c.deleted = 0)
                    AND (approver_t.deleted = 0)
                    AND (approver_tc.task_number_c IS NOT NULL) 
                    AND (approver_tc.task_duration_c > 0)
                    AND (TRIM(subcat.value_code) <> '')) 
                    -- AND (STR_TO_DATE(CONVERT_TZ(c.date_entered,'+00:00','+08:00'),'%Y-%m-%d') <= '2024-06-10')
                    AND (STR_TO_DATE(CONVERT_TZ(approver_t.reassign_time_c,'+00:00','+08:00'),'%Y-%m-%d') = '$dateSlaStop')
                    ORDER BY c.case_number DESC");

        $totalCase = count($result);

        $collect = collect();
        $msg = "*SLA TASK COMPLETED: S4 TASK (FLAG 4)*";
        $totalCount = 0;

        if($totalCase > 0){
            foreach($result as $case) {  
                 if($case->itapprover_actual_duration > $case->itapprover_available_duration) {
                    $totalCount = $totalCount + $totalCase;

                    Log::info(self::class . ' > '. __FUNCTION__ .' > Please check this case : ' .$case->case_number);
                    var_dump(self::class . ' > '. __FUNCTION__ .' > Please check this case : ' .$case->case_number);

                    $msg = $msg . "\n \n" .

"*Case Number :*  " .$case->case_number . "\n" .
"*Available Duration :*  " .$case->itapprover_available_duration . " days \n" .
"*Actual Duration :*  " .$case->itapprover_actual_duration . " days";
                 }
            }
        }
        if($totalCount > 0){
            self::notifyWhatsapp($msg);
        }
        Log::info(self::class . ' > '. __FUNCTION__ . ' > Completed');
        var_dump(self::class . ' > '. __FUNCTION__ . ' > Completed');
    }

    private static function notifyWhatsapp($msg) {
        DB::connection('mysql_ep_notify')
                ->insert('insert into notification  
                    (message,receiver_group,process,status,sent,date_entered,retry,source_app,source_class,source_remark) 
                    values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', [
                    $msg,
                    'CRM_SLA_MONITORING',
                    'notify', 0, 0,
                    Carbon::now(), 0,
                    'CrmIntegration',
                    __CLASS__,
                    'This alert to review cases with burst sla'
        ]);
    }

}
