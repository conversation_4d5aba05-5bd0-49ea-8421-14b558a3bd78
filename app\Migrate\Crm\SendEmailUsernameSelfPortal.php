<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\Crm;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Excel;
use App\Models\Contact;
use App\ContactCustom;
use Ramsey\Uuid\Uuid;
use App\Migrate\MigrateUtils;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class SendEmailUsernameSelfPortal {

    public static $USER_LOGGED_ID = '3';
    public static $PASSWORD = 'P@ssword1234';

    public static function run() {
        Log::debug(self::class . ' Starting using SMTP....... ' . env('MAIL_HOST') . '  -->' . __FUNCTION__);
        var_dump(self::class . ' Starting 12345....... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();
        self::sendEmailUsername();

        var_dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);

        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    public static function sendEmailUsername() {

        $take = 10;
        $skip = 10;
        $count = 0;
        $counter = 0;
        do {
//            if ($count > 0) {
//                sleep(60);
//            }
            $listPtjUsers = DB::table('ptj_users_temp')
                    ->whereNull('is_email')
                    ->take($take)
                    ->skip($skip * $count++)
                    ->get();
            foreach ($listPtjUsers as $row) {
                $counter++;
                $emailRaw = $row->contact_email;
                $email = filter_var($emailRaw, FILTER_SANITIZE_EMAIL);
                var_dump($counter.') verify  email : ' . $row->contact_email. ' on '.Carbon::now());
                if (!filter_var($email, FILTER_VALIDATE_EMAIL) == false) {
                    //$row->contact_email = "<EMAIL>";
                    self::sendEmailToUsers($row);

                    
                } else {
                    var_dump('	notvalid  email : ' . $row->contact_email);
                    DB::table('ptj_users_temp')
                            ->where('id', $row->id)
                            ->update([
                                'is_email' => 9
                    ]);
                }
            }
            
        } while ($counter <= 3000);
        var_dump('completed total : ' . $counter);
    }

    /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    public static function sendEmailToUsers($data) {
        $subject = 'Pengaktifan Akaun eAduan';
        try {
            Mail::send('emails.selfPortalUsername', [
                'subject' => $subject,
                'date' => Carbon::now()->toDateString(),
                'data' => $data
                    ], function($m) use ($data, $subject) {
                //$m->from('<EMAIL>', 'eAduan');
                $m->from('<EMAIL>', 'eAduan');
                $m->to($data->contact_email)->subject($subject);
                //$m->to("<EMAIL>")->subject($subject);
            });
            DB::table('ptj_users_temp')
                    ->where('id', $data->id)
                    ->update([
                        'is_email' => 1
            ]);
            var_dump('  success  send email to : ' . $data->contact_email);
        } catch (\Exception $e) {
            Log::error('Console :: sendEmailToUsers ', ['Email' => $data->contact_email, 'ERROR' => $e->getMessage()]);

            DB::table('ptj_users_temp')
                    ->where('id', $data->id)
                    ->update([
                        'is_email' => 0
            ]);
            var_dump('  failed  send : ' . $data->contact_email);
            return $e;
        }
    }

}
