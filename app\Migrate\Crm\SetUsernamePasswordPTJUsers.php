<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\Crm;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Excel;
use App\Models\Contact;
use App\Models\ContactCustom;
use Ramsey\Uuid\Uuid;
use App\Migrate\MigrateUtils;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class SetUsernamePasswordPTJUsers {

    public static $USER_LOGGED_ID = '3';
    public static $PASSWORD = 'P@ssword1234';

    public static function run() {
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();
        self::insertPTJUsersTemporaryTable();
        self::setUsernamePassword();
        
        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    public static function insertPTJUsersTemporaryTable() {
        
        if (!Schema::hasTable('ptj_users_temp')) {
            /** When using as integer or bigint for create column, Laravel define this column has foreign or primary key
             * * bigInteger('xxxx',false)  -- will set as length 20 as default
             * */
            Schema::create('ptj_users_temp', function (Blueprint $table) {
                    $table->increments('id');
                    $table->string('ministry',150)->comment('Ministry Name');  
                    $table->string('jabatan',150)->comment('Jabatan Name');  
                    $table->string('ptj_name',150)->comment('PTJ Name'); 
                    $table->string('ptj_code',150);  
                    $table->string('state',150)->nullable();  
                    $table->text('address')->nullable();  
                    $table->string('user_name',150);  
                    $table->string('ic_number',150);  
                    $table->string('contact_email',150)->nullable();  
                    $table->string('phone_number',150)->nullable();  
                    $table->string('fax_number',150)->nullable();  
                    $table->text('role')->nullable()->nullable();  
                    $table->string('is_updated_crm',1)->nullable();
                    $table->string('is_email',1)->nullable();
                    $table->string('userlogin',150)->nullable();
                    $table->string('password',150)->nullable();
                    $table->timestamp('created_date')->comment('Date and time  the record was created.');
                });
        }
        
        $filename = '/app/Migrate/Crm/data/ListActiveUserPTJWithEmail.xlsx';
        //$filename = '/app/Migrate/Crm/data/ListActiveUserPTJ.xlsx';
        Excel::load($filename, function($reader) {

            $reader->each(function($sheet) {
                // Loop through all rows
                $sheet->each(function($row) {
                    DB::table('ptj_users_temp')->insert(
                            [
                                'ministry' => $row->ministry,
                                'jabatan' => $row->jabatan,
                                'ptj_name' => $row->ptj_name,
                                'ptj_code' => $row->ptj_code,
                                'state' => $row->state,
                                'address' => $row->address,
                                'user_name' => $row->user_name,
                                'ic_number' => $row->ic_number,
                                'contact_email' => $row->contact_email,
                                'phone_number' => $row->phone_number,
                                'fax_number' => $row->fax_number,
                                'role' => $row->role,
                                'created_date' =>Carbon::now()
                            ]
                    );
                });
            });
        });
    }
    
    public static function setUsernamePassword() {
        
        $listPtjUsers = DB::table('ptj_users_temp')->get();
        foreach ($listPtjUsers as $row) {
            /*
              "ministry" => "PERUNTUKAN DI RAJA"
              "jabatan" => "PERUNTUKAN DIRAJA"
              "ptj_code" => "0011010000"
              "ptj_name" => "ISTANA NEGARA"
              "state" => "WILAYAH PERSEKUTUAN (K.L)"
              "address" => "BAHAGIAN KHIDMAT PENGURUSAN ISTANA NEGARA JALAN TUANKU ABDUL HALIM 50480 WILAYAH PERSEKUTUAN (K.L)"
              "user_name" => "ENCIK CHE HILMY BIN CHE MUSA"
              "ic_number" => "************"
              "contact_email" => "<EMAIL>"
              "phone_number" => "**********"
              "fax_number" => "**********"
              "role" => "Pegawai Penerima Barangan,Jawatankuasa Penilaian Teknikal"

             */
            $icNo = trim(strtoupper($row->ic_number));
            $jabatanPtjCode = trim($row->ptj_code);

            /** FIND account -> contact **/
            $list = DB::table('accounts as a')
                    ->join('accounts_cstm as b', 'a.id', '=', 'b.id_c')
                    ->join('accounts_contacts as c', 'a.id', '=', 'c.account_id')
                    ->join('contacts as d', 'c.contact_id', '=', 'd.id')
                    ->join('contacts_cstm as e', 'd.id', '=', 'e.id_c')
                    ->where('b.jabatan_ptj_code_c', $jabatanPtjCode)
                    ->where('a.deleted', '0')
                    ->where('e.ic_no_c', $icNo)
                    ->select('a.name as acc_name', 'a.id as acc_id', 'b.jabatan_ptj_code_c as acc_jab_ptj_code', 'b.gov_level_c as acc_gov_type', 'b.gov_code_c as acc_gov_code')
                    ->addSelect('d.first_name as contact_name', 'd.id as contact_id', 'e.ic_no_c as contact_icno', 'e.username_c as contact_username')
                    ->get();

            $contactId = null;
            if (count($list) == 0) {
                /** Create User **/
                Log::info(self::class . ' ' . __FUNCTION__ . ' user ptj not found : ' . $icNo . ' PTJCODE : ' . $jabatanPtjCode);
                $contactId = self::createContact($row);
            } else {
                $obj = $list->where('deleted',0)->first();
                if($obj && $obj->contact_id != ''){
                    $contactId = $obj->contact_id;
                }else{
                    $obj = $list->first();
                    $contactId = $obj->contact_id;

                }
            }
            if($contactId != null){
                var_dump('Contact ID : '.$contactId);
                $check = DB::table('contacts_cstm')->where('username_c', $icNo)->count();
                if ($check == 0) {
                    Log::info(self::class . ' ' . __FUNCTION__ . ' username ic_no IS available: ' . $icNo . ' PTJCODE : ' . $jabatanPtjCode);
                    self::saveContact($contactId, $row, null);
                    var_dump('  save username  : ' . $icNo . ' ,contactId: ' . $contactId);
                } else {
                    Log::info(self::class . ' ' . __FUNCTION__ . ' username ic_no IS NOT available: ' . $icNo . ' PTJCODE : ' . $jabatanPtjCode);

                    $contactCstmObj = DB::table('contacts_cstm')->where('id_c', $contactId)->first();
                    if($contactCstmObj->username_c == $icNo){
                        var_dump('  OK >> already exist - username  : ' . $icNo . ' ,contactId: ' . $contactId);
                        Log::info(self::class . ' ' . __FUNCTION__ . '  OK >> already exist - username  : ' . $icNo . ' ,contactId: ' . $contactId);
                    }else{

                        /** if username ic existed, make new username = icno+ptjcode **/
                        $ptjcode = $newstring = substr($jabatanPtjCode, -6);
                        $username = trim(strtoupper($icNo . '_' . $ptjcode));
                        $checkOther = DB::table('contacts_cstm')->where('username_c', $username)->count();
                        if ($checkOther == 0) {
                            self::saveContact($contactId, $row, $ptjcode);
                            var_dump('  save username + ptjcode : ' . $username . ' ,contactId: ' . $contactId);
                        } else {
                            $contactObj = DB::table('contacts_cstm')->where('id_c', $contactId)->first();
                            if($contactObj->username_c != $username){
                                Log::error(self::class . ' ' . __FUNCTION__ . ' PLEASE CHECK THIS USER PTJ -> username already exist: ' . $username . ' ICNO : ' . $icNo . ' PTJCODE : ' . $jabatanPtjCode);
                                var_dump('  ERROR!!!! PLEASE CHECK THIS USER PTJ -> username already exist: ' . $username . ' ICNO : ' . $icNo . ' PTJCODE : ' . $jabatanPtjCode. ' ,contactId: ' . $contactId);
                            }else{
                                var_dump('  OK >> already exist - username + ptjcode : ' . $username . ' ,contactId: ' . $contactId);
                                Log::info(self::class . '  OK >> already exist - username + ptjcode : ' . $username . ' ,contactId: ' . $contactId);
                            }
                        }
                    }
                }

                $email = $row->contact_email;
                if ($email && $email != '') {
                    $email =strtolower($email);
                    if (!filter_var($email, FILTER_VALIDATE_EMAIL) === false) {
                        self::saveEmailContact($email, $contactId);
                    } else {
                        Log::error(self::class . ' ' . __FUNCTION__ . ' PLEASE CHECK THIS USER PTJ -> email not valid: ' . $email . ' , ICNO : ' . $icNo . ' PTJCODE : ' . $jabatanPtjCode);
                        var_dump('  invalid email : ' . $email . ' ,contactId: ' . $contactId);
                    }
                }
                
                DB::table('ptj_users_temp')
                        ->where('id', $row->id)
                        ->update([
                            'is_updated_crm' => 1
                ]);
            }else{
                DB::table('ptj_users_temp')
                        ->where('id', $row->id)
                        ->update([
                            'is_updated_crm' => 0
                ]);
                var_dump('ERROR : Cannot process >> ICNO : ' . $icNo . ' PTJCODE : ' . $jabatanPtjCode);
                Log::error(self::class . ' ' . __FUNCTION__ . ' ERROR : Cannot process >> ICNO : ' . $icNo . ' PTJCODE : ' . $jabatanPtjCode);
            }
               
        }
    }

    protected static function saveContact($contactId, $row, $ptjcode) {
        Log::info(self::class . ' ' . __FUNCTION__ . ' contactId: ' . $contactId);
        $username = trim(strtoupper($row->ic_number));
        if ($ptjcode != null) {
            $username = trim(strtoupper($row->ic_number . '_' . $ptjcode));
        }

        DB::table('contacts_cstm')
                ->where('id_c', $contactId)
                ->update([
                    'username_c' => $username,
                    'password_c' => self::$PASSWORD
        ]);

        DB::table('contacts')
                ->where('id', $contactId)
                ->update([
                    'date_modified' => Carbon::now()->subHour(8),
                    'modified_user_id' => self::$USER_LOGGED_ID,
                    //'first_name' => trim(strtoupper($row->user_name)),
                    'phone_work' => $row->phone_number,
                    'phone_fax' => $row->fax_number,
                    'description' => trim(strtoupper($row->role))
        ]);
        
        DB::table('ptj_users_temp')
                ->where('id', $row->id)
                ->update([
                    'userlogin' => $username,
                    'password' => self::$PASSWORD
        ]);
        
        Log::info(self::class . ' ' . __FUNCTION__ . 'completed saved contactId: ' . $contactId);
        var_dump('      completed saved contactId: ' . $contactId);
    }

    protected static function createContact($row) {

        /** FIND account  * */
        $jabatanPtjCode = trim($row->ptj_code);
        $accountList = DB::table('accounts as a')
                ->join('accounts_cstm as b', 'a.id', '=', 'b.id_c')
                ->where('b.jabatan_ptj_code_c', $jabatanPtjCode)
                ->get();
        $accId = null;
        if(count($accountList)> 0){
            $accountObj = $accountList->where('deleted', 0)->first();

            if ($accountObj && $accountObj->id != '') {
                $accId = $accountObj->id;
            } else {
                $accountObj = $accountList->first();
                $accId = $accountObj->id;
            }
        }
        if ($accId != null) {
            $contactId = Uuid::uuid4()->toString();
            DB::table('contacts')->insert([
                'id' => $contactId,
                'date_entered' => Carbon::now()->subHour(8),
                'created_by' => self::$USER_LOGGED_ID,
                'date_modified' => Carbon::now()->subHour(8),
                'modified_user_id' => self::$USER_LOGGED_ID,
                'first_name' => trim(strtoupper($row->user_name)),
                'phone_work' => $row->phone_number,
                'phone_fax' => $row->fax_number,
                'description' => trim(strtoupper($row->role))
                    ]
            );

            DB::table('contacts_cstm')->insert([
                'id_c' => $contactId,
                'ic_no_c' => trim(strtoupper($row->ic_number))
                    ]
            );

            DB::table('accounts_contacts')->insert([
                'id' => $contactId,
                'contact_id' => $contactId,
                'account_id' => $accId,
                'deleted' => 0,
                'date_modified' => Carbon::now()->subHour(8)
                    ]
            );
            Log::info(self::class . ' ' . __FUNCTION__ . 'completed created contactId: ' . $contactId);
            var_dump('      completed created contactId: ' . $contactId);

            return $contactId;
        }
        return null;
    }

    protected static function saveEmailContact($email, $contactId) {

        $list = DB::table('email_addr_bean_rel as a')
                ->join('email_addresses as b', 'a.email_address_id', '=', 'b.id')
                ->where('b.email_address', $email)
                ->where('a.bean_module', 'Contacts')
                ->where('a.bean_id', $contactId)
                ->get();
        $emailObj = null;
        $isChanged = false;
        if (count($list) == 0) {
            DB::table('email_addresses')
                    ->insertGetId([
                        'id' => Uuid::uuid4()->toString(),
                        'email_address' => trim(strtolower($email)),
                        'email_address_caps' => trim(strtoupper($email)),
                        'date_created' => Carbon::now()->subHour(8),
                        'date_modified' => Carbon::now()->subHour(8),
                        'deleted' => 0,
            ]);
            $emailObj = DB::table('email_addresses')
                            ->where('email_address', $email)->orderBy('date_created', 'desc')->first();
            DB::table('email_addr_bean_rel')
                    ->insertGetId([
                        'id' => Uuid::uuid4()->toString(),
                        'email_address_id' => $emailObj->id,
                        'bean_id' => $contactId,
                        'bean_module' => 'Contacts',
                        'date_created' => Carbon::now()->subHour(8),
                        'date_modified' => Carbon::now()->subHour(8),
                        'primary_address' => 1,
                        'deleted' => 0,
            ]);
            $isChanged = true;
        }

        $list2 = DB::table('emails_beans as a')
                ->join('email_addresses as b', 'a.email_id', '=', 'b.id')
                ->where('b.email_address', $email)
                ->where('a.bean_module', 'Contacts')
                ->where('a.bean_id', $contactId)
                ->get();
        if (count($list2) == 0) {
            if ($emailObj == null) {
                DB::table('email_addresses')
                        ->insertGetId([
                            'id' => Uuid::uuid4()->toString(),
                            'email_address' => trim(strtolower($email)),
                            'email_address_caps' => trim(strtoupper($email)),
                            'date_created' => Carbon::now()->subHour(8),
                            'date_modified' => Carbon::now()->subHour(8),
                            'deleted' => 0,
                ]);

                $emailObj = DB::table('email_addresses')
                                ->where('email_address', $email)->orderBy('date_created', 'desc')->first();
            }
            DB::table('emails_beans')
                    ->insertGetId([
                        'id' => Uuid::uuid4()->toString(),
                        'email_id' => $emailObj->id,
                        'bean_id' => $contactId,
                        'bean_module' => 'Contacts',
                        'date_modified' => Carbon::now()->subHour(8),
                        'deleted' => 0,
            ]);
            $isChanged = true;
        }

        if ($isChanged == true) {
            Log::info(self::class . ' ' . __FUNCTION__ . 'completed save email : ' . $email . ' ,contactId: ' . $contactId);
            var_dump('      completed save email : ' . $email . ' ,contactId: ' . $contactId);
        }
    }

}
