<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\Crm;

use Carbon\Carbon;
use DB;
use Log;
use App\Migrate\MigrateUtils;
use App\Migrate\Nextgen\PMService;
use App\Migrate\Nextgen\SyncGovernmentInfo;

class SyncAccountEp {

    static $query_skip = 500;
    static $query_take = 500;
    static $USER_LOGGED_ID = '3';

    public static function pmService() {
        return new PMService;
    }

    public static function SyncGovernmentInfo() {
        return new SyncGovernmentInfo;
    }

    public static function run($lastDate) {
        Log::debug(self::class . ' Starting ... SyncAccountEp');
        $dtStartTime = Carbon::now();

        $start = 0;
        $skip = self::$query_skip;
        $take = self::$query_take;
        $totalRecords = 0;
        $dtStartTimeOP = Carbon::now();

        do {
            $nextSkip = $start++ * $skip;
            $result = self::getListAccount($lastDate, $take, $nextSkip);
            $totalRecords = $totalRecords + count($result);
            $dtStartTimeEachLoop = Carbon::now();
            dump(self::class . ' current totalrecords ' . count($result));

            foreach ($result as $obj) {
                $orgCode = $obj->org_gov_code;
                $orgName = $obj->name;
                $accountId = $obj->id;
                $data = null;

                if ($orgCode != null) {
                    $data = self::PMService()->getPMOrgProfileDetailsByOrgCode($orgCode);
                } else {
                    $data = self::PMService()->getPMOrgProfileDetailsByOrgCode($orgName);
                }

                if ($data) {
                    dump('Account Id : ' . $accountId . ' >>> Profile Ep Found.' . self::PMService()->getOrgProfileType($data->org_type_id) . ' -> ' . $data->org_code);
                    Log::info('Account Id : ' . $accountId . ' >>> Profile Ep Found: ' . self::PMService()->getOrgProfileType($data->org_type_id) . ' -> ' . $data->org_code);
                    self::SyncGovernmentInfo()->saveAccountPrimary($data, $accountId);
                    self::SyncGovernmentInfo()->saveAccountAddress($data->org_profile_id, $accountId);
                } else {
                    dump('Account Id : ' . $accountId . ' >>> Profile Ep Not Found. Set Account As Deleted');
                    Log::info('Account Id : ' . $accountId . ' >>> Profile Ep Not Found. Set Account As Deleted');
                    DB::table('accounts')
                            ->where('id', $accountId)
                            ->update([
                                'deleted' => 1,
                                'date_modified' => Carbon::now()->subHour(8),
                                'modified_user_id' => self::$USER_LOGGED_ID
                    ]);
                }
            }

            $takentimeeachLoop = array(
                'Counter' => $start,
                'Taken Time per Minutes' => $dtStartTimeEachLoop->diffInMinutes(Carbon::now()),
                'Taken Time per Seconds' => $dtStartTimeEachLoop->diffInSeconds(Carbon::now())
            );
            dump(self::class . '    :: LoopTakenTime >> Time   :   ', [$takentimeeachLoop]);
            dump(self::class . '    :: sum total current  :   ' . $totalRecords);
        } while (count($result) > 0 && count($result) == self::$query_take);

        $takentimeOP = array(
            'Counter' => $start,
            'Taken Time per Minutes' => $dtStartTimeOP->diffInMinutes(Carbon::now()),
            'Taken Time per Seconds' => $dtStartTimeOP->diffInSeconds(Carbon::now())
        );
        dump(self::class . '    :: AllLoopTakenTime >> Time   :   ', [$takentimeOP]);
        dump(self::class . ' queryReport. Total All :  ' . $totalRecords);
        dump(self::class . '--------------------------------------------');

        Log::debug(self::class . ' Completed SyncAccountEp --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    public static function getListAccount($lastDate, $take, $nextSkip) {
        $sql = DB::table('accounts')
                        ->join('accounts_cstm', 'accounts.id', '=', 'accounts_cstm.id_c')
                        ->where('accounts.deleted', 0)
                        ->where('accounts.account_type', 'GOVERNMENT')
                        //->where('accounts.id','2097574c-76a3-4377-9456-d8eb0ef465bf')
                        ->where(DB::raw("DATE(CONVERT_TZ(accounts.date_entered,'+00:00','+08:00'))"), '<=', $lastDate)
                        ->where(DB::raw("DATE(CONVERT_TZ(accounts.date_modified,'+00:00','+08:00'))"), '!=', '2022-02-10')
                        ->where(DB::raw("DATE(CONVERT_TZ(accounts.date_modified,'+00:00','+08:00'))"), '!=', '2022-02-11')
                        ->skip($nextSkip)->take($take)->limit('1000');

        return $sql->get();
    }

}
