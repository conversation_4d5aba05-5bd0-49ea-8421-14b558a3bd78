<?php

namespace App\Migrate\Crm;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Services\EPService;

class TaskMissingImport implements ToCollection, WithHeadingRow
{
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {
            $caseNumber = intval($row['crm_case_no']);
            $case = DB::table('cases')->where('case_number', $caseNumber)->first();

            if ($case) {
                $statusTask = trim($row['status']);
                $resolution = trim($row['remarks']);
                foreach (EPService::$TASK_MISSING_STATUS as $key => $value) {
                    if ($value == $statusTask) {
                        $statusTask = $key;
                        break;
                    }
                }
                if ($statusTask == null) {
                    $statusTask = '00'; //Pending In Action
                }
                $is_case_closed = 0;
                if ($case->status == 'Closed_Closed' || $case->status == 'Open_Resolved') {
                    $is_case_closed = 1;
                    if ($statusTask != '55') {
                        $statusTask = '55'; //Done
                        $resolution = $resolution . '&#13;&#10;Updated &#13;&#10;Case CRM resolved on ' . $case->date_modified;
                    }
                }
                DB::connection('mysql_ep_support')
                    ->insert('insert into ep_task_missing 
                        (case_no, case_status, batch, doc_no, module, process_status, composite_instance_id, resolution, is_case_closed, created_at, created_by)
                        values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
                        [
                            $row['crm_case_no'],
                            $case->status,
                            $row['batch'],
                            $row['document_number'],
                            trim($row['module']),
                            $statusTask,
                            $row['composite_instance_id'],
                            $resolution,
                            $is_case_closed,
                            Carbon::now(),
                            'epSupportSystem'
                        ]
                    );
            } else {
                dump($row);
            }
        }
    }
}
