<?php

namespace App\Migrate\Crm;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TopPtjImport implements ToCollection, WithHeadingRow
{
    public function collection(Collection $rows)
    {
        $counterFound = 0;
        foreach ($rows as $row) {
            $ptjCode = trim($row['ptjcode'] ?? '');
            $ptjName = trim($row['ptjname'] ?? '');

            var_dump("#######");
            var_dump("PTJ CODE : " . $ptjCode);
            var_dump("PTJ NAME : " . $ptjName);

            $ptj = DB::connection('mysql_crm')->table('accounts as A ')
                ->where('A.org_gov_code', $ptjCode)
                ->where('A.deleted', 0)
                ->first();

            if ($ptj) {
                $counterFound++;
                var_dump("  ------> Ok found " . $counterFound . " : " . $ptjCode);
                var_dump("      ORG GOV CODE : " . $ptj->org_gov_code);
                var_dump("      PTJ NAME : " . $ptj->name);

                $update = DB::table('accounts')
                    ->where('org_gov_code', $ptj->org_gov_code)
                    ->where('deleted', 0)
                    ->update(['top_ptj_500' => 'Yes']);

                Log::info('Total update for PTJ Code : ' . $ptjCode . ' = ' . $update);
                var_dump('Total update for PTJ Code : ' . $ptjCode . ' = ' . $update);
            } else {
                Log::info("  ------>>>> TAK JUMPA : " . $ptjCode);
                var_dump("  ------>>>> TAK JUMPA : " . $ptjCode);
            }
        }
    }
}
