<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\Crm;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Migrate\MigrateUtils;

class UpdateCaseS3 {

    public static function runCheckingUpdateCaseS3($dateStart = null, $dateEnd = null) {

        Log::debug(self::class . ' Starting... to find & update Case with Severity S3 to S2', ['Query Start Date' => $dateStart, 'Query End Date' => $dateEnd]);
        $dtStartTime = Carbon::now();

        self::UpdateCaseS3();

        Log::info(self::class . ' Completed.. Success Update Cases S3 to S2 --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function UpdateCaseS3() {

        $take = 1000;
        $dateToday = Carbon::now()->format('Y-m-d');
        $dateYesterday = Carbon::yesterday()->format('Y-m-d');

        do {

            $result = "SELECT DISTINCT `c`.`case_number` AS `case_number`, c.`status` AS caseStatus,`itspec_t`.`status` AS taskStatus,
                        `c`.`id` AS `case_id`, itspec_t.`date_entered` as taskCreated,
                        `itspec_t`.`id` AS `task_id`,
                        CONVERT_TZ(`c`.`date_entered`,'+00:00','+08:00') AS `itseverity_case_created`,
                        `c`.`name` AS `itseverity_name`,
                        `itspec_tc`.`task_number_c` AS `itseverity_task_number`,
                        `itspec_tc`.`sla_task_flag_c` AS `itseverity_sla_flag`,
                       cc. category_desc_c, cc.sub_category_desc_c, cc.sub_category_2_desc_c, itspec_t.`name` AS taskname,
                        CONVERT_TZ(`itspec_t`.`date_start`,'+00:00','+08:00') AS `itseverity_start_datetime`,
                        CONVERT_TZ(`itspec_t`.`date_due`,'+00:00','+08:00') AS `itseverity_due_datetime`,
                        `itspec_t`.`date_start` AS `itseverity_available_start_datetime`,
                        `itspec_t`.`date_due` AS `itseverity_available_due_datetime`,
                        CONVERT_TZ(`itspec_tc`.`sla_start_4hr_c`,'+00:00','+08:00') AS `itseverity_actual_start_datetime`,
                        CONVERT_TZ(`itspec_tc`.`sla_stop_4hr_c`,'+00:00','+08:00') AS `itseverity_completed_datetime`,
                        TIMESTAMPDIFF(SECOND,CONVERT_TZ(`itspec_t`.`date_start`,'+00:00','+08:00'),CONVERT_TZ(`itspec_t`.`date_due`,'+00:00','+08:00')) AS `itseverity_available_duration`,
                        TIMESTAMPDIFF(SECOND,CONVERT_TZ(`itspec_tc`.`sla_start_4hr_c`,'+00:00','+08:00'),CONVERT_TZ(`itspec_tc`.`sla_stop_4hr_c`,'+00:00','+08:00')) AS `itseverity_actual_duration` 
                        FROM ((((`cases` `c` 
                        JOIN `cases_cstm` `cc` ON((`c`.`id` = `cc`.`id_c`))) 
                        LEFT JOIN `tasks` `itspec_t` ON((`c`.`id` = `itspec_t`.`parent_id`))) 
                        LEFT JOIN `tasks_cstm` `itspec_tc` ON(((`itspec_t`.`id` = `itspec_tc`.`id_c`)))) 
                        LEFT JOIN `cstm_list_app` `subcat` ON(((`cc`.`sub_category_c` = `subcat`.`value_code`) 
                        AND (`subcat`.`type_code` = 'cdc_sub_category_list') 
                        AND (TRIM(`subcat`.`value_code`) <> '') 
                        AND (`itspec_t`.`date_entered` >= '2020-06-01')
                        AND (STR_TO_DATE(CONVERT_TZ(`itspec_t`.`date_modified`,'+00:00','+08:00'),'%Y-%m-%d') 
                        BETWEEN ? AND ?)
                        AND (`subcat`.`value_code` NOT IN ('10712_15034','10714_15842','10713_15534'))))) 
                        WHERE ((`cc`.`incident_service_type_c` = 'incident_it') 
                        AND (`cc`.`request_type_c` = 'incident')
                        AND (`c`.`deleted` = 0)
                        AND (`itspec_t`.`deleted` = 0) 
                        AND ( itspec_t.`assigned_user_id` <> '15c7bd74-12f3-311b-9e8f-5a810702a1a5')
                        AND (`itspec_tc`.`task_number_c` IS NOT NULL) 
                        AND (TRIM(`subcat`.`value_code`) <> ''))
                        ORDER BY itspec_t.`date_entered` DESC";

            $resultTask = DB::connection('mysql_crm')->select($result, array($dateYesterday, $dateToday));
            $total = count($resultTask);
            if ($total > 0) {
                self::checkSeverity($resultTask);
            }
        } while (count($resultTask) == $take);
    }

    private static function checkSeverity($data) {


        foreach ($data as $row) {

            $dateSLAAvailableStartS1 = new Carbon($row->itseverity_available_start_datetime);
            $dateSLAAvailableStopS1 = Carbon::parse($dateSLAAvailableStartS1)->addDays(1);

            $dateSLAAvailableStartS2 = new Carbon($row->itseverity_available_start_datetime);
            $dateSLAAvailableStopS2 = Carbon::parse($dateSLAAvailableStartS2)->addDays(3);

            $dateSLAAvailableStartS3 = new Carbon($row->itseverity_available_start_datetime);
            $dateSLAAvailableStopS3 = Carbon::parse($dateSLAAvailableStartS3)->addDays(5);

            $slaAvailableStart = new Carbon($row->itseverity_start_datetime);
            $slaAvailableStop = new Carbon($row->itseverity_due_datetime);
            $diffAvailable = $slaAvailableStop->diffInSeconds($slaAvailableStart);

            $dateSLAActualStart = new Carbon($row->itseverity_actual_start_datetime);
            $dateSLAActualStop = new Carbon($row->itseverity_completed_datetime);

            $durationAvailableS1 = $dateSLAAvailableStopS1->diffInSeconds($dateSLAAvailableStartS1);
            $durationAvailableS2 = $dateSLAAvailableStopS2->diffInSeconds($dateSLAAvailableStartS2);
            $durationAvailableS3 = $dateSLAAvailableStopS3->diffInSeconds($dateSLAAvailableStartS3);
            $AdiffInMinutesActualTime = $dateSLAActualStop->diffInSeconds($dateSLAActualStart);

            /*
             * check if selected severity is s3 and task completed less than 5 days, then reduce the available sla
             * if 1 day, then reduce to s1
             * if within 3 days, then reduce to s2
             * 
             * need to add checking :
             * 
             * 
             * if selected severity is s1 and task completed more than 1 day, then extend the available sla
             * if selected severity is s2 and task completed more than 3 days, then extend the available sla
             * 
             * s3 available : 432000
             * s2 available : 259200
             * s1 available : 86400
             */

            $caseNo = $row->case_number;
            $caseId = $row->case_id;
            $taskId = $row->task_id;
            $severity = $row->itseverity_sla_flag;
            $severityList = ['s1', 's2', 's3'];
            if ($row->taskStatus == 'Completed' && in_array($severity, $severityList)) {
                if ($severity == 's1') { 
                    if (($AdiffInMinutesActualTime > $durationAvailableS1) && ($AdiffInMinutesActualTime <= $durationAvailableS2)) {
                        Log::info($caseNo . ' > ' .$severity .' > Actual Bigger Than Available (s1) and Smaller Than s2 ' . $dateSLAAvailableStartS2 . ' : ' . $dateSLAAvailableStopS2);
                        self::updateSeverity($caseId, $taskId, $dateSLAAvailableStopS2, 's2');
                    } else if (($AdiffInMinutesActualTime > $durationAvailableS1) && ($AdiffInMinutesActualTime > $durationAvailableS2)) {
                        Log::info($caseNo . ' > ' .$severity .' > Actual Bigger Than Available (s1) and Bigger Than s2 ' . $dateSLAAvailableStartS3 . ' : ' . $dateSLAAvailableStopS3);
                        self::updateSeverity($caseId, $taskId, $dateSLAAvailableStopS3, 's3');
                    }
                } else if ($severity == 's2') { 
                    if (($AdiffInMinutesActualTime <= $durationAvailableS1)) {
                        Log::info($caseNo . ' > ' .$severity .' > Actual Smaller Than Available (s2) ' . $dateSLAAvailableStartS1 . ' : ' . $dateSLAAvailableStopS1);
                        self::updateSeverity($caseId, $taskId, $dateSLAAvailableStopS1, 's1');
                    } else if (($AdiffInMinutesActualTime > $durationAvailableS2)) {
                        Log::info($caseNo . ' > ' .$severity .' > Actual Bigger Than Available (s2) ' . $dateSLAAvailableStartS3 . ' : ' . $dateSLAAvailableStopS3);
                        self::updateSeverity($caseId, $taskId, $dateSLAAvailableStopS3, 's3');
                    }
                } else { 
                    if (($AdiffInMinutesActualTime <= $durationAvailableS1)) {
                        Log::info($caseNo . ' > ' .$severity .' > Actual Smaller Than Available (s3) And Duration s1 ' . $dateSLAAvailableStartS1 . ' : ' . $dateSLAAvailableStopS1);
                        self::updateSeverity($caseId, $taskId, $dateSLAAvailableStopS1, 's1');
                    } else if (($AdiffInMinutesActualTime > $durationAvailableS1) && ($AdiffInMinutesActualTime <= $durationAvailableS2)) {
                        Log::info($caseNo . ' > ' .$severity .' > Actual Smaller Than s2 And Bigger Than s1 ' . $dateSLAAvailableStartS2 . ' : ' . $dateSLAAvailableStopS2);
                        self::updateSeverity($caseId, $taskId, $dateSLAAvailableStopS2, 's2');
                    }
                }
            }
        }
    }

    private static function updateSeverity($caseId, $taskId, $dateSLAAvailableStop, $severity) {

        Log::info('updateSeverity ' .$caseId .' ' .$taskId);
        DB::table('tasks')
                ->where('parent_id', $caseId)
                ->where('id', $taskId)
                ->update(['date_due' => $dateSLAAvailableStop,
                    'task_severity' => $severity]);

        DB::table('tasks_cstm')
                ->where('id_c', $taskId)
                ->update(['sla_task_flag_c' => $severity]);
    }

}
