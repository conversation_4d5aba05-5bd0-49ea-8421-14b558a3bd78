<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\Crm;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Migrate\MigrateUtils;
use App\Services\CRMService;

class UpdateCaseStatus {

    public static function crmService() {
        return new CRMService;
    }

    public static function runUpdateCaseStatus($dateReport) {

        dump(self::class . ' Starting ... runUpdateCaseStatus ');
        Log::debug(self::class . ' Starting ... runUpdateCaseStatus ');
        $dtStartTime = Carbon::now();

        self::checkCaseStatus($dateReport);

        dump(self::class . ' Completed runUpdateCaseStatus --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info(self::class . ' Completed runUpdateCaseStatus --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function checkCaseStatus($dateReport) {

        dump(self::class . ' Start Check Case Status ' . __FUNCTION__ .' Date ' .$dateReport);
        Log::info(self::class . ' Start Check Case Status ' . __FUNCTION__ .' Date ' .$dateReport);

        $take = 50;
        $skip = 50;
        $countTask = 0;

        $dtStartTime = Carbon::now();

        do {
            $taskStatus = DB::table('cases as a')
                    ->join('cases_cstm as b', 'b.id_c', '=', 'a.id')
                    ->whereNull('a.status')
                    ->where('a.deleted', 0)
                    ->select('a.*', 'b.*')
                    ->take($take)
                    ->skip($skip * $countTask++);

            $resultTask = $taskStatus->get();
            $total = count($resultTask);
            if ($total > 0) {
                self::checkCase($resultTask);
            }
        } while (count($resultTask) == $take);

        dump(self::class . ' Update Case Status , Counter :' . $countTask . ' Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info(self::class . ' Update Case Status , Counter :' . $countTask . ' Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function checkCase($result) {

        foreach ($result as $row) {

            $caseId = $row->id;
            $caseNumber = $row->case_number;
            $caseState = $row->state;
            $caseStatus = $row->status;
            $reqType = $row->request_type_c;
            $incidentServiceType = $row->incident_service_type_c;
            $category = self::crmService()->getValueLookupCRM('category_list', $row->category_c);
            $subCategory = self::crmService()->getValueLookupCRM('cdc_sub_category_list', $row->sub_category_c);
            $subSubCategory = self::crmService()->getValueLookupCRM('cdc_sub_category_2_list', $row->sub_category_2_c);

            self::updateStatus($caseId, $caseNumber, $caseState, $caseStatus, $reqType, $incidentServiceType, $category, $subCategory, $subSubCategory);
        }
    }

    private static function updateStatus($caseId, $caseNumber, $state, $status, $reqType, $incidentServiceType, $category, $subCategory, $subSubCategory) {

        $closed = array('Closed_Closed', 'Closed_Cancelled_Eaduan', 'Closed_Rejected', 'Closed_Rejected_Eaduan', 'Closed_Duplicate', 'Closed_Verified_Eaduan');

        if ($reqType == 'enquiry' && $status == '') {
            if ($state == 'Closed') {
                DB::table('cases')
                        ->where('id', $caseId)
                        ->update(['status' => 'Closed_Closed']);
            } else {
                DB::table('cases')
                        ->where('id', $caseId)
                        ->update(['status' => 'Open_Assigned']);
            }

            dump('Update Case Enquiry : ' . $caseNumber . ' request type ' . $reqType . ' state ' . $state . ' status ' . $status
                    . ' Category ' . $category . ' Sub category ' . $subCategory . ' Sub sub category ' . $subSubCategory);
            Log::info('Update Case Enquiry : ' . $caseNumber . ' request type ' . $reqType . ' state ' . $state . ' status ' . $status
                    . ' Category ' . $category . ' Sub category ' . $subCategory . ' Sub sub category ' . $subSubCategory);
        } else {

            $task = self::crmService()->getDetailTaskLatestCRM($caseId);
            if ($task) {

                $taskName = $task->name;
                $taskStatus = self::crmService()->getValueLookupCRM('cdc_task_status_list', $task->status);
                $taskAssignGroupId = $task->assigned_user_id;

                // Onsite Support
                if ($category == 10719 || $category == 10720) {

                    if ($taskName == 'Pending Approval - Onsite Support' && $taskAssignGroupId == '22f5826e-3c95-5a9a-be15-5c2edb2c2a21') {

                        if ($state == 'Closed' && $status == 'Open_Pending_Approval' && $taskStatus == 'Pending Acknowledgement') {
                            DB::table('cases')
                                    ->where('id', $caseId)
                                    ->update(['state' => 'Open']);
                        } else if ($state == 'Open' && $status == 'Closed_Approved' && ($taskStatus == 'Approved' || $taskStatus == 'Rejected')) {
                            DB::table('cases')
                                    ->where('id', $caseId)
                                    ->update(['state' => 'Closed']);
                        }
                    }
                    // Log Case
                } else {

                    // Case State is Open when task is Assigned to Case Owner
                    if ($state == 'Open' && $taskName == 'Assigned to Case Owner' && $taskAssignGroupId == '2732c16c-1fff-9e6c-bb4e-58997edd3340') {

                        if ($incidentServiceType == 'service_it' || $incidentServiceType == 'incident_it') {
                            if ($taskStatus == 'Pending Acknowledgement') {
                                DB::table('cases')
                                        ->where('id', $caseId)
                                        ->update(['status' => 'Open_Resolved']);
                            } else {
                                DB::table('cases')
                                        ->where('id', $caseId)
                                        ->update(['status' => 'Pending_User_Verification']);
                            }
                        } else if ($incidentServiceType == 'service_business') {
                            DB::table('cases')
                                    ->where('id', $caseId)
                                    ->update(['status' => 'Open_Assigned']);
                        }

                        // Case state is open but status is closed?? Need to update state to closed when task is complete
                    } else if ($state == 'Open' && in_array($status, $closed)) {

                        if ($taskStatus == 'Rejected' || $taskStatus == 'Duplicate' || $taskStatus == 'Completed') {
                            DB::table('cases')
                                    ->where('id', $caseId)
                                    ->update(['state' => 'Closed']);
                        }

                        // Case state is closed but status is open?? Need to update state to open when task is still not complete
                    } else if ($state == 'Closed' && ($status == 'Pending_User_Verification' || $status == 'Open_Resolved')) {

                        if ($taskStatus == 'Acknowledge' || $taskStatus == 'Pending Acknowledgement') {
                            DB::table('cases')
                                    ->where('id', $caseId)
                                    ->update(['state' => 'Open']);
                        }
                    }
                }
            }

            dump('Update Case Incident/Service : ' . $caseNumber . ' request type ' . $reqType . ' state ' . $state . ' status ' . $status
                    . ' Category ' . $category . ' Sub category ' . $subCategory . ' Sub sub category ' . $subSubCategory);
            Log::info('Update Case Incident/Service : ' . $caseNumber . ' request type ' . $reqType . ' state ' . $state . ' status ' . $status
                    . ' Category ' . $category . ' Sub category ' . $subCategory . ' Sub sub category ' . $subSubCategory);
        }
    }

}
