<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\Crm;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Excel;
use App\Models\Contact;
use App\Models\ContactCustom;
use Ramsey\Uuid\Uuid;
use App\Migrate\MigrateUtils;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Services\CRMService;

class UpdateCheckCaseStatusExcel {

    public static function crmService() {
        return new CRMService;
    }
    
    public static function run() {
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();
        //self::updateExcel();
        //self::updateExcelForCaseInvestigationOrRedmine();  //Expired Code
        self::updateExcelResolutionCategory();   
//        self::updateCaseStatusForTaskMissing();
        
        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }
    
    protected static function updateExcelForCaseInvestigationOrRedmine() {
        //$filename = '/app/Migrate/Crm/data/CaseDetailsForInvetigation_28Apr2018.xls';
				
	$filename = '/app/Migrate/Crm/data/CaseDetailsForInvetigation_28Apr2018_Updated.xls';
        
        dump('starting ...'.__FUNCTION__);
        Log::info('starting ...'.__FUNCTION__);
        
        $collection = collect([]);
        Excel::load($filename, function($reader) use (&$collection) {
            $reader->each(function($row) use (&$collection) {
                $caseNumber = intval($row->case_no);
                $remark_check = trim($row->remark);
                $updated_by = trim($row->updateby);
                
                dump('Searching CaseNo: '.$caseNumber. ' >> for remark : '.$remark_check);
                Log::info('Searching CaseNo: '.$caseNumber. ' >> for remark : '.$remark_check);
                
                $case = DB::table('cases')->where('case_number',$caseNumber)->first();
                if($case != null){
                    dump('Found! ->> '.$case->case_number. ' >> case status: '.$case->status);
                    Log::info('Found! ->> '.$case->case_number. ' >> case status: '.$case->status);
                    
                    if($case->status == 'Open_Assigned'){
                        dump(' >> checking task .. ');
                        Log::info(' >> checking task .. ');
                        $task = self::crmService()->getDetailTaskLatestCRM($case->id);
                        if($task){
                            
                            $user = self::crmService()->getDetailUserCRMByUsername($updated_by);
                            
                            $taskNumber = $task->task_number_c;
                            $taskStatus = $task->status;
                            dump('Found! ->> '.$taskNumber. ' >> task status: '.$taskStatus);  
                            Log::info('Found! ->> '.$taskNumber. ' >> task status: '.$taskStatus);
                            
                            $casePendingInvestigation = 'Pending_Investigation';
                            $taskPendingInvestigation = 'Pending_Investigation';
                            $taskInProgress = 'In Progress';
                            $redmine = '#Redmine';
                            
                            if($remark_check == 'Further Investigation'){
                                // Will update case status, task status
                                $data= collect([]);
                                $data->put("CaseNo", $caseNumber);  
                                $data->put("CaseStatus", $casePendingInvestigation);
                                $data->put("CaseUpdateBy", $user->user_name);
                                $data->put("CaseUpdateByID", $user->id);
                                $data->put("TaskNo", $taskNumber); 
                                $data->put("TaskStatus", $taskPendingInvestigation); 
                                $data->put("TaskUpdateBy", $user->user_name);
                                $data->put("TaskUpdateByID", $user->id);
                                $collection->push($data);
                                dump($data);
                                Log::info($data);
                                
                                DB::table('cases')
                                    ->where('id', $case->id)
                                    ->update([
                                        'status' => $casePendingInvestigation,
                                        'modified_user_id' => $user->id,
                                        'date_modified' => Carbon::now()
                                       ]);
                                
                                DB::table('tasks')
                                    ->where('id', $task->id)
                                    ->update([
                                        'status' => $taskPendingInvestigation,
                                        'modified_user_id' => $user->id,
                                        'date_modified' => Carbon::now()
                                       ]);
                              
                            }else 
                            if($remark_check == 'Log in Redmine'){
                                // Will update case redmine , task status,redmine
                                
                                if(strlen(trim($task->case_redmine_number)) > 1){
                                    $redmine = trim($task->case_redmine_number);
                                }
                                
                                $data= collect([]);
                                $data->put("CaseNo", $caseNumber);  
                                $data->put("CaseStatus", $case->status);
                                $data->put("CaseRedmine", $redmine);
                                $data->put("CaseUpdateBy", $user->user_name);
                                $data->put("CaseUpdateByID", $user->id);
                                $data->put("TaskNo", $taskNumber); 
                                $data->put("TaskStatus", $taskInProgress); 
                                $data->put("TaskRedmine", $redmine);
                                $data->put("TaskUpdateBy", $user->user_name);
                                $data->put("TaskUpdateByID", $user->id);
                                $collection->push($data);
                                dump($data);
                                Log::info($data);
                                
                                DB::table('cases')
                                    ->where('id', $case->id)
                                    ->update([
                                        'redmine_number' => $redmine,
                                        'modified_user_id' => $user->id,
                                        'date_modified' => Carbon::now()
                                       ]);
                                
                                DB::table('tasks')
                                    ->where('id', $task->id)
                                    ->update([
                                        'status' => $taskInProgress,
                                        'case_redmine_number' => $redmine,
                                        'modified_user_id' => $user->id,
                                        'date_modified' => Carbon::now()
                                       ]);
                                 
                            }else{
                                
                            }

                        }
                    }
                }
            });
        });
        dump('Completed');
        dump('Total cases : '.count($collection));
        Log::info('Completed');
        Log::info('Total cases : '.count($collection));
    }
    
    
    protected static function updateCaseStatusForTaskMissing() {
        
        $listTaskMissing =  DB::table('cstm_list_app')
                ->whereIn('value_name',[
                    'Check document status',
                    'Missing Role',
                    'Missing data',
                    'Missing from Task List'
                ])
                ->select('value_code')
                ->get();
        $listTaskMissingIds = $listTaskMissing->pluck('value_code');
        
        // WHERE a.id=b.id_c AND b.incident_service_type_c IN ('incident_it','service_it') 
        $list = DB::table('cases as a')
                ->join('cases_cstm as b', 'b.id_c', '=', 'a.id')
                ->whereIn('b.sub_category_2_c',$listTaskMissingIds)
                ->whereIn('b.incident_service_type_c',['incident_it','service_it'])
                ->whereIn('a.status',['open_assigned'])
                ->whereRaw("DATE(CONVERT_TZ(a.date_entered, '+00:00', 'SYSTEM')) 
                                BETWEEN STR_TO_DATE('2018-01-01','%Y-%m-%d') AND STR_TO_DATE('2018-05-03','%Y-%m-%d')")
                ->get();
        Log::info(' Found today : '.count($list));
        dump(' Found today : '.count($list));
        
        $caseInProgress = 'In_Progress';
        $taskInProgress = 'In Progress';
        $user = self::crmService()->getDetailUserCRMByUsername('shahril');
        $collection = collect([]);
        foreach ($list as $case) {
            $redmine = '#Redmine';
            $task = self::crmService()->getDetailTaskLatestCRM($case->id);
            if($task){
                
                if(strlen(trim($task->case_redmine_number)) > 1){
                    $redmine = trim($task->case_redmine_number);
                }
                                
                $data= collect([]);
                $data->put("CaseNo", $case->case_number);  
                $data->put("CaseStatus", $caseInProgress);
                $data->put("CaseRedmine", $redmine);
                $data->put("CaseUpdateBy", $user->user_name);
                $data->put("CaseUpdateByID", $user->id);
                $data->put("TaskNo", $task->task_number_c); 
                $data->put("TaskStatus", $taskInProgress); 
                $data->put("TaskRedmine", $redmine);
                $data->put("TaskUpdateBy", $user->user_name);
                $data->put("TaskUpdateByID", $user->id);
                $collection->push($data);
                
                dump($data);
                Log::info($data);
                
                DB::table('cases')
                ->where('id', $case->id)
                ->update([
                    'status' => $caseInProgress,
                    'redmine_number' => $redmine,
                    'modified_user_id' => $user->id,
                    'date_modified' => Carbon::now()
                   ]);

                DB::table('tasks')
                ->where('id', $task->id)
                ->update([
                    'status' => $taskInProgress,
                    'case_redmine_number' => $redmine,
                    'modified_user_id' => $user->id,
                    'date_modified' => Carbon::now()
                   ]); 
                 
                
             }
            
        }
        //dump($collection);
        
        dump('Completed');
        dump('Total cases : '.count($collection));
        Log::info('Completed');
        Log::info('Total cases : '.count($collection));
    }
    
    
    protected static function updateExcel() {
        $filename = '/app/Migrate/Crm/data/Missing Task List Batch 1 - 12.xlsx';
        $counter = 0;
        $list = collect([]);
        Excel::load($filename, function($reader) use (&$counter, &$list) {
            $reader->each(function($row) use (&$counter, &$list) {
                $caseNumber = intval($row->crm_case_no);
                
                $case = DB::table('cases')->where('case_number',$caseNumber)->first();
                if($case && trim($row->status) == 'Done' ){
                    dump($case->case_number. ' >> status: '.$case->status. ' >> resolution: '.$case->resolution);

                    if($case->status != 'Closed_Closed' && $case->status != 'Open_Resolved'){
                        $counter ++;
                        $row->status_crm = $case->status;
                        $list->push($row);
                    }
                }
            });
        });
        
        Excel::create('updateExcelTaskMissing', function($excel)use($list) {
            $excel->setTitle('Task Missing Cases');

            $excel->sheet('TaskMissing', function($sheet) use ($list) {
                $sheet->setOrientation('landscape');

                $sheet->setStyle(array(
                    'font' => array(
                        'name' => 'Calibri',
                        'size' => 11,
                        'bold' => false
                    )
                ));
                
                //http://www.maatwebsite.nl/laravel-excel/docs/reference-guide#formatting
                $sheet->setColumnFormat(array(
                    'A' => '@',
                    'B' => '@',
                    'C' => '@',
                    'D' => '@',
                    'E' => '@',
                    'F' => '@',
                    'G' => '@',
                    'H' => '@',
                ));
                
                $sheet->row(1, array(
                    'BATCH', 'CRM CASE NO', 'STATUS CRM',
                    'DOC NO', 'MODULE', 'STATUS','COMPOSITE INSTANCE ID',
                    'REMARKS'
                ));    
                $sheet->row(1, function($row) {
                    // call cell manipulation methods
                    $row->setBackground('#684E49');
                });
                $sheet->setAutoSize(true);

                $sheet->cells('A1:H1', function($cells) {
                    // manipulate the range of cells
                    // Set with font color
                    $cells->setFontColor('#fff');

                    // Set font family
                    $cells->setFontFamily('Calibri');

                    // Set font size
                    $cells->setFontSize(11);

                    // Set font weight to bold
                    $cells->setFontWeight('bold');

                    // Set all borders (top, right, bottom, left)
                    $cells->setBorder('solid', 'solid', 'solid', 'solid');
                });
                
                $count = 2;

                foreach ($list as $obj) {
                    $sheet->row($count, array(
                            $obj->batch,
                            $obj->crm_case_no,
                            $obj->status_crm,
                            $obj->document_number,
                            $obj->module,
                            $obj->status,
                            $obj->composite_instance_id,
                            $obj->remarks,
                        )
                    );
                    $count++;
                }
            });         
            
         })->store('xlsx', storage_path('exports/task_missing'));
           
    }

    protected static function updateExcelResolutionCategory() {
        //$filename = '/app/Migrate/Crm/data/CaseDetailsForInvetigation_28Apr2018.xls';

        $filename = '/app/Migrate/Crm/data/ResolutionCategory2021.xlsx';

        dump('starting ...' . __FUNCTION__);
        Log::info('starting ...' . __FUNCTION__);

        $sheets = array('program_fix', 'user_familiarity', 'data_fix_system_problem', 'profile_maintenance', 'system_configuration');
        foreach ($sheets as $sheet) {
            Excel::selectSheets($sheet)->load($filename, function($reader) use (&$collection, $sheet) {
                $reader->each(function($row) use (&$collection, $sheet) {

                    dump('Sheet ->> ' . $sheet);
//                    Log::info('Sheet ->> ' . $sheet);

                    $caseNumber = intval($row->case_no);

                    $case = DB::table('cases')
                            ->where('case_number', $caseNumber)
                            ->first();

                    if ($case != null) {
                        dump('Found! ->> ' . $caseNumber . ' >> id: ' . $case->id);
//                        Log::info('Found! ->> ' . $caseNumber . ' >> id: ' . $case->id);

                        $task = self::crmService()->getDetailTaskLatestCRM($case->id);

                        if ($task) {
                            if ($task->resolution_category_c != null) {
                                $taskNumber = $task->task_number_c;
                                $taskResolutionCategory = $task->resolution_category_c;
                                dump('Task Found! ->> ' . $taskNumber . ' >> task resolution category: ' . $taskResolutionCategory);
//                                Log::info('Task Found! ->> ' . $taskNumber . ' >> task resolution category: ' . $taskResolutionCategory);

                        DB::table('tasks')
                                ->where('id', $task->id)
                                ->update([
                                    'resolution_category_c' => $sheet,
                        ]);
                            } else {

                                if ($task->resolution_category_c === null && $task->name === 'Closed by eAduan' || $task->name === 'Cancelled by eAduan') {
                                    $tasks = self::crmService()->getDetailTaskCRM($case->id);

                                    if ($tasks && $tasks->resolution_category_c != 'program_fix') {
                                        dump('Task Found! ->> ' . $tasks->task_number_c . ' >> Name ' . $task->name . ' >> task resolution category: ' . $tasks->resolution_category_c);
//                                        Log::info('Task Found! ->> ' . $tasks->task_number_c . ' >> Name ' . $task->name . ' >> task resolution category: ' . $tasks->resolution_category_c);

                        DB::table('tasks')
                                ->where('id', $tasks->id)
                                ->update([
                                    'resolution_category_c' => $sheet,
                        ]); 
                                    } else {
                                        dump('No Task Found! ->> ' . $caseNumber);
//                                        Log::info('No Tasks Found! ->> ' . $caseNumber);
                                    }
                                }
                            }
                        }
                    }
                });
            });
        }

        dump('Completed');
        dump('Total cases : ' . count($collection));
        Log::info('Completed');
        Log::info('Total cases : ' . count($collection));
    }

}
