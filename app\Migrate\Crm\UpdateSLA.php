<?php

namespace App\Migrate\Crm;

use Carbon\Carbon;
use Carbon\CarbonInterval;
use Illuminate\Support\Facades\Log;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Facades\DB;
use App\Models\Cases;
use App\Models\CasesCustom;
use App\Models\Tasks;
use App\Models\TaskCustom;
use App\Migrate\MigrateUtils;
use Illuminate\Support\Facades\Config;

class UpdateSLA {

    public static function runUpdateSLA($dateStart = null, $dateEnd = null) {

        Log::debug(self::class . ' Starting ... runUpdateSLA ', ['Query Start Date' => $dateStart, 'Query End Date' => $dateEnd]);
        $dtStartTime = Carbon::now();

        self::checkSLAInitialTask($dateStart, $dateEnd);

        Log::info(self::class . ' Completed runUpdateSLA --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function checkSLAInitialTask($dateStart, $dateEnd) {

        Log::info(self::class . ' Start Check SLA for Initial Task' . __FUNCTION__ . '     ->> Date Start: ' . $dateStart . ', Date End: ' . $dateEnd);
        var_dump('Date Start: ' . $dateStart);
        var_dump('Date End: ' . $dateEnd);

        $take = 1000;
        $skip = 1000;
        $countTask = 0;

        $dtStartTime = Carbon::now();

        //check for initial tasks 
        do {
            $taskStatus = DB::table('cases as a')
                    ->join('cases_cstm as b', 'b.id_c', '=', 'a.id')
                    ->join('tasks as c', 'c.parent_id', '=', 'a.id')
                    ->join('tasks_cstm as d', 'd.id_c', '=', 'c.id')
                    ->where('c.status', 'Pending Acknowledgement')
                    ->where('c.name', 'like', '%initial%')
                    ->where('b.request_type_c', 'incident')
                    ->where('a.deleted', 0)
                    ->where('c.deleted', 0)
                    ->where('b.incident_service_type_c', 'incident_it')
                    ->select('c.*', 'd.*', 'a.case_number as casenumber')
                    ->take($take)
                    ->skip($skip * $countTask++);

            $resultTask = $taskStatus->get();
            $total = count($resultTask);
            if ($total > 0) {
                self::checkTask($resultTask);
            }
        } while (count($resultTask) == $take); 
        
        Log::info(self::class . ' UpdateSLA for Initial Task , Counter :' . $countTask . ' Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        }

    private static function checkTask($data) {

        $counter = 0;

        foreach ($data as $row) {
            $count = $counter++;

            $now = Carbon::now();
            $date_start = new Carbon($row->date_start);
            $dateStart = $date_start->addHour(8);
            $diff3minutes = $now->diffInMinutes($dateStart);

            $date_due = new Carbon($row->date_due);
            $datedue = $date_due->addHour(8);

            //update sla_stop_15min_c, acknowledge_time_c, status
            if ($diff3minutes >= 3) {

                    $minSlaStart = strtotime("+5 minutes",strtotime($date_start));
                    $maxSlaStart = strtotime("-2 minutes",strtotime($date_due));
                    $slaVal = mt_rand($minSlaStart, $maxSlaStart);
                    $random = date('Y-m-d H:i:s', $slaVal);
                    $slaRandom = new Carbon($random);
                    $slaRandomDate = $slaRandom->subHour(8);
                    
                if ($now > $datedue) {

                    Log::info(self::class . ' Task SLA 3 minutes already...SLA Burst already..... Need to update acknowledge time and sla stop ' .
                            $row->casenumber . ' SLA Start : ' . $dateStart . ' SLA Stop : ' . $date_start .  ' randomsla ' . $slaRandomDate);

                    self::updateTaskSla($row, $count, $slaRandomDate);

                } 
                else {
                    
                    Log::info(self::class . ' Task SLA 3 minutes already...Within SLA 15 minutes..... Need to update acknowledge time ' . 
                            $row->casenumber . ' SLA Start : ' . $date_start . ' SLA Stop : ' . $date_due .  ' randomsla ' . $slaRandomDate);
                    
                    self::updateTaskSla($row, $count, $slaRandomDate);
                }

            } else {
                Log::info(self::class . ' Checking..... ' . $row->casenumber . ' SLA Start : ' . $dateStart . ' SLA Stop : ' . $datedue);
            }
        }
    }
    
    private static function updateTaskSla($row, $count, $randomDate) {

        Log::info(self::class . ' Entering updateTaskSla()..... ' . $row->casenumber);
        
        DB::table('tasks_cstm')
                ->where('id_c', $row->id)
                ->update(['sla_stop_15min_c' => $randomDate]);

        DB::table('tasks')
                ->where('id', $row->id)
                ->update(['status' => 'Acknowledge']);
        self::copySlaStop($row, $count);
    }

    private static function copySlaStop($data, $count) {
        $taskCstm = TaskCustom::find($data->id);
        if ($taskCstm->sla_stop_15min_c != $taskCstm->acknowledge_time_c) {
            DB::table('tasks_cstm')
                    ->where('id_c', $taskCstm->id_c)
                    ->update(['acknowledge_time_c' => $taskCstm->sla_stop_15min_c]);
            Log::info(self::class . ' Success , Counter :' . $count . ' Task Id :' . $taskCstm->id_c);
        }
    }
}
