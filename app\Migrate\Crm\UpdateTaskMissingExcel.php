<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\Crm;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use Maatwebsite\Excel\Facades\Excel;
use App\Migrate\Crm\TaskMissingImport;
use App\Models\Contact;
use App\Models\ContactCustom;
use Ramsey\Uuid\Uuid;
use App\Migrate\MigrateUtils;
use App\Services\EPService;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Services\CRMService;

class UpdateTaskMissingExcel {

    public static function crmService() {
        return new CRMService;
    }
    
    public static function run() {
        ini_set('memory_limit', '-1');
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();
        self::updateExcel();
        
        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
   
        
    }
    
    public static function runCheckCaseStatus() {
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();
        self::checkCaseStatus();
        
        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }
    
    public static function runAddCaseAsTaskMissing() {
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();
        self::addCaseAsTaskMissing();
        
        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }
 
    protected static function updateExcel() {
        $filename = base_path('app/Migrate/Crm/data/Missing Task List Batch 1 - 12.xlsx');
        Excel::import(new TaskMissingImport, $filename);
    }

    public static function updateTaskMissing(){
        $list = DB::connection('mysql_ep_support')
                    ->table('ep_task_missing')
                    ->whereNull('case_id')
                    ->orWhereNull('module')
                    ->get();
 
        $counter = 0;
        $modules = collect([]);
        foreach ($list as $obj){
            
            $caseObj = DB::table('cases as a')
                ->join('cases_cstm as b', 'b.id_c', '=', 'a.id')
                ->join('cstm_list_app as c', 'b.sub_category_c', '=', 'c.value_code')
                ->where('a.case_number',$obj->case_no)
                ->select('a.*','b.*','c.value_code','c.value_name')
                ->first();
            if($caseObj) {
                DB::connection('mysql_ep_support')
                ->table('ep_task_missing')
                ->where('task_id', $obj->task_id)
                ->update([
                    'case_id' => $caseObj->id,
                    'module' => $caseObj->value_name,
                    ]);
                $data = [
                    'case_id' => $caseObj->id,
                    'module' => $caseObj->value_name,
                    ];
                dump($data);
                $modules->push($caseObj->value_name);
                $counter++;
            }
            
        }
        dump('found: '.count($list));
        dump('total: '.$counter);
        dump($modules->unique());
    }
    
    public static function addCaseAsRedmine(){
        
        $listTaskMissing =  DB::table('cstm_list_app')
                ->whereIn('value_name',[
                    'Check document status',
                    'Missing Role',
                    'Missing data',
                    'Missing from Task List'
                ])
                ->select('value_code')
                ->get();
        $listTaskMissingIds = $listTaskMissing->pluck('value_code');

        $list = DB::table('cases as a')
                ->join('cases_cstm as b', 'b.id_c', '=', 'a.id')
                ->join('cstm_list_app as c', 'b.sub_category_c', '=', 'c.value_code')
                ->whereIn('b.sub_category_2_c',$listTaskMissingIds)
                ->whereNotIn('a.status',['Open_Resolved','Closed_Closed','Pending_User_Verification'])
                //->whereDate('a.date_modified', Carbon::now()->format('Y-m-d')) 
                ->select('a.*','b.*','c.value_code','c.value_name')
                ->get();
        Log::info(' Found today : '.count($list));
        dump(' Found today : '.count($list));
        $totalAdded = 0;
        foreach($list as $caseObj){
           // dd($caseObj);
            /**Auto update Redmine */ 
            $redmine = '#Redmine';
            if($caseObj->redmine_number == ''){
                DB::table('cases')
                    ->where('id', $caseObj->id)
                    ->update([
                        'redmine_number' => $redmine
                       ]);

                $taskObj = self::crmService()->getDetailTaskLatestCRM($caseObj->id);
                if($taskObj && $taskObj->case_redmine_number == ''){
                    DB::table('tasks')
                        ->where('id', $taskObj->id)
                        ->update([
                            'case_redmine_number' => $redmine
                           ]);
                }

                $totalAdded++;
            }
        }
        
        Log::info(' Total Added : '.$totalAdded);
        dump(' Total Added : '.$totalAdded);
    }
    
    protected static function insertCaseAsTaskMissing($caseObj,&$totalAdded){
        
        $taskMissCheck = DB::connection('mysql_ep_support')
                ->table('ep_task_missing')
                ->where('case_no', $caseObj->case_number)
                ->count();
        if($taskMissCheck  == 0){
            
            dump('Case #: '.$caseObj->case_number);
            dump('  Case Created Date UT8 : '.$caseObj->date_entered);
            dump('  Case Created Date KualaLumpur : '.Carbon::parse($caseObj->date_entered)->addHour(8)->toDateTimeString());
            Log::info(__FUNCTION__.'> Case #: '.$caseObj->case_number);
            Log::info(__FUNCTION__.'>   Case Created Date UT8 : '.$caseObj->date_entered);
            Log::info(__FUNCTION__.'>   Case Created Date KualaLumpur : '.Carbon::parse($caseObj->date_entered)->addHour(8)->toDateTimeString());
            
        
            /**Auto update Redmine */ 
            $redmine = '#Redmine';
            if($caseObj->redmine_number == ''){
                DB::table('cases')
                    ->where('id', $caseObj->id)
                    ->update([
                        'redmine_number' => $redmine
                       ]);

                $taskObj = self::crmService()->getDetailTaskLatestCRM($caseObj->id);
                if($taskObj && $taskObj->case_redmine_number == ''){
                    DB::table('tasks')
                        ->where('id', $taskObj->id)
                        ->update([
                            'case_redmine_number' => $redmine
                           ]);
                }
            }  

            DB::connection('mysql_ep_support')
                ->insert('insert into ep_task_missing 
                (   case_no,
                    case_status,
                    case_created,
                    case_id,
                    batch,
                    doc_no,
                    module,
                    problem,
                    process_status,
                    created_at,
                    created_by ) 
                values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', 
                [   
                    $caseObj->case_number, 
                    $caseObj->status,
                    Carbon::parse($caseObj->date_entered)->addHour(8)->toDateTimeString(),
                    $caseObj->id,
                    '',
                    $caseObj->doc_no,
                    $caseObj->value_name,
                    $caseObj->description,
                    '00',
                    Carbon::now(),
                    'SchedulerSystem',
                        ]);

            $totalAdded++;
            dump('addCaseAsTaskMissing -> Added cases : '.$caseObj->case_number.' on created case at: '.Carbon::parse($caseObj->date_entered)->addHour(8)->toDateTimeString());
        }
    }
    
    protected static function addCaseAsTaskMissing(){
        
        $listTaskMissing =  DB::table('cstm_list_app')
                ->whereIn('value_name',[
                    //'Check document status',
                    //'Missing Role',
                    //'Missing data',
                    'Missing from Task List',
                    'Stuck Task List'
                ])
                ->select('value_code')
                ->get();
        $listTaskMissingIds = $listTaskMissing->pluck('value_code');

        $list = DB::table('cases as a')
                ->join('cases_cstm as b', 'b.id_c', '=', 'a.id')
                ->join('cstm_list_app as c', 'b.sub_category_c', '=', 'c.value_code')
                ->whereIn('b.sub_category_2_c',$listTaskMissingIds)
                ->whereNotIn('a.status',['Open_Resolved','Closed_Closed','Pending_User_Verification','Closed_Rejected','Closed_Rejected_Eaduan','Closed_Verified_Eaduan'])
                //->whereDate('a.date_entered', Carbon::now()->format('Y-m-d')) 
                ->whereDate('a.date_modified', Carbon::now()->format('Y-m-d')) 
                ->select('a.*','b.*','c.value_code','c.value_name')
                ->get();
        Log::info('addCaseAsTaskMissing ->  Found today : '.count($list));
        dump('addCaseAsTaskMissing -> Found today : '.count($list));
        $totalAdded = 0;
        foreach($list as $caseObj){
            
            self::insertCaseAsTaskMissing($caseObj, $totalAdded);
        }
        
        Log::info('addCaseAsTaskMissing ->  Total Added : '.$totalAdded);
        dump('addCaseAsTaskMissing -> Total Added : '.$totalAdded);
    }
    protected static function checkCaseStatus(){
        $list = DB::connection('mysql_ep_support')->table('ep_task_missing')
                ->where('is_case_closed',0)
                ->whereIn('case_status',['Open_Pending Input','Open_Resolved','Pending_User_Verification','Open_Assigned','In_Progress','Closed_Verified_Eaduan','Closed_Rejected_Eaduan'])
                ->get();
        dump('checkCaseStatus >  total : '.count($list));
        
        $counterUpdateAsClose = 0;
        foreach ($list as $obj){
            $case = DB::table('cases')->where('case_number',$obj->case_no)->first();
            if($case){
                $is_case_closed = 0;
                if($case->status == 'Closed_Closed' 
                        || $case->status == 'Open_Resolved' 
                        || $case->status == 'Pending_User_Verification'
                        || $case->status == 'Closed_Rejected'
                        || $case->status == 'Closed_Verified_Eaduan'
                        || $case->status == 'Closed_Rejected_Eaduan' 
                        || $case->status == 'Closed_Cancelled_Eaduan' ){
                    //dump($obj);
                    $is_case_closed = 1;
                    
                    
                    if($obj->is_case_closed == 0){
                        DB::connection('mysql_ep_support')
                        ->table('ep_task_missing')
                        ->where('task_id', $obj->task_id)
                        ->update([
                            'resolution' => $obj->resolution.'&#13;&#10;Updated &#13;&#10;Case CRM resolved on '.$case->date_modified,
                            'case_status' => $case->status,
                            'is_case_closed' => $is_case_closed,
                            'process_status' => '55',
                            'completed_at'  => Carbon::now(),
                            //'completed_by'  => 'SchedulerSystem'
                            ]);
                        $counterUpdateAsClose++;
                        Log::info(' Updated  Case No: '.$case->case_number);
                        dump(' Updated  Case No: '.$case->case_number);
                    }
                }
            }
        }
        
        dump('checkCaseStatus -> Updated As Close :Total:: '.$counterUpdateAsClose);
    }
    
    public static function updateCaseInfoInTaskMissing(){
        $list = DB::connection('mysql_ep_support')->table('ep_task_missing')
                ->get();
        foreach ($list as $obj){
            $case = DB::table('cases')->where('case_number',$obj->case_no)->first();
            if($case){
                DB::connection('mysql_ep_support')
                    ->table('ep_task_missing')
                    ->where('task_id', $obj->task_id)
                    ->update([
                        'case_created' => $case->date_entered,
                        'problem' => $case->description,
                        'case_id' => $case->id    
                        ]);
            }
        }
    }


}
