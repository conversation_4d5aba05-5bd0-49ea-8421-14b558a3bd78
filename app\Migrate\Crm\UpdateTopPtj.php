<?php

namespace App\Migrate\Crm;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Models\Account;
use App\Migrate\MigrateUtils;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Excel;
use App\Migrate\Crm\TopPtjImport;

class UpdateTopPtj {

    public static $FULL_PATH_FILE_NAME = '/app/Migrate/Crm/data/Top_500_PTJ_List.xlsx';

    public static function runUpdatePtj() {
        Log::info(self::class . ' Starting ... runUpdatePtj');
        var_dump(self::class . ' Starting ... runUpdatePtj');
        $dtStartTime = Carbon::now();

        self::updatePtj();

        Log::info(self::class . ' Completed runUpdatePtj --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        var_dump(self::class . ' Completed runUpdatePtj --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function updatePtj() {
        $filename = base_path(self::$FULL_PATH_FILE_NAME);
    
        if (!file_exists($filename)) {
            Log::error("File not found: " . $filename);
            var_dump("File not found: " . $filename);
            return;
        }
    
        try {
            Excel::import(new TopPtjImport, $filename);
            Log::info("Top PTJ import completed successfully.");
            var_dump("Top PTJ import completed successfully.");
        } catch (\Exception $e) {
            Log::error("Top PTJ import failed: " . $e->getMessage());
            var_dump("Top PTJ import failed: " . $e->getMessage());
        }
    }

}
