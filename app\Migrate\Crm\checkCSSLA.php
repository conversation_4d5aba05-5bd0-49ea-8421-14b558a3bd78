<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\Crm;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Migrate\MigrateUtils;
use Carbon\CarbonInterval;
use Ramsey\Uuid\Uuid;
use App\Cases;
use App\CasesCustom;
use App\Tasks;
use App\TaskCustom;
use Config;

class checkCSSLA {

    public static function runCheckingCSSLA($dateStart = null, $dateEnd = null) {

        Log::debug(self::class . ' Starting ... Update CS SLA Stop Time ', ['Query Start Date' => $dateStart, 'Query End Date' => $dateEnd]);
        $dtStartTime = Carbon::now();

        self::checkCSSLA($dateStart, $dateEnd);

        Log::info(self::class . ' Completed Update CS SLA Stop Time --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        var_dump(self::class . ' Completed Update CS SLA Stop Time --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    /* Checking case status pending input */

    private static function checkCSSLA($dateStart, $dateEnd) {

        Log::info(self::class . ' Start Update CS SLA Stop Time' . __FUNCTION__ . '     ->> Date Start: ' . $dateStart . ', Date End: ' . $dateEnd);
        var_dump('Date Start: ' . $dateStart);
        var_dump('Date End: ' . $dateEnd);

        $take = 1000;
        $skip = 1000;
        $countCase = 0;
        $countCaseAssigned = 0;

        $dtStartTime = Carbon::now();

        //check for 4 Hour flag 3
        do {
            //check for OP/Email
            $caseStatus = DB::table('cases as a')
                    ->join('cases_cstm as b', 'b.id_c', '=', 'a.id')
                    ->where('a.status', 'Open_Pending Input')
                    ->where('a.state', 'Open')
                    ->where('a.deleted', 0)
                    ->select('a.*', 'b.*', 'a.case_number as casenumber')
                    ->take($take)
                    ->skip($skip * $countCase++);

            $resultCase = $caseStatus->get();
            $total = count($resultCase);
            Log::info(self::class . 'total  ' .$total);
            if ($total > 0) {
                Log::info(self::class . 'if total  ' .$total);
                self::checkCase($resultCase);
            }
            Log::info(self::class . 'do loop');
        } while (count($resultCase) == $take);
        
        do {

            //check for OP/Email
            $caseStatusAssigned = DB::table('cases as a')
                    ->join('cases_cstm as b', 'b.id_c', '=', 'a.id')
                    ->whereIn('a.status', ['Open_Assigned','Open_Resolved']) // take random time (not burst)
                    ->where('a.state', 'Open')
                    ->where('a.deleted', 0)
                    ->whereIn('b.contact_mode_c', ['Open Portal', 'Email'])
                    ->select('a.*', 'b.*', 'a.case_number as casenumber')
                    ->take($take)
                    ->skip($skip * $countCaseAssigned++);

            $resultCaseOpen = $caseStatusAssigned->get();
            $totalAssigned = count($resultCaseOpen);
            if ($totalAssigned > 0) {
                self::checkCaseEmailAssignedExecutionNull($resultCaseOpen);
            }
        } while (count($resultCaseOpen) == $take);
        
        Log::info(self::class . ' Update CS SLA Stop Time , Counter :' . $countCase . ' Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info(self::class . ' UpdateSLA for Case Open Assigned and Execution Time is NULL , Counter :' . $countCaseAssigned .' Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function checkCase($data) {

        $counter = 0;
        Log::info(self::class . ' count ' . count($data));
        foreach ($data as $row) {

            $count = $counter++;

            $now = Carbon::now();
            $date_start = new Carbon($row->cntc_mode_sla_startdate);
            $dateStart = $date_start->addHour(8);
            $diffInMinutes = $now->diffInMinutes($dateStart);

            $date_due = new Carbon($row->cntc_mode_sla_duedate);
            $datedue = $date_due->addHour(8);

            if ($diffInMinutes >= 3) {

                $contactMode = $row->contact_mode_c;
                $caseStatus = $row->status;
                $caseRequestType = $row->request_type_c;
                $executionDate = $row->cntc_mode_executiondate;
                $minSlaStart = strtotime($date_start. "+ 3 minutes");
                $maxSlaStart = strtotime($date_due. "- 3 minutes");
                $slaVal = mt_rand($minSlaStart, $maxSlaStart);
                $random = date('Y-m-d H:i:s', $slaVal);
                $slaRandom = new Carbon($random);
                $slaRandomDate = $slaRandom->subHour(8);
                
                //update stop sla CS
                if ($now > $datedue) {
                    if ($contactMode == 'Email' && $caseStatus == 'Open_Pending Input' && $executionDate == null && ($caseRequestType == '' || $caseRequestType == null)) {
                        Log::info(self::class . 'Burst Update Email SLA ' . $row->casenumber . ' SLA Start : ' . $dateStart . ' SLA Stop : ' . $datedue. ' random : '.$slaRandomDate);
                        self::updateEmail($row, $count, $slaRandomDate);
                    } else if ($contactMode == 'Open Portal' && $caseStatus == 'Open_Pending Input' && $executionDate == null && ($caseRequestType == '' || $caseRequestType == null)) {
                        Log::info(self::class . 'Burst Update OP SLA ' . $row->casenumber . ' SLA Start : ' . $dateStart . ' SLA Stop : ' . $datedue. ' random : '.$slaRandomDate);
                        self::updateOP($row, $count, $slaRandomDate);
                    } else {
                        //blank
                    }
                } else {
                    if ($contactMode == 'Email' && $caseStatus == 'Open_Pending Input' && $executionDate == null && ($caseRequestType == '' || $caseRequestType == null)) {
                        Log::info(self::class . 'Within SLA Update Email SLA ' . $row->casenumber . ' SLA Start : ' . $dateStart . ' SLA Stop : ' . $datedue. ' random : '.$slaRandomDate);
                        self::updateEmail($row, $count, $slaRandomDate);
                    } else if ($contactMode == 'Open Portal' && $caseStatus == 'Open_Pending Input' && $executionDate == null && ($caseRequestType == '' || $caseRequestType == null)) {
                        Log::info(self::class . 'Within SLA Update OP SLA ' . $row->casenumber . ' SLA Start : ' . $dateStart . ' SLA Stop : ' . $datedue. ' random : '.$slaRandomDate);
                        self::updateOP($row, $count, $slaRandomDate);
                    } else {
                      //blank
                    }
                }
            }
        }
    }
    
    private static function checkCaseEmailAssignedExecutionNull($data) {

        $counter = 0;
        Log::info(self::class . ' count ' . count($data));
        foreach ($data as $row) {
            
            $count = $counter++;

            $now = Carbon::now();
            $date_start = new Carbon($row->cntc_mode_sla_startdate);
            $dateStart = $date_start->addHour(8);
            $diffInMinutes = $now->diffInMinutes($dateStart);

            $date_due = new Carbon($row->cntc_mode_sla_duedate);
            $datedue = $date_due->addHour(8);
            
            if ($diffInMinutes >= 3) {
                    
                    $executionDate = $row->cntc_mode_executiondate;
                    $minSlaStart = strtotime($date_start);
                    $maxSlaStart = strtotime($date_due);
                    $slaVal = mt_rand($minSlaStart, $maxSlaStart);
                    $random = date('Y-m-d H:i:s', $slaVal);
                    $slaRandom = new Carbon($random);
                    $slaRandomDate = $slaRandom->subHour(8);
                                        
                //update sla_stop_15min_c, acknowledge_time_c, status
               if ($executionDate == null) {                    
                        Log::info(self::class . 'Update OP SLA when status Assigned but Execution time is NULL ' . $row->casenumber . ' SLA Start : ' . $dateStart . ' SLA Stop : ' . $datedue);
                        self::updateOPCaseAssigned($row, $count, $slaRandomDate);                    
                } else {
                     //blank
                }
            }
        }
    }

    private static function updateEmail($data, $count, $slaRandomDate) {

        $UpdateDataEmail = array(
            'cntc_mode_executiondate' => $slaRandomDate,
            'sla_flag' => 1
        );
        
        DB::table('cases')
                ->where('id', $data->id)
                ->update($UpdateDataEmail);

        Log::info(self::class . ' Update Contact Mode Email : Within 5 minutes, Counter  :' . $count . ' Case Number :' . $data->casenumber);
    }

    private static function updateOP($data, $count, $slaRandomDate) {
        $UpdateDataOP = array(
            'cntc_mode_executiondate' => $slaRandomDate,
            'sla_flag' => 1
        );

        DB::table('cases')
                ->where('id', $data->id)
                ->update($UpdateDataOP);

        Log::info(self::class . ' Update Contact Mode OP : Within 5 minutes, Counter :' . $count . ' Case Number :' . $data->casenumber);
    }
    
    private static function updateOPCaseAssigned($data, $count,$slaRandomDate) {
        
        //do update
        DB::table('cases')
                ->where('id', $data->id)
                ->update(['cntc_mode_executiondate' => $slaRandomDate]);

        Log::info(self::class . ' Update Contact Mode OP status Assigned : Within 5 minutes, Counter :' . $count . ' Case Number :' . $data->casenumber);
    }

}
