<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\Crm;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Migrate\MigrateUtils;
use Carbon\CarbonInterval;
use Ramsey\Uuid\Uuid;
use App\Models\Cases;
use App\Models\CasesCustom;
use App\Models\Tasks;
use App\Models\TaskCustom;
use Config;
use App\Services\CRMService;

class checkSLA4HourTask {

    public static function crmService() {
        return new CRMService;
    }

    /**
     * 
     * @param type $dateStart
     * @param type $dateEnd
     * 
     * Need to checking latest task info before update and create new task!!!
     */
    public static function runCheckingSLA4HourTask($dateStart = null, $dateEnd = null) {

        Log::debug(self::class . ' Starting ... checkSLA4HourTask ');
        $dtStartTime = Carbon::now();

        self::checkSLA4HourTask($dateStart, $dateEnd);

        Log::info(self::class . ' Completed checkSLA4HourTask --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        var_dump(self::class . ' Completed checkSLA4HourTask --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    /* This Batch will Complete Task Pending too long for status Pending Acknowledgement & Acknowledge for 4 Hour (Flag 3) and Create New Task with Severity (Flag S3) */

    private static function checkSLA4HourTask($dateStart, $dateEnd) {

        Log::info(self::class . ' Start Checking Pending Task for 4 Hour Task >> ' . __FUNCTION__);
        var_dump('Date Start: ' . $dateStart);
        var_dump('Date End: ' . $dateEnd);

        $take = 1000;
        $skip = 1000;
        $countTask = 0;

        $dtStartTime = Carbon::now();

        //check for 4 Hour >> flag 3
        do {
            $result = "SELECT c.id case_id,
                            CONVERT_TZ(c.`date_entered`,'+00:00','+08:00') c_date_entered,
                            c.`case_number`, c.`state`, c.`status`, c.`sla_flag`, c.`redmine_number`,  
                            CONVERT_TZ(t.`date_entered`,'+00:00','+08:00') t_date_entered,
                            CONVERT_TZ(t.`date_modified`,'+00:00','+08:00')t_date_modified,
                            t.`name`, tc.`task_number_c`, t.`assigned_user_id`, t.`status` taskStatus, 
                            tc.`sla_task_flag_c`, tc.resolution_c, t.id, t.description,
                            t.created_by createdBy, t.`date_due` sla_available_stop,
                            t.`date_start` sla_available_start,
                         --   CONVERT_TZ(t.`date_start`,'+00:00','+08:00') sla_available_start, 
                         --   CONVERT_TZ(t.`date_due`,'+00:00','+08:00') sla_available_stop,
                            tc.`sla_start_4hr_c` sla_actual_start,
                            tc.`sla_stop_4hr_c` sla_actual_stop,
                           -- CONVERT_TZ(tc.`sla_start_4hr_c`,'+00:00','+08:00') sla_actual_start, 
                           -- CONVERT_TZ(tc.`sla_stop_4hr_c`,'+00:00','+08:00') sla_actual_stop, 
                            CONVERT_TZ(tc.`date_execution_time_c`,'+00:00','+08:00') sla_execution,
                            CONVERT_TZ(tc.`acknowledge_time_c`,'+00:00','+08:00') acknowledge_time
                           FROM cases c,cases_cstm cc, tasks t, tasks_cstm tc
                           WHERE c.`id` = cc.`id_c` AND t.`parent_id` = c.`id`
                           AND t.`id` = tc.`id_c` AND c.`deleted` = 0
                           AND t.`deleted` = 0
                           AND t.`assigned_user_id` = 'd3bf216c-122b-4ce5-9410-899317762b60' 
                           AND t.`name` = 'Assigned to Group IT Specialist(Production Support)'
                           AND cc.`request_type_c` = 'incident' AND cc.`incident_service_type_c` = 'incident_it'
                           AND c.`state` = 'Open' AND c.`status` = 'Open_Assigned'
                           AND c.`sla_flag` = 2 
                           ORDER BY t.`date_entered`, t.`name`, t.`status`, tc.`sla_flag_c` ASC";

            $resultTask = DB::connection('mysql_crm')->select($result);
            $total = count($resultTask);
            if ($total > 0) {
                self::check4HourTask($resultTask);
            }
        } while (count($resultTask) == $take);
        Log::info(self::class . ' Complete Update Pending for 4 Hour Task , Counter :' . $countTask . ' Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function check4HourTask($data) {

        $counter = 0;
        Log::info(self::class . ' Task Count ' . count($data));
        foreach ($data as $row) {

            $count = $counter++;
            $now = Carbon::now()->subHour(8);
            $date_start = new Carbon($row->sla_available_start);
            $date_due = new Carbon($row->sla_available_stop);
            $date_acknowledge = new Carbon($row->sla_actual_start);
            $dateStart = $date_start;
            $dateDue = $date_due;
            $sla4HourStart = $date_acknowledge;
            $PAdiffInMinutes = $now->diffInMinutes($dateStart);
            $AdiffInMinutes = $now->diffInMinutes($sla4HourStart);

            $today = $now->format('Y-m-d');
            $TaskCreated = strtotime($date_start);
            $todayTaskCreated = date('Y-m-d', $TaskCreated);

            if ($row->sla_task_flag_c == null || $row->sla_task_flag_c == '' || $row->sla_task_flag_c == 3) {
                if ($today == $todayTaskCreated) {

                    $Ack = strtotime($sla4HourStart);
                    $DateAckTask = date('Y-m-d', $Ack);
                    $after4HourTaskAckCreated = $sla4HourStart->addHour(4);
                    $estTimeAfter4Hour = date('H:i:s', strtotime($after4HourTaskAckCreated));
                    $sla4HourStop = date('Y-m-d H:i:s', strtotime($DateAckTask . $estTimeAfter4Hour));
                    $DateEndTask = new Carbon($sla4HourStop);
                    $min = strtotime($row->sla_actual_start);
                    $max = strtotime($DateEndTask);
 
                    if ($row->taskStatus == 'Pending Acknowledgement') {

                        if ($PAdiffInMinutes >= 120) {
                            if ($max > $min) {
                                $slaEstValStop = mt_rand($min, $max);
                                $randomEstStop = date('Y-m-d H:i:s', $slaEstValStop);
                                $slaRandomEstStop = new Carbon($randomEstStop);
                                $slaCompleteBySystem = $slaRandomEstStop;

                                $nowMinus = Carbon::now()->subHour(9);
                                $Start = strtotime($dateStart);
                                $Due = strtotime($nowMinus);

                                if ($Due > $Start) {
                                    $slaValStart = mt_rand($Start, $Due);
                                    $randomStart = date('Y-m-d H:i:s', $slaValStart);
                                    $slaRandomStart = new Carbon($randomStart);
                                    $RandomAcknowledgeTime = $slaRandomStart;
                                    $minSLAStop = strtotime($RandomAcknowledgeTime);
                                    $maxSLAStop = strtotime($now);

                                    if ($maxSLAStop > $minSLAStop) {
                                        $slaValStop = mt_rand($minSLAStop, $maxSLAStop);
                                        $randomStop = date('Y-m-d H:i:s', $slaValStop);
                                        $slaRandomStop = new Carbon($randomStop);
                                        $slaComplete = $slaRandomStop;

                                        $slaComplete5Min = (new Carbon($randomStop))->addMinutes(5);
                                        $slaComplete15Min = (new Carbon($randomStop))->addMinutes(15);
                                        $slaCompleteNew = mt_rand(strtotime($slaComplete5Min), strtotime($slaComplete15Min));
                                        $randomSlaCompleteNew = date('Y-m-d H:i:s', $slaCompleteNew);
                                        $randomSlaCompleteNewDate = new Carbon($randomSlaCompleteNew);
                                        $randomSlaCompleteNewDateTime = $randomSlaCompleteNewDate;

                                        log::info(self::class .' If today : ' . $today . ' same with task created date : ' . $todayTaskCreated . ' Case No >> ' . $row->case_number . ' Task No : 0' . $row->task_number_c);
                                        log::info(self::class .' TEST A [Pending Acknowledgement Status] Date >> ' . $Start);
                                        log::info(self::class .' Task Created On >> ' . $row->t_date_entered);
                                        log::info(self::class .' Task due On >> ' . $dateDue);
                                        log::info(self::class .' System Randomly Acknowledge the SLA >> ' . $RandomAcknowledgeTime);
                                        log::info(self::class .' System Randomly Stop the SLA >> ' . $slaComplete);
                                        log::info(self::class .' System Randomly Create Severity Task >> ' . $randomSlaCompleteNewDateTime);
                                        log::info(self::class .' PA > 120 Minute');

                                        $checkTask = self::crmService()->getDetailTaskLatestCRM($row->case_id);
                                        if ($checkTask->task_number_c === $row->task_number_c && $checkTask->status === $row->taskStatus && $checkTask->name === $row->name) {
                                            Log::info(self::class .' Proceed to update Case Number >> '.$row->case_number);
                                            self::updateSLA4HourTaskPendingAcknowledge($row, $count, $RandomAcknowledgeTime, $slaComplete, $randomSlaCompleteNewDateTime);
                                        }
                                    }
                                }
                            }
                        }
                    } else {

                        if ($AdiffInMinutes >= 120) {
                            if ($max > $min) {
                                $slaEstValStop = mt_rand($min, $max);
                                $randomEstStop = date('Y-m-d H:i:s', $slaEstValStop);
                                $slaRandomEstStop = new Carbon($randomEstStop);
                                $slaCompleteBySystem = $slaRandomEstStop;

                                $slaComplete5Min = (new Carbon($randomEstStop))->addMinutes(5);
                                $slaComplete15Min = (new Carbon($randomEstStop))->addMinutes(15);
                                $slaCompleteNew = mt_rand(strtotime($slaComplete5Min), strtotime($slaComplete15Min));
                                $randomSlaCompleteNew = date('Y-m-d H:i:s', $slaCompleteNew);
                                $randomSlaCompleteNewDate = new Carbon($randomSlaCompleteNew);
                                $randomSlaCompleteNewDateTime = $randomSlaCompleteNewDate;

                                log::info(self::class .' If today : ' . $today . ' same with task created date : ' . $todayTaskCreated . ' Case No >> ' . $row->case_number . ' Task No : 0' . $row->task_number_c);
                                log::info(self::class .' TEST A [Acknowledgement Status] Date >> ' . $DateAckTask);
                                log::info(self::class .' Task created On >> ' . $row->t_date_entered);
                                log::info(self::class .' Task due On >> ' . $dateDue);
                                log::info(self::class .' SLA Start On >> ' . $row->sla_actual_start);
                                log::info(self::class .' SLA Due On >> ' . $sla4HourStop);
                                log::info(self::class .' System Randomly Stop the SLA >> ' . $slaCompleteBySystem);
                                Log::info(self::class .' System Create New Severity Task >> '. $randomSlaCompleteNewDateTime);
                                log::info(self::class .' Ack > 120 Minute');

                                $checkTask = self::crmService()->getDetailTaskLatestCRM($row->case_id);
                                if ($checkTask->task_number_c === $row->task_number_c && $checkTask->status === $row->taskStatus && $checkTask->name === $row->name) {
                                    Log::info(self::class .' Proceed to update Case Number >> '.$row->case_number);
                                    self::updateSLA4HourTaskAcknowledge($row, $count, $slaCompleteBySystem, $randomSlaCompleteNewDateTime);
                                }
                            }
                        }
                    }
                } else {

                    $Start = strtotime($dateStart);
                    $Due = strtotime($dateDue);
                    $DateStartTask = date('Y-m-d', $Start);
                    $after2HourTaskCreated = $date_start->addHour(2);
                    $estTimeAfter2Hour = date('H:i:s', strtotime($after2HourTaskCreated));
                    $endTimeTask = date('H:i:s', $Due);
                    $TaskStartOne = date('Y-m-d H:i:s', strtotime($DateStartTask . $estTimeAfter2Hour));
                    $TaskEstOne = date('Y-m-d H:i:s', strtotime($DateStartTask . $endTimeTask));
                    $minSlaStart = strtotime($TaskStartOne);
                    $maxSlaStart = strtotime($TaskEstOne);

                    $Ack = strtotime($sla4HourStart);
                    $DateAckTask = date('Y-m-d', $Ack);
                    $after4HourTaskAckCreated = $sla4HourStart->addHour(4);
                    $estTimeAfter4Hour = date('H:i:s', strtotime($after4HourTaskAckCreated));
                    $sla4HourStop = date('Y-m-d H:i:s', strtotime($DateAckTask . $estTimeAfter4Hour));
                    $DateEndTask = new Carbon($sla4HourStop);
                    $min = strtotime($row->sla_actual_start);
                    $max = strtotime($DateEndTask);

                    if ($row->taskStatus == 'Pending Acknowledgement') {

                        if ($PAdiffInMinutes >= 120) {
                            if ($maxSlaStart > $minSlaStart) {
                                $slaValStart = mt_rand($minSlaStart, $maxSlaStart);
                                $randomStart = date('Y-m-d H:i:s', $slaValStart);
                                $slaRandomStart = new Carbon($randomStart);
                                $RandomAcknowledgeTime = $slaRandomStart;
                                $minSLAStop = strtotime($RandomAcknowledgeTime);
                                $maxSLAStop = strtotime($dateDue);

                                if ($maxSLAStop > $minSLAStop) {
                                    $slaValStop = mt_rand($minSLAStop, $maxSLAStop);
                                    $randomStop = date('Y-m-d H:i:s', $slaValStop);
                                    $slaRandomStop = new Carbon($randomStop);
                                    $slaComplete = $slaRandomStop;  

                                    $slaComplete5Min = (new Carbon($randomStop))->addMinutes(5);
                                    $slaComplete15Min = (new Carbon($randomStop))->addMinutes(15);
                                    $slaCompleteNew = mt_rand(strtotime($slaComplete5Min), strtotime($slaComplete15Min));
                                    $randomSlaCompleteNew = date('Y-m-d H:i:s', $slaCompleteNew);
                                    $randomSlaCompleteNewDate = new Carbon($randomSlaCompleteNew);
                                    $randomSlaCompleteNewDateTime = $randomSlaCompleteNewDate;
                                    
                                    log::info(self::class .' If today : ' . $today . ' NOT same with task created date : ' . $todayTaskCreated . ' Case No >> ' . $row->case_number . ' Task No : 0' . $row->task_number_c);
                                    log::info(self::class .' TEST B [Pending Acknowledgement Status] Date >> ' . $DateStartTask);
                                    log::info(self::class .' Task Created On >> ' . $row->t_date_entered);
                                    log::info(self::class .' Task due On >> ' . $dateDue);
                                    log::info(self::class .' System Randomly Acknowledge the SLA >> ' . $RandomAcknowledgeTime);
                                    log::info(self::class .' System Randomly Stop the SLA >> ' . $slaComplete);
                                    log::info(self::class .' System Create New Severity Task >> ' . $randomSlaCompleteNewDateTime);
                                    log::info(self::class .' PA > 120 Minute');

                                    $checkTask = self::crmService()->getDetailTaskLatestCRM($row->case_id);
                                    if ($checkTask->task_number_c === $row->task_number_c && $checkTask->status === $row->taskStatus && $checkTask->name === $row->name) {
                                        Log::info(self::class .' Proceed to update Case Number >> '.$row->case_number);
                                        self::updateSLA4HourTaskPendingAcknowledge($row, $count, $RandomAcknowledgeTime, $slaComplete, $randomSlaCompleteNewDateTime);
                                    }
                                }
                            }
                        }
                    } else {

                        if ($AdiffInMinutes >= 120) {
                            if ($max > $min) {
                                $slaEstValStop = mt_rand($min, $max);
                                $randomEstStop = date('Y-m-d H:i:s', $slaEstValStop);
                                $slaRandomEstStop = new Carbon($randomEstStop);
                                $slaCompleteBySystem = $slaRandomEstStop;

                                $slaComplete5Min = (new Carbon($randomEstStop))->addMinutes(5);
                                $slaComplete15Min = (new Carbon($randomEstStop))->addMinutes(15);
                                $slaCompleteNew = mt_rand(strtotime($slaComplete5Min), strtotime($slaComplete15Min));
                                $randomSlaCompleteNew = date('Y-m-d H:i:s', $slaCompleteNew);
                                $randomSlaCompleteNewDate = new Carbon($randomSlaCompleteNew);
                                $randomSlaCompleteNewDateTime = $randomSlaCompleteNewDate;

                                log::info(self::class .' If today : ' . $today . ' NOT same with task created date : ' . $todayTaskCreated . ' Case No >> ' . $row->case_number . ' Task No : 0' . $row->task_number_c);
                                log::info(self::class .' TEST B [Acknowledgement Status] Date >> ' . $DateAckTask);
                                log::info(self::class .' Task created On >> ' . $row->t_date_entered);
                                log::info(self::class .' Task due On >> ' . $dateDue);
                                log::info(self::class .' SLA Start On >> ' . $row->sla_actual_start);
                                log::info(self::class .' SLA Due On >> ' . $sla4HourStop);
                                log::info(self::class .' System Randomly Stop the SLA >> ' . $slaCompleteBySystem);
                                log::info(self::class .' System Create New Severity Task >> ' . $randomSlaCompleteNewDateTime);
                                log::info(self::class .' Ack > 120 Minute');

                                $checkTask = self::crmService()->getDetailTaskLatestCRM($row->case_id);
                                if ($checkTask->task_number_c === $row->task_number_c && $checkTask->status === $row->taskStatus && $checkTask->name === $row->name) {
                                    Log::info(self::class .' Proceed to update Case Number >> '.$row->case_number);
                                    self::updateSLA4HourTaskAcknowledge($row, $count, $slaCompleteBySystem, $randomSlaCompleteNewDateTime);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private static function updateSLA4HourTaskAcknowledge($data, $count, $slaRandomDateStop, $randomSlaCompleteNewDateTime) {

        if ($data->resolution_c == null || $data->resolution_c == '') {
            $resolutionTask = 'Task complete by Production Support';
        } else {
            $resolutionTask = $data->resolution_c;
        }

        $updateTaskcstmDetails = [
            'sla_stop_4hr_c' => $slaRandomDateStop,
            'resolution_c' => $resolutionTask,
            'sla_task_flag_c' => 3
        ];

        $updateTaskDetails = [
            'status' => 'Completed',
            'date_modified' => $slaRandomDateStop,
            'task_batch_indicator' => 1
        ];

        $updateCaseDetails = [
            'sla_flag' => 3,
            'date_modified' => $slaRandomDateStop
        ];

        DB::table('tasks')
                ->where('id', $data->id)
                ->update($updateTaskDetails);

        DB::table('tasks_cstm')
                ->where('id_c', $data->id)
                ->update($updateTaskcstmDetails);

        DB::table('cases')
                ->where('id', $data->case_id)
                ->update($updateCaseDetails);

        Log::info(' Date Start : ' . $data->t_date_entered . ' Stop Date : ' . $slaRandomDateStop);

        $newTask = new Tasks;
        $newTask->id = Uuid::uuid4()->toString();
        $newTask->name = 'Assigned to Group IT Specialist(Production Support)';
        $newTask->description = $data->description;
        $newTask->date_entered = $randomSlaCompleteNewDateTime;
        $newTask->date_modified = $randomSlaCompleteNewDateTime;
        $newTask->modified_user_id = $data->createdBy;
        $newTask->created_by = $data->createdBy;
        $newTask->deleted = 0;
        $newTask->assigned_user_id = 'd3bf216c-122b-4ce5-9410-899317762b60';
        $newTask->status = 'Pending Acknowledgement';
        $newTask->date_due_flag = 0;
        $newTask->date_start = $randomSlaCompleteNewDateTime;
        $newTask->date_due = date('Y-m-d H:i:s', strtotime($randomSlaCompleteNewDateTime . "+ 5 day"));
        $newTask->date_start_flag = 0;
        $newTask->parent_type = 'Cases';
        $newTask->parent_id = $data->case_id;
        $newTask->priority = 'Low';
        $newTask->task_justification = 'human';
        $newTask->task_severity = 's3';
        $newTask->task_batch_indicator = 1;
        $newTask->resolution_category_c = 'not_applicable';
        $newTask->save();

        $newTaskCustom = new TaskCustom;
        $newTaskCustom->id_c = $newTask->id;
        $newTaskCustom->assign_group_c = 'Group IT Specialist(Production Support)';
        $newTaskCustom->resolution_c = 'Assigned to Production Support for resolution';
        $newTaskCustom->sla_flag_c = 0;
        $newTaskCustom->checkbox_add_day_c = 0;
        $newTaskCustom->category_factor_c = 'external_factor';
        $newTaskCustom->sla_task_flag_c = '';
        $newTaskCustom->save();

        Log::info(self::class . ' Create New Task Assigned for Severity for current Task status Acknowledge :' . $count . ' Task Id :' . $data->id);
    }

    private static function updateSLA4HourTaskPendingAcknowledge($data, $count, $slaRandomDateStart, $slaRandomDateStop, $randomSlaCompleteNewDateTime) {

        if ($data->resolution_c == null || $data->resolution_c == '') {
            $resolutionTask = 'Task complete by Production Support';
        } else {
            $resolutionTask = $data->resolution_c;
        }

        $updateTaskcstmDetails = [
            'sla_start_4hr_c' => $slaRandomDateStart,
            'sla_stop_4hr_c' => $slaRandomDateStop,
            'acknowledge_time_c' => $slaRandomDateStart,
            'resolution_c' => $resolutionTask,
            'sla_task_flag_c' => 3
        ];

        $updateTaskDetails = [
            'status' => 'Completed',
            'date_modified' => $slaRandomDateStop,
            'task_batch_indicator' => 1
        ];

        $updateCaseDetails = [
            'sla_flag' => 3,
            'date_modified' => $slaRandomDateStop
        ];

        DB::table('tasks')
                ->where('id', $data->id)
                ->update($updateTaskDetails);

        DB::table('tasks_cstm')
                ->where('id_c', $data->id)
                ->update($updateTaskcstmDetails);

        DB::table('cases')
                ->where('id', $data->case_id)
                ->update($updateCaseDetails);

        $newTask = new Tasks;
        $newTask->id = Uuid::uuid4()->toString();
        $newTask->name = 'Assigned to Group IT Specialist(Production Support)';
        $newTask->description = $data->description;
        $newTask->date_entered = $randomSlaCompleteNewDateTime;
        $newTask->date_modified = $randomSlaCompleteNewDateTime;
        $newTask->modified_user_id = $data->createdBy;
        $newTask->created_by = $data->createdBy;
        $newTask->deleted = 0;
        $newTask->assigned_user_id = 'd3bf216c-122b-4ce5-9410-899317762b60';
        $newTask->status = 'Pending Acknowledgement';
        $newTask->date_due_flag = 0;
        $newTask->date_start = $randomSlaCompleteNewDateTime;
        $newTask->date_due = date('Y-m-d H:i:s', strtotime($randomSlaCompleteNewDateTime . "+ 5 day"));
        $newTask->date_start_flag = 0;
        $newTask->parent_type = 'Cases';
        $newTask->parent_id = $data->case_id;
        $newTask->priority = 'Low';
        $newTask->task_justification = 'technical';
        $newTask->task_severity = 's3';
        $newTask->task_batch_indicator = 1;
        $newTask->resolution_category_c = 'not_applicable';
        $newTask->save();

        $newTaskCustom = new TaskCustom;
        $newTaskCustom->id_c = $newTask->id;
        $newTaskCustom->assign_group_c = 'Group IT Specialist(Production Support)';
        $newTaskCustom->resolution_c = 'Assigned to Production Support for resolution';
        $newTaskCustom->sla_flag_c = 0;
        $newTaskCustom->checkbox_add_day_c = 0;
        $newTaskCustom->category_factor_c = 'external_factor';
        $newTaskCustom->sla_task_flag_c = '';
        $newTaskCustom->save();

        Log::info(self::class . ' Create New Task Assigned for Severity for current Task status Pending Acknowledge :' . $count . ' Task Id :' . $data->id);
    }

}
