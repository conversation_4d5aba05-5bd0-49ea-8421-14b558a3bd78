<?php

namespace App\Migrate\Ep;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Facades\DB;
use App\Models\Account;
use App\Models\Contact;
use App\Models\AccountCustom;
use App\Models\ContactCustom;
use App\Models\LogIntegration;
use App\Models\EmailAddress;
use App\Migrate\MigrateUtils;
use Config;
use Excel;

/*
 * This will update PTJ as Active base don Excel file. IF not found in excel, data will set inactive
 * PTJ Information
 * 
 * If data in eP not exist in CRM, System will create to CRM.
 * 
 */

class CleaningPTJActive {

    public static $FULL_PATH_FILE_NAME = '/app/Migrate/Ep/data/ListPTJ_Feb2017.xlsx';

    public static function runCleaning() {
        Log::debug(self::class . ' Starting ... runCleaning');
        $dtStartTime = Carbon::now();

        self::cleanPTJGovernment();

        Log::info(self::class . ' Completed runCleaning --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function cleanPTJGovernment() {

        DB::transaction(function() {
//            DB::table('accounts')
//                    ->join('accounts_cstm', 'accounts.id', '=', 'accounts_cstm.id_c')
//                    ->where('accounts.account_type', 'GOVERNMENT')
//                    ->where('accounts_cstm.gov_level_c', 'PTJ')
//                    ->update(['deleted' => true, 'modified_user_id' => '3', 'date_modified' => Carbon::now()]);

            Excel::load(self::$FULL_PATH_FILE_NAME, function($reader) {
                $reader->each(function($row) {
                    $jabatanPtjCode = trim($row->ptj_code);
                    $ptjName = trim($row->ptj_name);
                    Log::debug(self::class . ' Check PTJ : '.$ptjName.', CODE '.$jabatanPtjCode);
                    
                    //$listPtj = AccountCustom::where('jabatan_ptj_code_c', $jabatanPtjCode)->get();
                    $listPtj = DB::table('accounts')
                    ->join('accounts_cstm', 'accounts.id', 'accounts_cstm.id_c')
                    //->where('accounts.deleted', '0')
                    ->where('accounts_cstm.jabatan_ptj_code_c', $jabatanPtjCode)
                    ->select('accounts.id as id','accounts.name as name')
                    ->get();  
                    if(count($listPtj)> 1) {
                        foreach ($listPtj as $ptjObj){
                            if($ptjObj->name != $ptjName){
                               $account = Account::where('id',$ptjObj->id)->first();
                               $account->deleted  = true;
                               $account->save();
                               Log::debug(self::class . '   -- DELETE PTJ : '.$account->name.', ID : '.trim($account->id));
                            }else{
                               Log::debug(self::class . '   -- REMAIN ACTIVE PTJ : '.$ptjObj->name.', ID : '.$ptjObj->id); 
                            }
                        }
                    }
                    
  
                    $ptj = DB::table('accounts')
                    ->join('accounts_cstm', 'accounts.id', 'accounts_cstm.id_c')
                    ->where('accounts.deleted', '0')
                    ->where('accounts_cstm.jabatan_ptj_code_c', $jabatanPtjCode)
                    ->select('accounts.*')
                    ->first();
  
                    //$ptj = AccountCustom::has('account')->where('jabatan_ptj_code_c', $jabatanPtjCode)->where('accounts.deleted','0')->get();
                    if ($ptj && $ptj->id != '') {
                        Log::debug(self::class . '   -- Get Active PTJ : '.$ptj->name.', ID '.$ptj->id);
                        
                        $account = Account::where('id',$ptj->id)->first();
                        $account->modified_user_id = '3';
                        $account->date_modified = Carbon::now();
                        $account->deleted  = false;
                        if($account->name != $ptjName){
                            //Log::debug(self::class . ' Rename PTJ Name from : '.$account->name. ' , New PTJ Name : '.trim($row->ptj_name));
                            $account->name = $ptjName;
                        }
                        $account->save();
                        //Log::debug(self::class . '   Successfull Active PTJ : '.$account->name. ' , JABATANPTJCODE : '.$jabatanPtjCode);
                    }else {
                        Log::debug(self::class . ' This PTJ not found id DB CRM. Please check PTJ : '.$row->ptj_name. ' , JABATANPTJCODE : '.$jabatanPtjCode);
                    }
                });
            });
        });
    }


}
