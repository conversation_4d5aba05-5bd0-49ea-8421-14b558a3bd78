<?php

namespace App\Migrate\Ep;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Facades\DB;
use App\Models\Account;
use App\Models\Contact;
use App\Models\AccountCustom;
use App\Models\ContactCustom;
use App\Models\LogIntegration;
use App\Models\EmailAddress;
use App\Migrate\MigrateUtils;
use Config;

/*
 * This integration migrate Data from eP to CRM. 
 * Ministry Information
 * Jabatan Information
 * PTJ Information
 * Users in PTJ information.
 * 
 * If data in eP not exist in CRM, System will create to CRM.
 * 
 */

class MigrateOrgGovernment {
    
    public static function runMigrate() {
        Log::debug(self::class . ' Starting ... runMigrate');
        $dtStartTime = Carbon::now();

        self::migrateOrganizationGovernment();

        Log::info(self::class . ' Completed runMigrate --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    /**
     * Migrate data from eP to CRM. We check id data not existed in crm, Data will insert to CRM
     */
    public static function migrateOrganizationGovernment() {

        //Get Ministry Information from eP
        $results = self::getQueryFromEp();

        foreach ($results as $row) {
            //var_dump('Ministry Code : '.$row->ministry_code);
            //$ministryCode = $row->ministry_code;
            $ministryId = self::saveMinistry($row);

            //Get Jabatan Information from eP
            $results = self::getQueryFromEp("JABATAN", $row->ministry_id);
            foreach ($results as $row) {
                //var_dump('  Jabatan Code : '.$row->jabatan_code);
                
                //$jabatanCode = $row->jabatan_code;
                $jabatanId = self::saveJabatan($row, $ministryId);

                //Get PTJ Information from eP
               
                //var_dump('  Jabatan ID : '.$row->jabatan_id);
                $results = self::getQueryFromEp("PTJ", $row->jabatan_id);
                foreach ($results as $row) {
                    //$ptjCode = $row->ptj_code;
                   
                    //var_dump('  PTJ Code : '.$row->ptj_code);

                    $accountPtjId = self::savePTJ($row, $row->ptj_id,$jabatanId);

                    $listUsers = self::getQueryUsersPtjFromEp($row->ptj_id);
                    foreach ($listUsers as $userRow) {
                        $account = Account::find($accountPtjId);
                        $account->load('accountCustom');
                        $contact = $account->contacts->where('first_name', trim(strtoupper($userRow->name)))->first();

                        if (!$contact) {
                            self::saveContact($account, $userRow);
                            
                        }
                    }
                        
                    
                    
                }
                
                
                 
                 
            }
        }
    }

    /**
     * Check in CRM , if record not existed, Data Ministry will inserted
     * @param type $rowData
     */
    public static function saveContact($account, $rowData) {
        $contact = new Contact;
        $contact->id = Uuid::uuid4()->toString();
        $contact->first_name = strtoupper(trim(strtoupper($rowData->name)));
        $contact->last_name = '';
        $contact->description = $rowData->position;


        $contact->primary_address_street = self::add_address_streets($rowData->address_1, $rowData->address_2, $rowData->address_3);
        $contact->primary_address_city = self::getCityName($rowData->district_id, $rowData->state_id);
        $contact->primary_address_state = self::getStateName($rowData->state_id);
        $contact->primary_address_postalcode = $rowData->post_code;
        $contact->primary_address_country = 'MALAYSIA';
        $contact->phone_work = $rowData->phone_no;


        $contact->date_entered = Carbon::now()->subHour(8);
        $contact->date_modified = Carbon::now()->subHour(8);
        $contact->created_by = 3;
        $contact->modified_user_id = 3;
        $contact->deleted = 0;

        $contact->save();


        if (ctype_alnum($rowData->new_ic)) {
            $contactCustom = new ContactCustom;
            $contactCustom->id_c = $contact->id;
            $contactCustom->ic_no_c = strtoupper(trim($rowData->new_ic));
            
            $check = DB::table('contacts_cstm')->where('username_c', $contactCustom->ic_no_c)->count();
            if ($check == 0) {
                $contactCustom->username_c = strtolower($contactCustom->ic_no_c);
                $contactCustom->password_c = Config::get('constant.default_password_self_portal');
            }else{
                $contactCustom->username_c = strtolower($contactCustom->ic_no_c . '_' . $account->accountCustom->gov_code_c);
                $contactCustom->password_c = Config::get('constant.default_password_self_portal');
            }
            
            $contactCustom->contact_type_c = 'PTJ';
            $contactCustom->save();
        }


        //Add relationship with account
        $contact->accounts()->attach($account, ['id' => Uuid::uuid4()->toString()]);



        if (filter_var($rowData->contact_email, FILTER_VALIDATE_EMAIL)) {
            $emailAddress = new EmailAddress;
            $emailAddress->id = Uuid::uuid4()->toString();
            $email = filter_var($rowData->contact_email, FILTER_SANITIZE_EMAIL);
            $emailAddress->email_address = strtolower($email);
            $emailAddress->email_address_caps = strtoupper($email);
            $emailAddress->date_created = Carbon::now()->subHour(8);
            $emailAddress->date_modified = Carbon::now()->subHour(8);
            $emailAddress->deleted = 0;
            $emailAddress->save();

            //Add relationship with email
            $contact->emails()->attach($emailAddress, [
                'id' => Uuid::uuid4()->toString(),
                'bean_module' => 'Contacts',
                'date_modified' => Carbon::now()->subHour(8),
                'deleted' => 0
            ]);
            $contact->emailsRelation()->attach($emailAddress, [
                'id' => Uuid::uuid4()->toString(),
                'bean_module' => 'Contacts',
                'primary_address' => 1,
                'reply_to_address' => 0,
                'date_created' => Carbon::now()->subHour(8),
                'date_modified' => Carbon::now()->subHour(8),
                'deleted' => 0
            ]);
        }


        self::addLogIntegration($rowData, 'CONTACTS', $account->accountCustom->gov_level_c . ' : ' . $account->name);
        Log::info('Successfully save Contacts from : ' . $account->name . ' : ' . $contact->first_name . ' (' . $contact->id . ')');

        return $contact->id;
    }

    /**
     * Check in CRM , if record not existed, Data Ministry will inserted
     * @param type $rowData
     */
    public static function saveMinistry($rowData) {
        $rowResult = self::getAccountGovernment($rowData->name, 'KEMENTERIAN', $rowData->ministry_code, null);
        if (!$rowResult) {
            Log::info(__FUNCTION__.' -> #####NOT FOUND KEMENTERIAN  ' . $rowData->name . ' (' . $rowData->ministry_code . ')');
            return self::saveAccount($rowData, 'KEMENTERIAN');
        } else {
            return $rowResult->id;
        }
    }

    /**
     * Check in CRM , if record not existed, Data Jabatan will inserted
     * @param type $rowData
     */
    public static function saveJabatan($rowData, $ministryId) {
        $rowResult = self::getAccountGovernment($rowData->name, 'JABATAN', $rowData->jabatan_code,$ministryId);
        if (!$rowResult) {
            Log::info(__FUNCTION__.' -> #####NOT FOUND JABATAN  ' . $rowData->name . ' (' . $rowData->jabatan_code . ')');
            return self::saveAccount($rowData, 'JABATAN', $ministryId);
        } else {
            return $rowResult->id;
        }
    }

    /**
     * Check in CRM , if record not existed, Data PTJ will inserted
     * @param type $rowData
     */
    public static function savePTJ($rowData, $ptjEpId,$jabatanId) {

        $rowResult = self::getAccountGovernment($rowData->name, 'PTJ', $rowData->ptj_code,$jabatanId);
        if (!$rowResult) {
            Log::info(__FUNCTION__.' -> #####NOT FOUND PTJ  ' . $rowData->name . ' (' . $rowData->ptj_code . ')');
            var_dump('      -> #####NOT FOUND PTJ  ' . $rowData->name . ' (' . $rowData->ptj_code . ')');
            
            $rowPtj = self::getQueryPTJDetailFromEp($ptjEpId);
            if($rowPtj != null){
                $rowData = $rowPtj;
            }
            return self::saveAccount($rowData, 'PTJ', $jabatanId);
        } else {
            //Log::info('PTJ Found ' . $rowResult->name . ' (' . $rowResult->id . ')');
            //Let fixed for added value field jabatan_ptj_code_c
            $ptj = AccountCustom::where('id_c', $rowResult->id)->first();
            if ($ptj->jabatan_ptj_code_c == '') {

                $jabatan = AccountCustom::where('id_c', $jabatanId)->first();
                $ptj->jabatan_ptj_code_c = $jabatan->gov_code_c . $ptj->gov_code_c;
                $ptj->save();
            }
            //Log::info('  Jabatan PTJ Code ' . $ptj->jabatan_ptj_code_c);
            return $rowResult->id;
        }
    }

    public static function getAccountGovernment($name, $levelGov, $govCode, $parentId = null) {
        $query = DB::table('accounts');
        $query->join('accounts_cstm', 'accounts.id', '=', 'accounts_cstm.id_c');
        //->where('name', trim(strtoupper($name)))
        $query->where('accounts.account_type', 'GOVERNMENT');
        $query->where('accounts_cstm.gov_level_c', $levelGov);
        $query->where('accounts_cstm.gov_code_c', $govCode);
        if ($parentId != null) {
            $query->where('accounts.parent_id', $parentId);
        }
        if($levelGov == 'KEMENTERIAN'  || $levelGov == 'JABATAN'){
            $query->where('accounts.deleted', '0');
        }
        //$query->where('deleted', '0');
        $query->select('accounts.*');
        return $query->first();
    }

    /**
     * Create Account for Government Information 
     * 
     * @param type $rowData
     * @param type $type
     * @param type $parentId
     * @return type
     */
    public static function saveAccount($rowData, $type, $parentId = null) {

        $account = new Account;
        $account->id = Uuid::uuid4()->toString();
        $account->name = trim(strtoupper($rowData->name));

        if ($type == 'PTJ' && isset($rowData->address_1)) {
            $account->billing_address_street = self::add_address_streets($rowData->address_1, $rowData->address_2, $rowData->address_3);
            $account->billing_address_city = self::getCityName($rowData->district_id, $rowData->state_id);
            $account->billing_address_state = self::getStateName($rowData->state_id);
            $account->billing_address_postalcode = $rowData->post_code;
            $account->billing_address_country = 'MALAYSIA';
            $account->phone_office = $rowData->phone_no;
            $account->phone_fax = $rowData->fax_no;
        }

        $account->date_entered = Carbon::now()->subHour(8);
        $account->date_modified = Carbon::now()->subHour(8);
        $account->created_by = 3;
        $account->modified_user_id = 3;
        
        if ($type == 'PTJ' && isset($rowData->address_1) == false) {
            $account->deleted = 1;
        }else{
            $account->deleted = 0; 
        }
        
        $account->account_type = 'GOVERNMENT';
        if ($parentId) {
            $account->parent_id = $parentId;
        }
        $account->save();

        $accountCustom = new AccountCustom;
        $accountCustom->id_c = $account->id;
        $accountCustom->gov_level_c = $type;
        if ($type == 'KEMENTERIAN') {
            $accountCustom->gov_code_c = $rowData->ministry_code;
        } else if ($type == 'JABATAN') {
            $accountCustom->gov_code_c = $rowData->jabatan_code;
        } else if ($type == 'PTJ') {
            $accountCustom->gov_code_c = $rowData->ptj_code;
            if ($parentId) {
                $jabatan = AccountCustom::where('id_c', $parentId)->first();
                $accountCustom->jabatan_ptj_code_c = $jabatan->gov_code_c . $accountCustom->gov_code_c;
            }
        }
        $accountCustom->save();

        self::addLogIntegration($rowData, $type, 'Parent ID : ' . $parentId);
        Log::info('Successfully save ' . $type . ' : ' . $account->name . ' (' . $account->id . ')');

        return $account->id;
    }

    /**
     * Get List Data (Ministry or Jabatan or PTJ) 
     * Data PTJ, will added field of address. 
     * 
     * @param type $typeQuery
     * @param type $orgId
     * @return type
     */
    public static function getQueryPTJDetailFromEp($orgId) {

        $sql = DB::connection('oracle_epcore_pms')
                ->table('pms_ptj');
        $sql->leftJoin('pms_address_header', 'pms_ptj.entity_id', '=', 'pms_address_header.entity_id');
        $sql->join('pms_address', 'pms_address_header.address_id', '=', 'pms_address.address_id');
       
        $sql->where('pms_ptj.is_active', 1)
            ->where('pms_ptj.is_deleted', 0);

        $sql->where('pms_ptj.ptj_id', $orgId);
        $sql->where('pms_address_header.address_type', 100);
        $sql->where('pms_address.is_default', 1);
        $sql->select('pms_ptj.ptj_id', 'pms_ptj.name', 'pms_ptj.ptj_code', 'pms_address.address_1', 'pms_address.address_2', 'pms_address.address_3', 'pms_address.post_code', 'pms_address.district_id', 'pms_address.state_id', 'pms_address.phone_no', 'pms_address.fax_no');
        $sql->groupBy('pms_ptj.ptj_id', 'pms_ptj.name', 'pms_ptj.ptj_code', 'pms_address.address_1', 'pms_address.address_2', 'pms_address.address_3', 'pms_address.post_code', 'pms_address.district_id', 'pms_address.state_id', 'pms_address.phone_no', 'pms_address.fax_no');


        //var_dump($sql->toSql());
     
        return $results = $sql->first();
    }
    
    /**
     * Get List Data (Ministry or Jabatan or PTJ) 
     * Data PTJ, will added field of address. 
     * 
     * @param type $typeQuery
     * @param type $orgId
     * @return type
     */
    public static function getQueryFromEp($typeQuery = null, $orgId = null) {

        $sql = DB::connection('oracle_epcore_pms')
                ->table('pms_ministry')
                ->join('pms_jabatan', 'pms_ministry.ministry_id', '=', 'pms_jabatan.ministry_id')
                ->join('pms_ptj', 'pms_jabatan.jabatan_id', '=', 'pms_ptj.jabatan_id');
        $sql->where('pms_ministry.is_active', 1)
                ->where('pms_ministry.is_deleted', 0)
                ->where('pms_jabatan.is_active', 1)
                ->where('pms_jabatan.is_deleted', 0)
                ->where('pms_ptj.is_active', 1)
                ->where('pms_ptj.is_deleted', 0);
        if ($typeQuery == 'JABATAN') {
            //Will return List of JABATAN by KEMENTERIAN ID
            $sql->where('pms_jabatan.ministry_id', $orgId);
            $sql->where('pms_jabatan.name', 'not like', '%DUMMY%');
            $sql->select('pms_jabatan.jabatan_id', 'pms_jabatan.name', 'pms_jabatan.jabatan_code');
            $sql->groupBy('pms_jabatan.jabatan_id', 'pms_jabatan.name', 'pms_jabatan.jabatan_code');
        } else if ($typeQuery == 'PTJ') {
            //Will return List of PTJ by JABATAN ID
            $sql->where('pms_ptj.jabatan_id', $orgId);
            $sql->where('pms_ptj.name', 'not like', '%DUMMY%');
            $sql->where('pms_ptj.name', 'not like', '%null%');
            $sql->select('pms_ptj.ptj_id', 'pms_ptj.name', 'pms_ptj.ptj_code');
            $sql->groupBy('pms_ptj.ptj_id', 'pms_ptj.name', 'pms_ptj.ptj_code');
        } else {
            //Will return List of Ministry
            $sql->select('pms_ministry.ministry_id', 'pms_ministry.name', 'pms_ministry.ministry_code');
            $sql->groupBy('pms_ministry.ministry_id', 'pms_ministry.name', 'pms_ministry.ministry_code');
        }
      
        return $results = $sql->get();
    }

    public static function add_address_streets($street_field1, $street_field2, $street_field3) {
        $street_field = "";

        if (isset($street_field1)) {
            $street_field .= "\n" . trim(strtoupper($street_field1));
        }
        if (isset($street_field2)) {
            $street_field .= "\n" . trim(strtoupper($street_field2));
        }
        if (isset($street_field3)) {
            $street_field .= "\n" . trim(strtoupper($street_field3));
        }
        return trim($street_field, "\n");
    }

    public static function getStateName($stateId) {
        var_dump('getStateName :'.$stateId);
        $statesArr =  array(
            "WILAYAH PERSEKUTUAN (K.L)" => '10001',
            "MELAKA" => '10002',
            "JOHOR" => '10003',
            "SELANGOR" => '10004',
            "PULAU PINANG" => '10005',
            "NEGERI SEMBILAN" => '10006',
            "PAHANG" => '10007',
            "PERAK" => '10008',
            "SABAH" => '10009',
            "SARAWAK" => '10010',
            "PERLIS" => '10011',
            "KEDAH" => '10012',
            "KELANTAN" => '10013',
            "TERENGGANU" => '10014',
            "WILAYAH PERSEKUTUAN (LABUAN)" => '10015',
            "WP CYBERJAYA" => '10016',
            "PUTRAJAYA" => '10017',
            "LUAR NEGERI" => '10018',
            "NOT DEFINED" => '10019'
        );
        $state = DB::connection('oracle_epcore_pms')
                ->table('pms_state')
                ->where('state_id', $stateId)
                ->first();
        if ($state) {
            var_dump('State : '.$state->name);
            $statename =  strtoupper(trim($state->name));
            try{
                return $statesArr[$statename];
            } catch (Exception $ex) {
                return $stateId;
            }
            
        } else {
            return $stateId;
        }
        
    }

    public static function getCityName($cityId, $stateId) {

        $result = DB::connection('oracle_epcore_pms')
                ->table('pms_district')
                ->where('district_id', $cityId)
                ->where('state_id', $stateId)
                ->first();
        if ($result) {
            return strtoupper(trim($result->name));
        } else {
            return null;
        }
    }

    public static function getQueryUsersPtjFromEp($ptjId) {

        $sql = DB::connection('oracle_epcore_pms')
                ->table('pms_ep_user');
        $sql->join('pms_address_header', 'pms_ep_user.ep_user_id', '=', 'pms_address_header.ep_user_id');
        $sql->join('pms_address', 'pms_address_header.address_id', '=', 'pms_address.address_id');
        $sql->where('pms_ep_user.ep_user_id', '>', 0);
        $sql->whereIn('pms_ep_user.organisation_type', [1003, 1004, 1008]);
        $sql->where('pms_address_header.address_type', 103);
        $sql->where('pms_ep_user.new_ic', 'not like', '%\%');
        $sql->where('pms_ep_user.new_ic', 'not like', '%/%');
        $sql->where('pms_ep_user.is_active', 1);
        $sql->where('pms_ep_user.is_deleted', 0);
        $sql->where('pms_ep_user.organisation_id', $ptjId);
        $sql->select(DB::raw("DECODE (pms_ep_user.organisation_type, 1003, 3, 1004, 2, 1008, 1, 0) AS organisation_type"));
        $sql->addSelect(DB::raw("NVL (pms_ep_user.organisation_id, 0) AS organisation_id "));
        $sql->addSelect(DB::raw("TRIM (pms_ep_user.NAME) AS name "));
        $sql->addSelect(DB::raw("TRIM (pms_ep_user.new_ic) AS new_ic "));
        $sql->addSelect(DB::raw("TRIM (pms_address.phone_no) AS phone_no"));
        $sql->addSelect(DB::raw("TRIM (pms_address.contact_email) AS contact_email"));
        $sql->addSelect(DB::raw("TRIM (pms_ep_user.nationality) AS nationality"));
        $sql->addSelect(DB::raw("TRIM (pms_ep_user.POSITION) AS position"));
        $sql->addSelect(DB::raw("TRIM (pms_address.address_1) AS address_1"));
        $sql->addSelect(DB::raw("TRIM (pms_address.address_2) AS address_2"));
        $sql->addSelect(DB::raw("TRIM (pms_address.address_3) AS address_3"));
        $sql->addSelect(DB::raw("TRIM (pms_address.post_code) AS post_code"));
        $sql->addSelect('pms_address.district_id AS district_id');
        $sql->addSelect('pms_address.state_id AS state_id');
        $sql->addSelect('pms_ep_user.MODIFIED_DATE as modified_Date');
        $sql->orderBy('pms_ep_user.new_ic');
        return $sql->get();
    }

    /**
     * Add log integration for Ep Government
     * @param type $rowData
     * @param type $type
     */
    public static function addLogIntegration($rowData, $type, $remarks = '') {
        $logIntegration = new LogIntegration;
        $logIntegration->id = Uuid::uuid4()->toString();
        $logIntegration->name = 'EP GOVERNMENT INTEGRATION';
        $logIntegration->service = 'BATCH INTEGRATION';
        $logIntegration->interface_service = $type;
        $logIntegration->status = 'COMPLETED';
        $logIntegration->remarks = $remarks;
        $logIntegration->description = 'Data received and completed insert into CRM';
        $logIntegration->date_entered = Carbon::now()->subHour(8);
        $logIntegration->date_modified = Carbon::now()->subHour(8);
        $logIntegration->created_by = 3;
        $logIntegration->modified_user_id = 3;
        $logIntegration->deleted = 0;
        $logIntegration->data = json_encode($rowData);

        $logIntegration->save();
    }

}
