<?php

namespace App\Migrate\Ep;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Facades\DB;
use App\Models\Account;
use App\Models\Contact;
use App\Models\AccountCustom;
use App\Models\ContactCustom;
use App\Models\LogIntegration;
use App\Models\EmailAddress;
use App\Migrate\MigrateUtils;
use Config;
use Excel;

/*
 * 
 */
class UpdatePersonnelSoftcert {

    public static $FULL_PATH_FILE_NAME = '/app/Migrate/Ep/data/To_change_softcert_status_to_1_on_20180220.xlsx';

    public static function run() {
        Log::info(self::class . ' Starting ... UpdatePersonnelSoftcert');
        var_dump(self::class . ' Starting ... UpdatePersonnelSoftcert');
        $dtStartTime = Carbon::now();

        self::updatePersonnelSoftcertStatus();

        var_dump(self::class . ' Completed UpdatePersonnelSoftcert --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info(self::class . ' Completed UpdatePersonnelSoftcert --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function updatePersonnelSoftcertStatus() {
        $counterFound = 0;
            Excel::load(self::$FULL_PATH_FILE_NAME, function($reader) use ($counterFound) {
                $reader->each(function($row) use ($counterFound)  {
                    $epno = trim($row->ep_no);
                    $icno = trim($row->identification_no);
                    $role = trim($row->role_code);
                    $softcert = trim($row->is_softcert);
                    var_dump("#######");
                    var_dump("ePno : ". $epno);
                    var_dump("icno : ". $icno);
                    var_dump("role : ". $role);
                    var_dump("softcert : ". $softcert);


                    $person= DB::connection('oracle_nextgen_rpt')->table('SM_PERSONNEL as P ')
                    ->join('SM_SUPPLIER as S ', 'S.latest_appl_id', 'P.appl_id')
                    ->where('S.ep_no', $epno)
                    ->where('P.identification_no', $icno)
                    ->where('P.ep_role', $role)
                    ->where('P.is_softcert', $softcert)
                    ->where('P.record_status', '1')
                    ->where('S.record_status', '1')
                    ->select('P.personnel_id','P.is_softcert')
                    ->first();  
                    if($person) {
                        $counterFound++;
                        var_dump("  ------> Ok found ".$counterFound." : ". $epno);
                        var_dump("      PersonnelID : ". $person->personnel_id);
                        var_dump("      Softcert Status : ". $person->is_softcert);

                        $query1 = "select personnel_id,name,identification_no,record_status,is_softcert from sm_personnel where personnel_id = '$person->personnel_id';";
                        $query2 = "update sm_personnel set is_softcert = 1 where personnel_id = '$person->personnel_id';";
                        $query3 = "select personnel_id,name,identification_no,record_status,is_softcert from sm_personnel where personnel_id = '$person->personnel_id';";
                        var_dump($query1.' \n '.$query2.' \n '.$query3);
                    }else{
                        var_dump("  ------>>>> TAK JUMPA : ". $icno);
                    }
                });
            });
       var_dump("Total :: ".$counterFound);
    }


}
