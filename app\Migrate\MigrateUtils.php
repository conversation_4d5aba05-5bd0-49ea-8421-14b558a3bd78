<?php

namespace App\Migrate;
use Carbon\Carbon;
use Carbon\CarbonInterval;
use Illuminate\Support\Facades\Log;

class MigrateUtils {

    public static function getTakenTime($dtStartTime) {
        return $takentime = array(
            'TakenTime' => $dtStartTime->diffForHumans(Carbon::now()),
            'TakenTimePerMinutes' => $dtStartTime->diffInMinutes(Carbon::now()),
            'TakenTimePerSeconds' => $dtStartTime->diffInSeconds(Carbon::now())
        );
    }
    
    public static function getDateStartByPeriod($periodMinute){
       $startCarbonInterval = CarbonInterval::create(0, 0, 0, 0, 0, $periodMinute, 0); 
       $cDateStart = Carbon::now()->sub($startCarbonInterval);
       $cDateStart->subSecond($cDateStart->second); //Set second clear 00
       return $cDateStart;
    }
    
    public static function getDateEndByPeriod(Carbon $cDateStart , $periodMinute){
       $endCarbonInterval = CarbonInterval::create(0, 0, 0, 0, 0, $periodMinute, 0); 
       $cDateEnd = new Carbon($cDateStart->format('Y-m-d H.i.s'));
       $cDateEnd->add($endCarbonInterval);
       return $cDateEnd;
    }

    public static function logDump($content){
        dump($content);
        Log::info($content);
    }
    
    public static function logErrorDump($content){
        dump($content);
        Log::error($content);
    }
    
    /**
     * Parse to Carbon or null
     * @param type $day
     * @return string
     */
    public static function parseStrDateToCarbon($strDate) {
        try {
            if ($strDate != null) {
                return Carbon::parse($strDate);
            }
        } catch (Exception $ex) {
            return null;
        }
        return null;
    }

}
