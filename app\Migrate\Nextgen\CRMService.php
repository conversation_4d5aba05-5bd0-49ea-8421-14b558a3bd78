<?php

namespace App\Migrate\Nextgen;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Facades\DB;
use App\Models\Account;
use App\Models\Contact;
use App\Models\LogScheduler;
use App\Models\AccountCustom;
use App\Models\ContactCustom;
use App\Models\LogIntegration;
use App\Models\EmailAddress;
use App\Migrate\Nextgen\PMService;
use App\Migrate\Nextgen\SMService;

/**
 * Description of Profile Management PMServiceTrait
 *
 * <AUTHOR>
 */
class CRMService {
    public static $USER_LOGGED_ID = '3';
    
    //AS REFERENCE ONLY
    public static $RECORD_STATUS = array(
                        '' => '',
                        0 => 'RECORD_STATUS_SUSPENDED',
                        1 => 'RECORD_STATUS_ACTIVE',
                        2 => 'RECORD_STATUS_CANCELLED',
                        3 => 'RECORD_STATUS_EXPIRED',
                        4 => 'RECORD_STATUS_REJECTED',
                        5 => 'RECORD_STATUS_IN_PROGRESS',
                        6 => 'RECORD_STATUS_PENDING_RE_APPROVAL',
                        7 => 'RECORD_STATUS_7', /** Not sure, Not in document **/
                        8 => 'RECORD_STATUS_PENDING_ACTIVATION',
                        9 => 'RECORD_STATUS_DELETED'
                        );
    
    public static $SOFTCERT_STATUS = array(
            '' => '',
            0 => 'Softcert is not required',
            4 => 'Softcert is required/Pending for softcert request',
            2 => 'Softcert requested',
            3 => 'Softcert processing',
            1 => 'Softcert issued',
            6 => 'Softcert rejected',
            9 => 'Softcert request failed (E.g. No response from spki)',
            5 => 'Softcert revoked',
            7 => 'Softcert ready for renewal',
        );
    
    public static $YES_NO= array(
            '' => '',
            1 => 'Yes',
            0 => 'No',
        );
    
    
    public static $MAP_STATE_LIST_CRM = array(
                        'WILAYAH PERSEKUTUAN KUALA LUMPUR'  =>  10001,
                        'MELAKA'    =>  10002,
                        'JOHOR'     =>  10003,
                        'SELANGOR'  =>  10004,
                        'PULAU PINANG'  =>  10005,
                        'NEGERI SEMBILAN'   =>  10006,
                        'PAHANG'    =>  10007,
                        'PERAK'     =>  10008,
                        'SABAH'     =>  10009,
                        'SARAWAK'   =>  10010,
                        'PERLIS'    =>  10011,
                        'KEDAH'     =>  10012,
                        'KELANTAN'  =>  10013,
                        'TERENGGANU'=>  10014,
                        'WILAYAH PERSEKUTUAN LABUAN'    =>  10015,
                        'WILAYAH PERSEKUTUAN PUTRAJAYA' =>  10017,
                        'PUTRAJAYA' => 10017,
                        'LUAR NEGERI'   =>  10018,
                    );
    
 
    /** Get Account Record for PM **/
    public static function getGovProfileCrm($org_code,$org_type_id) {
        $accountList = Account::where('org_gov_code',$org_code)
                    //->where('org_gov_type',PMService::$ORG_GOVERNMENT_TYPE[$org_type_id])->get();
                    //->where('org_gov_type',PMService::getOrgProfileType($org_type_id))->get();
                    ->where('org_gov_type',$org_type_id)->get();
    
        if(count($accountList) > 0){
            $account = self::getAccountCRMPriority($accountList);
            return $account;
        }
        return null;
    }
    
    public static function getGovProfileCrmByName($orgName){
        $accountList = Account::where('name',$orgName)->get();
        if(count($accountList) > 0){
            $account = self::getAccountCRMPriority($accountList);
            return $account;
        }
        return null;
    }


    /**
     * Find Account 
     * 1) Check epno is exist or not. <br />
     *      If not existed, <br />
     *          Check MOF -> check MOF No.<br />
     *          Check (Basic) -> check SSM No. <br />
     * 
     * In CRM, can be data redundant. To make sure get real data,
     *  get List then filter is Active first, if not have, then get In Active.
     * 
     * @param type $obj  this object is refer Table Nextgen Structure
     */
    public static function getSupplierCrm($obj) {
        $account = null;
        
        $accountsEpNo = array();
        if($obj->ep_no != null && $obj->ep_no != ''){
            $accountsEpNo = DB::table('accounts')
                    ->where('account_type', 'SUPPLIER')
                    ->where('ep_no', trim($obj->ep_no))
                    ->get();
        }
        if (count($accountsEpNo) == 0) {
            if ($obj->mof_no != '') {
                $accountsMofNew = DB::table('accounts')
                        ->where('account_type', 'SUPPLIER')
                        ->where('mof_no', trim($obj->mof_no))
                        ->get();
                if (count($accountsMofNew) == 0) {
                    $accountsMofOld = DB::table('accounts as a')
                            ->join('accounts_cstm as b', 'a.id', '=', 'b.id_c')
                            ->where('a.account_type', 'SUPPLIER')
                            ->where('b.mof_no_c', trim($obj->mof_no))
                            ->get();
                    if (count($accountsMofOld) == 0) {
                        Log::info('     NOT FOUND !! Get Account search By MOF No. (OLD): ' . $obj->mof_no);
                        return null;
                    } else {
                        $account = self::getAccountCRMPriority($accountsMofOld);
                        Log::info('     SUCCESS !! Get Account search By MOF No. (OLD): ' . $obj->mof_no);
                    }
                } else {
                    $account = self::getAccountCRMPriority($accountsMofNew);
                    Log::info('     SUCCESS !! Get Account search By MOF No. (NEW): ' . $account->mof_no);
                }
            } else {
                $accountsSsmNew = DB::table('accounts')
                        ->where('account_type', 'SUPPLIER')
                        ->where('registration_no', trim($obj->reg_no))
                        ->get();
                if (count($accountsSsmNew) == 0) {
                    $accountsSsmOld = DB::table('accounts as a')
                            ->join('accounts_cstm as b', 'a.id', '=', 'b.id_c')
                            ->where('a.account_type', 'SUPPLIER')
                            ->where('b.ssm_no_c', trim($obj->reg_no))
                            ->get();
                    if (count($accountsSsmOld) == 0) {
                        Log::info('     NOT FOUND !! Get Account search By Registration No. Company (OLD): ' . $obj->reg_no);
                        return null;
                    } else {
                        $account = self::getAccountCRMPriority($accountsSsmOld);
                        Log::info('     Get Account search By Registration No. Company (OLD): ' . $account->registration_no);
                    }
                } else {
                    // Fix issue same company name but diff ep no. This scenario can be happen in eP. 
                    $account = self::getAccountCRMPriority($accountsSsmNew);
                    if( trim($account->ep_no) == trim($obj->ep_no) ){
                        Log::info('     Get Account search By Registration No. Company (NEW): ' . $account->registration_no);
                    }else{
                        return null;
                    }
                    
                }
            }
        } else {
            $account = self::getAccountCRMPriority($accountsEpNo);
            Log::info('     Found & Get Account By search eP No: ' . $account->ep_no .'(Account ID : '.$account->id.')');
        }

        return $account;
    }

    public static function getAccountCRMPriority($accountList) {
        $account = $accountList->where('deleted', '0')->first();
        if ($account == null) {
            $account = $accountList->first();
        }
        return $account;
    }
    
    
    /**
     * Update Account Supplier Info
     * @param type $obj
     * @param type $accountId
     * 
     */
    public static function saveAccountSupplierPrimary($obj, $accountId = null) {
        
        //Create NEW
        $account = new Account;
        $account->id = Uuid::uuid4()->toString();
        
        // IF exist , overwrite and UPDATE
        if($accountId != null){
            $account = Account::find($accountId);
        }
        $account->name = $obj->company_name;
        
        if($obj->mof_no != null){
            $account->mof_no = $obj->mof_no;
        }
        
        $account->record_status = $obj->s_record_status; //CRMService::$RECORD_STATUS[$obj->s_record_status];

        /** When check branch_code is null mean, it refer to HQ Supplier**/
        if($obj->branch_code == null){
            $account->gst_no = $obj->gst_reg_no;
            if($obj->gst_eff_date){
                $gstEffDate = new Carbon($obj->gst_eff_date);
                //$gstEffDate->subHour(8);
                $account->gst_effective_date = $gstEffDate->format('Y-m-d');
            }
        }
        
        if($obj->mof_eff_date){
            $effDate = new Carbon($obj->mof_eff_date);
            //$effDate->subHour(8);
            $account->effective_from = $effDate->format('Y-m-d');
        }
        if($obj->mof_exp_date){
            $expDate = new Carbon($obj->mof_exp_date);
            //$expDate->subHour(8);
            $account->effective_to = $expDate->format('Y-m-d');
        }
        
        if($obj->supplier_type != null){
            $account->supplier_type = $obj->supplier_type; //SMService::$SUPPLIER_TYPE[$obj->supplier_type];
        }
        $account->ep_no = trim($obj->ep_no);
        if($obj->reg_no != null){
            $account->registration_no = strtoupper(trim($obj->reg_no));
        }        
        
        if($obj->appl_type != null){
            $account->appl_type = $obj->appl_type; //SMService::$APPL_TYPE[$obj->appl_type];
        }
        if($obj->a_status_id != null){
            $account->appl_status = SMService::getStatusDesc($obj->a_status_id); 
        }
        $account->appl_no = $obj->appl_no; 
        
        if($obj->mof_reg_status_id != null){
            $account->mof_reg_status = $obj->mof_reg_status_id;
        }
        
        $account->website = $obj->website;
        if ($obj->phone_country != '' && $obj->phone_area != '' && $obj->phone_no != '') {
            $account->phone_office = $obj->phone_country . $obj->phone_area . $obj->phone_no;
        }
        if ($obj->fax_country != '' && $obj->fax_area != '' && $obj->fax_no != '') {
            $account->phone_fax = $obj->fax_country . $obj->fax_area . $obj->fax_no;
        }
        
//        if($obj->s_record_status == 1){
//            $account->deleted = 0;  
//        }else{
//            $account->deleted = 1; 
//        }
        $account->deleted = 0; 
        
        $account->account_type = 'SUPPLIER';
        
        $account->date_modified = Carbon::now()->subHour(8);
        $account->modified_user_id = PMService::$USER_LOGGED_ID;
        $account->save();
        
        //Create New
        $accountCstm = new AccountCustom;
        $accountCstm->id_c = $account->id;
        
        if($accountId != null){
            $accountCstm = AccountCustom::find($accountId);
        }
        
        if($obj->mof_no != null){
            $accountCstm->mof_no_c = $obj->mof_no;
        }
        if($obj->appl_no != null){
            $accountCstm->appl_no_c = $obj->appl_no; 
        }
        
        if($obj->a_status_id != null){
            Log::info('Status ID: ' . $obj->a_status_id);
            $accountCstm->appl_status_c = SMService::getStatusDesc($obj->a_status_id); 
        }
        /** When check branch_code is null mean, it refer to HQ Supplier**/
        if($obj->branch_code == null){
            $accountCstm->gst_reg_no_c = $obj->gst_reg_no;
        }
        
        if($obj->reg_status_id != null){
            $accountCstm->reg_status_c = $obj->reg_status_id; //SMService::$MOF_REG_STATUS[$obj->reg_status_id];
        }
        if($obj->mof_exp_date){
            $expDate = new Carbon($obj->mof_exp_date);
            $expDate->subHour(8);
            $accountCstm->mof_expiry_c = $expDate->format('Y-m-d');
        }
        if($obj->reg_no != null){
            $accountCstm->ssm_no_c = strtoupper(trim($obj->reg_no));
        } 
        $accountCstm->save();
        
        Log::info('     Successfully save : ' . $account->name . ' (' . $account->ep_no . ')');
        //self::addLogSupplierIntegration($obj, 'ACCOUNT_SUPPLIER', ' ep_no : ' . $account->ep_no . ' supplier_type : ' . $account->supplier_type);
    
        return $account;
    }

    public static function saveAccountSupplierAddress($obj, $accountId) {
        /*


                [address_type] => C
                [ca_record_status] => 1
                [sa_record_status] => 1
                [address_id] => 7202950
                [address_1] => NO.27, JALAN PUJ 8/7, TAMAN PUNCAK JALIL
                [address_2] => BANDAR PUTRA PERMAI,
                [address_3] =>
                [postcode] => 43300
                [country_id] => 131
                [state_id] => 11
                [division_id] =>
                [district_id] => 82
                [city_id] => 1606
                [sa_created_date] => 2017-05-15 12:27:01
                [sa_changed_date] => 2017-05-15 12:28:32
         * 
         */
        
        $account = Account::find($accountId);
        $checkChanged = false;

        if ($obj->address_type == 'C') {
            $checkChanged = true;
            $account->billing_address_street = SMService::add_address_streets($obj->address_1, $obj->address_2, $obj->address_3);
            $account->billing_address_city = PMService::getCityName($obj->city_id);
            $account->billing_address_district = PMService::getDistrictName($obj->district_id);
            $account->billing_address_division = PMService::getDivisionName($obj->division_id);
            $account->billing_address_state = PMService::getStateName($obj->state_id);
            $account->billing_address_postalcode = $obj->postcode;
            if ($obj->country_id == '131') {
                $account->billing_address_country = 'MALAYSIA';
            } else {
                $account->billing_address_country = PMService::getCountryName($obj->country_id);
            }
        }

        if ($checkChanged == true) {
            $account->date_modified = Carbon::now()->subHour(8);
            $account->modified_user_id = PMService::$USER_LOGGED_ID;
            $account->save();
            Log::info('     ->> Successfully save address supplier by ep_no: ' . $obj->ep_no . ' : accountID ' . $accountId);
            //self::addLogSupplierIntegration($obj, 'ACCOUNT_SUPPLIER_ADDRESS', ' ep_no : ' . $obj->ep_no . ' accountID : ' . $accountId );
        }
    }
    
    /**
     * Check in CRM , if record not existed, Data Ministry will inserted
     * @param type $rowData
     */
    public static function createContactSupplier($account, $rowData) {
        //rowData refer prefix p mean come from sm_personnel. Prefer take from personnel table, cause this main users in suppliers.
        $contact = new Contact;
        $contact->id = Uuid::uuid4()->toString();
        $contact->first_name = strtoupper(trim(strtoupper($rowData->p_name)));
        $contact->last_name = '';
        $contact->salutation = PMService::getSalutationName($rowData->salutation_id);
        $contact->designation_nextgen = strtoupper(trim($rowData->p_designation));
 
        if($rowData->p_record_status == '1' &&  $rowData->login_id != null){
            $contact->login_id_nextgen = trim($rowData->login_id);
        }else{
           $contact->login_id_nextgen = null; 
        }
        
        $contact->user_id_nextgen = trim($rowData->personnel_id);
        $contact->identity_no_nextgen = trim($rowData->p_identification_no);
        if($rowData->p_identification_type_id != null){
            $contact->identity_type_nextgen = PMService::getIdentityType($rowData->p_identification_type_id);
        }
        $contact->record_status_nextgen = $rowData->p_record_status; //CRMService::$RECORD_STATUS[$rowData->p_record_status];

        if ($rowData->p_mobile_country != '' && $rowData->p_mobile_area != '' && $rowData->p_mobile_no != '') {
            $contact->phone_mobile = $rowData->p_mobile_country . $rowData->p_mobile_area . $rowData->p_mobile_no;
        }
        if ($rowData->p_phone_country != '' && $rowData->p_phone_area != '' && $rowData->p_phone_no != '') {
            $contact->phone_work = $rowData->p_phone_country . $rowData->p_phone_area . $rowData->p_phone_no;
        }
      
        $contact->date_entered = Carbon::now()->subHour(8);
        $contact->date_modified = Carbon::now()->subHour(8);
        $contact->created_by = CRMService::$USER_LOGGED_ID;
        $contact->modified_user_id = CRMService::$USER_LOGGED_ID;
        if($rowData->p_record_status == 1){
            $contact->deleted = 0;  
        }else{
            //$contact->deleted = 1; 
        }
        $contact->account_reference_id = $account->id;
        
        $contact->save();

        $contactCustom = new ContactCustom;
        $contactCustom->id_c = $contact->id;
        $contactCustom->ic_no_c = strtoupper(trim($rowData->p_identification_no));
        if($rowData->p_record_status == '1' &&  $rowData->login_id != null){
           //Set  flag is 1, this contacts has User Login in Nextgen. No need update first time login.
           $contactCustom->portal_login_flag_c = 1; 
        }
        $contactCustom->contact_type_c = 'Staff';
        if($rowData->p_is_equity_owner == 1){
            $contactCustom->contact_type_c = 'Owner';
        }
        $contactCustom->save();
        


        //Add relationship with account
        DB::table('accounts_contacts')
                ->insertGetid([
                    'id' => Uuid::uuid4()->toString(), 'date_modified' => Carbon::now()->subHour(8),
                    'account_id' => $account->id, 'contact_id' => $contact->id
        ]);
        //$contact->accounts()->attach($account, ['id' => Uuid::uuid4()->toString(),'date_modified' =>Carbon::now()]);



        if (filter_var($rowData->p_email, FILTER_VALIDATE_EMAIL)) {
            $emailAddress = new EmailAddress;
            $emailAddress->id = Uuid::uuid4()->toString();
            $email = filter_var($rowData->p_email, FILTER_SANITIZE_EMAIL);
            $emailAddress->email_address = strtolower($email);
            $emailAddress->email_address_caps = strtoupper($email);
            $emailAddress->date_created = Carbon::now()->subHour(8);
            $emailAddress->date_modified = Carbon::now()->subHour(8);
            $emailAddress->deleted = 0;
            $emailAddress->save();

            //Add relationship with email
            $contact->emails()->attach($emailAddress, [
                'id' => Uuid::uuid4()->toString(),
                'bean_module' => 'Contacts',
                'date_modified' => Carbon::now()->subHour(8),
                'deleted' => 0
            ]);
            $contact->emailsRelation()->attach($emailAddress, [
                'id' => Uuid::uuid4()->toString(),
                'bean_module' => 'Contacts',
                'primary_address' => 1,
                'reply_to_address' => 0,
                'date_created' => Carbon::now()->subHour(8),
                'date_modified' => Carbon::now()->subHour(8),
                'deleted' => 0
            ]);
        }


        //CRMService::addLogSupplierIntegration($rowData, 'ACCOUNT_SUPPLIER_CONTACT', 'CREATE CONTACT - ep_no:'.$account->ep_no . ' : ' . $contact->first_name. ' : ' . $contact->identity_no_nextgen);
        Log::info('Successfully save Contacts from : ' . $account->ep_no . ' : ' . $account->mof_no . ' :-> name ' . $contact->first_name . ' (' . $contact->id . ')');

        return $contact;
    }
    
    
    public static function saveContactSupplier($rowData,$contactObj) {
        $contactId = $contactObj->contact_id;
        Log::info(self::class . ' ' . __FUNCTION__ . ' contactId: ' . $contactId);

        $contact_type_c = 'Staff';
        if($rowData->p_is_equity_owner == 1){
            $contact_type_c = 'Owner';
        }
        
        $selfPortalLoginFlag = 0;
        if($rowData->p_record_status == '1' &&  $rowData->login_id != null){
           //Set  flag is 1, this contacts has User Login in Nextgen. No need update first time login.
           $selfPortalLoginFlag = 1; 
        }
        
        DB::table('contacts_cstm')
                ->where('id_c', $contactId)
                ->update([
                    'ic_no_c' => strtoupper(trim($rowData->p_identification_no)),
                    'contact_type_c'        => $contact_type_c,
                    'portal_login_flag_c'   => $selfPortalLoginFlag
        ]);

        $contact = Contact::find($contactId);
        $contact->account_reference_id = $contactObj->acc_id;
        $contact->first_name = strtoupper(trim(strtoupper($rowData->p_name)));
        $contact->salutation = PMService::getSalutationName($rowData->salutation_id);
        $contact->designation_nextgen = strtoupper(trim($rowData->p_designation));
        if($rowData->p_record_status == '1' &&  $rowData->login_id != null){
            $contact->login_id_nextgen = trim($rowData->login_id);
        }else{
           $contact->login_id_nextgen = null; 
        }
        $contact->user_id_nextgen = trim($rowData->personnel_id);
        $contact->identity_no_nextgen = trim($rowData->p_identification_no);
        if($rowData->p_identification_type_id != null){
            $contact->identity_type_nextgen = PMService::getIdentityType($rowData->p_identification_type_id);
        }
        $contact->record_status_nextgen = $rowData->p_record_status; //CRMService::$RECORD_STATUS[$rowData->p_record_status];

        if ($rowData->p_mobile_country != '' && $rowData->p_mobile_area != '' && $rowData->p_mobile_no != '') {
            $contact->phone_mobile = $rowData->p_mobile_country . $rowData->p_mobile_area . $rowData->p_mobile_no;
        }
        if ($rowData->p_phone_country != '' && $rowData->p_phone_area != '' && $rowData->p_phone_no != '') {
            $contact->phone_work = $rowData->p_phone_country . $rowData->p_phone_area . $rowData->p_phone_no;
        }
        $contact->date_modified = Carbon::now()->subHour(8);
        $contact->modified_user_id = CRMService::$USER_LOGGED_ID;
        
        if($rowData->p_record_status == 1){
            $contact->deleted = 0;  
        }else{
            //$contact->deleted = 1; 
        }
        $contact->save();
        

        //CRMService::addLogSupplierIntegration($rowData, 'ACCOUNT_SUPPLIER_CONTACT', 'UPDATE CONTACT - '.$contact->first_name. ' : ' . $contact->identity_no_nextgen);
        Log::info(self::class . ' ' . __FUNCTION__ . 'completed saved contactId: ' . $contactId);
        return $contact;
    }
    
    /**
     * Create contact Government. 
     * @param type $rowData
     */
    public static function createContactGovernment($account, $rowData, $role) {
        //rowData refer prefix uo mean come from pm_user_org 
        $contact = new Contact;
        $contact->id = Uuid::uuid4()->toString();
        $contact->first_name = strtoupper(trim(strtoupper($rowData->fullname)));
        $contact->last_name = '';
        $contact->salutation = PMService::getSalutationName($rowData->salutation_id);
        $contact->designation_nextgen = strtoupper(trim($rowData->designation));
        $contact->role_nextgen = strtoupper(trim($role));
        $contact->identity_no_nextgen = trim($rowData->identification_no);
        if($rowData->identification_type_id != null){
            $contact->identity_type_nextgen = PMService::getIdentityType($rowData->identification_type_id);
        }

        if ($rowData->mobile_country != '' && $rowData->mobile_area != '' && $rowData->mobile_no != '') {
            $contact->phone_mobile = $rowData->mobile_country . $rowData->mobile_area . $rowData->mobile_no;
        }
        if ($rowData->phone_country != '' && $rowData->phone_area != '' && $rowData->phone_no != '') {
            $contact->phone_work = $rowData->phone_country . $rowData->phone_area . $rowData->phone_no;
        }
        if ($rowData->fax_country != '' && $rowData->fax_area != '' && $rowData->fax_no != '') {
            $contact->phone_fax = $rowData->fax_country . $rowData->fax_area . $rowData->fax_no;
        }

        $contact->date_entered = Carbon::now()->subHour(8);
        $contact->date_modified = Carbon::now()->subHour(8);
        $contact->created_by = PMService::$USER_LOGGED_ID;
        $contact->modified_user_id = PMService::$USER_LOGGED_ID;
     
        /** We have to filter, some record can be more than 2 in PM_USER_ORG. We must get priority RECORD STATUS : ACTIVE first.**/
        $userOrg = PMService::getUserOrgPriority($rowData->user_id);
        $contact->user_id_nextgen = trim($userOrg->user_org_id);
        if($userOrg->record_status == '1'){
            //Only keep login ID with ptj active.
            $contact->login_id_nextgen = trim($rowData->login_id);
        }else{
            //ptj not active, remove it.
           $contact->login_id_nextgen = null; 
        }
        $contact->record_status_nextgen = $userOrg->record_status; //CRMService::$RECORD_STATUS[$userOrg->record_status];
        if($userOrg->record_status == 1){
            $contact->deleted = 0;  
        }else{
            //$contact->deleted = 1; 
        }
        
        $contact->account_reference_id = $account->id;
        
        $contact->save();

        $contactCustom = new ContactCustom;
        $contactCustom->id_c = $contact->id;
        $contactCustom->ic_no_c = strtoupper(trim($rowData->identification_no));
        if($rowData->org_type_id ==  5){
            $contactCustom->contact_type_c = 'PTJ';
        }else{
            $contactCustom->contact_type_c = 'Staff'; 
        }
        if($userOrg->record_status == '1'){
           //Set  flag is 1, this contacts has User Login in Nextgen. No need update first time login.
           $contactCustom->portal_login_flag_c = 1; 
        }
        $contactCustom->save();
        


        //Add relationship with account
        DB::table('accounts_contacts')
                ->insertGetid([
                    'id' => Uuid::uuid4()->toString(), 'date_modified' => Carbon::now()->subHour(8),
                    'account_id' => $account->id, 'contact_id' => $contact->id
        ]);
        //$contact->accounts()->attach($account, ['id' => Uuid::uuid4()->toString()]);



        if (filter_var($rowData->email, FILTER_VALIDATE_EMAIL)) {
            $emailAddress = new EmailAddress;
            $emailAddress->id = Uuid::uuid4()->toString();
            $email = filter_var($rowData->email, FILTER_SANITIZE_EMAIL);
            $emailAddress->email_address = strtolower($email);
            $emailAddress->email_address_caps = strtoupper($email);
            $emailAddress->date_created = Carbon::now()->subHour(8);
            $emailAddress->date_modified = Carbon::now()->subHour(8);
            $emailAddress->deleted = 0;
            $emailAddress->save();

            //Add relationship with email
            $contact->emails()->attach($emailAddress, [
                'id' => Uuid::uuid4()->toString(),
                'bean_module' => 'Contacts',
                'date_modified' => Carbon::now()->subHour(8),
                'deleted' => 0
            ]);
            $contact->emailsRelation()->attach($emailAddress, [
                'id' => Uuid::uuid4()->toString(),
                'bean_module' => 'Contacts',
                'primary_address' => 1,
                'reply_to_address' => 0,
                'date_created' => Carbon::now()->subHour(8),
                'date_modified' => Carbon::now()->subHour(8),
                'deleted' => 0
            ]);
        }


        //self::addLogGovernmentIntegration($rowData, 'ACCOUNT_GOVERNMENT_CONTACT', $account->org_gov_type . ' : ' . $account->org_gov_code);
        Log::info('Successfully save Contacts from : ' . $account->org_gov_type . ' : ' . $account->org_gov_code . ' :-> name ' . $contact->first_name . ' (' . $contact->id . ')');

        return $contact;
    }
    
    /*
     * Update contact government
     */
    public static function saveContactGovernment($rowData, $role,$contactObj) {
        $contactId = $contactObj->contact_id;
        Log::info(self::class . ' ' . __FUNCTION__ . ' contactId: ' . $contactId);

        DB::table('contacts_cstm')
                ->where('id_c', $contactId)
                ->update([
                    'ic_no_c' => strtoupper(trim($rowData->identification_no))
        ]);

        $contact = Contact::find($contactId);
        $contact->account_reference_id = $contactObj->acc_id;
        $contact->first_name = strtoupper(trim(strtoupper($rowData->fullname)));
        $contact->salutation = PMService::getSalutationName($rowData->salutation_id);
        $contact->designation_nextgen = strtoupper(trim($rowData->designation));
        $contact->role_nextgen = strtoupper(trim($role));
        
        $contact->identity_no_nextgen = trim($rowData->identification_no);
        if($rowData->identification_type_id != null){
            $contact->identity_type_nextgen = PMService::getIdentityType($rowData->identification_type_id);
        }

        if ($rowData->mobile_country != '' && $rowData->mobile_area != '' && $rowData->mobile_no != '') {
            $contact->phone_mobile = $rowData->mobile_country . $rowData->mobile_area . $rowData->mobile_no;
        }
        if ($rowData->phone_country != '' && $rowData->phone_area != '' && $rowData->phone_no != '') {
            $contact->phone_work = $rowData->phone_country . $rowData->phone_area . $rowData->phone_no;
        }
        if ($rowData->fax_country != '' && $rowData->fax_area != '' && $rowData->fax_no != '') {
            $contact->phone_fax = $rowData->fax_country . $rowData->fax_area . $rowData->fax_no;
        }
        $contact->date_modified = Carbon::now()->subHour(8);
        $contact->modified_user_id = PMService::$USER_LOGGED_ID;
        
        /** We have to filter, some record can be more than 2 in PM_USER_ORG. We must get priority RECORD STATUS : ACTIVE first.**/
        //$userOrg = PMService::getUserOrgPriority($rowData->user_id);
        $userOrg = PMService::getUserOrgPriorityDetails($rowData->user_id, $rowData->org_profile_id);
        $contact->user_id_nextgen = trim($userOrg->user_org_id);
        if($userOrg->record_status == '1'){
            //Only keep login ID with ptj active.
            $contact->login_id_nextgen = trim($rowData->login_id);
        }else{
            //ptj not active, remove it.
           $contact->login_id_nextgen = null; 
        }
        $contact->record_status_nextgen = $userOrg->record_status; //CRMService::$RECORD_STATUS[$userOrg->record_status];
        if($userOrg->record_status == 1){
            $contact->deleted = 0;  
        }else{
            //$contact->deleted = 1; 
        }
        
        $contact->save();
        
        $contactCustom = new ContactCustom;
        $contactCustom->id_c = $contact->id;
        if($contactId != null){
            $contactCustom = ContactCustom::find($contactId);
        }
        if($userOrg->record_status == '1'){
           //Set  flag is 1, this contacts has User Login in Nextgen. No need update first time login.
           $contactCustom->portal_login_flag_c = 1; 
        }
        $contactCustom->ic_no_c = strtoupper(trim($rowData->identification_no));
        $contactCustom->contact_type_c = 'PTJ';
        $contactCustom->save();

        //self::addLogGovernmentIntegration($rowData, 'ACCOUNT_GOVERNMENT_CONTACT', 'completed saved contactId: ' . $contactId);
        Log::info(self::class . ' ' . __FUNCTION__ . 'completed saved contactId: ' . $contactId);
        return $contact;
    }
    
    public static function saveEmailContact($email, $contactId) {
        $isChanged = false;
        if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $list = DB::table('email_addr_bean_rel as a')
                    ->join('email_addresses as b', 'a.email_address_id', '=', 'b.id')
                    ->where('b.email_address', $email)
                    ->where('a.bean_module', 'Contacts')
                    ->where('a.bean_id', $contactId)
                    ->get();
            
            $emailObj = null;

            if (count($list) == 0) {
                DB::table('email_addresses')
                        ->insertGetId([
                            'id' => Uuid::uuid4()->toString(),
                            'email_address' => trim(strtolower($email)),
                            'email_address_caps' => trim(strtoupper($email)),
                            'date_created' => Carbon::now()->subHour(8),
                            'date_modified' => Carbon::now()->subHour(8),
                            'deleted' => 0,
                ]);
                $emailObj = DB::table('email_addresses')
                                ->where('email_address', $email)->orderBy('date_created', 'desc')->first();
                DB::table('email_addr_bean_rel')
                        ->insertGetId([
                            'id' => Uuid::uuid4()->toString(),
                            'email_address_id' => $emailObj->id,
                            'bean_id' => $contactId,
                            'bean_module' => 'Contacts',
                            'date_created' => Carbon::now()->subHour(8),
                            'date_modified' => Carbon::now()->subHour(8),
                            'deleted' => 0,
                ]);
                $isChanged = true;
            }
            if($emailObj != null){
                DB::table('email_addr_bean_rel')
                    ->where('bean_module', 'Contacts')
                    ->where('bean_id', $contactId)
                    ->where('email_address_id','<>',$emailObj->id)
                    ->delete();
            }

            $list2 = DB::table('emails_beans as a')
                    ->join('email_addresses as b', 'a.email_id', '=', 'b.id')
                    ->where('b.email_address', $email)
                    ->where('a.bean_module', 'Contacts')
                    ->where('a.bean_id', $contactId)
                    ->get();
            
            
            
            if (count($list2) == 0) {
                if ($emailObj == null) {
                    DB::table('email_addresses')
                            ->insertGetId([
                                'id' => Uuid::uuid4()->toString(),
                                'email_address' => trim(strtolower($email)),
                                'email_address_caps' => trim(strtoupper($email)),
                                'date_created' => Carbon::now()->subHour(8),
                                'date_modified' => Carbon::now()->subHour(8),
                                'deleted' => 0,
                    ]);

                    $emailObj = DB::table('email_addresses')
                                    ->where('email_address', $email)->orderBy('date_created', 'desc')->first();
                }
                DB::table('emails_beans')
                        ->insertGetId([
                            'id' => Uuid::uuid4()->toString(),
                            'email_id' => $emailObj->id,
                            'bean_id' => $contactId,
                            'bean_module' => 'Contacts',
                            'date_modified' => Carbon::now()->subHour(8),
                            'deleted' => 0,
                ]);
                $isChanged = true;
            }
            if($emailObj != null){
                DB::table('emails_beans')
                    ->where('bean_module', 'Contacts')
                    ->where('bean_id', $contactId)
                    ->where('email_id','<>',$emailObj->id)
                    ->delete();
            }
        }


        if ($isChanged == true) {
            Log::info(self::class . ' ' . __FUNCTION__ . 'completed save email : ' . $email . ' ,contactId: ' . $contactId);
        }
    }
    
    
    public static function saveEmailAccount($email, $accountId) {
        $isChanged = false;
        if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $list = DB::table('email_addr_bean_rel as a')
                    ->join('email_addresses as b', 'a.email_address_id', '=', 'b.id')
                    ->where('b.email_address', $email)
                    ->where('a.bean_module', 'Accounts')
                    ->where('a.bean_id', $accountId)
                    ->get();
            
            $emailObj = null;

            if (count($list) == 0) {
                DB::table('email_addresses')
                        ->insertGetId([
                            'id' => Uuid::uuid4()->toString(),
                            'email_address' => trim(strtolower($email)),
                            'email_address_caps' => trim(strtoupper($email)),
                            'date_created' => Carbon::now()->subHour(8),
                            'date_modified' => Carbon::now()->subHour(8),
                            'deleted' => 0,
                ]);
                $emailObj = DB::table('email_addresses')
                                ->where('email_address', $email)->orderBy('date_created', 'desc')->first();
                DB::table('email_addr_bean_rel')
                        ->insertGetId([
                            'id' => Uuid::uuid4()->toString(),
                            'email_address_id' => $emailObj->id,
                            'bean_id' => $accountId,
                            'bean_module' => 'Accounts',
                            'date_created' => Carbon::now()->subHour(8),
                            'date_modified' => Carbon::now()->subHour(8),
                            'deleted' => 0,
                ]);
                $isChanged = true;
            }
            if($emailObj != null){
                DB::table('email_addr_bean_rel')
                    ->where('bean_module', 'Accounts')
                    ->where('bean_id', $accountId)
                    ->where('email_address_id','<>',$emailObj->id)
                    ->delete();
            }

            $list2 = DB::table('emails_beans as a')
                    ->join('email_addresses as b', 'a.email_id', '=', 'b.id')
                    ->where('b.email_address', $email)
                    ->where('a.bean_module', 'Accounts')
                    ->where('a.bean_id', $accountId)
                    ->get();
            
            
            
            if (count($list2) == 0) {
                if ($emailObj == null) {
                    DB::table('email_addresses')
                            ->insertGetId([
                                'id' => Uuid::uuid4()->toString(),
                                'email_address' => trim(strtolower($email)),
                                'email_address_caps' => trim(strtoupper($email)),
                                'date_created' => Carbon::now()->subHour(8),
                                'date_modified' => Carbon::now()->subHour(8),
                                'deleted' => 0,
                    ]);

                    $emailObj = DB::table('email_addresses')
                                    ->where('email_address', $email)->orderBy('date_created', 'desc')->first();
                }
                DB::table('emails_beans')
                        ->insertGetId([
                            'id' => Uuid::uuid4()->toString(),
                            'email_id' => $emailObj->id,
                            'bean_id' => $accountId,
                            'bean_module' => 'Accounts',
                            'date_modified' => Carbon::now()->subHour(8),
                            'deleted' => 0,
                ]);
                $isChanged = true;
            }
            if($emailObj != null){
                DB::table('emails_beans')
                    ->where('bean_module', 'Accounts')
                    ->where('bean_id', $accountId)
                    ->where('email_id','<>',$emailObj->id)
                    ->delete();
            }
        }


        if ($isChanged == true) {
            Log::info(self::class . ' ' . __FUNCTION__ . 'completed save email Accounts : ' . $email . ' ,AccountId: ' . $accountId);
        }
    }
    
    /**
     * Add log integration for Ep Government
     * @param type $rowData
     * @param type $type
     */
    protected static function addLogSupplierIntegration($rowData, $type, $remarks = '') {
        $logIntegration = new LogIntegration;
        $logIntegration->id = Uuid::uuid4()->toString();
        $logIntegration->name = 'NEXTGEN GOVERNMENT INTEGRATION';
        $logIntegration->service = 'BATCH INTEGRATION';
        $logIntegration->interface_service = $type;
        $logIntegration->status = 'COMPLETED';
        $logIntegration->remarks = $remarks;
        $logIntegration->description = 'Data received and completed insert into CRM';
        $logIntegration->date_entered = Carbon::now()->subHour(8);
        $logIntegration->date_modified = Carbon::now()->subHour(8);
        $logIntegration->created_by = PMService::$USER_LOGGED_ID;
        $logIntegration->modified_user_id = PMService::$USER_LOGGED_ID;
        $logIntegration->deleted = 0;
        $logIntegration->data = json_encode($rowData);

        $logIntegration->save();
    }

    /**
     * Add log integration for Ep Government
     * @param type $rowData
     * @param type $type
     */
    protected static function addErrorLogSupplierIntegration($rowData, $type, $remarks = '') {
        $logIntegration = new LogIntegration;
        $logIntegration->id = Uuid::uuid4()->toString();
        $logIntegration->name = 'NEXTGEN GOVERNMENT INTEGRATION';
        $logIntegration->service = 'BATCH INTEGRATION';
        $logIntegration->interface_service = $type;
        $logIntegration->status = 'ERROR';
        $logIntegration->remarks = $remarks;
        $logIntegration->description = 'Data error in processing';
        $logIntegration->date_entered = Carbon::now()->subHour(8);
        $logIntegration->date_modified = Carbon::now()->subHour(8);
        $logIntegration->created_by = PMService::$USER_LOGGED_ID;
        $logIntegration->modified_user_id = PMService::$USER_LOGGED_ID;
        $logIntegration->deleted = 0;
        $logIntegration->data = json_encode($rowData);

        $logIntegration->save();
    }
    
    /**
     * Add log integration for Ep Government
     * @param type $rowData
     * @param type $type
     */
    public static function addLogGovernmentIntegration($rowData, $type, $remarks = '') {
        $logIntegration = new LogIntegration;
        $logIntegration->id = Uuid::uuid4()->toString();
        $logIntegration->name = 'NEXTGEN GOVERNMENT INTEGRATION';
        $logIntegration->service = 'BATCH INTEGRATION';
        $logIntegration->interface_service = $type;
        $logIntegration->status = 'COMPLETED';
        $logIntegration->remarks = $remarks;
        $logIntegration->description = 'Data received and completed insert into CRM';
        $logIntegration->date_entered = Carbon::now()->subHour(8);
        $logIntegration->date_modified = Carbon::now()->subHour(8);
        $logIntegration->created_by = PMService::$USER_LOGGED_ID;
        $logIntegration->modified_user_id = PMService::$USER_LOGGED_ID;
        $logIntegration->deleted = 0;
        $logIntegration->data = json_encode($rowData);

        $logIntegration->save();
    }

    /**
     * Add log integration for Ep Government
     * @param type $rowData
     * @param type $type
     */
    public static function addErrorLogGovernmentIntegration($rowData, $type, $remarks = '') {
        $logIntegration = new LogIntegration;
        $logIntegration->id = Uuid::uuid4()->toString();
        $logIntegration->name = 'NEXTGEN GOVERNMENT INTEGRATION';
        $logIntegration->service = 'BATCH INTEGRATION';
        $logIntegration->interface_service = $type;
        $logIntegration->status = 'ERROR';
        $logIntegration->remarks = $remarks;
        $logIntegration->description = 'Data error in processing';
        $logIntegration->date_entered = Carbon::now()->subHour(8);
        $logIntegration->date_modified = Carbon::now()->subHour(8);
        $logIntegration->created_by = PMService::$USER_LOGGED_ID;
        $logIntegration->modified_user_id = PMService::$USER_LOGGED_ID;
        $logIntegration->deleted = 0;
        $logIntegration->data = json_encode($rowData);

        $logIntegration->save();
    }
    
    
    public static function addLogScheduler($cDateStart,$cDateEnd,$periodMinute,$serviceName){
        $dateStart = $cDateStart->format('Y-m-d H.i.s');
        $dateEnd = $cDateEnd->format('Y-m-d H.i.s');
        
        $logSchCheck = LogScheduler::where('name',$serviceName)
                        ->orderBy('date_entered','desc')
                        ->first();
        
        if($logSchCheck != null && $logSchCheck->status == 'Processing'){
            Log::info('  LogScheduler ('.$serviceName.'): Check status previous is Processing. Action Stop ');
            
            //check period running scheduler        
            $dteFrom = new Carbon($logSchCheck->date_entered);
            $dteTo = new Carbon();
            $now = $dteTo->subHour(8); 
            $totalMinutes = $dteFrom->diffInMinutes($now); 
            Log::info('  LogScheduler (' . $serviceName . '): Total Processing Time : ' . $totalMinutes);
            if($totalMinutes > 30) {
                $msg = '*[ALERT]* LogScheduler (' . $serviceName . ' ) Proceesing Time Is More Than '.$totalMinutes.' Minutes.Please check!!';
                self::notifyWhatsapp($msg);
                
                //auto patch
                LogScheduler::where('id', $logSchCheck->id) 
                ->update([
                    'status' => 'Error',
                    'remarks' => 'Proccessing More Than 30 Minutes. Status Patch By Notification Scheduler',
                    'date_modified'   => Carbon::now()->subHour(8)
        ]);

                
            }
        return null;
        }
        
        $logSch = LogScheduler::where('name',$serviceName)
                        ->where('status','Completed')
                        ->orderBy('date_entered','desc')
                        ->first();
        if($logSch != null){
            
            /** Get previous Date record logs **/
            $clastDateto = new Carbon($logSch->date_to);
            $datefrom = $clastDateto->format('Y-m-d H.i.s');
            
            $totalMinutes = $cDateEnd->diffInMinutes($clastDateto);
            $desc = 'Based On Scheduler -> Date Start  '.$dateStart. ' Date End  '.$dateEnd. ' Period  : '.$periodMinute. ' Minutes';
            $period = $totalMinutes. ' Minutes';
            return $logScheduler = self::createLogScheduler($serviceName,$period,$datefrom,$dateEnd,$desc,'Processing');
        }else{
            $desc = 'Based On Scheduler -> Date Start  '.$dateStart. ' Date End  '.$dateEnd. ' Period  : '.$periodMinute. ' Minutes';
            $period = $periodMinute. ' Minutes';
            return $logScheduler = self::createLogScheduler($serviceName,$period,$dateStart,$dateEnd,$desc,'Processing');
        }
    }
    
    public static function notifyWhatsapp($msg) {
        DB::connection('mysql_ep_notify')
                ->insert('insert into notification  
                    (message,receiver_group,process,status,sent,date_entered,retry,source_app,source_class,source_remark) 
                    values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', [
                    $msg,
                    'CRM_LOG_SCHEDULER',
                    'notify', 0, 0,
                    Carbon::now(), 0,
                    'CrmIntegration',
                    __CLASS__,
                    'This is to alert on log scheduler processing time more than 30 minutes.'
        ]);
    }
    
    public static function createLogScheduler($serviceName,$period,$dateFrom,$dateTo,$desc,$status) {
        $logScheduler = new LogScheduler;
        $logScheduler->id = Uuid::uuid4()->toString();
        $logScheduler->name = $serviceName;
        $logScheduler->status   = $status;
        $logScheduler->service_type = 'Batch Scheduler';
        $logScheduler->date_entered = Carbon::now()->subHour(8);
        $logScheduler->date_modified = Carbon::now()->subHour(8);
        $logScheduler->created_by = CRMService::$USER_LOGGED_ID;
        $logScheduler->modified_user_id = CRMService::$USER_LOGGED_ID;

        $logScheduler->period = $period;
        //$logScheduler->date_from = Carbon::parse($dateFrom)->subHour(8);
        //$logScheduler->date_to = Carbon::parse($dateTo)->subHour(8);
        $logScheduler->date_from = $dateFrom;
        $logScheduler->date_to = $dateTo;
        $logScheduler->description = $desc; 
        $logScheduler->save();
        return $logScheduler;
    }

}
