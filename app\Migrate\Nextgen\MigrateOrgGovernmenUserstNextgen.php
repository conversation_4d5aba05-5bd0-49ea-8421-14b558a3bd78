<?php

namespace App\Migrate\Nextgen;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Migrate\MigrateUtils;
use App\Migrate\Nextgen\PMService;

/*
 * This integration to sync data Users PTJ Nextgen to CRM
 * Update field data nextgen users into CRM.
 * 
 * 
 */
class MigrateOrgGovernmenUserstNextgen {
    
    public static $KEMENTERIAN = 'KEMENTERIAN';
    public static $PEGAWAI_PENGAWAL = 'PEGAWAI PENGAWAL';
    public static $KUMPULAN_PTJ = 'KUMPULAN PTJ';
    public static $PTJ = 'PTJ';
    public static $BPK = 'BPK';
    public static $USER_LOGGED_ID = '3';
    
    public static function runMigrate() {
        Log::debug(self::class . ' Starting ... '.__FUNCTION__);
        $dtStartTime = Carbon::now();

        self::migrateOrganizationUsersGovernment();
        var_dump(self::class . ' Completed '.__FUNCTION__.' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info(self::class . ' Completed '.__FUNCTION__.' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    /**
     * Migrate data from eP to CRM. We check id data not existed in crm, Data will insert to CRM
     */
    protected static function migrateOrganizationUsersGovernment() {

        //Get Ministry Information from eP
        $results = self::getAccountsOrgGovPtjProfilesCRM();
        Log::info(__FUNCTION__.' Count Account CRM : PTJ   ' . count($results));
        //var_dump(__FUNCTION__.' Count Account CRM : PTJ   ' . count($results));
        $count = 0;
        $countPtj = 0;
        foreach ($results as $row) {
            $ptjCode = $row->org_gov_code;
            Log::info($countPtj++.') Find PTJ Code '.$ptjCode);
            //var_dump(' Find PTJ Code '.$ptjCode);
 
            $objNewPTJ = PMService::getNewPTJCode($ptjCode);
            if ($objNewPTJ && $objNewPTJ->org_validity_id) {
                
                //Check Data from CRM
                $account = CRMService::getGovProfileCrm($objNewPTJ->org_code,$objNewPTJ->org_type_id);
                if($account == null){
                   $account = SyncGovernmentInfo::prepareCreateAccount($objNewPTJ);
                }
                        
                Log::info('      :: ->  PTJ New Code ' . $objNewPTJ->org_code);
                Log::info('      :: ->  PTJ New ' . $objNewPTJ->org_name);
                $listDataUsers = PMService::getPMOrgGovUsersDetailsByOrgProfileId($objNewPTJ->org_profile_id);
                Log::info('      :: ->  Total users ptj in nextgen  ' . count($listDataUsers));
                var_dump('      :: ->  Total users ptj in nextgen  ' . count($listDataUsers));
                if(count($listDataUsers) > 0){
                    foreach ($listDataUsers as $obj) {
                        $numCounter = $count++;
                        Log::info('       '.$numCounter.'):: ->  Looking check User ID  ' . $obj->identification_no);
                        $roles = PMService::getPMOrgGovUserRolesDetails($obj->user_org_id);
                        var_dump('         '.$numCounter.')Name :- ' . $obj->fullname);  
                        var_dump('          Identity No. :- ' . $obj->identification_no);  
                        var_dump('          Login ID :- ' . $obj->login_id); 
                        var_dump('          Status  :- ' . $obj->uo_record_status); 
                        Log::info('         :: ->  Count roles found  ' . count($roles));
                        $roles_name = null;
                        foreach ($roles as $objRole) {
                            if($roles_name == null){
                               $roles_name =  $objRole->role_code;
                            }else{
                               $roles_name = $roles_name.', '.$objRole->role_code;
                            }
                        }
                    
                        /** Let find and check in CRM **/
                        /** FIND account -> contact **/
                        $contact = DB::table('accounts as a')
                                ->join('accounts_contacts as ac', 'a.id', '=', 'ac.account_id')
                                ->join('contacts as c', 'ac.contact_id', '=', 'c.id')
                                ->where('a.id', $account->id)
                                ->where('c.identity_no_nextgen', trim($obj->identification_no))
                                ->select('a.name as acc_name', 'a.id as acc_id', 'a.org_gov_code', 'a.org_gov_type')
                                ->addSelect('c.first_name as contact_name', 'c.id as contact_id', 'c.identity_no_nextgen', 'c.user_id_nextgen')
                                ->first();
                        var_dump('          Check Contact is exist: ' . json_encode($contact));
                        if($contact == null){
                            //create it
                            if($account != null){
                                if($obj->uo_record_status == '1'){
                                    $contactObj = CRMService::createContactGovernment($account, $obj, $roles_name);
                                    Log::info('          :: ->  success create ' . $contactObj->id.' RECORD_STATUS: '.$contactObj->record_status_nextgen);
                                    var_dump('          :: ->  success create ' . $contactObj->id.' RECORD_STATUS: '.$contactObj->record_status_nextgen);
                                }else{
                                    Log::info('          :: ->  No need create this user . This user is belong Organization InActive');
                                    var_dump('          :: ->  No need create this user . This user is belong Organization InActive');
                                }
                            }
                        }else{
                            //update it
                            $contactObj = CRMService::saveContactGovernment($obj, $roles_name, $contact);
                            CRMService::saveEmailContact($obj->uo_email, $contact->contact_id);

                        }
                    }
                }


            } else {
                Log::info('        :: ->  Checking... -> PTJ New Code Is Not exist in NextGen  , new_ptj_code: '.$ptjCode);  
            }
                    
            
            
        }
    }
    
    

    /**
     * Get List Data PTJ with return org_gov_code
     * 
     * 
     * @return type
     */
    protected static function getAccountsOrgGovPtjProfilesCRM() {
        
        $sql = DB::table('accounts');
        $sql->select('org_gov_code',DB::raw('COUNT(org_gov_code) as total'));
        $sql->where('account_type', 'GOVERNMENT');
        $sql->where('org_gov_type', 'PTJ');
        $sql->groupBy('org_gov_code');
        $sql->havingRaw('COUNT(org_gov_code) = 1');
        return $results = $sql->get();
    }


}
