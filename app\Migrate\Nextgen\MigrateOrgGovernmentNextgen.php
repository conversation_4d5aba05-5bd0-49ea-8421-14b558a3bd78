<?php

namespace App\Migrate\Nextgen;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Facades\DB;
use App\Models\Account;
use App\Models\Contact;
use App\Models\AccountCustom;
use App\Models\ContactCustom;
use App\Models\LogIntegration;
use App\Models\EmailAddress;
use App\Migrate\MigrateUtils;
use Config;
use App\Migrate\Nextgen\PMService;

/*
 * This integration to sync data Org Gov. Profile CRM with Org Gov. Profile Nextgen
 * In current CRM   :   Kementerian,                        Jabatan,        PTJ
 * In Nextgen       :   Kementerian,   Pegawai Pengawal,    Kumpulan PTJ,   PTJ 
 * In Nextgen, Code Org Profile is not same with current eP. we have to sync, get new Code Org Profile sync with existing in CRM
 * 
 * 
 */

class MigrateOrgGovernmentNextgen {

    public static $KEMENTERIAN = 'KEMENTERIAN';
    public static $PEGAWAI_PENGAWAL = 'PEGAWAI PENGAWAL';
    public static $KUMPULAN_PTJ = 'KUMPULAN PTJ';
    public static $PTJ = 'PTJ';
    public static $BPK = 'BPK';
    public static $USER_LOGGED_ID = '3';

    public static function runMigrate() {
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();

        self::migrateOrganizationGovernment();
        self::migrateListPTJOrganizationGovernment();

        var_dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    /**
     * Migrate data from eP to CRM. We check id data not existed in crm, Data will insert to CRM
     */
    public static function migrateOrganizationGovernment() {

        //Get Ministry Information from eP
        $results = self::getAccountsOrgGovProfilesCRM();
        Log::info(__FUNCTION__);
        foreach ($results as $row) {
            Log::info(' Find Ministry Code ' . $row->gov_code_c);

            $ministryLevel = 2;
            $objRes = PMService::getNextGenOrgGovProfile($ministryLevel, $row->gov_code_c);

            if ($objRes && $objRes->org_validity_id) {

                Log::info('   ->  Ministry Name ' . $objRes->org_name);
                Log::info('   ->  Ministry Code ' . $objRes->org_code);
                Log::info('   ->  Org Profile ID ' . $objRes->org_profile_id);
                $objNewMin = PMService::getNewMinistryCode($objRes->org_profile_id);
                if ($objNewMin && $objNewMin->org_validity_id) {
                    Log::info('   ->  Ministry New Code ' . $objNewMin->org_code);
                    self::saveAccountKementerian($row, $objNewMin);
                } else {
                    Log::info('   ->  Checking... -> Ministry New Code Is Not exist in NextGen  , org_profile_id: ' . $objRes->org_profile_id);
                }

                //var_dump('   ->  Ministry Name '.$objRes->org_name);
                self::createPegawaiPengawalProfile($row->id, $objRes->org_profile_id);

                Log::info('   ->  Find List Jabatan By  Ministry ID ' . $row->id);
                $resultsJabatan = self::getAccountsOrgGovProfilesCRM('JABATAN', $row->id);
                Log::info('   ->  Total List Jabatan By  Ministry ID ' . count($resultsJabatan));

                foreach ($resultsJabatan as $rowJab) {
                    Log::info('     Find Jabatan Code ' . $rowJab->gov_code_c);

                    $jabLevel = 4; //KUMPULAN PTJ (JABATAN)
                    $objOldJab = PMService::getNextGenOrgGovProfile($jabLevel, $rowJab->gov_code_c);
                    if ($objOldJab && $objOldJab->org_validity_id) {
                        Log::info('      :: ->  Jabatan Name ' . $objOldJab->org_name);
                        Log::info('      :: ->  Jabatan Code ' . $objOldJab->org_code);
                        Log::info('      :: ->  Org Profile ID ' . $objOldJab->org_profile_id);

                        $objNewJab = PMService::getNewJabatanCodeWithParent($objOldJab->org_profile_id);
                        if ($objNewJab && $objNewJab->org_validity_id && strlen($objNewJab->org_code) == 2) {
                            Log::info('      :: ->  Jabatan New Code ' . $objNewJab->org_code);
                            Log::info('      :: ->  (Parent) Pegawai Pengawal New Code ' . $objNewJab->parent_code);

                            $accountPegPengawal = Account::where('org_gov_code', $objNewJab->parent_code)
                                    ->where('org_gov_type', self::$PEGAWAI_PENGAWAL)
                                    ->first();
                            if ($accountPegPengawal && $accountPegPengawal->id != '') {
                                var_dump('Sync Jabatan');
                                self::saveAccountKumpulanPTJ($row, $rowJab, $objNewJab, $accountPegPengawal);
                            }

                            //self::syncPtjList($rowJab->id,$objNewJab->org_code,$objOldJab->org_code);
                        } else {
                            Log::info('      :: ->  Checking... -> Jabatan New Code Is Not exist in NextGen  , org_profile_id: ' . $objOldJab->org_profile_id);
                        }
                    }
                }
            }
        }
    }

    // App\Migrate\Nextgen\MigrateOrgGovernmentNextgen::migrateListJabatanOrganization();
    /**
     * To resync get current Jabatan migrate with Kumpulan PTJ
     */
    public static function migrateListJabatanOrganization() {
        //Get Ministry Information from eP
        $results = self::getAccountsOrgGovProfilesCRM();
        Log::info(__FUNCTION__);
        foreach ($results as $row) {

            $resultsJabatan = self::getAccountsOrgGovProfilesCRM('JABATAN', $row->id);

            foreach ($resultsJabatan as $rowJab) {
                var_dump('     Find Jabatan Code ' . $rowJab->gov_code_c);

                $jabLevel = 4; //KUMPULAN PTJ (JABATAN)
                $objOldJab = PMService::getNextGenOrgGovProfile($jabLevel, $rowJab->gov_code_c);
                if ($objOldJab && $objOldJab->org_validity_id) {
                    

                    $objNewJab = PMService::getNewJabatanCodeWithParent($objOldJab->org_profile_id);
                    if ($objNewJab && $objNewJab->org_validity_id && strlen($objNewJab->org_code) == 2) {
                        var_dump('   Ministry ID ' . $row->id . ' :: '.$row->name);
                        var_dump('    Check   :: ->  Jabatan Name ' . $objOldJab->org_name);
                        var_dump('    Check   :: ->  Jabatan Code ' . $objOldJab->org_code);
                        var_dump('    Check    :: ->  Org Profile ID ' . $objOldJab->org_profile_id);
                        var_dump('      :: ->  Jabatan New Code ' . $objNewJab->org_code);
                        var_dump('      :: ->  (Parent) Pegawai Pengawal : New Code ' . $objNewJab->parent_code);

                        $accountPegPengawal = Account::where('org_gov_code', $objNewJab->parent_code)
                                ->where('org_gov_type', self::$PEGAWAI_PENGAWAL)
                                ->first();
                        if ($accountPegPengawal && $accountPegPengawal->id != '') {
                            var_dump('      Detect Sync Update :: ->  Jabatan New Code ' . $objNewJab->org_code);
                            self::saveAccountKumpulanPTJ($row, $rowJab, $objNewJab, $accountPegPengawal);
                        }

                        //self::syncPtjList($rowJab->id,$objNewJab->org_code,$objOldJab->org_code);
                    } else {
                        var_dump('      :: ->  Checking... -> Jabatan New Code Is Not exist in NextGen  , org_profile_id: ' . $objOldJab->org_profile_id);
                    }
                }
            }
        }
    }

    public static function migrateListPTJOrganizationGovernment() {
        $sql = DB::table('accounts')
                ->join('accounts_cstm', 'accounts.id', '=', 'accounts_cstm.id_c');
        $sql->where('accounts.account_type', 'GOVERNMENT');
        $sql->where('accounts.org_gov_type', 'KUMPULAN PTJ');
        $sql->where('accounts.deleted', '0');
        $sql->select('accounts.*', 'accounts_cstm.*');
        $listAccKumpulanPtj = $sql->get();
        foreach ($listAccKumpulanPtj as $accObj) {
            self::syncPtjList($accObj->id, $accObj->org_gov_code, $accObj->gov_code_c);
        }
    }

    /**
     * 
     * @param type $accJabId  from CRM Account -> Jabatan -> id 
     * @param type $newJabCode from Nextgen Kumpulan PTJ Code
     * @param type $oldJabCode from CRM Account -> Jabatan -> code 
     */
    public static function syncPtjList($accJabId, $newJabCode, $oldJabCode) {
        Log::info('         Find List PTJ By  Jabatan ID ' . $accJabId);
        $resultsPTJ = self::getAccountsOrgGovProfilesCRM('PTJ', $accJabId);
        Log::info('         Total List PTJ By  Jabatan ID ' . count($resultsPTJ));
        var_dump('         Total List PTJ By  Jabatan ID (' . $accJabId . ') : ' . $newJabCode . ' = ' . count($resultsPTJ));
        foreach ($resultsPTJ as $rowPTJ) {

            if (strlen(trim($rowPTJ->gov_code_c)) == 6) {
                /* Combine with New Kump. PTJ code + Old PTJ Code ----> NEW PTJ CODE (8 char) */
                $newPtjCode = trim($newJabCode) . trim($rowPTJ->gov_code_c);
                Log::info('        Find New PTJ Code ' . $newPtjCode);
                Log::info('        --Old JabatanPTJ Code ' . $oldJabCode . $rowPTJ->gov_code_c);
                $objNewPTJ = PMService::getNewPTJCode($newPtjCode);
                if ($objNewPTJ && $objNewPTJ->org_validity_id) {
                    Log::info('        :: ->  PTJ New Code ' . $objNewPTJ->org_code);
                    Log::info('        :: ->  PTJ New ' . $objNewPTJ->org_name);
                    Log::info('        :: ->  PTJ Old ' . $rowPTJ->name);
                    self::saveAccountPTJ($rowPTJ->id, $objNewPTJ);
                } else {
                    Log::info('        :: ->  Checking... -> PTJ New Code Is Not exist in NextGen  , new_ptj_code: ' . $newPtjCode);
                }
            } else {
                Log::info('        :: ->  Check PTJ Code In Account Is Not Valid : ' . $rowPTJ->gov_code_c);
            }
        }
    }

    public static function createPegawaiPengawalProfile($accountMinistryId, $ministryOrgProfileId) {
        //List of Pegawai Pengawal
        $orgProfileList = DB::connection('oracle_nextgen')
                ->table('PM_ORG_PROFILE as OP')
                ->join('PM_ORG_VALIDITY as OV', 'OP.ORG_PROFILE_ID', '=', 'OV.ORG_PROFILE_ID')
                ->where('OP.PARENT_ORG_PROFILE_ID', $ministryOrgProfileId)
                ->where('OP.ORG_TYPE_ID', '3')
                ->select('OV.ORG_VALIDITY_ID', 'OV.ORG_CODE', 'OV.ORG_NAME', 'OV.EFF_DATE', 'OV.EXP_DATE', 'OV.RECORD_STATUS')
                ->addSelect('OP.ORG_PROFILE_ID', 'OP.ORG_TYPE_ID', 'OP.PARENT_ORG_PROFILE_ID', 'OP.GROUP_ORG_TYPE', 'OP.IS_EP_PTJ')
                ->get();

        if (count($orgProfileList) > 0) {
            foreach ($orgProfileList as $obj) {

                $org = PMService::getListNextgenOrgGov($obj->org_profile_id)->first(); //Result should return one. Just make return one
                $accountCheck = Account::where('org_gov_code', $org->org_code)
                        ->where('org_gov_type', self::$PEGAWAI_PENGAWAL)
                        ->count();
                if ($accountCheck == 0) {
                    var_dump('   ->  Create Pegawai Pengawal: ' . $org->org_code);
                    self::saveAccount($org, self::$PEGAWAI_PENGAWAL, $accountMinistryId);
                }
            }
        }
    }

    /**
     * Save Account for Government Information :: KEMENTERIAN
     * 
     * @param  $accMinistryObj -> Account Object (Ministry)
     * @param  $accJabObj ->  Account Object (Jabatan)
     * @param  $objNewJab -> Org_Validity Object (Kumpulan PTJ)
     * @param  $accPegPengawalObj -> Account Object (Pegawai Pengawal)
     * 
     */
    protected static function saveAccountKementerian($accMinistryObj, $objNewMinistry) {
        $acc = Account::find($accMinistryObj->id);
        $acc->org_gov_code = $objNewMinistry->org_code;
        $acc->org_gov_type = self::$KEMENTERIAN;
        $acc->record_status = $objNewMinistry->record_status; //PMService::$RECORD_STATUS[$objNewMinistry->record_status];

        if ($objNewMinistry->eff_date != null) {
            $effDate = new Carbon($objNewMinistry->eff_date);
            //$effDate->subHour(8);
            $acc->effective_from = $effDate->format('Y-m-d');
        }

        if ($objNewMinistry->exp_date != null) {
            $expDate = new Carbon($objNewMinistry->exp_date);
            //$expDate->subHour(8);
            $acc->effective_to = $expDate->format('Y-m-d');
        }

        $acc->date_modified = Carbon::now()->subHour(8);
        $acc->modified_user_id = self::$USER_LOGGED_ID;
        $acc->save();

        Log::info('Successfully save ' . self::$KEMENTERIAN . ' : ' . $acc->name . ' (' . $acc->org_gov_code . ')');
        self::saveAccountAddress($objNewMinistry->org_profile_id, $accMinistryObj->id);

        self::addLogIntegration($objNewMinistry, self::$KEMENTERIAN, 'Parent ID : ' . $acc->parent_id);
    }

    /**
     * Save Account for Government Information :: KumpulanPTJ
     * 
     * @param  $accMinistryObj -> Account Object (Ministry)
     * @param  $accJabObj ->  Account Object (Jabatan)
     * @param  $objNewJab -> Org_Validity Object (Kumpulan PTJ)
     * @param  $accPegPengawalObj -> Account Object (Pegawai Pengawal)
     * 
     */
    protected static function saveAccountKumpulanPTJ($accMinistryObj, $accJabObj, $objNewJab, $accPegPengawalObj) {
        $accJab = Account::find($accJabObj->id);
        $accJab->org_gov_code = $objNewJab->org_code;
        $accJab->org_gov_type = self::$KUMPULAN_PTJ;
        $accJab->parent_old_id = $accMinistryObj->id;
        $accJab->parent_id = $accPegPengawalObj->id;
        $accJab->record_status = $objNewJab->record_status; //PMService::$RECORD_STATUS[$objNewJab->record_status];

        if ($objNewJab->eff_date != null) {
            $effDate = new Carbon($objNewJab->eff_date);
            //$effDate->subHour(8);
            $accJab->effective_from = $effDate->format('Y-m-d');
        }

        if ($objNewJab->exp_date != null) {
            $expDate = new Carbon($objNewJab->exp_date);
            //$expDate->subHour(8);
            $accJab->effective_to = $expDate->format('Y-m-d');
        }

        $accJab->date_modified = Carbon::now()->subHour(8);
        $accJab->modified_user_id = self::$USER_LOGGED_ID;
        $accJab->save();

        Log::info('Successfully save ' . self::$KUMPULAN_PTJ . ' : ' . $accJab->name . ' (' . $accJab->org_gov_code . ')');
        self::saveAccountAddress($objNewJab->org_profile_id, $accJabObj->id);

        self::addLogIntegration($objNewJab, self::$KUMPULAN_PTJ, 'Parent ID : ' . $accJab->parent_id);
    }

    protected static function saveAccountPTJ($accPtjId, $objNewJab) {
        $accJab = Account::find($accPtjId);
        $accJab->org_gov_code = $objNewJab->org_code;
        //$accJab->org_gov_type = self::$PTJ;
        //$accJab->org_gov_type = PMService::getOrgProfileType($objNewJab->org_type_id);
        $accJab->org_gov_type = $objNewJab->org_type_id;
        $accJab->record_status = $objNewJab->record_status; //PMService::$RECORD_STATUS[$objNewJab->record_status];

        $effDate = new Carbon($objNewJab->eff_date);
        //$effDate->subHour(8);
        $accJab->effective_from = $effDate->format('Y-m-d');

        $expDate = new Carbon($objNewJab->exp_date);
        //$expDate->subHour(8);
        $accJab->effective_to = $expDate->format('Y-m-d');

        $accJab->date_modified = Carbon::now()->subHour(8);
        $accJab->modified_user_id = self::$USER_LOGGED_ID;
        $accJab->save();

        Log::info('Successfully save ' . self::$PTJ . ' : ' . $accJab->name . ' (' . $accJab->org_gov_code . ')');
        var_dump('      Successfully save ' . self::$PTJ . ' : ' . $accJab->name . ' (' . $accJab->org_gov_code . ')');
        self::saveAccountAddress($objNewJab->org_profile_id, $accPtjId);

        self::addLogIntegration($objNewJab, self::$PTJ, 'Parent ID : ' . $accJab->parent_id);
    }

    public static function saveAccountAddress($orgProfileId, $accountId) {
        $account = Account::find($accountId);
        $checkChanged = false;
        $listAddresses = PMService::getAddressDetails($orgProfileId);
        //$billAddr = $listAddresses->where('address_type','B')->first();
        $billAddr = PMService::getAddressPriority($listAddresses, 'B');
        if ($billAddr && $billAddr->address_type_id != '') {
            $checkChanged = true;
            $account->billing_address_street = PMService::add_address_streets($billAddr->address_name, $billAddr->address_1, $billAddr->address_2, $billAddr->address_3);
            $account->billing_address_city = PMService::getCityName($billAddr->city_id);
            $account->billing_address_district = PMService::getDistrictName($billAddr->district_id);
            $account->billing_address_division = PMService::getDivisionName($billAddr->division_id);
            $account->billing_address_state = PMService::getStateName($billAddr->state_id);
            $account->billing_address_postalcode = $billAddr->postcode;
            if ($billAddr->country_id == '131') {
                $account->billing_address_country = 'MALAYSIA';
            } else {
                $account->billing_address_country = PMService::getCountryName($billAddr->country_id);
            }
            if ($billAddr->phone_country != '' && $billAddr->phone_area != '' && $billAddr->phone_no != '') {
                $account->phone_office = $billAddr->phone_country . $billAddr->phone_area . $billAddr->phone_no;
            }
            if ($billAddr->fax_country != '' && $billAddr->fax_area != '' && $billAddr->fax_no != '') {
                $account->phone_fax = $billAddr->fax_country . $billAddr->fax_area . $billAddr->fax_no;
            }
        }

        //$deliverAddr = $listAddresses->where('address_type','D')->first();
        $deliverAddr = PMService::getAddressPriority($listAddresses, 'D');
        if ($deliverAddr && $deliverAddr->address_type_id != '') {
            $checkChanged = true;
            $account->shipping_address_street = PMService::add_address_streets($deliverAddr->address_name, $deliverAddr->address_1, $deliverAddr->address_2, $deliverAddr->address_3);
            $account->shipping_address_city = PMService::getCityName($deliverAddr->city_id);
            $account->shipping_address_district = PMService::getDistrictName($deliverAddr->district_id);
            $account->shipping_address_division = PMService::getDivisionName($deliverAddr->division_id);
            $account->shipping_address_state = PMService::getStateName($deliverAddr->state_id);
            $account->shipping_address_postalcode = $deliverAddr->postcode;
            if ($deliverAddr->country_id == '131') {
                $account->shipping_address_country = 'MALAYSIA';
            } else {
                $account->shipping_address_country = PMService::getCountryName($deliverAddr->country_id);
            }
        }
        if ($checkChanged == true) {
            $account->date_modified = Carbon::now()->subHour(8);
            $account->modified_user_id = self::$USER_LOGGED_ID;
            $account->save();
            Log::info('     ->> Successfully save address by org_profile_id' . $orgProfileId . ' : accountID ' . $accountId);
        }
    }

    /**
     * Create Account for Government Information 
     * 
     * @param type $rowData
     * @param type $type
     * @param type $parentId
     * @return type
     */
    public static function saveAccount($rowData, $type, $parentId = null) {

        $account = new Account;
        $account->id = Uuid::uuid4()->toString();
        $account->name = trim(strtoupper($rowData->org_name));
        $account->org_gov_code = $rowData->org_code;
        $account->org_gov_type = $type;
        $account->record_status = $rowData->record_status; //PMService::$RECORD_STATUS[$rowData->record_status];

        $effDate = new Carbon($rowData->eff_date);
        //$effDate->subHour(8);
        $account->effective_from = $effDate->format('Y-m-d');

        $expDate = new Carbon($rowData->exp_date);
        //$expDate->subHour(8);
        $account->effective_to = $expDate->format('Y-m-d');

        $account->date_entered = Carbon::now()->subHour(8);
        $account->date_modified = Carbon::now()->subHour(8);
        $account->created_by = self::$USER_LOGGED_ID;
        $account->modified_user_id = self::$USER_LOGGED_ID;
        $account->deleted = 0;
        $account->account_type = 'GOVERNMENT';
        if ($parentId) {
            $account->parent_id = $parentId;
            $account->parent_old_id = $parentId;
        }
        $account->save();

        $accountCustom = new AccountCustom;
        $accountCustom->id_c = $account->id;
        $accountCustom->gov_level_c = $type;
        if ($type == 'PEGAWAI PENGAWAL') {
            $accountCustom->gov_code_c = $rowData->org_code;
        }
        $accountCustom->save();

        Log::info('Successfully save ' . $type . ' : ' . $account->name . ' (' . $account->id . ')');
        self::saveAccountAddress($rowData->org_profile_id, $account->id);

        self::addLogIntegration($rowData, $type, 'Parent ID : ' . $parentId);

        return $account->id;
    }

    /**
     * Get List Data (Ministry or Jabatan or PTJ) 
     * Data PTJ, will added field of address. 
     * 
     * @param type $typeQuery
     * @param type $orgId
     * @return type
     */
    protected static function getAccountsOrgGovProfilesCRM($typeQuery = null, $orgId = null) {

        $sql = DB::table('accounts')
                ->join('accounts_cstm', 'accounts.id', '=', 'accounts_cstm.id_c');

        $sql->where('accounts.account_type', 'GOVERNMENT');

        if ($orgId != null) {
            $sql->where('accounts.parent_id', $orgId);
        }
        if ($typeQuery == null) {
            $sql->where('accounts_cstm.gov_level_c', 'KEMENTERIAN');
        }
        if ($typeQuery == 'JABATAN') {
            $sql->where('accounts_cstm.gov_level_c', 'JABATAN');
        }
        if ($typeQuery == 'PTJ') {
            $sql->where('accounts_cstm.gov_level_c', 'PTJ');
        }
        $sql->select('accounts.*', 'accounts_cstm.*');
        return $results = $sql->get();
    }

    protected static function add_address_streets($address_name, $street_field1, $street_field2, $street_field3) {
        $street_field = "";

        if (isset($address_name)) {
            $street_field .= trim(strtoupper($address_name));
        }
        if (isset($street_field1)) {
            $street_field .= "\n" . trim(strtoupper($street_field1));
        }
        if (isset($street_field2)) {
            $street_field .= "\n" . trim(strtoupper($street_field2));
        }
        if (isset($street_field3)) {
            $street_field .= "\n" . trim(strtoupper($street_field3));
        }
        return trim($street_field, "\n");
    }

    /**
     * Add log integration for Ep Government
     * @param type $rowData
     * @param type $type
     */
    protected static function addLogIntegration($rowData, $type, $remarks = '') {
        $logIntegration = new LogIntegration;
        $logIntegration->id = Uuid::uuid4()->toString();
        $logIntegration->name = 'NEXTGEN GOVERNMENT INTEGRATION';
        $logIntegration->service = 'FIRST MAPPING INTO NEXTGEN';
        $logIntegration->interface_service = $type;
        $logIntegration->status = 'COMPLETED';
        $logIntegration->remarks = $remarks;
        $logIntegration->description = 'Data received and completed insert or update into CRM';
        $logIntegration->date_entered = Carbon::now()->subHour(8);
        $logIntegration->date_modified = Carbon::now()->subHour(8);
        $logIntegration->created_by = self::$USER_LOGGED_ID;
        $logIntegration->modified_user_id = self::$USER_LOGGED_ID;
        $logIntegration->deleted = 0;
        $logIntegration->data = json_encode($rowData);

        $logIntegration->save();
    }

}
