<?php

namespace App\Migrate\Nextgen;

use Carbon\Carbon;
use Carbon\CarbonInterval;
use Illuminate\Support\Facades\Log;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Facades\DB;
use App\Models\Account;
use App\Models\Contact;
use App\Models\AccountCustom;
use App\Models\ContactCustom;
use App\Models\LogIntegration;
use App\Models\EmailAddress;
use App\Migrate\MigrateUtils;
use Config;
use App\Migrate\Nextgen\PMService;
use App\Migrate\Nextgen\SMService;
use App\Migrate\Nextgen\CRMService;

/*
 * This integration to sync data Org Gov. Profile CRM with Org Gov. Profile Nextgen
 * In current CRM   :   Kementerian,                        Jabatan,        PTJ
 * In Nextgen       :   Kementerian,   Pegawai Pengawal,    Kumpulan PTJ,   PTJ 
 * In Nextgen, Code Org Profile is not same with current eP. we have to sync, get new Code Org Profile sync with existing in CRM
 * 
 * 
 */

class MigrateSupplierNextgen {

    public static $USER_LOGGED_ID = '3';
    public static $QUERY_SKIP = 5000;
    public static $QUERY_TAKE = 5000;

    public static function runMigrate() {
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();

        self::updateSupplierInfo();
        
        var_dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
  
        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    /**
     * Just one time run at 13/08/2023
     */
    public static function runMigrateMofMoreThanOneGenerated() {
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();

        self::updateSupplierMofMoreThanOneInfo();
        
        var_dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
  
        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    protected static function updateSupplierMofMoreThanOneInfo() {
        Log::info(__METHOD__.' entering.. ');
        $results = SMService::getSMSuppliersMofMoreThanOneGenerated();
        Log::info(__METHOD__.' total result supplier found.. '. count($results));
        foreach($results as $row){
            $resultUsers = SMService::getSMSupplierUsersActiveByLoginID($row->login_id);
            $obj = $resultUsers->where('mof_record_status',1)->first();

            if($obj != null){
                Log::info(' Check LoginID :- ' . $obj->login_id);
                Log::info(' Check Company Name :- ' . $obj->company_name);
                Log::info('      eP No. :- ' . $obj->ep_no);
                $account = CRMService::getSupplierCrm($obj);
            
                if ($account != null){
                    $supplierObj = SMService::getSMSuppliersDetail($obj->supplier_id);
                    if($account->mof_no != $supplierObj->mof_no){
                        Log::info('checking crm mof no : '.$account->mof_no .' not same with in eP Mof No '.$supplierObj->mof_no );
                        Log::info(json_encode($supplierObj));
                        $account = CRMService::saveAccountSupplierPrimary($supplierObj,$account->id);
                        CRMService::saveAccountSupplierAddress($supplierObj, $account->id);
                    }
                    //if($supplierObj->)
                }
                if ($account == null ) {
                    //Create Account
                    Log::info('     CREATE ACCOUNT :- ' . $obj->ep_no);
                    /* Need get Details */
                    $supplierObj = SMService::getSMSuppliersDetail($obj->supplier_id);
                    Log::info(json_encode($supplierObj));
                    $account = CRMService::saveAccountSupplierPrimary($supplierObj);
                    CRMService::saveAccountSupplierAddress($supplierObj, $account->id);
                }
                /** FIND account -> contact * */
                $query = DB::table('accounts as a')
                        ->join('accounts_contacts as ac', 'a.id', '=', 'ac.account_id')
                        ->join('contacts as c', 'ac.contact_id', '=', 'c.id')
                        ->where('ac.account_id', $account->id)
                        ->where('c.identity_no_nextgen', trim($obj->identification_no))
                        ->select('a.name as acc_name', 'a.id as acc_id')
                        ->addSelect('c.first_name as contact_name', 'c.id as contact_id', 'c.identity_no_nextgen', 'c.user_id_nextgen');
                //var_dump($query->toSql());
                $contact = $query->first();
                if ($contact == null) {

                    if ($obj->p_record_status == '1') {
                        $contactObj = CRMService::createContactSupplier($account, $obj);
                        Log::info('        :: ->  success create ' . $contactObj->id . ' RECORD_STATUS: ' . $contactObj->record_status_nextgen);
                    } else {
                        Log::info('        :: ->  No need create this user . This user is InActive and not in record CRM');
                    }
                } else {
                    //update it
                    $contactObj = CRMService::saveContactSupplier($obj, $contact);
                    CRMService::saveEmailContact($obj->p_email, $contact->contact_id);
                    Log::info('        :: ->  success update ' . $contactObj->id . ' RECORD_STATUS: ' . $contactObj->record_status_nextgen);
                }
            }
            
        }
    }    

    /**
     * DELAY + TIME INTERVAL
     * @param type $dateStart
     * @param type $dateEnd
     */
    protected static function updateSupplierInfo() {

        Log::info(self::class . ' Start ' . __FUNCTION__);



        $start = 0;
        $skip = self::$QUERY_SKIP;
        $take = self::$QUERY_TAKE;
        $count = 0;
        $total = 0;
        do {
            $nextSkip = $start++ * $skip;
            $results = SMService::getSMSuppliersActiveAll($take, $nextSkip);

            Log::info(' Count Total Query :- ' . count($results));
            var_dump(' Count Total Query :- ' . count($results));
            $total = $total+count($results);
            foreach ($results as $obj) {
                
                //print_r($obj);
                
                //If branch_code is null -> mean refer to HQ supplier
                if($obj->branch_code == null){
                    $counter = $count++;
                    var_dump(' '.$counter.'). ePNo :- ' . $obj->ep_no);
                    Log::info(' '.$counter.'). ePNo :- ' . $obj->ep_no);
                    var_dump('     Company :- ' . $obj->company_name);  
                    var_dump('     Mof No :- ' . $obj->mof_no);  
                    var_dump('     Reg. No :- ' . $obj->reg_no); 
          
                    $account = CRMService::getSupplierCrm($obj);

                    if($account == null){
                        //Create Account
                        var_dump('     CREATE ACCOUNT :- ' . $obj->ep_no); 
                        $accountNew = CRMService::saveAccountSupplierPrimary($obj);
                        CRMService::saveAccountSupplierAddress($obj,$accountNew->id);
                    }else{
                        //Update Account
                        var_dump('     UPDATE ACCOUNT :- ' . $obj->ep_no); 
                        CRMService::saveAccountSupplierPrimary($obj, $account->id);
                        CRMService::saveAccountSupplierAddress($obj,$account->id);
                    }
                    
                }
                /*
                [supplier_id] => 11936
                [company_name] => NFAS OMEGA
                [ep_no] => eP-1008F1416
                [reg_no] => *********-T
                [business_type] => F
                [s_status_id] =>
                [s_record_status] => 1
                [s_created_date] => 2007-11-14 09:08:49
                [s_changed_date] => 2017-05-15 14:00:02
                [a_record_status] => 1
                [appl_id] => 1714594
                [supplier_type] => K
                [appl_type] => R
                [appl_no] => KR-********-0001
                [a_status_id] => 20701
                [a_created_date] => 2017-05-15 12:27:01
                [a_changed_date] => 2017-05-16 12:29:13
                [cb_company_name] => NFAS OMEGA
                [cb_rev_no] => 1
                [phone_no] => ********
                [phone_country] => 6
                [phone_area] => 019
                [fax_no] =>
                [fax_country] => 6
                [fax_area] =>
                [website] =>
                [cb_record_status] => 1
                [company_basic_id] => 864525
                [cb_created_date] => 2017-05-15 12:27:01
                [cb_changed_date] => 2017-05-15 12:59:13
                [address_type] => C
                [ca_record_status] => 1
                [gst_reg_no] =>
                [gst_eff_date] =>
                [gst_end_date] =>
                [branch_code] =>
                [mof_no] => 357-02094129
                [ma_supplier_type] => K
                [mof_eff_date] => 2017-05-15 00:00:00
                [mof_exp_date] => 2020-05-14 00:00:00
                [mof_reg_status_id] => 2
                [sa_record_status] => 1
                [address_id] => 7202950
                [address_1] => NO.27, JALAN PUJ 8/7, TAMAN PUNCAK JALIL
                [address_2] => BANDAR PUTRA PERMAI,
                [address_3] =>
                [postcode] => 43300
                [country_id] => 131
                [state_id] => 11
                [division_id] =>
                [district_id] => 82
                [city_id] => 1606
                [sa_created_date] => 2017-05-15 12:27:01
                [sa_changed_date] => 2017-05-15 12:28:32

                 */
            }
        } while (count($results) > 0 && count($results) == $take);
        
        var_dump('     Total Results :- ' . $total); 
        Log::info('     Total Results :- ' . $total);
    }

}
