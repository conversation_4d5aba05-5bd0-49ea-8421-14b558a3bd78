<?php

namespace App\Migrate\Nextgen;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Migrate\MigrateUtils;
use App\Migrate\Nextgen\SMService;
use App\Migrate\Nextgen\CRMService;

/*
 * This integration to sync data Org Gov. Profile CRM with Org Gov. Profile Nextgen
 * In current CRM   :   Kementerian,                        Jabatan,        PTJ
 * In Nextgen       :   Kementerian,   Pegawai Pengawal,    Kumpulan PTJ,   PTJ 
 * In Nextgen, Code Org Profile is not same with current eP. we have to sync, get new Code Org Profile sync with existing in CRM
 * 
 * 
 */

class MigrateSupplierUsersNextgen {

    public static $QUERY_SKIP = 50;
    public static $QUERY_TAKE = 50;

    public static function runMigrate() {
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();

      
            self::updateSupplierUsersInfo();

  
        var_dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    /**
     * DELAY + TIME INTERVAL
     * @param type $dateStart
     * @param type $dateEnd
     */
    protected static function updateSupplierUsersInfo() {

        Log::info(self::class . ' Start ' . __FUNCTION__ );



        $start = 0;
        $skip = self::$QUERY_SKIP;
        $take = self::$QUERY_TAKE;
        $counterUpdateUser = 0;
        $sumTotal = 0;
        do {
            $nextSkip = $start++ * $skip;
            $results = SMService::getSMSupplierUsersActiveAll( $take, $nextSkip);
            
            $sumTotal = $sumTotal + count($results);
            Log::info(' Sum Total Query  :- '.$sumTotal);
            var_dump(' Sum Total Query :- '.$sumTotal);
            
            if (count($results) > 0) {
                foreach ($results as $obj) {
                    
                    var_dump(' Check Company Name :- '.$obj->company_name);
                    var_dump('      eP No. :- '.$obj->ep_no);
                    Log::info(' Check Company Name :- '.$obj->company_name);
                    Log::info('      eP No. :- '.$obj->ep_no);
                    $account = CRMService::getSupplierCrm($obj);
                    
                    /** Create Account if not exist in CRM **/
                    if($account == null){
                        //Create Account
                        var_dump('     CREATE ACCOUNT :- SupplierId : '.$obj->supplier_id.' , epNo : ' . $obj->ep_no); 
                        Log::info('     CREATE ACCOUNT :- SupplierId : '.$obj->supplier_id.' , epNo : ' . $obj->ep_no);  
                        /* Need get Details */
                        $supplierObj = SMService::getSMSuppliersDetail($obj->supplier_id);
                        Log::info(json_encode($supplierObj));
                        if($supplierObj != null){
                            $account = CRMService::saveAccountSupplierPrimary($supplierObj);
                            CRMService::saveAccountSupplierAddress($supplierObj,$account->id);
                        }else{
                            var_dump('     COULD NOT CREATE THIS SUPPLIER INTO CRM :- SupplierId : '.$obj->supplier_id.' , epNo : ' . $obj->ep_no); 
                            Log::info('     COULD NOT CREATE THIS SUPPLIER INTO CRM :- SupplierId : '.$obj->supplier_id.' , epNo : ' . $obj->ep_no); 
                        }
                    }
                    
                    /** Update users in contact CRM **/
                    if($account != null){
                        /** FIND account -> contact **/
                        $query = DB::table('accounts as a')
                                ->join('accounts_contacts as ac', 'a.id', '=', 'ac.account_id')
                                ->join('contacts as c', 'ac.contact_id', '=', 'c.id')
                                ->where('ac.account_id', $account->id)
                                ->where('c.identity_no_nextgen', trim($obj->identification_no))
                                ->select('a.name as acc_name', 'a.id as acc_id')
                                ->addSelect('c.first_name as contact_name', 'c.id as contact_id', 'c.identity_no_nextgen', 'c.user_id_nextgen');
                        //var_dump($query->toSql());
                        $contact = $query->first();
                        var_dump('      Check Contact is exist: ' . json_encode($contact));
                        if($contact == null){
                            var_dump('      start create Contact: ' . json_encode($obj));
                            if($obj->p_record_status == '1'){
                                $contactObj = CRMService::createContactSupplier($account, $obj);
                                $counterUpdateUser++;
                                Log::info('        :: ->  success create ' . $contactObj->id.' RECORD_STATUS: '.$contactObj->record_status_nextgen);
                                var_dump('        :: ->  success create ' . $contactObj->id.' RECORD_STATUS: '.$contactObj->record_status_nextgen);
                            }else{
                                Log::info('        :: ->  No need create this user . This user is InActive and not in record CRM');
                            }

                        }else{
                            //update it
                            $contactObj = CRMService::saveContactSupplier($obj, $contact);
                            CRMService::saveEmailContact($obj->p_email, $contact->contact_id);
                            $counterUpdateUser++;
                            var_dump('        :: ->  success update ' . $contactObj->id.' RECORD_STATUS: '.$contactObj->record_status_nextgen);

                        }
                    }
                    
                }
            }
        } while (count($results) > 0 && count($results) == $take);
        
        Log::info('COMPLETED Sum Total Query  :- '.$sumTotal);
        var_dump('COMPLETED Sum Total Query :- '.$sumTotal);
        
        Log::info('COMPLETED Total User Insert / Updated :- '.$counterUpdateUser);
        var_dump('COMPLETED Total User Insert / Updated :- '.$counterUpdateUser);
    }

    
    
    

    

}
