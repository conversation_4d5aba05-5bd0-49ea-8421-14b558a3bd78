First Time Migration in Nextgen:-

1)After DEPLOYMENT CODE into Production.
    Need to repair code using Admin login
    Please take note: 
        Accounts (Added new fields)
        Contacts (Added new fields)

2)First Deployment & Mapping
    a)Check Query Gov. Accounts in CRM
        1)Check PTJ (Total record should be 0). To make sure no duplicate data in Active Record
            SELECT b.jabatan_ptj_code_c, COUNT(b.jabatan_ptj_code_c) AS total FROM accounts a , accounts_cstm b WHERE a.id = b.`id_c` AND  
            b.`gov_level_c` = 'PTJ' AND a.deleted = 0 AND a.`parent_id` != '' 
            GROUP BY b.jabatan_ptj_code_c 
            HAVING total > 1 ;
            
        2)Check JABATAN (Total record should be 0). To make sure no duplicate data in Active Record 
            SELECT DISTINCT b.gov_code_c, b.gov_code_c,COUNT(b.gov_code_c) AS total FROM accounts a, accounts_cstm b
            WHERE a.id=b.id_c AND a.deleted = 0 AND b.gov_level_c = 'JABATAN' 
            GROUP BY b.gov_code_c HAVING total > 1  ORDER BY total DESC;
        
        3)Check MINISTRY (Total record should be 0). To make sure no duplicate data in Active Record             
            SELECT gov_code_c, COUNT(gov_code_c) AS total FROM accounts a, accounts_cstm b WHERE  a.id = b.id_c AND gov_level_c = 'KEMENTERIAN' AND a.`deleted` = 0   
            GROUP BY gov_code_c HAVING total > 1  ORDER BY total DESC;
    
    ### RUN SCRIPT to add indexing each table related
      ALTER TABLE `cdccrm`.`accounts`   
        ADD  INDEX `idx_account_type_mof_no` (`account_type`, `mof_no`),
        ADD  INDEX `idx_account_type_registration_no` (`account_type`, `registration_no`),
        ADD  INDEX `idx_account_type_ep_no` (`account_type`, `ep_no`);


      ALTER TABLE `cdccrm`.`accounts`   
        ADD  INDEX `idx_mof_no` (`mof_no`),
        ADD  INDEX `idx_ep_no` (`ep_no`),
        ADD  INDEX `idx_registration_no` (`registration_no`),
        ADD  INDEX `idx_org_gov_code_type` (`org_gov_code`, `org_gov_type`);


      ALTER TABLE `cdccrm`.`accounts_cstm`   
        ADD  INDEX `idx_mof_no_c` (`mof_no_c`),
        ADD  INDEX `idx_ssm_no_c` (`ssm_no_c`),
        ADD  INDEX `idx_jabatan_ptj_code_c` (`jabatan_ptj_code_c`);


      ALTER TABLE `cdccrm`.`contacts`   
        ADD  INDEX `idx_login_id_nextgen` (`login_id_nextgen`);

      ALTER TABLE `cdccrm`.`contacts`   
        ADD  INDEX `idx_identity_no_nextgen` (`identity_no_nextgen`),
        ADD  INDEX `idx_user_id_nextgen` (`user_id_nextgen`);

      ALTER TABLE `cdccrm`.`contacts_cstm`   
        ADD  INDEX `idx_username_password` (`password_c`, `username_c`),
        ADD  INDEX `idx_icno_c` (`ic_no_c`);




    b)Map Government Profile & Users
        php artisan map-nextgen-gov
            This code : will integrate and mapping data from accounts table from kementerian > jabatan > ptj  to kementerian > pegawai pengawal > kumpulan ptj > ptj .
            Integrate with Nextgen , fields added in accounts table : org_gov_code,org_gov_type,record_status,effective_from,effective_to
            field related in account_cstm still remain. 
            Gov Code Change :
            Kumpulan PTJ/Jabatan  (OLD : 4 digit) (NEW : 2 DIGIT)
            PTJ  (OLD : 6 digit) (NEW : 8 DIGIT [Combine New Kumpulan PTJ + New PTJ Code)
            (Test Time Completed : 22 Minutes )

        php artisan map-nextgen-gov-users
            This code will get all PTJ records active in table accounts. From PTJ Code, will sync in Nextgen to get all users then will sync to contacts in CRM.
            If User Inactive in Nextgen , but not found in CRM, this user will ignore.
            If User in Nextgen found in CRM, will update status record and all information.
            New fields added in contacts table (account_id,record_status_nextgen,designation_nextgen,login_id_nextgen,user_id_nextgen,identity_no_nextgen,identity_type_nextgen,user_id_nextgen,role_nextgen
            login_id_nextgen -> We will get this login ID if this user is Active in PM_USER_ORG ( In Nextgen, one user login will active One Org Profile Only )
            (Test Time Completed : 34 Minutes)


        php artisan map-nextgen-gov-others
            TO add organization profile : factoring company
            (Test Time Completed : 1 Minutes)


    b)Map Supplier Profile & Users    
        php artisan map-nextgen-supplier
            Sync supplier data in accounts table with Nextgen. 
            New fields added in accounts table (gst_no,gst_effective_date,ep_no,registration_no,billing_address_district,shipping_address_district,billing_address_division,shipping_address_division,mof_no,supplier_type,appl_no,appl_type,appl_status)
            Field related in account_cstm table still remain.
            ep_no -> this will be unique identity in CRM. Make sure ep_no must not duplicate in CRM. 
            supplier_type -> MOF No , Basic Online, Gov Seller
            (Test Time Completed : 254 Minutes)

        php artisan map-nextgen-supplier-users
            Sync for users in suppliers.

3)Batch Scheduler
    a)Update Government Information
    b)Update Government Users Information
    c)Update Supplier Information
    d)Update Supplier Users Information
            


    
        
        