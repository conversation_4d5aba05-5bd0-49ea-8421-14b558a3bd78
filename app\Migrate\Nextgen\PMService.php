<?php

namespace App\Migrate\Nextgen;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Facades\DB;
use App\Models\Account;
use App\Models\Contact;
use App\Models\AccountCustom;
use App\Models\ContactCustom;
use App\Models\LogIntegration;
use App\Models\EmailAddress;
use App\Migrate\MigrateUtils;
use Config;

/**
 * Description of Profile Management PMServiceTrait
 *
 * <AUTHOR>
 */
class PMService {
    
    public static $KEMENTERIAN = 'KEMENTERIAN';
    public static $PEGAWAI_PENGAWAL = 'PEGAWAI PENGAWAL';
    public static $KUMPULAN_PTJ = 'KUMPULAN PTJ';
    public static $PTJ = 'PTJ';
    public static $BPK = 'BPK';
    public static $USER_LOGGED_ID = '3';
    
    
    public static $ORG_GOVERNMENT_TYPE = array(
                        2 => 'KEMENTERIAN',
                        3 => 'PEGA<PERSON><PERSON> PENGAWAL',
                        4 => 'KUMPULAN PTJ',
                        5 => 'PTJ'
                        );
    
    public static $OLD_ORG_GOVERNMENT_TYPE = array(
                        2 => 'KEMENTERIAN',
                        3 => 'PEGAWAI PENGAWAL',
                        4 => 'JABATAN',
                        5 => 'PTJ'
                        );
    
    
    // As reference 
    public static $RECORD_STATUS = array(
                        0 => 'RECORD_STATUS_SUSPENDED',
                        1 => 'RECORD_STATUS_ACTIVE',
                        2 => 'RECORD_STATUS_CANCELLED',
                        3 => 'RECORD_STATUS_EXPIRED',
                        4 => 'RECORD_STATUS_REJECTED',
                        5 => 'RECORD_STATUS_IN_PROGRESS',
                        6 => 'RECORD_STATUS_PENDING_RE_APPROVAL',
                        7 => 'RECORD_STATUS_7', /** Not sure, Not in document **/
                        8 => 'RECORD_STATUS_PENDING_ACTIVATION',
                        9 => 'RECORD_STATUS_DELETED'
                        );
   
    /**
     *  Get details Profile Org for type [6,7,8,9,10,11,12,13,14,209] only
     * @param type $take
     * @param type $nextSkip
     * @return type
     */
    public static function getListPMOrgProfileDetails($take, $nextSkip) {
        $query = DB::connection('oracle_nextgen')->table('PM_ORG_VALIDITY as OV');
        $query->join('PM_ORG_PROFILE as OP', 'OV.ORG_PROFILE_ID', '=', 'OP.ORG_PROFILE_ID');
        //$query->join('PM_ADDRESS_TYPE as ADT', 'OP.ORG_PROFILE_ID', '=', 'ADT.ORG_PROFILE_ID');
        //$query->join('PM_ADDRESS as AD', 'ADT.ADDRESS_ID', '=', 'AD.ADDRESS_ID');
        //$query->where('OV.RECORD_STATUS', 1);
        //$query->where('OP.RECORD_STATUS', 1);
        $query->whereIn('OP.ORG_TYPE_ID', [6,8,9,10,11,12,13,14,209]);
        $query->select('OV.ORG_VALIDITY_ID', 'OV.ORG_CODE', 'OV.ORG_NAME', 'OV.EFF_DATE', 'OV.EXP_DATE', 'OV.RECORD_STATUS as OV_RECORD_STATUS', 'OV.CREATED_DATE as OV_CREATED_DATE', 'OV.CHANGED_DATE as OV_CHANGED_DATE');
        $query->addSelect('OP.RECORD_STATUS as OP_RECORD_STATUS','OP.ORG_PROFILE_ID', 'OP.ORG_TYPE_ID', 'OP.PARENT_ORG_PROFILE_ID','OP.FACTORING_ORG_ID', 'OP.GROUP_ORG_TYPE', 'OP.IS_EP_PTJ', 'OP.CREATED_DATE as OP_CREATED_DATE', 'OP.CHANGED_DATE as OP_CHANGED_DATE');
        //$query->addSelect('ADT.RECORD_STATUS as ADT_STATUS','ADT.ADDRESS_TYPE', 'ADT.CREATED_DATE as ADT_CREATED_DATE', 'ADT.CHANGED_DATE as ADT_CHANGED_DATE');
        //$query->addSelect('AD.RECORD_STATUS as AD_STATUS','AD.ADDRESS_ID', 'AD.ADDRESS_NAME', 'AD.ADDRESS_1', 'AD.ADDRESS_2', 'AD.ADDRESS_3', 'AD.POSTCODE', 'AD.COUNTRY_ID', 'AD.STATE_ID', 'AD.DIVISION_ID', 'AD.DISTRICT_ID', 'AD.CITY_ID', 'AD.PHONE_COUNTRY', 'AD.PHONE_AREA', 'AD.PHONE_NO', 'AD.FAX_COUNTRY', 'AD.FAX_AREA', 'AD.FAX_NO', 'AD.CREATED_DATE as AD_CREATED_DATE', 'AD.CHANGED_DATE as AD_CHANGED_DATE');
        $query->orderBy('OP.ORG_TYPE_ID');
        $query->skip($nextSkip)->take($take);
        $data = array(
            "sql" => $query->toSql(),
            "parameter" => $query->getBindings()
        );
        //Log::debug(self::class . ' :: ' . __FUNCTION__ . ' >> SQL   :   '. json_encode($data));
        return $query->get();
    }
    
    
    /**
     * Get Query for Update Government Info (KEMENTERIAN, PEGAWAI PENGAWAL &  KUMP. PTJ)
     * @param type $dateStart
     * @param type $dateEnd
     * @param type $take
     * @param type $nextSkip
     * @return type
     */
    public static function getListPMOrgProfileDetailsHighLevel() {
        
        $query = DB::connection('oracle_nextgen')->table('PM_ORG_VALIDITY as OV');
        $query->join('PM_ORG_PROFILE as OP', 'OV.ORG_PROFILE_ID', '=', 'OP.ORG_PROFILE_ID');
        $query->where('OV.RECORD_STATUS', 1); //must be 1 , to make sure get one record only. 
        $query->whereIn('OP.ORG_TYPE_ID', [2,3,4]);

        $query->select('OV.ORG_VALIDITY_ID', 'OV.ORG_CODE', 'OV.ORG_NAME', 'OV.EFF_DATE', 'OV.EXP_DATE', 'OV.RECORD_STATUS as OV_RECORD_STATUS', 'OV.CREATED_DATE as OV_CREATED_DATE', 'OV.CHANGED_DATE as OV_CHANGED_DATE');
        $query->addSelect('OP.RECORD_STATUS as OP_RECORD_STATUS','OP.ORG_PROFILE_ID', 'OP.ORG_TYPE_ID', 'OP.PARENT_ORG_PROFILE_ID','OP.FACTORING_ORG_ID', 'OP.GROUP_ORG_TYPE', 'OP.IS_EP_PTJ', 'OP.CREATED_DATE as OP_CREATED_DATE', 'OP.CHANGED_DATE as OP_CHANGED_DATE');
        $query->orderBy('OP.ORG_TYPE_ID');
        $data = array(
            "sql" => $query->toSql(),
            "parameter" => $query->getBindings()
        );
        //dd($data);
        //Log::debug(self::class . ' :: ' . __FUNCTION__ . ' >> SQL   :   '. json_encode($data));
        return $query->get();
    }
    
    /**
     * Get Query for Update Government Info
     * @param type $dateStart
     * @param type $dateEnd
     * @param type $take
     * @param type $nextSkip
     * @return type
     */
    public static function getListPMOrgProfileDetailsByDate($dateStart, $dateEnd, $take, $nextSkip) {
        /*
         * Sample Query test to Oracle Query
         SELECT "OV"."ORG_VALIDITY_ID",
            "OV"."ORG_CODE",
            "OV"."ORG_NAME",
            "OV"."EFF_DATE",
            "OV"."EXP_DATE",
            "OV"."RECORD_STATUS" AS "OV_RECORD_STATUS",
            "OV"."CREATED_DATE"  AS "OV_CREATED_DATE",
            "OV"."CHANGED_DATE"  AS "OV_CHANGED_DATE",
            "OP"."RECORD_STATUS" AS "OP_RECORD_STATUS",
            "OP"."ORG_PROFILE_ID",
            "OP"."ORG_TYPE_ID",
            "OP"."PARENT_ORG_PROFILE_ID",
            "OP"."GROUP_ORG_TYPE",
            "OP"."IS_EP_PTJ",
            "OP"."CREATED_DATE"   AS "OP_CREATED_DATE",
            "OP"."CHANGED_DATE"   AS "OP_CHANGED_DATE",
            "ADT"."RECORD_STATUS" AS "ADT_STATUS",
            "ADT"."ADDRESS_TYPE",
            "ADT"."CREATED_DATE" AS "ADT_CREATED_DATE",
            "ADT"."CHANGED_DATE" AS "ADT_CHANGED_DATE",
            "AD"."RECORD_STATUS" AS "AD_STATUS",
            "AD"."ADDRESS_ID",
            "AD"."ADDRESS_NAME",
            "AD"."ADDRESS_1",
            "AD"."ADDRESS_2",
            "AD"."ADDRESS_3",
            "AD"."POSTCODE",
            "AD"."COUNTRY_ID",
            "AD"."STATE_ID",
            "AD"."DIVISION_ID",
            "AD"."DISTRICT_ID",
            "AD"."CITY_ID",
            "AD"."PHONE_COUNTRY",
            "AD"."PHONE_AREA",
            "AD"."PHONE_NO",
            "AD"."FAX_COUNTRY",
            "AD"."FAX_AREA",
            "AD"."FAX_NO",
            "AD"."CREATED_DATE" AS "AD_CREATED_DATE",
            "AD"."CHANGED_DATE" AS "AD_CHANGED_DATE"
          FROM "PM_ORG_VALIDITY" OV
          INNER JOIN "PM_ORG_PROFILE" OP
          ON "OV"."ORG_PROFILE_ID" = "OP"."ORG_PROFILE_ID"
          INNER JOIN "PM_ADDRESS_TYPE" ADT
          ON "OP"."ORG_PROFILE_ID" = "ADT"."ORG_PROFILE_ID"
          INNER JOIN "PM_ADDRESS" AD
          ON "ADT"."ADDRESS_ID"      = "AD"."ADDRESS_ID"
          WHERE "OV"."RECORD_STATUS" = 1
          AND "OP"."RECORD_STATUS"   = 1
          AND "OP"."ORG_TYPE_ID"    IN (2, 3, 4, 5)
          AND (("OV"."CREATED_DATE" BETWEEN '27-MAR-17 08:15:00 PM' AND '27-MAR-17 08:19:59 PM')
          OR ("OV"."CHANGED_DATE" BETWEEN '27-MAR-17 08:15:00 PM' AND '27-MAR-17 08:19:59 PM')
          OR ("OP"."CREATED_DATE" BETWEEN '27-MAR-17 08:15:00 PM' AND '27-MAR-17 08:19:59 PM')
          OR ("OP"."CHANGED_DATE" BETWEEN '27-MAR-17 08:15:00 PM' AND '27-MAR-17 08:19:59 PM')
          OR ("ADT"."CREATED_DATE" BETWEEN '27-MAR-17 08:15:00 PM' AND '27-MAR-17 08:19:59 PM')
          OR ("ADT"."CHANGED_DATE" BETWEEN '27-MAR-17 08:15:00 PM' AND '27-MAR-17 08:19:59 PM')
          OR ("AD"."CREATED_DATE" BETWEEN '27-MAR-17 08:15:00 PM' AND '27-MAR-17 08:19:59 PM')
          OR ("AD"."CHANGED_DATE" BETWEEN '27-MAR-17 08:15:00 PM' AND '27-MAR-17 08:19:59 PM'))
          ORDER BY "OP"."ORG_TYPE_ID" ASC;
         */
        $query = DB::connection('oracle_nextgen')->table('PM_ORG_VALIDITY as OV');
        $query->join('PM_ORG_PROFILE as OP', 'OV.ORG_PROFILE_ID', '=', 'OP.ORG_PROFILE_ID');
        $query->join('PM_ADDRESS_TYPE as ADT', 'OP.ORG_PROFILE_ID', '=', 'ADT.ORG_PROFILE_ID');
        $query->join('PM_ADDRESS as AD', 'ADT.ADDRESS_ID', '=', 'AD.ADDRESS_ID');
        $query->where('OV.RECORD_STATUS', 1); //must be 1 , to make sure get one record only. 
        //$query->where('OP.RECORD_STATUS', 1);
        $query->whereIn('OP.ORG_TYPE_ID', [2,3,4,5,6,7,8,9,10,11,12,13,14,209]);
        $query->where(function ($query) use($dateStart, $dateEnd) {
            
            $dateFormat = 'YYYY-MM-DD HH24:MI:SS';
            
            $query->orWhereRaw("OV.CREATED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            $query->orWhereRaw("OV.CHANGED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            
            $query->orWhereRaw("OP.CREATED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            $query->orWhereRaw("OP.CHANGED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            
            $query->orWhereRaw("ADT.CREATED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            $query->orWhereRaw("ADT.CHANGED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            
            $query->orWhereRaw("AD.CREATED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            $query->orWhereRaw("AD.CHANGED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            
            /*
             * Can't use this query below, some data will not appear in oracle query
             
            $query->orWhere(function ($query) use($dateStart, $dateEnd) {
                $query->whereBetween('OV.CREATED_DATE', [$dateStart, $dateEnd]);
            });
            $query->orWhere(function ($query) use($dateStart, $dateEnd) {
                $query->whereBetween('OV.CHANGED_DATE', [$dateStart, $dateEnd]);
            });
            $query->orWhere(function ($query) use($dateStart, $dateEnd) {
                $query->whereBetween('OP.CREATED_DATE', [$dateStart, $dateEnd]);
            });
            $query->orWhere(function ($query) use($dateStart, $dateEnd) {
                $query->whereBetween('OP.CHANGED_DATE', [$dateStart, $dateEnd]);
            });
            $query->orWhere(function ($query) use($dateStart, $dateEnd) {
                $query->whereBetween('ADT.CREATED_DATE', [$dateStart, $dateEnd]);
            });
            $query->orWhere(function ($query) use($dateStart, $dateEnd) {
                $query->whereBetween('ADT.CHANGED_DATE', [$dateStart, $dateEnd]);
            });
            $query->orWhere(function ($query) use($dateStart, $dateEnd) {
                $query->whereBetween('AD.CREATED_DATE', [$dateStart, $dateEnd]);
            });
            $query->orWhere(function ($query) use($dateStart, $dateEnd) {
                $query->whereBetween('AD.CHANGED_DATE', [$dateStart, $dateEnd]);
            });
             * 
             */
        });


        $query->select('OV.ORG_VALIDITY_ID', 'OV.ORG_CODE', 'OV.ORG_NAME', 'OV.EFF_DATE', 'OV.EXP_DATE', 'OV.RECORD_STATUS as OV_RECORD_STATUS', 'OV.CREATED_DATE as OV_CREATED_DATE', 'OV.CHANGED_DATE as OV_CHANGED_DATE');
        $query->addSelect('OP.RECORD_STATUS as OP_RECORD_STATUS','OP.ORG_PROFILE_ID', 'OP.ORG_TYPE_ID', 'OP.PARENT_ORG_PROFILE_ID','OP.FACTORING_ORG_ID', 'OP.GROUP_ORG_TYPE', 'OP.IS_EP_PTJ', 'OP.CREATED_DATE as OP_CREATED_DATE', 'OP.CHANGED_DATE as OP_CHANGED_DATE');
        $query->addSelect('ADT.RECORD_STATUS as ADT_STATUS','ADT.ADDRESS_TYPE', 'ADT.CREATED_DATE as ADT_CREATED_DATE', 'ADT.CHANGED_DATE as ADT_CHANGED_DATE');
        $query->addSelect('AD.RECORD_STATUS as AD_STATUS','AD.ADDRESS_ID', 'AD.ADDRESS_NAME', 'AD.ADDRESS_1', 'AD.ADDRESS_2', 'AD.ADDRESS_3', 'AD.POSTCODE', 'AD.COUNTRY_ID', 'AD.STATE_ID', 'AD.DIVISION_ID', 'AD.DISTRICT_ID', 'AD.CITY_ID', 'AD.PHONE_COUNTRY', 'AD.PHONE_AREA', 'AD.PHONE_NO', 'AD.FAX_COUNTRY', 'AD.FAX_AREA', 'AD.FAX_NO', 'AD.CREATED_DATE as AD_CREATED_DATE', 'AD.CHANGED_DATE as AD_CHANGED_DATE');
        $query->orderBy('OP.ORG_TYPE_ID');
        $query->skip($nextSkip)->take($take);
        $data = array(
            "sql" => $query->toSql(),
            "parameter" => $query->getBindings()
        );
        //dd($data);
        //Log::debug(self::class . ' :: ' . __FUNCTION__ . ' >> SQL   :   '. json_encode($data));
        return $query->get();
    }
    
    public static function getPMOrgProfileDetailsByOrgCode($orgcode,$status=null) {
       
        $query = DB::connection('oracle_nextgen')->table('PM_ORG_VALIDITY as OV');
        $query->join('PM_ORG_PROFILE as OP', 'OV.ORG_PROFILE_ID', '=', 'OP.ORG_PROFILE_ID');
        $query->leftJoin('PM_ADDRESS_TYPE as ADT', 'OP.ORG_PROFILE_ID', '=', 'ADT.ORG_PROFILE_ID');
        $query->leftJoin('PM_ADDRESS as AD', 'ADT.ADDRESS_ID', '=', 'AD.ADDRESS_ID');
        if($status == true){
            $query->where('OV.RECORD_STATUS', 0); 
        }else{
            $query->where('OV.RECORD_STATUS', 1); //must be 1 , to make sure get one record only.
        }
         
        //$query->where('OP.RECORD_STATUS', 1);
        $query->where('OV.ORG_CODE',$orgcode );
        $query->select('OV.ORG_VALIDITY_ID', 'OV.ORG_CODE', 'OV.ORG_NAME', 'OV.EFF_DATE', 'OV.EXP_DATE', 'OV.RECORD_STATUS as OV_RECORD_STATUS', 'OV.CREATED_DATE as OV_CREATED_DATE', 'OV.CHANGED_DATE as OV_CHANGED_DATE');
        $query->addSelect('OP.RECORD_STATUS as OP_RECORD_STATUS','OP.ORG_PROFILE_ID', 'OP.ORG_TYPE_ID', 'OP.PARENT_ORG_PROFILE_ID','OP.FACTORING_ORG_ID', 'OP.GROUP_ORG_TYPE', 'OP.IS_EP_PTJ', 'OP.CREATED_DATE as OP_CREATED_DATE', 'OP.CHANGED_DATE as OP_CHANGED_DATE');
        $query->addSelect('ADT.RECORD_STATUS as ADT_STATUS','ADT.ADDRESS_TYPE', 'ADT.CREATED_DATE as ADT_CREATED_DATE', 'ADT.CHANGED_DATE as ADT_CHANGED_DATE');
        $query->addSelect('AD.RECORD_STATUS as AD_STATUS','AD.ADDRESS_ID', 'AD.ADDRESS_NAME', 'AD.ADDRESS_1', 'AD.ADDRESS_2', 'AD.ADDRESS_3', 'AD.POSTCODE', 'AD.COUNTRY_ID', 'AD.STATE_ID', 'AD.DIVISION_ID', 'AD.DISTRICT_ID', 'AD.CITY_ID', 'AD.PHONE_COUNTRY', 'AD.PHONE_AREA', 'AD.PHONE_NO', 'AD.FAX_COUNTRY', 'AD.FAX_AREA', 'AD.FAX_NO', 'AD.CREATED_DATE as AD_CREATED_DATE', 'AD.CHANGED_DATE as AD_CHANGED_DATE');
        $query->orderBy('OV.ORG_VALIDITY_ID','desc');
        return $query->first();
    }
    
    public static function getPMOrgProfileDetailsByOrgName($orgname,$status=null) {
       
        $query = DB::connection('oracle_nextgen')->table('PM_ORG_VALIDITY as OV');
        $query->join('PM_ORG_PROFILE as OP', 'OV.ORG_PROFILE_ID', '=', 'OP.ORG_PROFILE_ID');
        $query->leftJoin('PM_ADDRESS_TYPE as ADT', 'OP.ORG_PROFILE_ID', '=', 'ADT.ORG_PROFILE_ID');
        $query->leftJoin('PM_ADDRESS as AD', 'ADT.ADDRESS_ID', '=', 'AD.ADDRESS_ID');
        if($status == true){
            $query->where('OV.RECORD_STATUS', 0); 
        }else{
            $query->where('OV.RECORD_STATUS', 1); //must be 1 , to make sure get one record only.
        }
         
        //$query->where('OP.RECORD_STATUS', 1);
        $query->where('OV.ORG_NAME',$orgname );
        $query->select('OV.ORG_VALIDITY_ID', 'OV.ORG_CODE', 'OV.ORG_NAME', 'OV.EFF_DATE', 'OV.EXP_DATE', 'OV.RECORD_STATUS as OV_RECORD_STATUS', 'OV.CREATED_DATE as OV_CREATED_DATE', 'OV.CHANGED_DATE as OV_CHANGED_DATE');
        $query->addSelect('OP.RECORD_STATUS as OP_RECORD_STATUS','OP.ORG_PROFILE_ID', 'OP.ORG_TYPE_ID', 'OP.PARENT_ORG_PROFILE_ID','OP.FACTORING_ORG_ID', 'OP.GROUP_ORG_TYPE', 'OP.IS_EP_PTJ', 'OP.CREATED_DATE as OP_CREATED_DATE', 'OP.CHANGED_DATE as OP_CHANGED_DATE');
        $query->addSelect('ADT.RECORD_STATUS as ADT_STATUS','ADT.ADDRESS_TYPE', 'ADT.CREATED_DATE as ADT_CREATED_DATE', 'ADT.CHANGED_DATE as ADT_CHANGED_DATE');
        $query->addSelect('AD.RECORD_STATUS as AD_STATUS','AD.ADDRESS_ID', 'AD.ADDRESS_NAME', 'AD.ADDRESS_1', 'AD.ADDRESS_2', 'AD.ADDRESS_3', 'AD.POSTCODE', 'AD.COUNTRY_ID', 'AD.STATE_ID', 'AD.DIVISION_ID', 'AD.DISTRICT_ID', 'AD.CITY_ID', 'AD.PHONE_COUNTRY', 'AD.PHONE_AREA', 'AD.PHONE_NO', 'AD.FAX_COUNTRY', 'AD.FAX_AREA', 'AD.FAX_NO', 'AD.CREATED_DATE as AD_CREATED_DATE', 'AD.CHANGED_DATE as AD_CHANGED_DATE');
        $query->orderBy('OV.ORG_VALIDITY_ID','desc');
        return $query->first();
    }
    
    public static function getPMOrgGovFactoringDetailsByOrgCode( $orgcode) {
        
        $query = DB::connection('oracle_nextgen')->table('PM_FINANCIAL_ORG as FO');
        $query->join('PM_ORG_PROFILE as OP', 'FO.FINANCIAL_ORG_ID', '=', 'OP.FACTORING_ORG_ID');
        $query->join('PM_FINANCIAL_SVC as FS', 'FO.FINANCIAL_ORG_ID', '=', 'FS.FINANCIAL_ORG_ID');
        $query->join('PM_ADDRESS_TYPE as ADT', 'FO.FINANCIAL_ORG_ID', '=', 'ADT.FINANCIAL_ORG_ID');
        $query->join('PM_ADDRESS as AD', 'ADT.ADDRESS_ID', '=', 'AD.ADDRESS_ID');
        $query->where('FO.RECORD_STATUS', 1);
        $query->where('OP.RECORD_STATUS', 1);
        $query->where('OP.ORG_TYPE_ID', 7);
        $query->where('FO.BIZ_REG_NO',$orgcode );
        $query->select('FO.FINANCIAL_ORG_ID', 'FO.FIN_ORG_NAME as ORG_NAME', 'FO.BIZ_REG_NO', 'FO.BIZ_REG_NO as ORG_CODE', 'FO.RECORD_STATUS as FO_RECORD_STATUS', 'FO.CREATED_DATE as FO_CREATED_DATE', 'FO.CHANGED_DATE as FO_CHANGED_DATE');
        $query->addSelect('OP.RECORD_STATUS as OP_RECORD_STATUS','OP.ORG_PROFILE_ID', 'OP.ORG_TYPE_ID','OP.FACTORING_ORG_ID', 'OP.PARENT_ORG_PROFILE_ID', 'OP.GROUP_ORG_TYPE', 'OP.IS_EP_PTJ', 'OP.CREATED_DATE as OP_CREATED_DATE', 'OP.CHANGED_DATE as OP_CHANGED_DATE');
        $query->addSelect('FS.RECORD_STATUS as FS_RECORD_STATUS','FS.BANK_CODE', 'FS.CREATED_DATE as FS_CREATED_DATE', 'FS.CHANGED_DATE as FS_CHANGED_DATE');
        $query->addSelect('ADT.RECORD_STATUS as ADT_STATUS','ADT.ADDRESS_TYPE', 'ADT.CREATED_DATE as ADT_CREATED_DATE', 'ADT.CHANGED_DATE as ADT_CHANGED_DATE');
        $query->addSelect('AD.RECORD_STATUS as AD_STATUS','AD.ADDRESS_ID', 'AD.ADDRESS_NAME', 'AD.ADDRESS_1', 'AD.ADDRESS_2', 'AD.ADDRESS_3', 'AD.POSTCODE', 'AD.COUNTRY_ID', 'AD.STATE_ID', 'AD.DIVISION_ID', 'AD.DISTRICT_ID', 'AD.CITY_ID', 'AD.PHONE_COUNTRY', 'AD.PHONE_AREA', 'AD.PHONE_NO', 'AD.FAX_COUNTRY', 'AD.FAX_AREA', 'AD.FAX_NO', 'AD.CREATED_DATE as AD_CREATED_DATE', 'AD.CHANGED_DATE as AD_CHANGED_DATE');
        return $query->first();
    }
    
    
    /**
     * Get Query for Update Government (Factoring/Financial Organization) Info
     * @param type $dateStart
     * @param type $dateEnd
     * @param type $take
     * @param type $nextSkip
     * @return type
     */
    public static function getListPMOrgGovFactoringDetailsActive( $take, $nextSkip) {
        
        $query = DB::connection('oracle_nextgen')->table('PM_FINANCIAL_ORG as FO');
        $query->join('PM_ORG_PROFILE as OP', 'FO.FINANCIAL_ORG_ID', '=', 'OP.FACTORING_ORG_ID');
        $query->join('PM_FINANCIAL_SVC as FS', 'FO.FINANCIAL_ORG_ID', '=', 'FS.FINANCIAL_ORG_ID');
        $query->join('PM_ADDRESS_TYPE as ADT', 'FO.FINANCIAL_ORG_ID', '=', 'ADT.FINANCIAL_ORG_ID');
        $query->join('PM_ADDRESS as AD', 'ADT.ADDRESS_ID', '=', 'AD.ADDRESS_ID');
        $query->where('FO.RECORD_STATUS', 1);
        $query->where('OP.RECORD_STATUS', 1);
        $query->where('OP.ORG_TYPE_ID', 7);
 
        $query->select('FO.FINANCIAL_ORG_ID', 'FO.FIN_ORG_NAME as ORG_NAME', 'FO.BIZ_REG_NO', 'FO.BIZ_REG_NO as ORG_CODE', 'FO.RECORD_STATUS as FO_RECORD_STATUS', 'FO.CREATED_DATE as FO_CREATED_DATE', 'FO.CHANGED_DATE as FO_CHANGED_DATE');
        $query->addSelect('OP.RECORD_STATUS as OP_RECORD_STATUS','OP.ORG_PROFILE_ID', 'OP.ORG_TYPE_ID','OP.FACTORING_ORG_ID', 'OP.PARENT_ORG_PROFILE_ID', 'OP.GROUP_ORG_TYPE', 'OP.IS_EP_PTJ', 'OP.CREATED_DATE as OP_CREATED_DATE', 'OP.CHANGED_DATE as OP_CHANGED_DATE');
        $query->addSelect('FS.RECORD_STATUS as FS_RECORD_STATUS','FS.BANK_CODE', 'FS.CREATED_DATE as FS_CREATED_DATE', 'FS.CHANGED_DATE as FS_CHANGED_DATE');
        $query->addSelect('ADT.RECORD_STATUS as ADT_STATUS','ADT.ADDRESS_TYPE', 'ADT.CREATED_DATE as ADT_CREATED_DATE', 'ADT.CHANGED_DATE as ADT_CHANGED_DATE');
        $query->addSelect('AD.RECORD_STATUS as AD_STATUS','AD.ADDRESS_ID', 'AD.ADDRESS_NAME', 'AD.ADDRESS_1', 'AD.ADDRESS_2', 'AD.ADDRESS_3', 'AD.POSTCODE', 'AD.COUNTRY_ID', 'AD.STATE_ID', 'AD.DIVISION_ID', 'AD.DISTRICT_ID', 'AD.CITY_ID', 'AD.PHONE_COUNTRY', 'AD.PHONE_AREA', 'AD.PHONE_NO', 'AD.FAX_COUNTRY', 'AD.FAX_AREA', 'AD.FAX_NO', 'AD.CREATED_DATE as AD_CREATED_DATE', 'AD.CHANGED_DATE as AD_CHANGED_DATE');
        $query->skip($nextSkip)->take($take);
        $data = array(
            "sql" => $query->toSql(),
            "parameter" => $query->getBindings()
        );
        //dd($data);
        //Log::debug(self::class . ' :: ' . __FUNCTION__ . ' >> SQL   :   '. json_encode($data));
        return $query->get();
    }
    
    
    /**
     * Get Query for Update Government (Factoring/Financial Organization) Info
     * @param type $dateStart
     * @param type $dateEnd
     * @param type $take
     * @param type $nextSkip
     * @return type
     */
    public static function getListPMOrgProfileFactoringDetailsByDate($dateStart, $dateEnd, $take, $nextSkip) {
        
        $query = DB::connection('oracle_nextgen')->table('PM_FINANCIAL_ORG as FO');
        $query->join('PM_ORG_PROFILE as OP', 'FO.FINANCIAL_ORG_ID', '=', 'OP.FACTORING_ORG_ID');
        $query->join('PM_FINANCIAL_SVC as FS', 'FO.FINANCIAL_ORG_ID', '=', 'FS.FINANCIAL_ORG_ID');
        $query->join('PM_ADDRESS_TYPE as ADT', 'FO.FINANCIAL_ORG_ID', '=', 'ADT.FINANCIAL_ORG_ID');
        $query->join('PM_ADDRESS as AD', 'ADT.ADDRESS_ID', '=', 'AD.ADDRESS_ID');
        $query->where('OP.ORG_TYPE_ID', 7);
        $query->where(function ($query) use($dateStart, $dateEnd) {
            
            $dateFormat = 'YYYY-MM-DD HH24:MI:SS';
            
            $query->orWhereRaw("FO.CREATED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            $query->orWhereRaw("FO.CHANGED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            
            $query->orWhereRaw("OP.CREATED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            $query->orWhereRaw("OP.CHANGED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            
            
            $query->orWhereRaw("FS.CREATED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            $query->orWhereRaw("FS.CHANGED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            
            $query->orWhereRaw("ADT.CREATED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            $query->orWhereRaw("ADT.CHANGED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            
            $query->orWhereRaw("AD.CREATED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            $query->orWhereRaw("AD.CHANGED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            
            
        });


        $query->select('FO.FINANCIAL_ORG_ID', 'FO.FIN_ORG_NAME as ORG_NAME', 'FO.BIZ_REG_NO','FO.BIZ_REG_NO as ORG_CODE', 'FO.RECORD_STATUS as FO_RECORD_STATUS', 'FO.CREATED_DATE as FO_CREATED_DATE', 'FO.CHANGED_DATE as FO_CHANGED_DATE');
        $query->addSelect('OP.RECORD_STATUS as OP_RECORD_STATUS','OP.ORG_PROFILE_ID', 'OP.ORG_TYPE_ID','OP.FACTORING_ORG_ID', 'OP.PARENT_ORG_PROFILE_ID', 'OP.GROUP_ORG_TYPE', 'OP.IS_EP_PTJ', 'OP.CREATED_DATE as OP_CREATED_DATE', 'OP.CHANGED_DATE as OP_CHANGED_DATE');
        $query->addSelect('FS.RECORD_STATUS as FS_RECORD_STATUS','FS.BANK_CODE', 'FS.CREATED_DATE as FS_CREATED_DATE', 'FS.CHANGED_DATE as FS_CHANGED_DATE');
        $query->addSelect('ADT.RECORD_STATUS as ADT_STATUS','ADT.ADDRESS_TYPE', 'ADT.CREATED_DATE as ADT_CREATED_DATE', 'ADT.CHANGED_DATE as ADT_CHANGED_DATE');
        $query->addSelect('AD.RECORD_STATUS as AD_STATUS','AD.ADDRESS_ID', 'AD.ADDRESS_NAME', 'AD.ADDRESS_1', 'AD.ADDRESS_2', 'AD.ADDRESS_3', 'AD.POSTCODE', 'AD.COUNTRY_ID', 'AD.STATE_ID', 'AD.DIVISION_ID', 'AD.DISTRICT_ID', 'AD.CITY_ID', 'AD.PHONE_COUNTRY', 'AD.PHONE_AREA', 'AD.PHONE_NO', 'AD.FAX_COUNTRY', 'AD.FAX_AREA', 'AD.FAX_NO', 'AD.CREATED_DATE as AD_CREATED_DATE', 'AD.CHANGED_DATE as AD_CHANGED_DATE');
        $query->skip($nextSkip)->take($take);
        $data = array(
            "sql" => $query->toSql(),
            "parameter" => $query->getBindings()
        );
        //dd($data);
        //Log::debug(self::class . ' :: ' . __FUNCTION__ . ' >> SQL   :   '. json_encode($data));
        return $query->get();
    }
    
    /**
     * Detect in PM_USER_ORG can be more than 1 record with same user_id and org_profile_id. We have to priority get Record Status Active 1st before get others.
     * @param type $userId
     * @return type
     */
    public static function getUserOrgPriority($userId) {
        $userOrgRecords = DB::connection('oracle_nextgen')->table('PM_USER_ORG')->where('user_id',$userId)->get();
        $userOrg = $userOrgRecords->where('record_status', 1)->first();
        if ($userOrg == null) {
            $userOrg = $userOrgRecords->first();
        }
        return $userOrg;
    }
    
    /**
     * Detect in PM_USER_ORG can be more than 1 record with same user_id and org_profile_id. We have to priority get Record Status Active 1st before get others.
     * @param type $userId
     * @return type
     */
    public static function getUserOrgPriorityDetails($userId,$orgProfileId) {
        $userOrgRecords = DB::connection('oracle_nextgen')->table('PM_USER_ORG')
                ->where('user_id',$userId)
                ->where('org_profile_id',$orgProfileId)->get();
        $userOrg = $userOrgRecords->where('record_status', 1)->first();
        if ($userOrg == null) {
            $userOrg = $userOrgRecords->first();
        }
        return $userOrg;
    }
    
    /**
     * Get Query for Update Government Users Info. Not include roles. 
     * @param type $dateStart
     * @param type $dateEnd
     * @param type $take
     * @param type $nextSkip
     * @return type
     */
    public static function getPMOrgGovUsersDetails($dateStart, $dateEnd, $take, $nextSkip) {
        /*
         * Sample Query test to Oracle Query
         select  
            u.user_id,u.login_id,u.user_name as fullname,u.nationality_id,u.identification_type_id,
              u.org_type_id,u.identification_no,u.designation,u.email,u.record_status as u_record_status,
              u.created_date,u.changed_date,u.mobile_country,u.mobile_area,u.mobile_no,
              u.phone_country,u.phone_area,u.phone_no,u.fax_country,u.fax_area,u.fax_no,
              u.salutation_id,
            uo.user_org_id,uo.designation as uo_designation,u.email as uo_email,u.record_status as uo_record_status,
              uo.mobile_country as uo_mobile_country,uo.mobile_area as uo_mobile_area,uo.mobile_no as uo_mobile_no,
              uo.phone_country as uo_phone_country,uo.phone_area as uo_phone_area,uo.phone_no as uo_phone_no,
              uo.fax_country as uo_fax_country,uo.fax_area as uo_fax_area,uo.fax_no as uo_fax_no,
            op.org_profile_id,op.org_type_id as op_org_type_id,
            ov.org_name,ov.org_code
          from pm_user u, pm_user_org uo, pm_org_profile op, pm_org_validity ov 
          where u.user_id = uo.user_id 
          and uo.org_profile_id = op.org_profile_id and op.org_profile_id = ov.org_profile_id 
          and op.record_status = 1 and ov.record_status = 1 and op.org_type_id in (2,3,4,5) 
          and ((u.created_date between '23-mar-17 08:15:00 pm' and '27-mar-17 08:19:59 pm')
                    or (u.changed_date between '23-mar-17 08:15:00 pm' and '27-mar-17 08:19:59 pm')
                    or (uo.created_date between '23-mar-17 08:15:00 pm' and '27-mar-17 08:19:59 pm')
                    or (uo.changed_date between '23-mar-17 08:15:00 pm' and '27-mar-17 08:19:59 pm'));
         */
        $query = DB::connection('oracle_nextgen')->table('PM_USER as U');
        $query->join('PM_USER_ORG as UO', 'U.USER_ID', '=', 'UO.USER_ID');
        $query->join('PM_ORG_PROFILE as OP', 'UO.ORG_PROFILE_ID', '=', 'OP.ORG_PROFILE_ID');
        $query->join('PM_ORG_VALIDITY as OV', 'OP.ORG_PROFILE_ID', '=', 'OV.ORG_PROFILE_ID');
        $query->where('OV.RECORD_STATUS', 1); //This will make sure will get result new code. old code will set record_status as deleted
        $query->where('OP.RECORD_STATUS', 1);
        $query->whereIn('OP.ORG_TYPE_ID', [2,3,4,5,6,7,8,9,10,11,12,13,14,209]);
        $query->where(function ($query) use($dateStart, $dateEnd) {
            $dateFormat = 'YYYY-MM-DD HH24:MI:SS';
            
            $query->orWhereRaw("U.CREATED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            $query->orWhereRaw("U.CHANGED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            
            $query->orWhereRaw("UO.CREATED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            $query->orWhereRaw("UO.CHANGED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");

            /*
             * Can't use this query below, some data will not appear in oracle query
             
            $query->orWhere(function ($query) use($dateStart, $dateEnd) {
                $query->whereBetween('U.CREATED_DATE', [$dateStart, $dateEnd]);
            });
            $query->orWhere(function ($query) use($dateStart, $dateEnd) {
                $query->whereBetween('U.CHANGED_DATE', [$dateStart, $dateEnd]);
            });
            $query->orWhere(function ($query) use($dateStart, $dateEnd) {
                $query->whereBetween('UO.CREATED_DATE', [$dateStart, $dateEnd]);
            });
            $query->orWhere(function ($query) use($dateStart, $dateEnd) {
                $query->whereBetween('UO.CHANGED_DATE', [$dateStart, $dateEnd]);
            });
             * 
             */
           
        });


        $query->select('U.USER_ID','U.LOGIN_ID','U.USER_NAME AS FULLNAME','U.NATIONALITY_ID','U.IDENTIFICATION_TYPE_ID');
        $query->addSelect('U.ORG_TYPE_ID','U.IDENTIFICATION_NO','U.DESIGNATION','U.EMAIL','U.RECORD_STATUS AS U_RECORD_STATUS');
        $query->addSelect('U.CREATED_DATE','U.CHANGED_DATE','U.MOBILE_COUNTRY','U.MOBILE_AREA','U.MOBILE_NO');
        $query->addSelect('U.PHONE_COUNTRY','U.PHONE_AREA','U.PHONE_NO','U.FAX_COUNTRY','U.FAX_AREA','U.FAX_NO','U.SALUTATION_ID');
        $query->addSelect('UO.USER_ORG_ID','UO.DESIGNATION AS UO_DESIGNATION','UO.EMAIL AS UO_EMAIL','UO.RECORD_STATUS AS UO_RECORD_STATUS');
        $query->addSelect('UO.MOBILE_COUNTRY AS UO_MOBILE_COUNTRY','UO.MOBILE_AREA AS UO_MOBILE_AREA','UO.MOBILE_NO AS UO_MOBILE_NO');
        $query->addSelect('UO.PHONE_COUNTRY AS UO_PHONE_COUNTRY','UO.PHONE_AREA AS UO_PHONE_AREA','UO.PHONE_NO AS UO_PHONE_NO');
        $query->addSelect('UO.FAX_COUNTRY AS UO_FAX_COUNTRY','UO.FAX_AREA AS UO_FAX_AREA','UO.FAX_NO AS UO_FAX_NO');
        $query->addSelect('OP.ORG_PROFILE_ID','OP.ORG_TYPE_ID AS OP_ORG_TYPE_ID','OP.FACTORING_ORG_ID','OP.PARENT_ORG_PROFILE_ID','OP.RECORD_STATUS as OP_RECORD_STATUS');
        $query->addSelect('OV.ORG_NAME','OV.ORG_CODE','OV.RECORD_STATUS as OV_RECORD_STATUS');
        $query->orderBy('UO.CHANGED_DATE','DESC');
        $query->skip($nextSkip)->take($take);
        $data = array(
            "sql" => $query->toSql(),
            "parameter" => $query->getBindings()
        );
        
        //Log::debug(self::class . ' :: ' . __FUNCTION__ . ' >> SQL   :   '. json_encode($data));
        return $query->get();
    }
    
    
    /**
     * Get Query for  Government Users Info.  Not include roles. 
     * @param type $loginID
     * @return type
     */
    public static function getPMOrgGovUsersDetailsByLoginID($loginID) {
        
        $query = DB::connection('oracle_nextgen')->table('PM_USER as U');
        $query->join('PM_USER_ORG as UO', 'U.USER_ID', '=', 'UO.USER_ID');
        $query->join('PM_ORG_PROFILE as OP', 'UO.ORG_PROFILE_ID', '=', 'OP.ORG_PROFILE_ID');
        $query->join('PM_ORG_VALIDITY as OV', 'OP.ORG_PROFILE_ID', '=', 'OV.ORG_PROFILE_ID');
        $query->where('OV.RECORD_STATUS', 1); //This will make sure will get result new code. old code will set record_status as deleted
        $query->where('OP.RECORD_STATUS', 1);
        $query->whereIn('OP.ORG_TYPE_ID', [1,2,3,4,5,6,7,8,9,10,11,12,13,14,209]);
        $query->where('U.login_id', $loginID);
        $query->select('U.USER_ID','U.LOGIN_ID','U.USER_NAME AS FULLNAME','U.NATIONALITY_ID','U.IDENTIFICATION_TYPE_ID');
        $query->addSelect('U.ORG_TYPE_ID','U.IDENTIFICATION_NO','U.DESIGNATION','U.EMAIL','U.RECORD_STATUS AS U_RECORD_STATUS');
        $query->addSelect('U.CREATED_DATE','U.CHANGED_DATE','U.MOBILE_COUNTRY','U.MOBILE_AREA','U.MOBILE_NO');
        $query->addSelect('U.PHONE_COUNTRY','U.PHONE_AREA','U.PHONE_NO','U.FAX_COUNTRY','U.FAX_AREA','U.FAX_NO','U.SALUTATION_ID');
        $query->addSelect('UO.USER_ORG_ID','UO.DESIGNATION AS UO_DESIGNATION','UO.EMAIL AS UO_EMAIL','UO.RECORD_STATUS AS UO_RECORD_STATUS');
        $query->addSelect('UO.MOBILE_COUNTRY AS UO_MOBILE_COUNTRY','UO.MOBILE_AREA AS UO_MOBILE_AREA','UO.MOBILE_NO AS UO_MOBILE_NO');
        $query->addSelect('UO.PHONE_COUNTRY AS UO_PHONE_COUNTRY','UO.PHONE_AREA AS UO_PHONE_AREA','UO.PHONE_NO AS UO_PHONE_NO');
        $query->addSelect('UO.FAX_COUNTRY AS UO_FAX_COUNTRY','UO.FAX_AREA AS UO_FAX_AREA','UO.FAX_NO AS UO_FAX_NO');
        $query->addSelect('OP.ORG_PROFILE_ID','OP.ORG_TYPE_ID AS OP_ORG_TYPE_ID','OP.FACTORING_ORG_ID','OP.PARENT_ORG_PROFILE_ID','OP.RECORD_STATUS as OP_RECORD_STATUS');
        $query->addSelect('OV.ORG_NAME','OV.ORG_CODE','OV.RECORD_STATUS as OV_RECORD_STATUS');
        $query->orderBy('UO.CHANGED_DATE','ASC');
        $query->get();
        $data = array(
            "sql" => $query->toSql(),
            "parameter" => $query->getBindings()
        );
        
        //Log::debug(self::class . ' :: ' . __FUNCTION__ . ' >> SQL   :   '. json_encode($data));
        return $query->get();
    }
    
    public static function getPMOrgFactoringUsersDetailsByLoginID($loginID) {
        
        $query = DB::connection('oracle_nextgen')->table('PM_USER as U');
        $query->join('PM_USER_ORG as UO', 'U.USER_ID', '=', 'UO.USER_ID');
        $query->join('PM_ORG_PROFILE as OP', 'UO.ORG_PROFILE_ID', '=', 'OP.ORG_PROFILE_ID');
        $query->join('PM_FINANCIAL_ORG as OV', 'OP.FACTORING_ORG_ID', '=', 'OV.FINANCIAL_ORG_ID');
        $query->where('OV.RECORD_STATUS', 1); //This will make sure will get result new code. old code will set record_status as deleted
        $query->where('OP.RECORD_STATUS', 1);
        $query->whereIn('OP.ORG_TYPE_ID', [7]);
        $query->where('U.login_id', $loginID);
        $query->select('U.USER_ID','U.LOGIN_ID','U.USER_NAME AS FULLNAME','U.NATIONALITY_ID','U.IDENTIFICATION_TYPE_ID');
        $query->addSelect('U.ORG_TYPE_ID','U.IDENTIFICATION_NO','U.DESIGNATION','U.EMAIL','U.RECORD_STATUS AS U_RECORD_STATUS');
        $query->addSelect('U.CREATED_DATE','U.CHANGED_DATE','U.MOBILE_COUNTRY','U.MOBILE_AREA','U.MOBILE_NO');
        $query->addSelect('U.PHONE_COUNTRY','U.PHONE_AREA','U.PHONE_NO','U.FAX_COUNTRY','U.FAX_AREA','U.FAX_NO','U.SALUTATION_ID');
        $query->addSelect('UO.USER_ORG_ID','UO.DESIGNATION AS UO_DESIGNATION','UO.EMAIL AS UO_EMAIL','UO.RECORD_STATUS AS UO_RECORD_STATUS');
        $query->addSelect('UO.MOBILE_COUNTRY AS UO_MOBILE_COUNTRY','UO.MOBILE_AREA AS UO_MOBILE_AREA','UO.MOBILE_NO AS UO_MOBILE_NO');
        $query->addSelect('UO.PHONE_COUNTRY AS UO_PHONE_COUNTRY','UO.PHONE_AREA AS UO_PHONE_AREA','UO.PHONE_NO AS UO_PHONE_NO');
        $query->addSelect('UO.FAX_COUNTRY AS UO_FAX_COUNTRY','UO.FAX_AREA AS UO_FAX_AREA','UO.FAX_NO AS UO_FAX_NO');
        $query->addSelect('OP.ORG_PROFILE_ID','OP.ORG_TYPE_ID AS OP_ORG_TYPE_ID','OP.FACTORING_ORG_ID','OP.PARENT_ORG_PROFILE_ID','OP.RECORD_STATUS as OP_RECORD_STATUS');
        $query->addSelect('OV.FIN_ORG_NAME as ORG_NAME', 'OV.BIZ_REG_NO', 'OV.BIZ_REG_NO as ORG_CODE','OV.RECORD_STATUS as OV_RECORD_STATUS');
        $query->orderBy('UO.CHANGED_DATE','ASC');
        $query->get();
        $data = array(
            "sql" => $query->toSql(),
            "parameter" => $query->getBindings()
        );
        
        //Log::debug(self::class . ' :: ' . __FUNCTION__ . ' >> SQL   :   '. json_encode($data));
        return $query->get();
    }
    
    /**
     * Get Query for Update Government Users Info. Not include roles By. 
     * @param type $dateStart
     * @param type $dateEnd
     * @param type $take
     * @param type $nextSkip
     * @return type
     */
    public static function getPMOrgGovUsersDetailsByOrgProfileId($orgProfileId) {

        $query = DB::connection('oracle_nextgen')->table('PM_USER as U');
        $query->join('PM_USER_ORG as UO', 'U.USER_ID', '=', 'UO.USER_ID');
        $query->where('UO.ORG_PROFILE_ID', $orgProfileId);
        $query->select('U.USER_ID','U.LOGIN_ID','U.USER_NAME AS FULLNAME','U.NATIONALITY_ID','U.IDENTIFICATION_TYPE_ID');
        $query->addSelect('U.ORG_TYPE_ID','U.IDENTIFICATION_NO','U.DESIGNATION','U.EMAIL','U.RECORD_STATUS AS U_RECORD_STATUS');
        $query->addSelect('U.CREATED_DATE','U.CHANGED_DATE','U.MOBILE_COUNTRY','U.MOBILE_AREA','U.MOBILE_NO');
        $query->addSelect('U.PHONE_COUNTRY','U.PHONE_AREA','U.PHONE_NO','U.FAX_COUNTRY','U.FAX_AREA','U.FAX_NO','U.SALUTATION_ID');
        $query->addSelect('UO.USER_ORG_ID','UO.DESIGNATION AS UO_DESIGNATION','UO.EMAIL AS UO_EMAIL','UO.RECORD_STATUS AS UO_RECORD_STATUS');
        $query->addSelect('UO.MOBILE_COUNTRY AS UO_MOBILE_COUNTRY','UO.MOBILE_AREA AS UO_MOBILE_AREA','UO.MOBILE_NO AS UO_MOBILE_NO');
        $query->addSelect('UO.PHONE_COUNTRY AS UO_PHONE_COUNTRY','UO.PHONE_AREA AS UO_PHONE_AREA','UO.PHONE_NO AS UO_PHONE_NO');
        $query->addSelect('UO.FAX_COUNTRY AS UO_FAX_COUNTRY','UO.FAX_AREA AS UO_FAX_AREA','UO.FAX_NO AS UO_FAX_NO');
        $query->addSelect('UO.ORG_PROFILE_ID');

        $data = array(
            "sql" => $query->toSql(),
            "parameter" => $query->getBindings()
        );
        
        //Log::debug(self::class . ' :: ' . __FUNCTION__ . ' >> SQL   :   '. json_encode($data));
        return $query->get();
    }
    
    /**
     * Get Query for Update Government Users Info. Not include roles By. 
     * @param type $dateStart
     * @param type $dateEnd
     * @param type $take
     * @param type $nextSkip
     * @return type
     */
    public static function getPMOrgGovFactoringDetail($financialOrgId) {
        $query = DB::connection('oracle_nextgen')->table('PM_FINANCIAL_ORG as FO');
        $query->join('PM_ORG_PROFILE as OP', 'FO.FINANCIAL_ORG_ID', '=', 'OP.FACTORING_ORG_ID');
        $query->join('PM_FINANCIAL_SVC as FS', 'FO.FINANCIAL_ORG_ID', '=', 'FS.FINANCIAL_ORG_ID');
        $query->join('PM_ADDRESS_TYPE as ADT', 'FO.FINANCIAL_ORG_ID', '=', 'ADT.FINANCIAL_ORG_ID');
        $query->join('PM_ADDRESS as AD', 'ADT.ADDRESS_ID', '=', 'AD.ADDRESS_ID');
        //$query->where('FO.RECORD_STATUS', 1);
        //$query->where('OP.RECORD_STATUS', 1);
        $query->where('OP.FACTORING_ORG_ID', $financialOrgId);
	$query->select('FO.FINANCIAL_ORG_ID', 'FO.FIN_ORG_NAME as ORG_NAME', 'FO.BIZ_REG_NO', 'FO.BIZ_REG_NO as ORG_CODE', 'FO.RECORD_STATUS as FO_RECORD_STATUS', 'FO.CREATED_DATE as FO_CREATED_DATE', 'FO.CHANGED_DATE as FO_CHANGED_DATE');
        $query->addSelect('OP.RECORD_STATUS as OP_RECORD_STATUS','OP.ORG_PROFILE_ID', 'OP.ORG_TYPE_ID','OP.FACTORING_ORG_ID', 'OP.PARENT_ORG_PROFILE_ID', 'OP.GROUP_ORG_TYPE', 'OP.IS_EP_PTJ', 'OP.CREATED_DATE as OP_CREATED_DATE', 'OP.CHANGED_DATE as OP_CHANGED_DATE');
        $query->addSelect('FS.RECORD_STATUS as FS_RECORD_STATUS','FS.BANK_CODE','FS.BANK_CODE  as ORG_CODE', 'FS.CREATED_DATE as FS_CREATED_DATE', 'FS.CHANGED_DATE as FS_CHANGED_DATE');
        $query->addSelect('ADT.RECORD_STATUS as ADT_STATUS','ADT.ADDRESS_TYPE', 'ADT.CREATED_DATE as ADT_CREATED_DATE', 'ADT.CHANGED_DATE as ADT_CHANGED_DATE');
        $query->addSelect('AD.RECORD_STATUS as AD_STATUS','AD.ADDRESS_ID', 'AD.ADDRESS_NAME', 'AD.ADDRESS_1', 'AD.ADDRESS_2', 'AD.ADDRESS_3', 'AD.POSTCODE', 'AD.COUNTRY_ID', 'AD.STATE_ID', 'AD.DIVISION_ID', 'AD.DISTRICT_ID', 'AD.CITY_ID', 'AD.PHONE_COUNTRY', 'AD.PHONE_AREA', 'AD.PHONE_NO', 'AD.FAX_COUNTRY', 'AD.FAX_AREA', 'AD.FAX_NO', 'AD.CREATED_DATE as AD_CREATED_DATE', 'AD.CHANGED_DATE as AD_CHANGED_DATE');
	return $query->first();
        
    }
    
    /**
     * Get Query for Roles Users Info
     * @param type $dateStart
     * @param type $dateEnd
     * @param type $take
     * @param type $nextSkip
     * @return type
     */
    public static function getPMOrgGovUserRolesDetails($userOrgId) {
        /*
         * Sample Query test to Oracle Query
            select "UR".* from "PM_USER_ORG" UO inner join "PM_USER_ROLE" UR on "UO"."USER_ORG_ID" = "UR"."USER_ORG_ID" 
         *      where "UO"."USER_ORG_ID" = ? and "UO"."RECORD_STATUS" = 1"
         */
        $query = DB::connection('oracle_nextgen')->table('PM_USER_ORG as UO');
        $query->join('PM_USER_ROLE as UR', 'UO.USER_ORG_ID', '=', 'UR.USER_ORG_ID');
        $query->where('UO.USER_ORG_ID',$userOrgId);
        $query->where('UO.RECORD_STATUS', 1);
        $query->select('UR.*');
        $data = array(
            "sql" => $query->toSql(),
            "parameter" => $query->getBindings()
        );
        //Log::debug(self::class . ' :: ' . __FUNCTION__ . ' >> SQL   :   '. json_encode($data));
        return $query->get();
    }

    /*
     * Get Details all
      Type of address can be associated to the user or the organisation.
      C - Contact/User Address (from migrated data)
      D - Delivery Address (Only for PTJ)
      R - Receiving Officer Address (Only for user with Receiving Officer Role)
      B - Billing Address (Org Profile & Financial Org) as the office address
     */

    public static function getNextgenOrgGovDetailsActive($orgProfileId, $orgCode = null) {
        $query = DB::connection('oracle_nextgen')->table('PM_ORG_VALIDITY as OV');
        $query->join('PM_ORG_PROFILE as OP', 'OV.ORG_PROFILE_ID', '=', 'OP.ORG_PROFILE_ID');
        $query->join('PM_ADDRESS_TYPE as ADT', 'OP.ORG_PROFILE_ID', '=', 'ADT.ORG_PROFILE_ID');
        $query->join('PM_ADDRESS as AD', 'ADT.ADDRESS_ID', '=', 'AD.ADDRESS_ID');
        $query->where('OP.ORG_PROFILE_ID', $orgProfileId);
        if ($orgCode != null) {
            $query->where('OV.ORG_CODE', $orgCode);
        }
        $query->where('OV.RECORD_STATUS', 1);
        $query->where('OP.RECORD_STATUS', 1);
        $query->select('OV.ORG_VALIDITY_ID', 'OV.ORG_CODE', 'OV.ORG_NAME', 'OV.EFF_DATE', 'OV.EXP_DATE', 'OV.RECORD_STATUS');
        $query->addSelect('OP.ORG_PROFILE_ID', 'OP.ORG_TYPE_ID','OP.FACTORING_ORG_ID', 'OP.PARENT_ORG_PROFILE_ID', 'OP.GROUP_ORG_TYPE', 'OP.IS_EP_PTJ');
        $query->addSelect('ADT.ADDRESS_TYPE');
        $query->addSelect('AD.ADDRESS_ID', 'AD.ADDRESS_NAME', 'AD.ADDRESS_1', 'AD.ADDRESS_2', 'AD.ADDRESS_3', 'AD.POSTCODE', 'AD.COUNTRY_ID', 'AD.STATE_ID', 'AD.DIVISION_ID', 'AD.DISTRICT_ID', 'AD.CITY_ID', 'AD.PHONE_COUNTRY', 'AD.PHONE_AREA', 'AD.PHONE_NO', 'AD.FAX_COUNTRY', 'AD.FAX_AREA', 'AD.FAX_NO');
        return $query->get();
    }

    /*
     * 
      Type of address can be associated to the user or the organisation.
      C - Contact/User Address (from migrated data)
      D - Delivery Address (Only for PTJ)
      R - Receiving Officer Address (Only for user with Receiving Officer Role)
      B - Billing Address (Org Profile & Financial Org) as the office address
     */
    public static function getAddressDetails($orgProfileId) {
        $query = DB::connection('oracle_nextgen')->table('PM_ADDRESS_TYPE as ADT');
        $query->join('PM_ADDRESS as AD', 'ADT.ADDRESS_ID', '=', 'AD.ADDRESS_ID');
        $query->where('ADT.ORG_PROFILE_ID', $orgProfileId);
        $query->addSelect('ADT.ADDRESS_TYPE_ID', 'ADT.ADDRESS_TYPE', 'ADT.RECORD_STATUS', 'ADT.CREATED_DATE', 'ADT.CHANGED_DATE');
        $query->addSelect('AD.ADDRESS_ID', 'AD.ADDRESS_NAME', 'AD.ADDRESS_1', 'AD.ADDRESS_2', 'AD.ADDRESS_3', 'AD.POSTCODE', 'AD.COUNTRY_ID', 'AD.STATE_ID', 'AD.DIVISION_ID', 'AD.DISTRICT_ID', 'AD.CITY_ID', 'AD.PHONE_COUNTRY', 'AD.PHONE_AREA', 'AD.PHONE_NO', 'AD.FAX_COUNTRY', 'AD.FAX_AREA', 'AD.FAX_NO');
        $query->orderBy('ADT.CHANGED_DATE', 'DESC');
        return $query->get();
    }
    
    public static function getAddressFactoringDetails($financialOrgId) {
        $query = DB::connection('oracle_nextgen')->table('PM_ADDRESS_TYPE as ADT');
        $query->join('PM_ADDRESS as AD', 'ADT.ADDRESS_ID', '=', 'AD.ADDRESS_ID');
        $query->where('ADT.FINANCIAL_ORG_ID', $financialOrgId);
        $query->addSelect('ADT.ADDRESS_TYPE_ID', 'ADT.ADDRESS_TYPE', 'ADT.RECORD_STATUS', 'ADT.CREATED_DATE', 'ADT.CHANGED_DATE');
        $query->addSelect('AD.ADDRESS_ID', 'AD.ADDRESS_NAME', 'AD.ADDRESS_1', 'AD.ADDRESS_2', 'AD.ADDRESS_3', 'AD.POSTCODE', 'AD.COUNTRY_ID', 'AD.STATE_ID', 'AD.DIVISION_ID', 'AD.DISTRICT_ID', 'AD.CITY_ID', 'AD.PHONE_COUNTRY', 'AD.PHONE_AREA', 'AD.PHONE_NO', 'AD.FAX_COUNTRY', 'AD.FAX_AREA', 'AD.FAX_NO');
        $query->orderBy('ADT.CHANGED_DATE', 'DESC');
        return $query->get();
    }
    
    public static function getContactDetails($addressTypeId) {
        $query = DB::connection('oracle_nextgen')->table('PM_CONTACT as PC');
        $query->where('PC.ADDRESS_TYPE_ID', $addressTypeId);
        $query->orderBy('PC.CHANGED_DATE', 'DESC');
        return $query->first();
    }
    

    public static function getListNextgenOrgGov($orgProfileId, $orgCode = null, $active = false, $level = false) {
        $query = DB::connection('oracle_nextgen')->table('PM_ORG_VALIDITY as OV');
        $query->join('PM_ORG_PROFILE as OP', 'OV.ORG_PROFILE_ID', '=', 'OP.ORG_PROFILE_ID');
        $query->where('OP.ORG_PROFILE_ID', $orgProfileId);
        if ($orgCode != null) {
            $query->where('OV.ORG_CODE', $orgCode);
        }
        if ($active == true) {
            $query->where('OV.RECORD_STATUS', 1);
            $query->where('OP.RECORD_STATUS', 1);
        }
        if ($level != null) {
            $query->where('OP.ORG_TYPE_ID', $level);
        }

        $query->select('OV.ORG_VALIDITY_ID', 'OV.ORG_CODE', 'OV.ORG_NAME', 'OV.EFF_DATE', 'OV.EXP_DATE', 'OV.RECORD_STATUS');
        $query->addSelect('OP.ORG_PROFILE_ID', 'OP.ORG_TYPE_ID','OP.FACTORING_ORG_ID', 'OP.PARENT_ORG_PROFILE_ID', 'OP.GROUP_ORG_TYPE', 'OP.IS_EP_PTJ');
        return $query->get();
    }

    public static function getNextGenOrgGovProfile($level, $code) {
        $query = DB::connection('oracle_nextgen')->table('pm_org_validity');
        $query->join('pm_org_profile', 'pm_org_validity.org_profile_id', '=', 'pm_org_profile.org_profile_id');
        $query->where('pm_org_profile.org_type_id', $level);
        $query->where('pm_org_validity.org_code', $code);
        $query->select('pm_org_validity.*');
        return $query->first();
    }

    public static function getNewMinistryCode($org_profile_id) {
        //So far, no new code for ministry.
        $query = DB::connection('oracle_nextgen')->table('pm_org_validity');
        $query->join('pm_org_profile', 'pm_org_validity.org_profile_id', '=', 'pm_org_profile.org_profile_id');
        $query->where('pm_org_profile.org_type_id', 2);
        $query->where('pm_org_validity.record_status', 1);
        $query->where('pm_org_validity.org_profile_id', $org_profile_id);
        $query->select('pm_org_validity.*');
        return $query->first();
    }

    /**
     * Also as Kumpulan PTJ
     * @param type $org_profile_id
     * @return type
     */
    public static function getNewPegawaiPengawalCode($org_profile_id) {
        //Must get Status is Active to identify latest code or old code.
        $query = DB::connection('oracle_nextgen')->table('pm_org_validity');
        $query->join('pm_org_profile', 'pm_org_validity.org_profile_id', '=', 'pm_org_profile.org_profile_id');
        $query->where('pm_org_profile.org_type_id', 3);
        $query->where('pm_org_validity.record_status', 1);
        $query->where('pm_org_validity.org_profile_id', $org_profile_id);
        $query->select('pm_org_validity.*');
        return $query->first();
    }

    public static function getNewJabatanCodeWithParent($org_profile_id) {
        //Status must be TRUE, It will return New Kump. PTJ Code , Also need to check code length should be 2
        $query = DB::connection('oracle_nextgen')->table('pm_org_validity v');
        $query->join('pm_org_profile p', 'v.org_profile_id', '=', 'p.org_profile_id');
        $query->join('pm_org_profile pp', 'p.parent_org_profile_id', '=', 'pp.org_profile_id');
        $query->join('pm_org_validity pv', 'pp.org_profile_id', '=', 'pv.org_profile_id');
        $query->where('p.org_type_id', 4);
        $query->where('p.record_status', 1);
        $query->where('v.record_status', 1);
        $query->where('pv.record_status', 1);
        $query->where('p.org_profile_id', $org_profile_id);
        $query->select('pv.org_code as parent_code');
        $query->addSelect('v.ORG_VALIDITY_ID', 'v.ORG_CODE', 'v.ORG_NAME', 'v.EFF_DATE', 'v.EXP_DATE', 'v.RECORD_STATUS');
        $query->addSelect('p.ORG_PROFILE_ID', 'p.ORG_TYPE_ID', 'p.PARENT_ORG_PROFILE_ID', 'p.GROUP_ORG_TYPE', 'p.IS_EP_PTJ');
        return $query->first();
    }
    
    public static function getOrgGovWithParent($org_profile_id) {
        $query = DB::connection('oracle_nextgen')->table('pm_org_validity v');
        $query->join('pm_org_profile p', 'v.org_profile_id', '=', 'p.org_profile_id');
        $query->join('pm_org_profile pp', 'p.parent_org_profile_id', '=', 'pp.org_profile_id');
        $query->join('pm_org_validity pv', 'pp.org_profile_id', '=', 'pv.org_profile_id');
        $query->where('p.record_status', 1);
        $query->where('v.record_status', 1);
        $query->where('pv.record_status', 1);
        $query->where('p.org_profile_id', $org_profile_id);
        $query->select('pv.org_code as parent_org_code','pv.org_name as parent_org_name','pv.org_profile_id as parent_org_profile_id','pp.org_type_id as parent_org_type_id');
        $query->addSelect('v.ORG_VALIDITY_ID', 'v.ORG_CODE', 'v.ORG_NAME', 'v.EFF_DATE', 'v.EXP_DATE', 'v.RECORD_STATUS');
        $query->addSelect('p.ORG_PROFILE_ID', 'p.ORG_TYPE_ID', 'p.GROUP_ORG_TYPE', 'p.IS_EP_PTJ');
        return $query->first();
    }

    /**
     * To get New PTJ Code, Length of org_code : 8
     * @param type $newPtjCode
     * @return type
     */
    public static function getNewPTJCode($newPtjCode) {
        //No need check active or not. Just get New Code.
        $query = DB::connection('oracle_nextgen')->table('pm_org_validity');
        $query->join('pm_org_profile', 'pm_org_validity.org_profile_id', '=', 'pm_org_profile.org_profile_id');
        $query->where('pm_org_profile.org_type_id', 5);
        $query->where('pm_org_validity.org_code', $newPtjCode);
        $query->where('pm_org_validity.record_status', 1);
        $query->select('pm_org_validity.*','pm_org_profile.org_type_id','pm_org_validity.record_status as ov_record_status','pm_org_profile.record_status as op_record_status');
        return $query->first();
    }
    
    /** 
     * Get description Org Profile Type
     * @param type $orgTypeId
     * @return type
     */
    public static function getOrgProfileType($orgTypeId) {
        if ($orgTypeId == null || $orgTypeId == '') {
            return null;
        }
        if ($orgTypeId == 2) {        
            return "KEMENTERIAN";
        } 
        $result = DB::connection('oracle_nextgen')
                ->table('pm_parameter')
                ->join('pm_parameter_desc', 'pm_parameter.parameter_id', '=', 'pm_parameter_desc.parameter_id')
                ->where('pm_parameter_desc.language_code', 'en')
                ->where('pm_parameter.parameter_type', 'OT')
                ->where('pm_parameter.parameter_id',$orgTypeId)
                ->select('pm_parameter_desc.*')
                ->first();
        if ($result) {
            return strtoupper(trim($result->code_name));
        } else {
            return null;
        }
    }
    
    public static function getCountryName($countryId) {
        if ($countryId == null || $countryId == '') {
            return null;
        }

        $result = DB::connection('oracle_nextgen')
                ->table('PM_COUNTRY')
                ->where('COUNTRY_ID', $countryId)
                ->first();
        if ($result) {
            return strtoupper(trim($result->country_name));
        } else {
            return null;
        }
    }

    public static function getStateName($stateId) {
        if ($stateId == null || $stateId == '') {
            return null;
        }
        $result = DB::connection('oracle_nextgen')
                ->table('PM_STATE')
                ->where('STATE_ID', $stateId)
                ->first();
        if ($result) {
            if (array_key_exists(strtoupper(trim($result->state_name)),CRMService::$MAP_STATE_LIST_CRM)){
                return $state = CRMService::$MAP_STATE_LIST_CRM[strtoupper(trim($result->state_name))];
            }
            return strtoupper(trim($result->state_name));
        } else {
            return null;
        }
    }

    public static function getDistrictName($districtId) {
        if ($districtId == null || $districtId == '') {
            return null;
        }
        $result = DB::connection('oracle_nextgen')
                ->table('PM_DISTRICT')
                ->where('DISTRICT_ID', $districtId)
                ->first();
        if ($result) {
            return strtoupper(trim($result->district_name));
        } else {
            return null;
        }
    }
    
    public static function getSalutationName($salutationId) {
        if ($salutationId == null || $salutationId == '') {
            return null;
        }
        $result = DB::connection('oracle_nextgen')
                ->table('PM_SALUTATION')
                ->where('SALUTATION_ID', $salutationId)
                ->first();
        if ($result) {
            return strtoupper(trim($result->salutation_name));
        } else {
            return null;
        }
    }

    public static function getDivisionName($divisionId) {
        if ($divisionId == null || $divisionId == '') {
            return null;
        }
        $result = DB::connection('oracle_nextgen')
                ->table('PM_DIVISION')
                ->where('DIVISION_ID', $divisionId)
                ->first();
        if ($result) {
            return strtoupper(trim($result->division_name));
        } else {
            return null;
        }
    }

    public static function getCityName($cityId) {
        if ($cityId == null || $cityId == '') {
            return null;
        }
        $result = DB::connection('oracle_nextgen')
                ->table('PM_CITY')
                ->where('CITY_ID', $cityId)
                ->first();
        if ($result) {
            return strtoupper(trim($result->city_name));
        } else {
            return null;
        }
    }
    
    public static function getAddressPriority($listAddresses,$typeAddress){
        $billAddr = $listAddresses->where('address_type',$typeAddress)
                ->where('record_status',1)
                ->first();
        if($billAddr == null){
            $billAddr = $listAddresses->where('address_type',$typeAddress)
                ->first();
        }
        return $billAddr;
    }
    
    public static function getIdentityType($typeId) {
        if ($typeId == null || $typeId == '') {
            return null;
        }
        $result = DB::connection('oracle_nextgen')
                ->table('PM_PARAMETER_DESC')
                ->where('PARAMETER_ID', $typeId)
                ->where('LANGUAGE_CODE', 'en')
                ->first();
        if ($result) {
            return strtoupper(trim($result->code_name));
        } else {
            return null;
        }
    }
    
    public static function add_address_streets($address_name,$street_field1, $street_field2, $street_field3) {
        $street_field = "";

        if (isset($address_name)) {
            $street_field .= trim(strtoupper($address_name));
        }
        if (isset($street_field1)) {
            $street_field .= "\n" . trim(strtoupper($street_field1));
        }
        if (isset($street_field2)) {
            $street_field .= "\n" . trim(strtoupper($street_field2));
        }
        if (isset($street_field3)) {
            $street_field .= "\n" . trim(strtoupper($street_field3));
        }
        return trim($street_field, "\n");
    }
    

}
