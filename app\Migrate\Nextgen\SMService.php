<?php

namespace App\Migrate\Nextgen;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Facades\DB;
use App\Models\Account;
use App\Models\Contact;
use App\Models\AccountCustom;
use App\Models\ContactCustom;
use App\Models\LogIntegration;
use App\Models\EmailAddress;
use App\Migrate\MigrateUtils;
use Config;

/**
 * Description of Profile Management PMServiceTrait
 *
 * <AUTHOR>
 */
class SMService {
    
    public static $FORMAT_DATE_ORACLE = 'YYYY-MM-DD HH24:MI:SS';
    public static $USER_LOGGED_ID = '3';

    public static $RECORD_STATUS = array(
                        0 => 'RECORD_STATUS_SUSPENDED',
                        1 => 'RECORD_STATUS_ACTIVE',
                        2 => 'RECORD_STATUS_CANCELLED',
                        3 => 'RECORD_STATUS_EXPIRED',
                        4 => 'RECORD_STATUS_REJECTED',
                        5 => 'RECORD_STATUS_IN_PROGRESS',
                        6 => 'RECORD_STATUS_PENDING_RE_APPROVAL',
                        7 => 'RECORD_STATUS_7', /** Not sure, Not in document **/
                        8 => 'RECORD_STATUS_PENDING_ACTIVATION',
                        9 => 'RECORD_STATUS_DELETED'
                        );

    /**
     * Get Query for Update Government Info
     * @param type $dateStart
     * @param type $dateEnd
     * @param type $take
     * @param type $nextSkip
     * @return type
     */
    public static function getSMSuppliersActive($dateStart, $dateEnd, $take, $nextSkip) {
        /*
         * Sample Query test to Oracle Query
         SELECT *
            FROM sm_supplier s,
              sm_appl a,
              sm_company_basic cb,
              sm_gst_supplier gs,
              sm_mof_account ma,
              sm_company_address ca,
              sm_address sa
            WHERE s.supplier_id  = a.supplier_id
            AND s.latest_appl_id = a.appl_id
            AND a.appl_id        = cb.appl_id
            AND a.appl_no        = gs.appl_no (+)
            AND s.supplier_id    = ma.supplier_id (+)
            AND cb.company_basic_id    = ca.company_basic_id (+)
            AND ca.address_id    = sa.address_id (+)
            AND s.record_status  = 1
            AND ca.record_status  = 1
            AND ca.address_type  = 'C'
            AND sa.record_status  = 1
            ORDER BY cb.rev_no;
         */
        $query = DB::connection('oracle_nextgen')->table('SM_SUPPLIER as S');
        $query->join('SM_APPL as A', 'S.SUPPLIER_ID', '=', 'A.SUPPLIER_ID');
        $query->join('SM_COMPANY_BASIC as CB', 'A.APPL_ID', '=', 'CB.APPL_ID');
        $query->join('SM_COMPANY_ADDRESS as CA', 'CB.COMPANY_BASIC_ID', '=', 'CA.COMPANY_BASIC_ID');
        $query->join('SM_ADDRESS as SA', 'CA.ADDRESS_ID', '=', 'SA.ADDRESS_ID');
        $query->leftJoin('SM_MOF_ACCOUNT as MA', 'S.SUPPLIER_ID', '=', 'MA.SUPPLIER_ID');
        $query->leftJoin('SM_GST_SUPPLIER as GS', 'S.SUPPLIER_ID', '=', 'GS.SUPPLIER_ID');
        $query->whereRaw('S.LATEST_APPL_ID = A.APPL_ID');
        //$query->whereRaw('GS.APPL_NO = A.APPL_NO'); can't use this.. 
        //$query->where('S.RECORD_STATUS', 1);  Set get All Status supplier sync with CRM
        $query->where('CA.RECORD_STATUS', 1);
        $query->where('CA.ADDRESS_TYPE', 'C');
        $query->where('SA.RECORD_STATUS', 1);
        
        $query->where(function ($query) use($dateStart, $dateEnd) {
     
            $dateFormat = 'YYYY-MM-DD HH24:MI:SS';
            
            $query->orWhereRaw("S.CREATED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            $query->orWhereRaw("S.CHANGED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            
            $query->orWhereRaw("A.CREATED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            $query->orWhereRaw("A.CHANGED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            
            $query->orWhereRaw("CB.CREATED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            $query->orWhereRaw("CB.CHANGED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            
            $query->orWhereRaw("SA.CREATED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            $query->orWhereRaw("SA.CHANGED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            
            $query->orWhereRaw("GS.CREATED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            $query->orWhereRaw("GS.CHANGED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            
            $query->orWhereRaw("MA.CREATED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            $query->orWhereRaw("MA.CHANGED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            
            /*
             * Cant use this query below , cause using oracle query, some data not appear in result.
            $query->orWhere(function ($query) use($dateStart, $dateEnd) {
                $query->whereBetween('S.CREATED_DATE', [$dateStart, $dateEnd]);
            });
            $query->orWhere(function ($query) use($dateStart, $dateEnd) {
                $query->whereBetween('S.CHANGED_DATE', [$dateStart, $dateEnd]);
            });
            $query->orWhere(function ($query) use($dateStart, $dateEnd) {
                $query->whereBetween('A.CREATED_DATE', [$dateStart, $dateEnd]);
            });
            $query->orWhere(function ($query) use($dateStart, $dateEnd) {
                $query->whereBetween('A.CHANGED_DATE', [$dateStart, $dateEnd]);
            });
            $query->orWhere(function ($query) use($dateStart, $dateEnd) {
                $query->whereBetween('CB.CREATED_DATE', [$dateStart, $dateEnd]);
            });
            $query->orWhere(function ($query) use($dateStart, $dateEnd) {
                $query->whereBetween('CB.CHANGED_DATE', [$dateStart, $dateEnd]);
            });
            $query->orWhere(function ($query) use($dateStart, $dateEnd) {
                $query->whereBetween('SA.CREATED_DATE', [$dateStart, $dateEnd]);
            });
            $query->orWhere(function ($query) use($dateStart, $dateEnd) {
                $query->whereBetween('SA.CHANGED_DATE', [$dateStart, $dateEnd]);
            });
            $query->orWhere(function ($query) use($dateStart, $dateEnd) {
                $query->whereBetween('GS.CREATED_DATE', [$dateStart, $dateEnd]);
            });
            $query->orWhere(function ($query) use($dateStart, $dateEnd) {
                $query->whereBetween('GS.CHANGED_DATE', [$dateStart, $dateEnd]);
            });
            $query->orWhere(function ($query) use($dateStart, $dateEnd) {
                $query->whereBetween('MA.CREATED_DATE', [$dateStart, $dateEnd]);
            });
            $query->orWhere(function ($query) use($dateStart, $dateEnd) {
                $query->whereBetween('MA.CHANGED_DATE', [$dateStart, $dateEnd]);
            });
             
             */
        });
        

        $query->select('S.SUPPLIER_ID', 'S.COMPANY_NAME', 'S.EP_NO','S.REG_NO', 'S.BUSINESS_TYPE', 'S.STATUS_ID as S_STATUS_ID', 'S.RECORD_STATUS as S_RECORD_STATUS', 'S.CREATED_DATE as S_CREATED_DATE', 'S.CHANGED_DATE as S_CHANGED_DATE');
        $query->addSelect('A.RECORD_STATUS as A_RECORD_STATUS','A.APPL_ID', 'A.SUPPLIER_TYPE', 'A.APPL_TYPE', 'A.APPL_NO', 'A.STATUS_ID as A_STATUS_ID', 'A.CREATED_DATE as A_CREATED_DATE', 'A.CHANGED_DATE as A_CHANGED_DATE','A.REG_STATUS_ID');
        $query->addSelect('CB.COMPANY_NAME as CB_COMPANY_NAME','CB.REV_NO as CB_REV_NO','CB.PHONE_NO','CB.PHONE_COUNTRY','CB.PHONE_AREA','CB.FAX_NO','CB.FAX_COUNTRY','CB.FAX_AREA','CB.WEBSITE','CB.RECORD_STATUS as CB_RECORD_STATUS','CB.COMPANY_BASIC_ID', 'CB.CREATED_DATE as CB_CREATED_DATE', 'CB.CHANGED_DATE as CB_CHANGED_DATE');
        $query->addSelect('CA.ADDRESS_TYPE','CA.RECORD_STATUS as CA_RECORD_STATUS');
        $query->addSelect('GS.GST_REG_NO','GS.GST_EFF_DATE','GS.GST_END_DATE','GS.BRANCH_CODE');
        $query->addSelect('MA.MOF_NO','MA.SUPPLIER_TYPE as MA_SUPPLIER_TYPE','MA.EFF_DATE as MOF_EFF_DATE','MA.EXP_DATE as MOF_EXP_DATE','MA.REG_STATUS_ID as MOF_REG_STATUS_ID');
        $query->addSelect('SA.RECORD_STATUS as SA_RECORD_STATUS','SA.ADDRESS_ID','SA.ADDRESS_1', 'SA.ADDRESS_2', 'SA.ADDRESS_3', 'SA.POSTCODE', 'SA.COUNTRY_ID', 'SA.STATE_ID', 'SA.DIVISION_ID', 'SA.DISTRICT_ID', 'SA.CITY_ID', 'SA.CREATED_DATE as SA_CREATED_DATE', 'SA.CHANGED_DATE as SA_CHANGED_DATE');
        $query->orderBy('CB.REV_NO','DESC');
        $query->skip($nextSkip)->take($take);
        $data = array(
            "sql" => $query->toSql(),
            "parameter" => $query->getBindings()
        );
        //dd($data);
        //Log::debug(self::class . ' :: ' . __FUNCTION__ . ' >> SQL   :   '. json_encode($data));

        return $query->get();
    }
    
    public static function getSMSuppliersMofMoreThanOneGenerated() {
        return DB::connection('oracle_nextgen')->select("SELECT u.login_id,p.ep_role,s.ep_no,s.record_status, 
            m.mof_no,m.exp_date FROM sm_supplier s, sm_personnel p, sm_mof_account  m , pm_user u
            WHERE s.latest_appl_id = p.appl_id 
            AND s.supplier_id = m.supplier_id 
            AND u.user_id = p.user_id
            AND s.supplier_id IN (
                SELECT supplier_id FROM (
                SELECT supplier_id, count(*) FROM sm_mof_account 
                GROUP BY supplier_id 
                HAVING count(*)  > 1 ) tmp
            )
            AND p.ep_role IN ('MOF_SUPPLIER_ADMIN','BASIC_SUPPLIER_ADMIN','SUPPLIER_TEMP','G2G_ADMIN') 
            AND m.record_status <> 9");
    }

    /**
     * Get Query for Update Government Info
     * @param type $dateStart
     * @param type $dateEnd
     * @param type $take
     * @param type $nextSkip
     * @return type
     */
    public static function getSMSuppliersActiveAll( $take, $nextSkip) {
        
        $query = DB::connection('oracle_nextgen')->table('SM_SUPPLIER as S');
        $query->join('SM_APPL as A', 'S.SUPPLIER_ID', '=', 'A.SUPPLIER_ID');
        $query->join('SM_COMPANY_BASIC as CB', 'A.APPL_ID', '=', 'CB.APPL_ID');
        $query->join('SM_COMPANY_ADDRESS as CA', 'CB.COMPANY_BASIC_ID', '=', 'CA.COMPANY_BASIC_ID');
        $query->join('SM_ADDRESS as SA', 'CA.ADDRESS_ID', '=', 'SA.ADDRESS_ID');
        $query->leftJoin('SM_MOF_ACCOUNT as MA', 'S.SUPPLIER_ID', '=', 'MA.SUPPLIER_ID');
        $query->leftJoin('SM_GST_SUPPLIER as GS', 'S.SUPPLIER_ID', '=', 'GS.SUPPLIER_ID');
        $query->whereRaw('S.LATEST_APPL_ID = A.APPL_ID');
        //$query->whereRaw('GS.APPL_NO = A.APPL_NO');
        $query->where('S.RECORD_STATUS', 1); 
        $query->where('CA.RECORD_STATUS', 1); 
        $query->where('CA.ADDRESS_TYPE', 'C');
        $query->where('SA.RECORD_STATUS', 1);

        $query->select('S.SUPPLIER_ID', 'S.COMPANY_NAME', 'S.EP_NO','S.REG_NO', 'S.BUSINESS_TYPE', 'S.STATUS_ID as S_STATUS_ID', 'S.RECORD_STATUS as S_RECORD_STATUS', 'S.CREATED_DATE as S_CREATED_DATE', 'S.CHANGED_DATE as S_CHANGED_DATE');
        $query->addSelect('A.RECORD_STATUS as A_RECORD_STATUS','A.APPL_ID', 'A.SUPPLIER_TYPE', 'A.APPL_TYPE', 'A.APPL_NO', 'A.STATUS_ID as A_STATUS_ID', 'A.CREATED_DATE as A_CREATED_DATE', 'A.CHANGED_DATE as A_CHANGED_DATE','A.REG_STATUS_ID');
        $query->addSelect('CB.COMPANY_NAME as CB_COMPANY_NAME','CB.REV_NO as CB_REV_NO','CB.PHONE_NO','CB.PHONE_COUNTRY','CB.PHONE_AREA','CB.FAX_NO','CB.FAX_COUNTRY','CB.FAX_AREA','CB.WEBSITE','CB.RECORD_STATUS as CB_RECORD_STATUS','CB.COMPANY_BASIC_ID', 'CB.CREATED_DATE as CB_CREATED_DATE', 'CB.CHANGED_DATE as CB_CHANGED_DATE');
        $query->addSelect('CA.ADDRESS_TYPE','CA.RECORD_STATUS as CA_RECORD_STATUS');
        $query->addSelect('GS.GST_REG_NO','GS.GST_EFF_DATE','GS.GST_END_DATE','GS.BRANCH_CODE');
        $query->addSelect('MA.MOF_NO','MA.SUPPLIER_TYPE as MA_SUPPLIER_TYPE','MA.EFF_DATE as MOF_EFF_DATE','MA.EXP_DATE as MOF_EXP_DATE','MA.REG_STATUS_ID as MOF_REG_STATUS_ID');
        $query->addSelect('SA.RECORD_STATUS as SA_RECORD_STATUS','SA.ADDRESS_ID','SA.ADDRESS_1', 'SA.ADDRESS_2', 'SA.ADDRESS_3', 'SA.POSTCODE', 'SA.COUNTRY_ID', 'SA.STATE_ID', 'SA.DIVISION_ID', 'SA.DISTRICT_ID', 'SA.CITY_ID', 'SA.CREATED_DATE as SA_CREATED_DATE', 'SA.CHANGED_DATE as SA_CHANGED_DATE');
        $query->orderBy('CB.REV_NO','DESC');
        $query->skip($nextSkip)->take($take);
        $data = array(
            "sql" => $query->toSql(),
            "parameter" => $query->getBindings()
        );
        //dd($data);
        //Log::debug(self::class . ' :: ' . __FUNCTION__ . ' >> SQL   :   '. json_encode($data));
        return $query->get();
    }
    
    /**
     * Get Query for Supplier Info Detail
     * @param type $dateStart
     * @param type $dateEnd
     * @param type $take
     * @param type $nextSkip
     * @return type
     */
    public static function getSMSuppliersDetail( $supplierId) {
        
        $query = DB::connection('oracle_nextgen')->table('SM_SUPPLIER as S');
        $query->join('SM_APPL as A', 'S.SUPPLIER_ID', '=', 'A.SUPPLIER_ID');
        $query->join('SM_COMPANY_BASIC as CB', 'A.APPL_ID', '=', 'CB.APPL_ID');
        $query->join('SM_COMPANY_ADDRESS as CA', 'CB.COMPANY_BASIC_ID', '=', 'CA.COMPANY_BASIC_ID');
        $query->join('SM_ADDRESS as SA', 'CA.ADDRESS_ID', '=', 'SA.ADDRESS_ID');
        $query->leftJoin('SM_MOF_ACCOUNT as MA', 'S.SUPPLIER_ID', '=', 'MA.SUPPLIER_ID');
        $query->leftJoin('SM_GST_SUPPLIER as GS', 'S.SUPPLIER_ID', '=', 'GS.SUPPLIER_ID');
        $query->whereRaw('S.LATEST_APPL_ID = A.APPL_ID');
        //$query->where('S.RECORD_STATUS', 1);
        $query->where('CA.RECORD_STATUS', 1);
        $query->where('CA.ADDRESS_TYPE', 'C');
        $query->where('SA.RECORD_STATUS', 1);
        $query->where('S.SUPPLIER_ID', $supplierId);

        $query->select('S.SUPPLIER_ID', 'S.COMPANY_NAME', 'S.EP_NO','S.REG_NO', 'S.BUSINESS_TYPE', 'S.STATUS_ID as S_STATUS_ID', 'S.RECORD_STATUS as S_RECORD_STATUS', 'S.CREATED_DATE as S_CREATED_DATE', 'S.CHANGED_DATE as S_CHANGED_DATE');
        $query->addSelect('A.RECORD_STATUS as A_RECORD_STATUS','A.APPL_ID', 'A.SUPPLIER_TYPE', 'A.APPL_TYPE', 'A.APPL_NO', 'A.STATUS_ID as A_STATUS_ID', 'A.CREATED_DATE as A_CREATED_DATE', 'A.CHANGED_DATE as A_CHANGED_DATE','A.REG_STATUS_ID');
        $query->addSelect('CB.COMPANY_NAME as CB_COMPANY_NAME','CB.REV_NO as CB_REV_NO','CB.PHONE_NO','CB.PHONE_COUNTRY','CB.PHONE_AREA','CB.FAX_NO','CB.FAX_COUNTRY','CB.FAX_AREA','CB.WEBSITE','CB.RECORD_STATUS as CB_RECORD_STATUS','CB.COMPANY_BASIC_ID', 'CB.CREATED_DATE as CB_CREATED_DATE', 'CB.CHANGED_DATE as CB_CHANGED_DATE');
        $query->addSelect('CA.ADDRESS_TYPE','CA.RECORD_STATUS as CA_RECORD_STATUS');
        $query->addSelect('GS.GST_REG_NO','GS.GST_EFF_DATE','GS.GST_END_DATE','GS.BRANCH_CODE');
        $query->addSelect('MA.MOF_NO','MA.SUPPLIER_TYPE as MA_SUPPLIER_TYPE','MA.EFF_DATE as MOF_EFF_DATE','MA.EXP_DATE as MOF_EXP_DATE','MA.REG_STATUS_ID as MOF_REG_STATUS_ID','MA.RECORD_STATUS as MOF_RECORD_STATUS');
        $query->addSelect('SA.RECORD_STATUS as SA_RECORD_STATUS','SA.ADDRESS_ID','SA.ADDRESS_1', 'SA.ADDRESS_2', 'SA.ADDRESS_3', 'SA.POSTCODE', 'SA.COUNTRY_ID', 'SA.STATE_ID', 'SA.DIVISION_ID', 'SA.DISTRICT_ID', 'SA.CITY_ID', 'SA.CREATED_DATE as SA_CREATED_DATE', 'SA.CHANGED_DATE as SA_CHANGED_DATE');
        $query->orderBy('CB.REV_NO','DESC');
        $data = array(
            "sql" => $query->toSql(),
            "parameter" => $query->getBindings()
        );
        $list = $query->get();
        //dd($data);
        //Log::debug(self::class . ' :: ' . __FUNCTION__ . ' >> SQL   :   '. json_encode($data));
        foreach ($list as $row){
            // on 13/08/2023 found out issue supplier can be multiple mof_no if supplier downgrade basic account then after renew will get new generated mof no
            if($row->mof_record_status == 1){
                return $row;
            }
        }
        return $list [0];
    }
    
    /**
     * Get Query for Supplier Users Info. Not include roles. 
     * @param type $take
     * @param type $nextSkip
     * @return type
     */
    public static function getSMSupplierUsersActiveAll( $take, $nextSkip) {
        /*
         * Sample Query test to Oracle Query
  
         */
        $query = DB::connection('oracle_nextgen')->table('SM_PERSONNEL as P');
        $query->join('SM_APPL as A', 'P.APPL_ID', '=', 'A.APPL_ID');
        $query->join('SM_SUPPLIER as S', 'A.SUPPLIER_ID', '=', 'S.SUPPLIER_ID');
        $query->leftJoin('PM_USER as U', 'P.USER_ID', '=', 'U.USER_ID');
        $query->leftJoin('SM_MOF_ACCOUNT as MA', 'S.SUPPLIER_ID', '=', 'MA.SUPPLIER_ID');
        $query->whereRaw('S.LATEST_APPL_ID = A.APPL_ID');
        $query->where('P.RECORD_STATUS', 1);
        $query->where('S.RECORD_STATUS', 1);
        $query->select('U.USER_ID','U.LOGIN_ID','U.USER_NAME AS FULLNAME','U.NATIONALITY_ID','U.IDENTIFICATION_TYPE_ID');
        $query->addSelect('U.ORG_TYPE_ID','U.IDENTIFICATION_NO','U.DESIGNATION','U.EMAIL','U.RECORD_STATUS AS U_RECORD_STATUS');
        $query->addSelect('U.CREATED_DATE','U.CHANGED_DATE','U.MOBILE_COUNTRY','U.MOBILE_AREA','U.MOBILE_NO');
        $query->addSelect('U.PHONE_COUNTRY','U.PHONE_AREA','U.PHONE_NO','U.FAX_COUNTRY','U.FAX_AREA','U.FAX_NO','U.SALUTATION_ID');
        $query->addSelect('P.PERSONNEL_ID','P.RECORD_STATUS AS P_RECORD_STATUS','P.TITLE_ID','P.NAME as P_NAME','P.IDENTIFICATION_NO as P_IDENTIFICATION_NO','P.NATIONALITY_ID as P_NATIONALITY_ID','U.IDENTIFICATION_TYPE_ID as P_IDENTIFICATION_TYPE_ID','P.IS_EQUITY_OWNER as P_IS_EQUITY_OWNER');
        $query->addSelect('P.DESIGNATION as P_DESIGNATION');
        $query->addSelect('P.PHONE_COUNTRY as P_PHONE_COUNTRY','P.PHONE_AREA as P_PHONE_AREA','P.PHONE_NO as P_PHONE_NO','P.MOBILE_COUNTRY as P_MOBILE_COUNTRY','P.MOBILE_AREA as P_MOBILE_AREA','P.MOBILE_NO as P_MOBILE_NO','P.EMAIL as P_EMAIL');
        $query->addSelect('S.SUPPLIER_ID', 'S.COMPANY_NAME', 'S.EP_NO','S.REG_NO', 'S.BUSINESS_TYPE', 'S.STATUS_ID as S_STATUS_ID', 'S.RECORD_STATUS as S_RECORD_STATUS', 'S.CREATED_DATE as S_CREATED_DATE', 'S.CHANGED_DATE as S_CHANGED_DATE');
        $query->addSelect('MA.MOF_NO');
        $query->skip($nextSkip)->take($take);
        $data = array(
            "sql" => $query->toSql(),
            "parameter" => $query->getBindings()
        );
        
        //Log::debug(self::class . ' :: ' . __FUNCTION__ . ' >> SQL   :   '. json_encode($data));
        return $query->get();
    }
    
    /**
     * Get Query for Update Supplier Users Info. Not include roles. 
     * @param type $dateStart
     * @param type $dateEnd
     * @param type $take
     * @param type $nextSkip
     * @return type
     */
    public static function getSMSupplierUsersActive($dateStart, $dateEnd, $take, $nextSkip) {
        /*
         * Sample Query test to Oracle Query
  
         */
        $query = DB::connection('oracle_nextgen')->table('SM_PERSONNEL as P');
        $query->join('SM_APPL as A', 'P.APPL_ID', '=', 'A.APPL_ID');
        $query->join('SM_SUPPLIER as S', 'A.SUPPLIER_ID', '=', 'S.SUPPLIER_ID');
        $query->leftJoin('PM_USER as U', 'P.USER_ID', '=', 'U.USER_ID');
        $query->leftJoin('SM_MOF_ACCOUNT as MA', 'S.SUPPLIER_ID', '=', 'MA.SUPPLIER_ID');
        $query->whereRaw('S.LATEST_APPL_ID = A.APPL_ID');
        $query->where('P.RECORD_STATUS', 1);
        $query->where(function ($query) use($dateStart, $dateEnd) {
            
            $dateFormat = 'YYYY-MM-DD HH24:MI:SS';
            
            $query->orWhereRaw("P.CREATED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            $query->orWhereRaw("P.CHANGED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            
            $query->orWhereRaw("U.CREATED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            $query->orWhereRaw("U.CHANGED_DATE BETWEEN TO_DATE('$dateStart', '$dateFormat') AND TO_DATE('$dateEnd', '$dateFormat') ");
            
            /*
             * Can't use this query below, some data not appear.
            $query->orWhere(function ($query) use($dateStart, $dateEnd) {
                $query->whereBetween('P.CREATED_DATE', [$dateStart, $dateEnd]);
            });
            $query->orWhere(function ($query) use($dateStart, $dateEnd) {
                $query->whereBetween('P.CHANGED_DATE', [$dateStart, $dateEnd]);
            });
            $query->orWhere(function ($query) use($dateStart, $dateEnd) {
                $query->whereBetween('U.CREATED_DATE', [$dateStart, $dateEnd]);
            });
            $query->orWhere(function ($query) use($dateStart, $dateEnd) {
                $query->whereBetween('U.CHANGED_DATE', [$dateStart, $dateEnd]);
            });
            */
        });


        $query->select('U.USER_ID','U.LOGIN_ID','U.USER_NAME AS FULLNAME','U.NATIONALITY_ID','U.IDENTIFICATION_TYPE_ID');
        $query->addSelect('U.ORG_TYPE_ID','U.IDENTIFICATION_NO','U.DESIGNATION','U.EMAIL','U.RECORD_STATUS AS U_RECORD_STATUS');
        $query->addSelect('U.CREATED_DATE','U.CHANGED_DATE','U.MOBILE_COUNTRY','U.MOBILE_AREA','U.MOBILE_NO');
        $query->addSelect('U.PHONE_COUNTRY','U.PHONE_AREA','U.PHONE_NO','U.FAX_COUNTRY','U.FAX_AREA','U.FAX_NO','U.SALUTATION_ID');
        $query->addSelect('P.PERSONNEL_ID','P.RECORD_STATUS AS P_RECORD_STATUS','P.TITLE_ID','P.NAME as P_NAME','P.IDENTIFICATION_NO as P_IDENTIFICATION_NO','P.NATIONALITY_ID as P_NATIONALITY_ID','U.IDENTIFICATION_TYPE_ID as P_IDENTIFICATION_TYPE_ID','P.IS_EQUITY_OWNER as P_IS_EQUITY_OWNER');
        $query->addSelect('P.DESIGNATION as P_DESIGNATION');
        $query->addSelect('P.PHONE_COUNTRY as P_PHONE_COUNTRY','P.PHONE_AREA as P_PHONE_AREA','P.PHONE_NO as P_PHONE_NO','P.MOBILE_COUNTRY as P_MOBILE_COUNTRY','P.MOBILE_AREA as P_MOBILE_AREA','P.MOBILE_NO as P_MOBILE_NO','P.EMAIL as P_EMAIL');
        $query->addSelect('S.SUPPLIER_ID', 'S.COMPANY_NAME', 'S.EP_NO','S.REG_NO', 'S.BUSINESS_TYPE', 'S.STATUS_ID as S_STATUS_ID', 'S.RECORD_STATUS as S_RECORD_STATUS', 'S.CREATED_DATE as S_CREATED_DATE', 'S.CHANGED_DATE as S_CHANGED_DATE');
        $query->addSelect('MA.MOF_NO');
        $query->skip($nextSkip)->take($take);
        $data = array(
            "sql" => $query->toSql(),
            "parameter" => $query->getBindings()
        );
        
        //Log::debug(self::class . ' :: ' . __FUNCTION__ . ' >> SQL   :   '. json_encode($data));
        return $query->get();
    }
    
    /**
     * Get Query for  Supplier Users Info. Not include roles. 
     * @param type $dateStart
     * @param type $dateEnd
     * @param type $take
     * @param type $nextSkip
     * @return type
     */
    public static function getSMSupplierUsersActiveByLoginID($loginID) {
        /*
         * Sample Query test to Oracle Query
  
         */
        $query = DB::connection('oracle_nextgen')->table('SM_PERSONNEL as P');
        $query->join('SM_APPL as A', 'P.APPL_ID', '=', 'A.APPL_ID');
        $query->join('SM_SUPPLIER as S', 'A.SUPPLIER_ID', '=', 'S.SUPPLIER_ID');
        $query->leftJoin('PM_USER as U', 'P.USER_ID', '=', 'U.USER_ID');
        $query->leftJoin('SM_MOF_ACCOUNT as MA', 'S.SUPPLIER_ID', '=', 'MA.SUPPLIER_ID');
        $query->whereRaw('S.LATEST_APPL_ID = A.APPL_ID');
        $query->where('P.RECORD_STATUS', 1);
        $query->where('U.LOGIN_ID', $loginID);
        $query->select('U.USER_ID','U.LOGIN_ID','U.USER_NAME AS FULLNAME','U.NATIONALITY_ID','U.IDENTIFICATION_TYPE_ID');
        $query->addSelect('U.ORG_TYPE_ID','U.IDENTIFICATION_NO','U.DESIGNATION','U.EMAIL','U.RECORD_STATUS AS U_RECORD_STATUS');
        $query->addSelect('U.CREATED_DATE','U.CHANGED_DATE','U.MOBILE_COUNTRY','U.MOBILE_AREA','U.MOBILE_NO');
        $query->addSelect('U.PHONE_COUNTRY','U.PHONE_AREA','U.PHONE_NO','U.FAX_COUNTRY','U.FAX_AREA','U.FAX_NO','U.SALUTATION_ID');
        $query->addSelect('P.PERSONNEL_ID','P.RECORD_STATUS AS P_RECORD_STATUS','P.TITLE_ID','P.NAME as P_NAME','P.IDENTIFICATION_NO as P_IDENTIFICATION_NO','P.NATIONALITY_ID as P_NATIONALITY_ID','U.IDENTIFICATION_TYPE_ID as P_IDENTIFICATION_TYPE_ID','P.IS_EQUITY_OWNER as P_IS_EQUITY_OWNER');
        $query->addSelect('P.DESIGNATION as P_DESIGNATION');
        $query->addSelect('P.PHONE_COUNTRY as P_PHONE_COUNTRY','P.PHONE_AREA as P_PHONE_AREA','P.PHONE_NO as P_PHONE_NO','P.MOBILE_COUNTRY as P_MOBILE_COUNTRY','P.MOBILE_AREA as P_MOBILE_AREA','P.MOBILE_NO as P_MOBILE_NO','P.EMAIL as P_EMAIL');
        $query->addSelect('S.SUPPLIER_ID', 'S.COMPANY_NAME', 'S.EP_NO','S.REG_NO', 'S.BUSINESS_TYPE', 'S.STATUS_ID as S_STATUS_ID', 'S.RECORD_STATUS as S_RECORD_STATUS', 'S.CREATED_DATE as S_CREATED_DATE', 'S.CHANGED_DATE as S_CHANGED_DATE');
        $query->addSelect('MA.MOF_NO','MA.RECORD_STATUS as mof_record_status');
        $query->get();
        $data = array(
            "sql" => $query->toSql(),
            "parameter" => $query->getBindings()
        );
        
        //Log::debug(self::class . ' :: ' . __FUNCTION__ . ' >> SQL   :   '. json_encode($data));
        return $query->get();
    }
    
    public static function getStatusDesc($statusId) {

        if ($statusId != null) {
            $query = DB::connection('oracle_nextgen')->table('PM_STATUS_DESC');
            $query->where('LANGUAGE_CODE', 'en');
            $query->where('STATUS_ID', $statusId);
            $data = $query->first();
            if ($data != null) {
                return $data->status_name;
            }
        }
        return $statusId;
    }

    public static function add_address_streets($street_field1, $street_field2, $street_field3) {
        $street_field = "";

        if (isset($address_name)) {
            $street_field .= trim(strtoupper($address_name));
        }
        if (isset($street_field1)) {
            $street_field .= "\n" . trim(strtoupper($street_field1));
        }
        if (isset($street_field2)) {
            $street_field .= "\n" . trim(strtoupper($street_field2));
        }
        if (isset($street_field3)) {
            $street_field .= "\n" . trim(strtoupper($street_field3));
        }
        return trim($street_field, "\n");
    }
    
    
    /**
     * select a.PARAMETER_ID, b.CODE_NAME ,a.PARAMETER_CODE from pm_parameter a, pm_parameter_desc b 
        where a.PARAMETER_ID = b.PARAMETER_ID and b.LANGUAGE_CODE = 'en' and a.parameter_type = 'AT';
     * 
     */
    public static $APPL_TYPE = array(
                        'O' => 'Application for Basic Online Account',
                        'N' => 'New Application for MOF',
                        'B' => 'Application for Bumiputera Status',
                        'A' => 'Application for add Category Code',
                        'U' => 'Application for update MOF profile',
                        'R' => 'Renew MOF Account',
                        'C' => 'Cancel MOF Account'
                        );
    
    /** 
     * select a.PARAMETER_ID, b.CODE_NAME,a.PARAMETER_CODE from pm_parameter a, pm_parameter_desc b where a.PARAMETER_ID = b.PARAMETER_ID and b.LANGUAGE_CODE = 'en' and a.parameter_type = 'ST';
     'K' => 'Contractor',
     'J' => 'Consultant (Perunding)',
     'B' => 'Basic/Online without for going MOF-Registered',
     'G' => 'G2G Basic (E.g. State Government, Local Council, Federal Statutory Bodies)',
     'P' => 'PTJ Government Seller',
     * **/
    public static $SUPPLIER_TYPE = array(
                        'K' => 'Contractor',
                        'J' => 'Consultant (Perunding)',
                        'B' => 'Basic/Online without for going MOF-Registered',
                        'G' => 'G2G Basic (E.g. State Government, Local Council, Federal Statutory Bodies)',
                        'P' => 'PTJ Government Seller',
                        );
    
    public static $MOF_REG_STATUS = array(
                        1 => 'Not Defined',
                        2 => 'Bumiputera',
                        3 => 'Registered',
                        4 => 'Bumiputera Operated from Home',
                        5 => 'Non-Bumi Operated from Home',
                        6 => 'Bumiputera and Foreign Company Joint Venture',
                        7 => 'Bumiputera and Non-Bumiputera and Foreign Company Joint Venture',
                        8 => 'Foreign Company',
                        );
    
    
}
