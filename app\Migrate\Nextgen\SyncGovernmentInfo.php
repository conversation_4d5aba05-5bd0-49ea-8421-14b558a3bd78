<?php

namespace App\Migrate\Nextgen;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Ramsey\Uuid\Uuid;
use App\Models\Account;
use App\Models\LogScheduler;
use App\Models\AccountCustom;
use App\Migrate\MigrateUtils;
use App\Migrate\Nextgen\PMService;
use App\Migrate\Nextgen\CRMService;


/*
 * This integration to sync data Org Gov. Profile CRM with Org Gov. Profile Nextgen
 * In current CRM   :   Kementerian,                        Jabatan,        PTJ
 * In Nextgen       :   Kementerian,   Pegawai Pengawal,    Kumpulan PTJ,   PTJ 
 * In Nextgen, Code Org Profile is not same with current eP. we have to sync, get new Code Org Profile sync with existing in CRM
 * 
 * 
 */
class SyncGovernmentInfo {
    
    public static $KEMENTERIAN = 'KEMENTERIAN';
    public static $PEGAWAI_PENGAWAL = 'PEGAWAI PENGAWAL';
    public static $KUMPULAN_PTJ = 'KUMPULAN PTJ';
    public static $PTJ = 'PTJ';
    public static $BPK = 'BPK';
    public static $USER_LOGGED_ID = '3';
    public static $ORG_GOVERNMENT_TYPE = array(
                        2 => 'KEMENTERIAN',
                        3 => 'PEGAWAI PENGAWAL',
                        4 => 'KUMPULAN PTJ',
                        5 => 'PTJ'
                        );
    
    public static $OLD_ORG_GOVERNMENT_TYPE = array(
                        2 => 'KEMENTERIAN',
                        3 => 'PEGAWAI PENGAWAL',
                        4 => 'JABATAN',
                        5 => 'PTJ'
                        );
    
    public static $QUERY_SKIP = 500;
    public static $QUERY_TAKE = 500;
    
    public static function runUpdateGovernmentInfo(LogScheduler $logScheduler,$dateStart,$dateEnd) {
        Log::debug(self::class . ' Starting ... '.__FUNCTION__);
        $dtStartTime = Carbon::now();

        self::updateGovernmentInfo($logScheduler,$dateStart,$dateEnd);
        self::updateGovernmentFactoringInfo($logScheduler,$dateStart, $dateEnd);
        $logScheduler->status = 'Completed';
        $logScheduler->save();
        

        Log::info(self::class . ' Completed '.__FUNCTION__.' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        var_dump(self::class . ' Completed '.__FUNCTION__.' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }
    
    protected static function updateGovernmentFactoringInfo(LogScheduler $logScheduler,$dateStart,$dateEnd) {
        Log::info(self::class . ' Start '.__FUNCTION__.'     ->> Date Start: ' . $dateStart . ', Date End: '.$dateEnd );
        var_dump('Date Start: '.$dateStart);
        var_dump('Date End: '.$dateEnd); 
        
        $start = 0;
        $skip = self::$QUERY_SKIP;
        $take = self::$QUERY_TAKE;
        $totalRecords = 0;
        do {
            $nextSkip = $start++ * $skip;
            $results = PMService::getListPMOrgProfileFactoringDetailsByDate($dateStart, $dateEnd, $take, $nextSkip);
      
            Log::info(' Count Total Query :- '.count($results));
            $totalRecords = $totalRecords + count($results);
             
            foreach ($results as $obj){
                $orgcode = $obj->org_code;
                var_dump(PMService::getOrgProfileType($obj->org_type_id) .' -> '.$orgcode);
                //var_dump('  '.json_encode($obj));
                Log::info('     '.PMService::getOrgProfileType($obj->org_type_id) .' -> '.$orgcode);
                //Log::info('     '.json_encode($obj));

                //Check Data from CRM
                $account = CRMService::getGovProfileCrm($obj->org_code,$obj->org_type_id);
                if($account != null){
                    self::saveAccountFactoryPrimary($obj,$account->id);
                    self::saveAccountAddressFactoring($obj->factoring_org_id, $account->id); 
                }else{
                    self::prepareCreateAccount($obj);
                }
            }
        }while (count($results) > 0 && count($results) == $take);
        
        $logScheduler->total = $logScheduler->total + $totalRecords;
        $logScheduler->save();
    }
   
    /**
     * Run this to get update profile for all Level from Kumpulan PTJ until Ministry
     */
    public static function updateGovernmentInfoHighLevel() {
        Log::info(self::class . ' Start '.__FUNCTION__ );

        $totalRecords = 0;
       
            $results = PMService::getListPMOrgProfileDetailsHighLevel();
      
            Log::info(' Count Total getListPMOrgProfileDetailsHighLevel :- '.count($results));

            foreach ($results as $obj){
                $orgcode = $obj->org_code;

                var_dump(PMService::getOrgProfileType($obj->org_type_id) .' -> '.$orgcode);
                Log::info('     '.PMService::getOrgProfileType($obj->org_type_id) .' -> '.$orgcode);
                Log::info('     '.json_encode($obj));

                //Check Data from CRM
                $account = CRMService::getGovProfileCrm($obj->org_code,$obj->org_type_id);
                if($account != null){
                    var_dump('Jumpa : '.$orgcode);
                    self::saveAccountPrimary($obj,$account->id);
                    self::saveAccountAddress($obj->org_profile_id, $account->id); 
                }else{
                    var_dump('Check Tak Jumpa : '.$orgcode);
                    var_dump('     '.json_encode($obj));
                    $totalRecords ++;
                    self::prepareCreateAccount($obj);
                }
                        
                
            } 
            
            var_dump("Total Tak Jumpa : ".$totalRecords );
        //$logScheduler->total = $totalRecords;
        //$logScheduler->save();
    }
    /**
     * DELAY + TIME INTERVAL
     * @param type $dateStart
     * @param type $dateEnd
     */
    protected static function updateGovernmentInfo(LogScheduler $logScheduler,$dateStart,$dateEnd) {
        
        Log::info(__METHOD__.'     ->> Date Start: ' . $dateStart . ', Date End: '.$dateEnd );
        var_dump('Date Start: '.$dateStart);
        var_dump('Date End: '.$dateEnd);
        
        
        $start = 0;
        $skip = self::$QUERY_SKIP;
        $take = self::$QUERY_TAKE;
        $totalRecords = 0;
        do {
            $nextSkip = $start++ * $skip;
            $results = PMService::getListPMOrgProfileDetailsByDate($dateStart, $dateEnd, $take, $nextSkip);
            $totalRecords = $totalRecords + count($results);
            Log::info(__METHOD__."$start >>  Count Total Query :- ".count($results).' , Total SUM '.$totalRecords); 
            foreach ($results as $obj){
                $orgcode = $obj->org_code;
                
                
                var_dump(PMService::getOrgProfileType($obj->org_type_id) .' -> '.$orgcode);
                //var_dump('  '.json_encode($obj));
                Log::info('     '.PMService::getOrgProfileType($obj->org_type_id) .' -> '.$orgcode);
                //Log::info('     '.json_encode($obj));

                //Check Data from CRM
                $account = CRMService::getGovProfileCrm($obj->org_code,$obj->org_type_id);
                if($account != null){
                    self::saveAccountPrimary($obj,$account->id);
                    self::saveAccountAddress($obj->org_profile_id, $account->id); 
                }else{
                    self::prepareCreateAccount($obj);
                }
                        
                /*
                    +"rn": "1"
                    +"org_validity_id": "350"
                    +"org_code": "********"
                    +"org_name": "PEJABAT PILIHANRAYA NEGERI TERENGGANU"
                    +"eff_date": "2005-01-01 00:00:00"
                    +"exp_date": "9999-12-31 00:00:00"
                    +"ov_record_status": "1"
                    +"ov_created_date": "2000-09-27 15:26:45"
                    +"ov_changed_date": "2015-01-07 18:56:14"
                    +"op_record_status": "1"
                    +"org_profile_id": "303769"
                    +"org_type_id": "5"
                    +"parent_org_profile_id": "303724"
                    +"group_org_type": null
                    +"is_ep_ptj": "1"
                    +"op_created_date": "2000-09-27 15:26:45"
                    +"op_changed_date": "2015-01-07 18:56:14"
                    +"adt_status": "8"
                    +"address_type": "B"
                    +"adt_created_date": "2017-03-27 21:41:47"
                    +"adt_changed_date": "2017-03-27 21:41:47"
                    +"ad_status": "1"
                    +"address_id": "810"
                    +"address_name": "PEJABAT PILIHAN RAYA NEGERI TERENGGANU"
                    +"address_1": "TINGKAT 6, WISMA PERSEKUTUAN"
                    +"address_2": "JALAN SULTAN ISMAIL"
                    +"address_3": null
                    +"postcode": "20200"
                    +"country_id": "131"
                    +"state_id": "12"
                    +"division_id": null
                    +"district_id": "91"
                    +"city_id": "1749"
                    +"phone_country": "60"
                    +"phone_area": "9"
                    +"phone_no": "6221218"
                    +"fax_country": "60"
                    +"fax_area": "9"
                    +"fax_no": "6221272"
                    +"ad_created_date": "2017-03-27 20:19:26"
                    +"ad_changed_date": "2017-03-27 20:19:26"

                 */
            } 
            
        } while (count($results) > 0 && count($results) == $take);
        
        $logScheduler->total = $totalRecords;
        $logScheduler->save();

    }
    
    /**
     * One time migration only for factoring
     */
    public static function createGovernmentFactoringInfo() {
        Log::info(self::class . ' Start '.__FUNCTION__.' .. ');
        var_dump(self::class . ' Start '.__FUNCTION__.' .. ');
        
        $start = 0;
        $skip = self::$QUERY_SKIP;
        $take = self::$QUERY_TAKE;
        $totalRecords = 0;
        do {
            $nextSkip = $start++ * $skip;
            $results = PMService::getListPMOrgGovFactoringDetailsActive($take, $nextSkip);
      
            Log::info(' Count Total Query :- '.count($results));
            $totalRecords = $totalRecords + count($results);
             
            foreach ($results as $objOrg){
                self::createOrgProfileAndUserInfo($objOrg);
            } 
            
        } while (count($results) > 0 && count($results) == $take);

        var_dump('  Total Records Created : '.$totalRecords);
    }
    
    /**
     * One time migration only for orgProfile exclude (ministry,pegawai pengawal,kump.jabatan,ptj,factoring)
     */
    public static function createGovernmentInfo() {
        
        Log::info(self::class . ' Start '.__FUNCTION__.' .. ');
        var_dump(self::class . ' Start '.__FUNCTION__.' .. ');
        
        $start = 0;
        $skip = self::$QUERY_SKIP;
        $take = self::$QUERY_TAKE;
        $totalRecords = 0;
        do {
            $nextSkip = $start++ * $skip;
            $results = PMService::getListPMOrgProfileDetails($take, $nextSkip);
      
            Log::info(' Count Total Query :- '.count($results));
            $totalRecords = $totalRecords + count($results);
             
            foreach ($results as $objOrg){
                self::createOrgProfileAndUserInfo($objOrg);
            } 
            
        } while (count($results) > 0 && count($results) == $take);

        var_dump('  Total Records Created : '.$totalRecords);
    }
    
    protected static function createOrgProfileAndUserInfo($objOrg) {
        $orgcode = $objOrg->org_code;

        var_dump(PMService::getOrgProfileType($objOrg->org_type_id) .' -> '.$orgcode);
        Log::info('     '.PMService::getOrgProfileType($objOrg->org_type_id) .' -> '.$orgcode);
        Log::info('     '.json_encode($objOrg));

        //Check Data from CRM
        $account = CRMService::getGovProfileCrm($objOrg->org_code,$objOrg->org_type_id);
        if($account == null){
            $account =  self::prepareCreateAccount($objOrg);
        }


        if($account != null){
            $listDataUsers = PMService::getPMOrgGovUsersDetailsByOrgProfileId($objOrg->org_profile_id);
            Log::info('      :: ->  Total users   ' . count($listDataUsers));
            var_dump('      :: ->  Total users  ' . count($listDataUsers));
            if(count($listDataUsers) > 0){
                foreach ($listDataUsers as $obj) {
                    Log::info('       :: ->  Looking check User ID  ' . $obj->identification_no);
                    $roles = PMService::getPMOrgGovUserRolesDetails($obj->user_org_id);
                    var_dump('          Name :- ' . $obj->fullname);  
                    var_dump('          Identity No. :- ' . $obj->identification_no);  
                    var_dump('          Login ID :- ' . $obj->login_id); 
                    var_dump('          Status  :- ' . $obj->uo_record_status); 
                    Log::info('         :: ->  Count roles found  ' . count($roles));
                    $roles_name = null;
                    foreach ($roles as $objRole) {
                        if($roles_name == null){
                           $roles_name =  $objRole->role_code;
                        }else{
                           $roles_name = $roles_name.', '.$objRole->role_code;
                        }
                    }

                    /** Let find and check in CRM **/
                    /** FIND account -> contact **/
                    $contact = DB::table('accounts as a')
                            ->join('accounts_contacts as ac', 'a.id', '=', 'ac.account_id')
                            ->join('contacts as c', 'ac.contact_id', '=', 'c.id')
                            ->where('a.id', $account->id)
                            ->where('c.identity_no_nextgen', trim($obj->identification_no))
                            ->select('a.name as acc_name', 'a.id as acc_id', 'a.org_gov_code', 'a.org_gov_type')
                            ->addSelect('c.first_name as contact_name', 'c.id as contact_id', 'c.identity_no_nextgen', 'c.user_id_nextgen')
                            ->first();
                    var_dump('          Check Contact is exist: ' . json_encode($contact));
                    if($contact == null){
                        //create it
                        if($account != null){
                            if($obj->uo_record_status == '1'){
                                $contactObj = CRMService::createContactGovernment($account, $obj, $roles_name);
                                Log::info('          :: ->  success create ' . $contactObj->id.' RECORD_STATUS: '.$contactObj->record_status_nextgen);
                            }else{
                                Log::info('          :: ->  No need create this user . This user is belong Organization InActive');
                            }
                        }
                    }else{
                        //update it
                        $contactObj = CRMService::saveContactGovernment($obj, $roles_name, $contact);
                        CRMService::saveEmailContact($obj->email, $contact->contact_id);
                    }
                }
            }
        }
    }
    
    public static function prepareCreateAccount($obj) {
        Log::info('     '.__FUNCTION__.'>> start in...');
        $isCreated = false;
        /** Create new government organization  **/
        
        // We must check org_type_id in (2,3,4,5) only. Others org_type_id no parent.
        if($obj->org_type_id && $obj->org_type_id > 1 && $obj->org_type_id < 6){
            
            if( $obj->org_type_id == 2){
                Log::info('     '.__FUNCTION__.'>> detect as Kementerian : ('.$obj->org_code.') '.$obj->org_name);
                var_dump('     '.__FUNCTION__.'>> detect as Kementerian : ('.$obj->org_code.') '.$obj->org_name);
                $account = self::createAccount($obj);
                self::saveAccountAddress($obj->org_profile_id, $account->id);
                $isCreated = true;
                return $account;
            }else{
                $objParent = PMService::getOrgGovWithParent($obj->org_profile_id);
                if($objParent && $objParent->parent_org_profile_id){
                    Log::info('     '.__FUNCTION__.'>> get parent account... parent_org_profile_id : '.$objParent->parent_org_type_id);
                    //$orgGovType = PMService::getOrgProfileType($objParent->parent_org_type_id);
                    $orgGovType = $objParent->parent_org_type_id;
                    $accParent = Account::where('org_gov_code',$objParent->parent_org_code)->where('org_gov_type',$orgGovType)->first();

                    if($accParent && $accParent->id){
                        Log::info('     '.__FUNCTION__.'>> found parent account in CRM... account ID : '.$accParent->id.' gov_code: '.$accParent->org_gov_code);
                        $account = self::createAccount($obj, $accParent->id);
                        self::saveAccountAddress($obj->org_profile_id, $account->id);
                        $isCreated = true;
                        return $account;
                    }else{
                        Log::error('     '.__FUNCTION__.'->> ERROR CREATE ACCOUNT: looking parent account not found ' . $obj->org_profile_id . ' : org_code ' . $obj->org_code);
                    }
                }
            }
        }else if ($obj->org_type_id && $obj->org_type_id == 7){
            //Refer as Factoring Company (Different Structure Table)
            $orgGovTypeDesc = PMService::getOrgProfileType($obj->org_type_id);
            $factoringAccount = PMService::getPMOrgGovFactoringDetail($obj->factoring_org_id);
            Log::info('     '.__FUNCTION__.'>> This account refer Type : '.$orgGovTypeDesc);
            //var_dump($factoringAccount);
            $account = self::createAccountFactoring($obj);
            self::saveAccountAddressFactoring($obj->factoring_org_id, $account->id);
            $isCreated = true;
            return $account;
            
        }else{
            //This type organization is no parent tree. 
            $orgGovTypeDesc = PMService::getOrgProfileType($obj->org_type_id);
            Log::info('     '.__FUNCTION__.'>> This account refer Type : '.$orgGovTypeDesc);
            $account = self::createAccount($obj);
            self::saveAccountAddress($obj->org_profile_id, $account->id);
            $isCreated = true;
            return $account;
        }
        if($isCreated == false){
            Log::error('     '.__FUNCTION__.'->> Error create account (ORG_PROFILE_ID): ' . $obj->org_profile_id . ' : org_code ' . $obj->org_code );
            var_dump('     '.__FUNCTION__.'->> Error create account (ORG_PROFILE_ID): ' . $obj->org_profile_id . ' : org_code ' . $obj->org_code );
            
            $remarks = ' org_code : '.$obj->org_code.' org_type : '.PMService::getOrgProfileType($obj->org_type_id).' org_profile_id : '.$obj->org_profile_id;
            CRMService::addErrorLogGovernmentIntegration($obj,'ACCOUNT_GOVERNMENT',$remarks );
        }
        return null;
    }
    
    /**
     * Create Account for Government Information 
     * 
     * @param type $rowData
     * @param type $type
     * @param type $parentId
     * @return type
     */
    public static function createAccount($rowData, $parentId = null) {

        $account = new Account;
        $account->id = Uuid::uuid4()->toString();
        $account->name = trim(strtoupper($rowData->org_name));
        $account->org_gov_code = $rowData->org_code;
        //$account->org_gov_type = PMService::getOrgProfileType($rowData->org_type_id);
        $account->org_gov_type = $rowData->org_type_id;
        $account->record_status = $rowData->op_record_status; //PMService::$RECORD_STATUS[$rowData->op_record_status];
        
        if(isset($rowData->eff_date) && $rowData->eff_date != null){
            $effDate = new Carbon($rowData->eff_date);
            //$effDate->subHour(8);
            $account->effective_from = $effDate->format('Y-m-d');
        }
        if(isset($rowData->exp_date) && $rowData->exp_date != null){
            $expDate = new Carbon($rowData->exp_date);
            //$expDate->subHour(8);
            $account->effective_to = $expDate->format('Y-m-d');
        }
        
        $account->date_entered = Carbon::now()->subHour(8);
        $account->date_modified = Carbon::now()->subHour(8);
        $account->created_by = PMService::$USER_LOGGED_ID;
        $account->modified_user_id = PMService::$USER_LOGGED_ID;
        $account->deleted = 0;
        $account->account_type = 'GOVERNMENT';
        if ($parentId) {
            $account->parent_id = $parentId;
            $account->parent_old_id = $parentId;
        }
        $account->save();
        
        if($rowData->org_type_id > 1 && $rowData->org_type_id < 6){
            $accountCustom = new AccountCustom;
            $accountCustom->id_c = $account->id;

            $accountCustom->gov_level_c = PMService::$OLD_ORG_GOVERNMENT_TYPE[$rowData->org_type_id];
            $accountCustom->gov_code_c = $rowData->org_code;
            if(PMService::$OLD_ORG_GOVERNMENT_TYPE[$rowData->org_type_id] == 'PTJ'){
                $accountCustom->jabatan_ptj_code_c = $rowData->org_code;
            }
            $accountCustom->save();
        }
        $orgTypeDesc = PMService::getOrgProfileType($rowData->org_type_id);
        Log::info('     Successfully save ' . $orgTypeDesc . ' : ' . $account->name . ' (' . $account->id . ')');
        var_dump('     Successfully save ' . $orgTypeDesc . ' : ' . $account->name . ' (' . $account->id . ')');
        //CRMService::addLogGovernmentIntegration($rowData,'ACCOUNT_GOVERNMENT', ' org_code : '.$account->org_gov_code.' org_type : '.$account->org_gov_type.' org_profile_id : '.$rowData->org_profile_id);
        
        
        return $account;
    }
    
    /**
     * Create Account for Factoring (Financial Org Account) Information 
     * 
     * @param type $rowData
     * @param type $type
     * @param type $parentId
     * @return type
     */
    public static function createAccountFactoring($rowData) {

        $account = new Account;
        $account->id = Uuid::uuid4()->toString();
        $account->name = trim(strtoupper($rowData->org_name));
        $account->org_gov_code = $rowData->org_code;
        $account->registration_no = $rowData->biz_reg_no;
        //$account->org_gov_type = PMService::getOrgProfileType($rowData->org_type_id);
        $account->org_gov_type = $rowData->org_type_id;
        $account->record_status = $rowData->fo_record_status; //PMService::$RECORD_STATUS[$rowData->fo_record_status];
        
        $account->date_entered = Carbon::now()->subHour(8);
        $account->date_modified = Carbon::now()->subHour(8);
        $account->created_by = PMService::$USER_LOGGED_ID;
        $account->modified_user_id = PMService::$USER_LOGGED_ID;
        $account->deleted = 0;
        $account->account_type = 'GOVERNMENT';
        $account->save();
        
        if($rowData->org_type_id > 1 && $rowData->org_type_id < 6){
            $accountCustom = new AccountCustom;
            $accountCustom->id_c = $account->id;
            $accountCustom->gov_level_c = PMService::$OLD_ORG_GOVERNMENT_TYPE[$rowData->org_type_id];
            $accountCustom->gov_code_c = $rowData->org_code;
            $accountCustom->save();
        }
        
        $orgTypeDesc = PMService::getOrgProfileType($rowData->org_type_id);
        Log::info('     Successfully save ' . $orgTypeDesc . ' : ' . $account->name . ' (' . $account->id . ')');
        var_dump('     Successfully save ' . $orgTypeDesc . ' : ' . $account->name . ' (' . $account->id . ')');
        //CRMService::addLogGovernmentIntegration($rowData,'ACCOUNT_GOVERNMENT', ' org_code : '.$account->org_gov_code.' org_type : '.$account->org_gov_type.' org_profile_id : '.$rowData->org_profile_id);
        
        
        return $account;
    }
    
    public static function saveAccountPrimary($obj,$accountId) {
        $account = Account::find($accountId);
        $account->name = $obj->org_name;
        $account->org_gov_code = $obj->org_code;
        //$account->org_gov_type = PMService::getOrgProfileType($obj->org_type_id);
        $account->org_gov_type = $obj->org_type_id;
        $account->record_status = $obj->op_record_status; //PMService::$RECORD_STATUS[$obj->op_record_status];
        
        if($obj->eff_date != null){
            $effDate = new Carbon($obj->eff_date);
            //$effDate->subHour(8);
            $account->effective_from = $effDate->format('Y-m-d');
        }
        if($obj->exp_date != null){
            $expDate = new Carbon($obj->exp_date);
            //$expDate->subHour(8);
            $account->effective_to = $expDate->format('Y-m-d');
        }
        if($obj->ov_record_status == 1){
            $account->deleted = 0;
        }else{
            $account->deleted = 1;
        }
        $account->date_modified = Carbon::now()->subHour(8);
        $account->modified_user_id = PMService::$USER_LOGGED_ID;
        $account->save();
        $orgTypeDesc = PMService::getOrgProfileType($obj->org_type_id);
        Log::info('     Successfully save ' . $orgTypeDesc. ' : ' . $account->name . ' (' . $account->org_gov_code . ')');
        var_dump('      Successfully save ' . $orgTypeDesc . ' : ' . $account->name . ' (' . $account->org_gov_code . ')');
        //CRMService::addLogGovernmentIntegration($obj,'ACCOUNT_GOVERNMENT', ' org_code : '.$account->org_gov_code.' org_type : '.$account->org_gov_type.' org_profile_id : '.$obj->org_profile_id);
  
 
    }
    
    public static function saveAccountFactoryPrimary($obj,$accountId) {
        $account = Account::find($accountId);
        $account->name = $obj->org_name;
        $account->org_gov_code = $obj->org_code;
        $account->registration_no = $obj->biz_reg_no;
        //$account->org_gov_type = PMService::getOrgProfileType($obj->org_type_id);
        $account->org_gov_type = $obj->org_type_id;
        $account->record_status = $obj->fo_record_status; //PMService::$RECORD_STATUS[$obj->fo_record_status];
        
        $account->date_modified = Carbon::now()->subHour(8);
        $account->modified_user_id = PMService::$USER_LOGGED_ID;
        $account->save();
        $orgTypeDesc = PMService::getOrgProfileType($obj->org_type_id);
        Log::info('     Successfully save ' . $orgTypeDesc. ' : ' . $account->name . ' (' . $account->org_gov_code . ')');
        var_dump('      Successfully save ' . $orgTypeDesc . ' : ' . $account->name . ' (' . $account->org_gov_code . ')');
        //CRMService::addLogGovernmentIntegration($obj,'ACCOUNT_GOVERNMENT', ' org_code : '.$account->org_gov_code.' org_type : '.$account->org_gov_type.' org_profile_id : '.$obj->org_profile_id);
    }
    
    
    public static function saveAccountAddress($orgProfileId, $accountId) {
        $listAddresses = PMService::getAddressDetails($orgProfileId);
        var_dump('     ->> $listAddresses total : ' . count($listAddresses));
        if(count($listAddresses) == 0){
            return;
        }
        self::updateAccountAddress($listAddresses,$accountId);
        
        
    }
    
    /**
     * Address for Financial Account (Factoring) 
     * @param type $financialOrgId
     * @param type $accountId
     * @return type
     */
    public static function saveAccountAddressFactoring($financialOrgId, $accountId) {
        $listAddresses = PMService::getAddressFactoringDetails($financialOrgId);
        var_dump('     ->> $listAddresses Factoring total : ' . count($listAddresses));
        if(count($listAddresses) == 0){
            return;
        }
        self::updateAccountAddress($listAddresses,$accountId);
    }
    
    public static function updateAccountAddress($listAddresses,$accountId) {
        $account = Account::find($accountId);
        $checkChanged = false;
        
        $billAddr = PMService::getAddressPriority($listAddresses, 'B');
                
        if ($billAddr && $billAddr->address_type_id != '') {
            $checkChanged = true;
            $account->billing_address_street = PMService::add_address_streets($billAddr->address_name,$billAddr->address_1, $billAddr->address_2, $billAddr->address_3);
            if(strlen($account->billing_address_street) > 255){
                $account->billing_address_street = substr($account->billing_address_street, 0, 254);
            }
            $account->billing_address_city = PMService::getCityName($billAddr->city_id);
            $account->billing_address_district = PMService::getDistrictName($billAddr->district_id);
            $account->billing_address_division = PMService::getDivisionName($billAddr->division_id);
            $account->billing_address_state = PMService::getStateName($billAddr->state_id);
            $account->billing_address_postalcode = $billAddr->postcode;
            if($billAddr->country_id == '131'){
                $account->billing_address_country = 'MALAYSIA';
            }else{
                $account->billing_address_country = PMService::getCountryName($billAddr->country_id);
            }
            if($billAddr->phone_country != '' && $billAddr->phone_area != '' && $billAddr->phone_no != ''){
                $account->phone_office = $billAddr->phone_country.$billAddr->phone_area.$billAddr->phone_no;
            }
            if($billAddr->fax_country != '' && $billAddr->fax_area != '' && $billAddr->fax_no != ''){
                $account->phone_fax = $billAddr->fax_country.$billAddr->fax_area.$billAddr->fax_no;
            }
            
            /** Update eMail Account **/
            $contactSupplier = PMService::getContactDetails($billAddr->address_type_id);
            if($contactSupplier){
                CRMService::saveEmailAccount($contactSupplier->email, $accountId);
            }
        }
        
        
        $deliverAddr = PMService::getAddressPriority($listAddresses, 'D');
        if ($deliverAddr && $deliverAddr->address_type_id != '') {
            $checkChanged = true;
            $account->shipping_address_street = PMService::add_address_streets($deliverAddr->address_name,$deliverAddr->address_1, $deliverAddr->address_2, $deliverAddr->address_3);
            $account->shipping_address_city = PMService::getCityName($deliverAddr->city_id);
            $account->shipping_address_district = PMService::getDistrictName($deliverAddr->district_id);
            $account->shipping_address_division = PMService::getDivisionName($deliverAddr->division_id);
            $account->shipping_address_state = PMService::getStateName($deliverAddr->state_id);
            $account->shipping_address_postalcode = $deliverAddr->postcode;
            if($deliverAddr->country_id == '131'){
                $account->shipping_address_country = 'MALAYSIA';
            }else{
                $account->shipping_address_country = PMService::getCountryName($deliverAddr->country_id);
            }
        }
        if($checkChanged == true){
            $account->date_modified = Carbon::now()->subHour(8);
            $account->modified_user_id = PMService::$USER_LOGGED_ID;
            $account->save();
            Log::info(__METHOD__.'     ->> Successfully save address by  : accountID ' . $accountId );
            var_dump('     ->> Successfully save address by : accountID ' . $accountId );
            //CRMService::addLogGovernmentIntegration($account,'ACCOUNT_GOVERNMENT_ADDRESS', ' org_code : '.$account->org_gov_code.' org_type : '.$account->org_gov_type);
        }
        
    }
    
    
    
    
    
    

}

