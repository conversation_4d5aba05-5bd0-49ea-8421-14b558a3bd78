<?php

namespace App\Migrate\Nextgen;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Models\LogScheduler;
use App\Migrate\MigrateUtils;
use App\Migrate\Nextgen\PMService;
use App\Migrate\Nextgen\CRMService;
use App\Migrate\Nextgen\SyncGovernmentInfo;

/*
 * This integration to sync data Org Gov. Profile CRM with Org Gov. Profile Nextgen
 * In current CRM   :   Kementerian,                        Jabatan,        PTJ
 * In Nextgen       :   Kementerian,   Pegawai Pengawal,    Kumpulan PTJ,   PTJ 
 * In Nextgen, Code Org Profile is not same with current eP. we have to sync, get new Code Org Profile sync with existing in CRM
 * 
 * 
 */

class SyncGovernmentUsersInfo {

    public static $QUERY_SKIP = 50;
    public static $QUERY_TAKE = 50;

    public static function runUpdateGovernmentUsersInfo(LogScheduler $logScheduler,$dateStart,$dateEnd) {
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();

        self::updateGovernmentUsersInfo($logScheduler,$dateStart,$dateEnd);
        $logScheduler->status = 'Completed';
        $logScheduler->save();

        var_dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    /**
     * DELAY + TIME INTERVAL
     * @param type $dateStart
     * @param type $dateEnd
     */
    protected static function updateGovernmentUsersInfo(LogScheduler $logScheduler,$dateStart,$dateEnd) {

        Log::info(self::class . ' Start ' . __FUNCTION__ . '     ->> Date Start: ' . $dateStart . ', Date End: ' . $dateEnd);
        var_dump('Date Start: ' . $dateStart);
        var_dump('Date End: ' . $dateEnd);


        $start = 0;
        $skip = self::$QUERY_SKIP;
        $take = self::$QUERY_TAKE;
        $totalRecordUpdate = 0;
        do {
            $nextSkip = $start++ * $skip;
            $results = PMService::getPMOrgGovUsersDetails($dateStart, $dateEnd, $take, $nextSkip);

            Log::info(' Count Total Query :- ' . count($results));
            
            if (count($results) > 0) {
                foreach ($results as $obj) {
                    //dd($obj);
                    $orgcode = $obj->org_code;
                    $orgTypeDesc = PMService::getOrgProfileType($obj->op_org_type_id);
                    var_dump('  '.__FUNCTION__. '> '.$orgTypeDesc . ' -> ' . $orgcode);
                    //var_dump('  ' . json_encode($obj));
                    var_dump('    Name :- ' . $obj->fullname);  
                    var_dump('     Identity No. :- ' . $obj->identification_no);  
                    var_dump('     Login ID :- ' . $obj->login_id); 
                    var_dump('     Status  :- ' . $obj->uo_record_status); 
                    Log::info('     ' . $orgTypeDesc . ' -> ' . $orgcode);
                    //Log::info('     ' . json_encode($obj));
                    Log::info('    Name :- ' . $obj->fullname);  
                    Log::info('     Identity No. :- ' . $obj->identification_no);  
                    Log::info('     Login ID :- ' . $obj->login_id); 
                    Log::info('     Status  :- ' . $obj->uo_record_status); 
                    $roles = PMService::getPMOrgGovUserRolesDetails($obj->user_org_id);
                    var_dump('      Count roles found  ' . count($roles));
                    $roles_name = null;
                    foreach ($roles as $objRole) {
                        if($roles_name == null){
                           $roles_name =  $objRole->role_code;
                        }else{
                           $roles_name = $roles_name.', '.$objRole->role_code;
                        }
                    }
                    Log::info('     ' . $roles_name);
                    var_dump('      '.$roles_name);
                    
                    
                    //Check Data from CRM
                    $account = CRMService::getGovProfileCrm($obj->org_code,$obj->op_org_type_id);
                    if($account == null){
                       $account = SyncGovernmentInfo::prepareCreateAccount($obj);
                    }
                    
                    if($account != null){
                        /** Let find and check in CRM **/
                        /** FIND account -> contact **/
                        $contact = DB::table('accounts as a')
                                ->join('accounts_contacts as ac', 'a.id', '=', 'ac.account_id')
                                ->join('contacts as c', 'ac.contact_id', '=', 'c.id')
                                ->where('a.id', $account->id)
                                ->where('c.identity_no_nextgen', trim($obj->identification_no))
                                ->select('a.name as acc_name', 'a.id as acc_id', 'a.org_gov_code', 'a.org_gov_type')
                                ->addSelect('c.first_name as contact_name', 'c.id as contact_id', 'c.identity_no_nextgen', 'c.user_id_nextgen')
                                ->first();
                        var_dump('          Check Contact is exist: ' . json_encode($contact));
                        if($contact == null){
                            //create it
                            if($account != null){
                                if($obj->uo_record_status == '1'){
                                    $contactObj = CRMService::createContactGovernment($account, $obj, $roles_name);
                                    $totalRecordUpdate++;
                                    Log::info('          :: ->  success create ' . $contactObj->id.' RECORD_STATUS: '.$contactObj->record_status_nextgen);
                                }else{
                                    Log::info('          :: ->  No need create this user . This user is belong Organization InActive');
                                }
                            }
                        }else{
                            //update it
                            $contactObj = CRMService::saveContactGovernment($obj, $roles_name, $contact);
                            CRMService::saveEmailContact($obj->email, $contact->contact_id);
                            $totalRecordUpdate++;

                        }
                    }
                    /*
                     * Sample result : Return each Object 
                      +"rn": "1"
                      +"user_id": "151931"
                      +"login_id": "3000657"
                      +"fullname": "KASIRAN BIN REJO"
                      +"nationality_id": "131"
                      +"org_type_id": "5"
                      +"identification_no": "3000657"
                      +"identification_type_id": "96"
                      +"designation": "PEGAWAI MEMERINTAH"
                      +"email": "<EMAIL>"
                      +"u_record_status": "1"
                      +"created_date": "2012-06-04 15:57:06"
                      +"changed_date": "2014-09-26 11:36:54"
                      +"mobile_country": null
                      +"mobile_area": null
                      +"mobile_no": null
                      +"phone_country": "60"
                      +"phone_area": "7"
                      +"phone_no": "7788242"
                      +"fax_country": null
                      +"fax_area": null
                      +"fax_no": "077788242"
                      +"salutation_id": "46"
                      +"uo_designation": "PEGAWAI MEMERINTAH"
                      +"uo_email": "<EMAIL>"
                      +"uo_record_status": "1"
                      +"uo_mobile_country": null
                      +"uo_mobile_area": null
                      +"uo_mobile_no": null
                      +"uo_phone_country": "60"
                      +"uo_phone_area": "7"
                      +"uo_phone_no": "7788242"
                      +"uo_fax_country": null
                      +"uo_fax_area": null
                      +"uo_fax_no": "077788242"
                      +"role_code": "PUBLICATION_APPROVER"
                      +"ur_created_date": "2017-03-27 19:54:41"
                      +"ur_changed_date": "2017-03-27 19:54:41"
                      +"org_profile_id": "308159"
                      +"op_org_type_id": "5"
                      +"org_name": "MARKAS 3 DIVISYEN"
                      +"org_code": "51340401"

                     */
                }
            }
        } while (count($results) > 0 && count($results) == $take);
        
        $logScheduler->total = $totalRecordUpdate;
        $logScheduler->save();
    }

}
