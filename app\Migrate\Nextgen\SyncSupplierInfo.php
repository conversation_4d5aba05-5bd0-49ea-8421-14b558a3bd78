<?php

namespace App\Migrate\Nextgen;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Ramsey\Uuid\Uuid;
use App\Models\Account;
use App\Models\AccountCustom;
use App\Migrate\MigrateUtils;
use App\Models\LogScheduler;
use App\Migrate\Nextgen\PMService;
use App\Migrate\Nextgen\SMService;
use App\Migrate\Nextgen\CRMService;

/*
 * This integration to sync data Org Gov. Profile CRM with Org Gov. Profile Nextgen
 * In current CRM   :   Kementerian,                        Jabatan,        PTJ
 * In Nextgen       :   Kementerian,   Pegawai Pengawal,    Kumpulan PTJ,   PTJ 
 * In Nextgen, Code Org Profile is not same with current eP. we have to sync, get new Code Org Profile sync with existing in CRM
 * 
 * 
 */

class SyncSupplierInfo {

    public static $USER_LOGGED_ID = '3';
    public static $QUERY_SKIP = 5000;
    public static $QUERY_TAKE = 5000;

    public static function runUpdateSupplierInfo(LogScheduler $logScheduler,$dateStart,$dateEnd) {
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();

       
            self::updateSupplierInfo($logScheduler,$dateStart, $dateEnd);
            $logScheduler->status = 'Completed';
            $logScheduler->save();
        
        
        
        var_dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
  
        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    /**
     * DELAY + TIME INTERVAL
     * @param type $dateStart
     * @param type $dateEnd
     */
    protected static function updateSupplierInfo(LogScheduler $logScheduler,$dateStart, $dateEnd) {

        Log::info(self::class . ' Start ' . __FUNCTION__ . '     ->> Date Start: ' . $dateStart . ', Date End: ' . $dateEnd);
        var_dump('Date Start: ' . $dateStart);
        var_dump('Date End: ' . $dateEnd);

        $start = 0;
        $skip = self::$QUERY_SKIP;
        $take = self::$QUERY_TAKE;
        $countSupplierUpdate = 0;
        $total = 0;
        
        do {
            $nextSkip = $start++ * $skip;
            $results = SMService::getSMSuppliersActive($dateStart, $dateEnd, $take, $nextSkip);

            Log::info(' Count Total Query :- ' . count($results));
            var_dump(' Count Total Query :- ' . count($results));
            $total = $total+count($results);
            foreach ($results as $obj) {
                
                //print_r($obj);
                
                //If branch_code is null -> mean refer to HQ supplier
                if($obj->branch_code == null){
                    $counter = $countSupplierUpdate++;
                    var_dump(' '.$counter.'). ePNo :- ' . $obj->ep_no);
                    Log::info(' '.$counter.'). ePNo :- ' . $obj->ep_no);
                    var_dump('     Company :- ' . $obj->company_name);  
                    var_dump('     Mof No :- ' . $obj->mof_no);  
                    var_dump('     Reg. No :- ' . $obj->reg_no); 
          
                    $account = CRMService::getSupplierCrm($obj);

                    if($account == null){
                        //Create Account
                        var_dump('     CREATE ACCOUNT :- ' . $obj->ep_no); 
                        $accountNew = CRMService::saveAccountSupplierPrimary($obj);
                        CRMService::saveAccountSupplierAddress($obj,$accountNew->id);
                    }else{
                        //Update Account
                        var_dump('     UPDATE ACCOUNT :- ' . $obj->ep_no); 
                        CRMService::saveAccountSupplierPrimary($obj, $account->id);
                        CRMService::saveAccountSupplierAddress($obj,$account->id);
                    }
                }
                /*
                [supplier_id] => 11936
                [company_name] => NFAS OMEGA
                [ep_no] => eP-1008F1416
                [reg_no] => *********-T
                [business_type] => F
                [s_status_id] =>
                [s_record_status] => 1
                [s_created_date] => 2007-11-14 09:08:49
                [s_changed_date] => 2017-05-15 14:00:02
                [a_record_status] => 1
                [appl_id] => 1714594
                [supplier_type] => K
                [appl_type] => R
                [appl_no] => KR-********-0001
                [a_status_id] => 20701
                [a_created_date] => 2017-05-15 12:27:01
                [a_changed_date] => 2017-05-16 12:29:13
                [cb_company_name] => NFAS OMEGA
                [cb_rev_no] => 1
                [phone_no] => ********
                [phone_country] => 6
                [phone_area] => 019
                [fax_no] =>
                [fax_country] => 6
                [fax_area] =>
                [website] =>
                [cb_record_status] => 1
                [company_basic_id] => 864525
                [cb_created_date] => 2017-05-15 12:27:01
                [cb_changed_date] => 2017-05-15 12:59:13
                [address_type] => C
                [ca_record_status] => 1
                [gst_reg_no] =>
                [gst_eff_date] =>
                [gst_end_date] =>
                [branch_code] =>
                [mof_no] => 357-02094129
                [ma_supplier_type] => K
                [mof_eff_date] => 2017-05-15 00:00:00
                [mof_exp_date] => 2020-05-14 00:00:00
                [mof_reg_status_id] => 2
                [sa_record_status] => 1
                [address_id] => 7202950
                [address_1] => NO.27, JALAN PUJ 8/7, TAMAN PUNCAK JALIL
                [address_2] => BANDAR PUTRA PERMAI,
                [address_3] =>
                [postcode] => 43300
                [country_id] => 131
                [state_id] => 11
                [division_id] =>
                [district_id] => 82
                [city_id] => 1606
                [sa_created_date] => 2017-05-15 12:27:01
                [sa_changed_date] => 2017-05-15 12:28:32

                 */
            }
        } while (count($results) > 0 && count($results) == $take);
        
        
        $logScheduler->total = $countSupplierUpdate;
        $logScheduler->save();
        
        var_dump('     Total Results :- ' . $total); 
        var_dump('     Total Results Update :- ' . $countSupplierUpdate); 
        Log::info('     Total Results :- ' . $total);
        Log::info('     Total Results Update :- ' . $countSupplierUpdate); 
    }

    
    /**
     * Create Account for Supplier Information 
     * 
     * @deprecated since version number 
     * @param type $rowData
     * @param type $type
     * @param type $parentId
     * @return type
     */
    protected static function createAccount($obj) {
        
        
        
        $account = new Account;
        $account->id = Uuid::uuid4()->toString();
        if($obj->mof_no != null){
            $account->mof_no = $obj->mof_no;
        }
        
        $account->record_status = $obj->s_record_status; //PMService::$RECORD_STATUS[$obj->s_record_status];

        /** When check branch_code is null mean, it refer to HQ Supplier**/
        if($obj->branch_code == null){
            $account->gst_no = $obj->gst_reg_no;
            if($obj->gst_eff_date){
                $gstEffDate = new Carbon($obj->gst_eff_date);
                //$gstEffDate->subHour(8);
                $account->gst_effective_date = $gstEffDate->format('Y-m-d');
            }
        }
        if($obj->mof_eff_date){
            $effDate = new Carbon($obj->mof_eff_date);
            //$effDate->subHour(8);
            $account->effective_from = $effDate->format('Y-m-d');
        }
        if($obj->mof_exp_date){
            $expDate = new Carbon($obj->mof_exp_date);
            //$expDate->subHour(8);
            $account->effective_to = $expDate->format('Y-m-d');
        }
        
        if($obj->supplier_type != null){
            $account->supplier_type = $obj->supplier_type; //SMService::$SUPPLIER_TYPE[$obj->supplier_type];
        }
        $account->ep_no = trim($obj->ep_no);
        if($obj->reg_no != null){
            $account->registration_no = strtoupper(trim($obj->reg_no));
        }        
        
        if($obj->appl_type != null){
            $account->appl_type = $obj->appl_type; //SMService::$APPL_TYPE[$obj->appl_type];
        }
        if($obj->a_status_id != null){
            $account->appl_status = SMService::getStatusDesc($obj->a_status_id); 
        }
        $account->appl_no = $obj->appl_no; 
        
        $account->website = $obj->website;
        if ($obj->phone_country != '' && $obj->phone_area != '' && $obj->phone_no != '') {
            $account->phone_office = $obj->phone_country . $obj->phone_area . $obj->phone_no;
        }
        if ($obj->fax_country != '' && $obj->fax_area != '' && $obj->fax_no != '') {
            $account->phone_fax = $obj->fax_country . $obj->fax_area . $obj->fax_no;
        }
        
        if($obj->s_record_status == 1){
            $account->deleted = 0;  
        }else{
            $account->deleted = 1; 
        }
        
        if($obj->mof_reg_status_id != null){
            $account->mof_reg_status = $obj->mof_reg_status_id;
        }
        
        $account->account_type = 'SUPPLIER';
        $account->date_entered = Carbon::now()->subHour(8);
        $account->date_modified = Carbon::now()->subHour(8);
        $account->created_by = PMService::$USER_LOGGED_ID;
        $account->modified_user_id = PMService::$USER_LOGGED_ID;
        
        $account->save();

        $accountCstm = new AccountCustom;
        $accountCstm->id_c = $account->id;
        $accountCstm->mof_no_c = $obj->mof_no;
        $accountCstm->appl_no_c = $obj->appl_no; 
        if($obj->a_status_id != null){
            $accountCstm->appl_status_c = SMService::getStatusDesc($obj->a_status_id); 
        }
        
        /** When check branch_code is null mean, it refer to HQ Supplier**/
        if($obj->branch_code == null){
            $accountCstm->gst_reg_no_c = $obj->gst_reg_no;
        }
        
        if($obj->mof_reg_status_id != null){
            $accountCstm->reg_status_c = $obj->mof_reg_status_id; //SMService::$MOF_REG_STATUS[$obj->mof_reg_status_id];
        }
        if($obj->mof_exp_date){
            $expDate = new Carbon($obj->mof_exp_date);
            //$expDate->subHour(8);
            $accountCstm->mof_expiry_c = $expDate->format('Y-m-d');
        }
        if($obj->reg_no != null){
            $accountCstm->ssm_no_c = strtoupper(trim($obj->reg_no));
        } 
        $accountCstm->save();
        
        Log::info('     Successfully save : ' . $account->name . ' (' . $account->id . ')');
        var_dump('     Successfully save  : ' . $account->name . ' (' . $account->id . ')');
        self::addLogIntegration($obj, 'ACCOUNT_SUPPLIER', ' org_code : ' . $account->org_gov_code . ' org_type : ' . $account->org_gov_type . ' org_profile_id : ' . $rowData->org_profile_id);


        return $account;
    }

    

    

}
