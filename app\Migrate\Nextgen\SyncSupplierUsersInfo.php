<?php

namespace App\Migrate\Nextgen;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Models\LogScheduler;
use App\Migrate\MigrateUtils;
use App\Migrate\Nextgen\SMService;
use App\Migrate\Nextgen\CRMService;

/*
 * This integration to sync data Org Gov. Profile CRM with Org Gov. Profile Nextgen
 * In current CRM   :   Kementerian,                        Jabatan,        PTJ
 * In Nextgen       :   Kementerian,   Pegawai Pengawal,    Kumpulan PTJ,   PTJ 
 * In Nextgen, Code Org Profile is not same with current eP. we have to sync, get new Code Org Profile sync with existing in CRM
 * 
 * 
 */

class SyncSupplierUsersInfo {

    public static $QUERY_SKIP = 50;
    public static $QUERY_TAKE = 50;

    public static function runUpdateSupplierUsersInfo(LogScheduler $logScheduler,$dateStart,$dateEnd) {
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();

      
            self::updateSupplierUsersInfo($logScheduler,$dateStart, $dateEnd);
            $logScheduler->status = 'Completed';
            $logScheduler->save();
        

        
        var_dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    /**
     * DELAY + TIME INTERVAL
     * @param type $dateStart
     * @param type $dateEnd
     */
    protected static function updateSupplierUsersInfo(LogScheduler $logScheduler,$dateStart, $dateEnd) {

        Log::info(self::class . ' Start ' . __FUNCTION__ . '     ->> Date Start: ' . $dateStart . ', Date End: ' . $dateEnd);
        var_dump('Date Start: ' . $dateStart);
        var_dump('Date End: ' . $dateEnd);


        $start = 0;
        $skip = self::$QUERY_SKIP;
        $take = self::$QUERY_TAKE;
        $counterUpdateUser = 0;
        do {
            $nextSkip = $start++ * $skip;
            $results = SMService::getSMSupplierUsersActive($dateStart, $dateEnd, $take, $nextSkip);

            Log::info(' Count Total Query :- '.count($results));
            var_dump(' Count Total Query :- '.count($results));
            
            if (count($results) > 0) {
                foreach ($results as $obj) {
                    
                    var_dump(' Check Company Name :- '.$obj->company_name);
                    var_dump('      eP No. :- '.$obj->ep_no);
                    $account = CRMService::getSupplierCrm($obj);

                    if($account == null){
                        //Create Account
                        var_dump('     CREATE ACCOUNT :- ' . $obj->ep_no); 
                        Log::info('     CREATE ACCOUNT :- ' . $obj->ep_no); 
                        /* Need get Details */
                        $supplierObj = SMService::getSMSuppliersDetail($obj->supplier_id);
                        //Log::info(json_encode($supplierObj));
                        $account = CRMService::saveAccountSupplierPrimary($supplierObj);
                        CRMService::saveAccountSupplierAddress($supplierObj,$account->id);
                    }
                    
                    /** FIND account -> contact **/
                    $query = DB::table('accounts as a')
                            ->join('accounts_contacts as ac', 'a.id', '=', 'ac.account_id')
                            ->join('contacts as c', 'ac.contact_id', '=', 'c.id')
                            ->where('ac.account_id', $account->id)
                            ->where('c.identity_no_nextgen', trim($obj->identification_no))
                            ->select('a.name as acc_name', 'a.id as acc_id')
                            ->addSelect('c.first_name as contact_name', 'c.id as contact_id', 'c.identity_no_nextgen', 'c.user_id_nextgen');
                    //var_dump($query->toSql());
                    $contact = $query->first();
                    var_dump('      Check Contact is exist: ' . json_encode($contact));
                    if($contact == null){
                        
                        if($obj->p_record_status == '1'){
                            $contactObj = CRMService::createContactSupplier($account, $obj);
                            $counterUpdateUser++;
                            Log::info('        :: ->  success create ' . $contactObj->id.' RECORD_STATUS: '.$contactObj->record_status_nextgen);
                            var_dump('        :: ->  success create ' . $contactObj->id.' RECORD_STATUS: '.$contactObj->record_status_nextgen);
                        }else{
                            Log::info('        :: ->  No need create this user . This user is InActive and not in record CRM');
                        }

                    }else{
                        //update it
                        $contactObj = CRMService::saveContactSupplier($obj, $contact);
                        CRMService::saveEmailContact($obj->p_email, $contact->contact_id);
                        $counterUpdateUser++;
                        var_dump('        :: ->  success update ' . $contactObj->id.' RECORD_STATUS: '.$contactObj->record_status_nextgen);
                        
                    }
                    
                }
            }
        } while (count($results) > 0 && count($results) == $take);
        
        $logScheduler->total = $counterUpdateUser;
        $logScheduler->save();
    }

    
    
    

    

}
