<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Cases extends Model {
    protected $primaryKey = "id";
    public $incrementing = false;
    public $timestamps = false;

    
    public function caseCustom() {
        return $this->hasOne('App\Models\CasesCustom', 'id_c');
    }

    public function tasks() {
        return $this->belongsToMany('App\Models\Tasks', 'tasks', 'parent_id')
                ->withPivot('id');
    }

}
