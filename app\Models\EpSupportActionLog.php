<?php

namespace App\Models;


use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Model;

/** This class mysql_ep_support re-pointing to mysql_ep_support table ep_action_log **/
class EpSupportActionLog extends Model {
    protected $connection= 'mysql_ep_support';
    protected $primaryKey = 'id';
    protected $table = "ep_action_log";
    
    public static function createActionLog($actionName,$actionType,$actionData,$actionParameter, $username = null){
        if($username == null){
           $username = Auth::user()->user_name ;
        }
        $actionLog  = new EpSupportActionLog;
        $actionLog->action_name = $actionName;
        $actionLog->action_type = $actionType;
        $actionLog->action_data = $actionData;
        $actionLog->action_parameter = $actionParameter;
        $actionLog->action_status = 'Processing';
        $actionLog->created_by = $username;
        $actionLog->save();
        
        return $actionLog;
    }
    
    public static function updateActionLog($actionLog,$status, $username = null){
        if($username == null){
           $username = Auth::user()->user_name ;
        }
        $actionLog->action_status = $status;
        $actionLog->updated_by = $username;
        $actionLog->save();
    }
    
    public static function saveActionLog($actionName,$actionType,$actionData,$actionParameter,$status, $username = null){
        if($username == null){
           $username = Auth::user()->user_name ;
        }
        $actionLog  = new EpSupportActionLog;
        $actionLog->action_name = $actionName;
        $actionLog->action_type = $actionType;
        $actionLog->action_data = $actionData;
        $actionLog->action_parameter = $actionParameter;
        $actionLog->action_status = $status;
        $actionLog->created_by = $username;
        $actionLog->save();
        
        return $actionLog;
    }
    
}
