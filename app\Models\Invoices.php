<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Invoices extends Model {

    protected $table = "aos_invoices";
    protected $primaryKey = "id";
    public $incrementing = false;
    public $timestamps = false;
    
    public function invoiceCustom() {
        return $this->hasOne('App\Models\InvoiceCustom', 'id_c');
    }

    public function accounts() {
        return $this->hasOne('App\Models\Account', 'id');
    }

    public function lineitem() {
        return $this->belongsToMany('App\Models\LineItem', 'aos_products_quotes', 'parent_id','group_id')
                        ->withPivot('id', 'name', 'date_entered','date_modified','modified_user_id','created_by','description',
                                'deleted','assigned_user_id','currency_id','part_number','item_description','number',
                                'product_qty','product_cost_price','product_list_price','product_discount','product_discount_amount',
                                'discount','product_unit_price','vat_amount','product_total_price','vat','parent_type','product_id');
    }

}
