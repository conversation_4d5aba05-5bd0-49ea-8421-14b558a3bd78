<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LineItemGroup extends Model {
    protected $table = "aos_line_item_groups";
    protected $primaryKey = "id";
    public $incrementing = false;
    public $timestamps = false;
   
//    public function invoice()
//    {
//        return $this->hasOne('App\Models\Invoices','id');
//    }
//    
//    public function lineitem()
//    {
//        return $this->hasMany('App\Models\LineItem','id');
//    }
}


