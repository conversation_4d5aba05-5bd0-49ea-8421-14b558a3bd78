<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LineItemProductStaff extends Model {
    protected $table = "aos_lineitem_product_staffs";
    protected $primaryKey = "id";
    public $incrementing = false;
    public $timestamps = false;
    
    public function lineItem() {
        return $this->hasOne('App\Models\LineItem', 'id');
    }
    
    public function lineItemCustom() {
        return $this->hasOne('App\Models\LineItemCustom', 'id_c');
    }
    
    public function invoice()
    {
        return $this->hasMany('App\Models\Invoices','id');
    }
}