<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class RegisteredUser extends Model {
    protected $table = "users";
    protected $primaryKey = "id";
    public $incrementing = false;
    public $timestamps = false;
    
    /**
     * The email that belong to the user.
     */
    public function emailsUserRelation() {
        return $this->belongsToMany('App\Models\EmailAddress', 'email_addr_bean_rel', 'bean_id', 'email_address_id')
                        ->withPivot('id', 'bean_module', 'primary_address', 'reply_to_address', 'date_created', 'date_modified', 'deleted');
    }

    /**
     * The email relation that belong to the user.
     */
    public function emailsUser() {
        return $this->belongsToMany('App\Models\EmailAddress', 'emails_beans', 'bean_id', 'email_id')
                        ->withPivot('id', 'bean_module', 'campaign_data', 'date_modified', 'deleted');
    }
    
    /**
     * The role relation that belong to the user.
     */
    public function rolesUser() {
        return $this->belongsToMany('App\Models\Roles', 'acl_roles_users', 'user_id', 'role_id')
                        ->withPivot('id', 'date_modified', 'deleted');
    }
    
    /**
     * The group relation that belong to the user.
     */
    public function groupUser() {
        return $this->belongsToMany('App\Models\Groups', 'securitygroups_users', 'user_id', 'securitygroup_id')
                        ->withPivot('id', 'date_modified', 'deleted', 'primary_group', 'noninheritable');
    }
    
}
