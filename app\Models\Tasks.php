<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Tasks extends Model {
    protected $table = "tasks";
    protected $primaryKey = "id";
    public $incrementing = false;
    public $timestamps = false;
    
    public function taskCustom() {
        return $this->hasOne('App\Models\TaskCustom', 'id_c');
    }

    public function cases()
    {
        return $this->hasOne('App\Models\Cases','id');
    }
}