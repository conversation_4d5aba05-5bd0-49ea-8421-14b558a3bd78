<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Trainee extends Model {
    
     //securitygroup
    public $fillablesecurity = ['id', 'name', 'date_entered', 'date_modified','modified_user_id','created_by', 'description','deleted','assigned_user_id','noninheritable'];

     //role
    public $fillablerole = ['id', 'date_entered', 'date_modified','modified_user_id','created_by','name', 'description','deleted'];
       
    //usergroup
    public $fillableuser =   ['id' , 'user_name', 'user_hash','system_generated_password','sugar_login',
                    'first_name','last_name', 'is_admin','external_auth_only',
                    'receive_notifications','description','date_entered','date_modified',
                    'modified_user_id','created_by','title','phone_home','phone_mobile',
                    'phone_work','phone_other','phone_fax','status',
                    'address_street','address_city','address_state','address_country',
                    'address_postalcode','deleted','portal_only','show_on_employees','is_group'];
    
   }