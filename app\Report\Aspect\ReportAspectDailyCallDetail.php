<?php

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2020-10-01 10:04:28
 * @modify date 2020-10-01 10:04:28
 */

namespace App\Report\Aspect;

use Carbon\Carbon;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\AspectDailyCallDetailExport;

class ReportAspectDailyCallDetail
{
    public function __construct()
    {
    }

    public static function run(Carbon $date)
    {
        $dateFrom = Carbon::parse($date)->startOfDay();
        $dateTo = Carbon::parse($date)->endOfDay();

        $dataList = self::getDataList($dateFrom, $dateTo);

        if ($dataList) {
            self::generateExcelReport($dataList, $dateFrom);
        }
    }

    public static function runConsole($date)
    {

        $dateFrom = Carbon::createFromFormat('Y-m-d H:i:s', $date . ' 00:00:00');
        $dateTo = Carbon::createFromFormat('Y-m-d H:i:s', $date . ' 23:59:59');

        $dataList = self::getDataList($dateFrom, $dateTo);

        if ($dataList) {
            self::generateExcelReport($dataList, $dateFrom);
        }
    }

    public static function generateExcelReport($data, $date)
    {
        $start = 0;
        $totalRecords = 0;

        $dtStartTimeOP = Carbon::now();

        ini_set('memory_limit', '-1');
        ini_set('max_execution_time', 300); //3 minutes

        $dateReport = Carbon::parse($date)->format('dmY');
        $fileName = 'Call_Details_eP_' . $dateReport;

        // Use Laravel Excel 3.x Export class
        Excel::store(new AspectDailyCallDetailExport($data, $fileName), 'exports/aspect/'.$fileName.'.xlsx');

        $dataReport = collect([]);
        $dataReport->put("report_date", $date);
        $dataReport->put("report_name", 'Daily Call Details');
        $dataReport->put("file_name", $fileName . '.xlsx');

        self::sendEmail($dataReport);
    }

    protected static function getDataList($dateFrom, $dateTo)
    {
        $result = DB::connection('sqlsrv_aspect')->select(
            "SELECT Dateadd(hour, 8, callstartdt) as calltime, service_id, user_id, callactionid, callactionreasonid, dnis, origserviceid, ani
            FROM   detail_epro..acdcalldetail
            WHERE  Dateadd(hour, 8, callstartdt) >= ?
            AND Dateadd(hour, 8, callstartdt) <= ?
            AND origserviceid IN ( 26, 27, 38, 39 ) ",
            array($dateFrom, $dateTo)
        );

        return $result;
    }

    protected static function  sendEmail($dataReport)
    {

        $data = array(
            // "to" => ['<EMAIL>'],
            // "cc" => ['<EMAIL>'],
            "to" => ['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>'],
            "cc" => ['<EMAIL>','<EMAIL>'],
            "subject" => "Server (" . env('APP_ENV') . ") > Report : " . $dataReport->get('report_name') . " on " . Carbon::parse($dataReport->get('report_date'))->format('d/m/Y'),

        );
        try {
            Mail::send('emails.aspect_daily_call_detail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'data' => $dataReport], function ($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])->cc($data["cc"])->subject($data["subject"]);
                // ->setBody('<a href="'.url('/crm/casedetail/download/'.$fileName).'" target =" blank"> Download Report </a>', 'text/html');
            });
            dump('done send');
        } catch (\Exception $e) {
            echo $e;
            Log::error(self::class . ' Error ... ' . __FUNCTION__ . ' ::  ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            dump('error' . $e);
            return $e;
        }
    }
}
