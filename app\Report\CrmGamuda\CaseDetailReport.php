<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Report\CrmGamuda;

use Carbon\Carbon;
use DB;
use Log;
use Excel;
use Mail;
use Config;
use App\Migrate\MigrateUtils;
use App\Services\CrmGamudaService;

class CaseDetailReport
{

    static $query_skip = 500;
    static $query_take = 500;

    public static function CrmGamudaService()
    {
        return new CrmGamudaService;
    }

    public static function run($startDate, $endDate)
    {
        dump(self::class . ' Starting ... ' . __FUNCTION__);
        Log::info(self::class . ' Starting ... ' . __FUNCTION__ . ' Start Date: ' . $startDate . ' End Date: ' . $endDate);
        $dtStartTime = Carbon::now();
        $report_path = storage_path('app/exports/crmgamuda') . '/';

        $case_dtl_rpt_name = 'CaseDtl_' . $startDate . '_' . $endDate;
        self::case_detail($startDate, $endDate, $case_dtl_rpt_name, $report_path);

        $dataReport = collect([]);
        $dataReport->put("report_group", 'Case Detail For CRM Gamuda ' . $startDate . '_' . $endDate);
        $dataReport->put("report_date", $startDate . '_' . $endDate);
        $dataReport->put("report_name", [$case_dtl_rpt_name]);
        $dataReport->put("file_name", [$case_dtl_rpt_name . '.csv']);
        $dataReport->put("report_path", [$report_path . $case_dtl_rpt_name . '.csv']);
        self::sendEmail($dataReport);

        dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    public static function case_detail($startDate, $endDate, $rpt_nm, $report_path)
    {
        $CsvData = array(
            'Case Number,Subject,Description,Resolution,Type,Channel,Email,Account Name,Office Phone,Phone Alternate,Priority,' .
                'Category,Category 2,Category 3,State,Status,Escalation Department,Escalation Email Address,Internal Update,Case Update Threaded,' .
                'Case Created By Name,Date Created,Date Modified'
        );

        $start = 0;
        $skip = self::$query_skip;
        $take = self::$query_take;
        $totalRecords = 0;
        $dtStartTimeOP = Carbon::now();

        do {
            $nextSkip = $start++ * $skip;
            $result = self::getCaseDetail($startDate, $endDate, $take, $nextSkip);
            $totalRecords = $totalRecords + count($result);
            $dtStartTimeEachLoop = Carbon::now();
            dump(self::class . ' current totalrecords ' . count($result));

            foreach ($result as $obj) {
                $emailAddress = null;
                $phoneOffice = null;
                $phoneAlternate = null;
                $assignTo = null;
                $caseThread = null;
                $description = null;
                $resolution = null;
                $subject = null;
                $accName = null;
                $createdBy = null;
                $category = null;
                $category2 = null;
                $category3 = null;
                $internalUpdate = null;

                if ($obj->name != '') {
                    $caseName = $obj->name;
                    $subject = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($caseName));
                }
                if ($obj->description != '') {
                    $desc = strip_tags($obj->description);
                    $trimdescription = preg_replace('!\s+!', ' ', trim($desc));
                    $trimdescriptionLen = '';
                    if ((strlen($trimdescription)) > 30000) {
                        $trimdescriptionLen = substr($trimdescription, 0, 10000);
                    } else {
                        $trimdescriptionLen = $trimdescription;
                    }
                    $description = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($trimdescriptionLen));
                }

                if ($obj->resolution != '') {
                    $res = strip_tags($obj->resolution);
                    $trimres = preg_replace('!\s+!', ' ', trim($res));
                    $trimresLen = '';
                    if ((strlen($trimres)) > 30000) {
                        $trimresLen = substr($trimres, 0, 10000);
                    } else {
                        $trimresLen = $trimres;
                    }
                    $resolution = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($trimresLen));
                }
                if ($obj->cstatus != '' && $obj->cstatus != 'New') {
                    $thread = self::CrmGamudaService()->getCaseThreaded($obj->id);
                    if (count($thread) > 0) {
                        $listGroupName = $thread->pluck('notename');
                        $caseThread = implode(";", $listGroupName->toArray());
                    }
                }
                if ($obj->phone_office != '') {
                    $phoneOffice = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($obj->phone_office));
                }
                if ($obj->phone_alternate != '') {
                    $phoneAlternate = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($obj->phone_alternate));
                }
                if ($obj->email != '') { //   /[,]+/
                    $emailAddress = trim($obj->email); //preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($obj->email));
                }
                if ($obj->assigned_user_id != '') {
                    $group = self::CrmGamudaService()->getSecurityGroup($obj->assigned_user_id);
                    if (count($group) > 0) {
                        $listGroupName = $group->pluck('name');
                        $assignGroup = implode(";", $listGroupName->toArray());
                        $assignTo = preg_replace('/[^a-zA-Z0-9\']/', ' ', $assignGroup);
                    }
                }

                if ($obj->accname != '') {
                    $accName = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($obj->accname));
                }
                if ($obj->createdby != '') {
                    $createdBy = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($obj->createdby));
                }
                if ($obj->modifiedby != '') {
                    $modifiedBy = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($obj->modifiedby));
                }
                if ($obj->cat != '') {
                    $category = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($obj->cat));
                }
                if ($obj->cat2 != '') {
                    $category2 = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($obj->cat2));
                }
                if ($obj->cat3 != '') {
                    $category3 = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($obj->cat3));
                }
                if ($obj->id != '') {
                    $caseUpdate = self::CrmGamudaService()->getCaseUpdate($obj->id);
                    if ($caseUpdate) {
                        $internalUpdate = 'internal_update';
                    }
                }
                $CsvData[] = ($obj->case_number . ',' .
                    $subject . ',' .
                    $description . ',' .
                    $resolution . ',' .
                    $obj->casetype . ',' .
                    $obj->channel . ',' .
                    $emailAddress . ',' .
                    $accName . ',' .
                    $phoneOffice . ',' .
                    $phoneAlternate . ',' .
                    $obj->priority . ',' .
                    $category . ',' .
                    $category2 . ',' .
                    $category3 . ',' .
                    $obj->state . ',' .
                    $obj->cstatus . ',' .
                    $obj->escalation_department. ',' .
                    $obj->escalation_email . ',' .
                    $internalUpdate . ',' .
                    $caseThread . ',' .
                    $createdBy . ',' .
                    $obj->createdat . ',' .
                    $obj->mofiedat . ','
                );
            }
            $takentimeeachLoop = array(
                'Counter' => $start,
                'Taken Time per Minutes' => $dtStartTimeEachLoop->diffInMinutes(Carbon::now()),
                'Taken Time per Seconds' => $dtStartTimeEachLoop->diffInSeconds(Carbon::now())
            );
            dump(self::class . '    :: LoopTakenTime >> Time   :   ', [$takentimeeachLoop]);
            dump(self::class . '    :: sum total current  :   ' . $totalRecords);
        } while (count($result) > 0 && count($result) == self::$query_take);

        $takentimeOP = array(
            'Counter' => $start,
            'Taken Time per Minutes' => $dtStartTimeOP->diffInMinutes(Carbon::now()),
            'Taken Time per Seconds' => $dtStartTimeOP->diffInSeconds(Carbon::now())
        );
        dump(self::class . '    :: AllLoopTakenTime >> Time   :   ', [$takentimeOP]);
        dump(self::class . ' queryReport. Total All :  ' . $totalRecords);
        dump(self::class . '--------------------------------------------');
        Log::info(self::class . '    :: AllLoopTakenTime >> Time   :   ', [$takentimeOP]);
        Log::info(self::class . ' queryReport. Total All :  ' . $totalRecords);

        $filename = $rpt_nm . ".csv";
        $file_path = $report_path . $filename;
        $file = fopen($file_path, "w+");
        foreach ($CsvData as $exp_data) {
            fputcsv($file, explode(',', $exp_data));
        }
        fclose($file);

        $dataReport = collect([]);
        $dataReport->put("date_start", $startDate);
        $dataReport->put("date_end", $endDate);
        $dataReport->put("report_name", $rpt_nm);
        $dataReport->put("file_name", $filename);
    }

    public static function getCaseDetail($startDate, $endDate, $take, $nextSkip)
    {
        dump('DateStart:' . $startDate, 'DateEnd:' . $endDate, 'Take:' . $take, 'Skip:' . $nextSkip);
        return DB::connection('mysql_crm_gamuda')
            ->select(
                "SELECT a.id, a.assigned_user_id,
                a.case_number,a.name,a.description,a.resolution,a.channel,a.escalation_department,a.escalation_email,
                (SELECT bb.email_address FROM email_addr_bean_rel aa,email_addresses bb WHERE aa.email_address_id = bb.id 
                AND aa.bean_id = b.id AND aa.deleted = 0 AND bb.deleted = 0 LIMIT 0,1) 'email',
                b.phone_office,b.phone_alternate,a.priority,
                (SELECT value_name FROM cstm_list_app casetype WHERE casetype.value_code = a.type AND casetype.type_code = 'case_type_dom') casetype,
                (SELECT value_name FROM cstm_list_app cat WHERE cat.value_code = a.category AND cat.type_code = 'category_list') cat,
                (SELECT value_name FROM cstm_list_app cat2 WHERE cat2.value_code = a.category_2 AND cat2.type_code = 'category_2_list') cat2,
                (SELECT value_name FROM cstm_list_app cat3 WHERE cat3.value_code = a.category_3 AND cat3.type_code = 'category_3_list') cat3,
                a.state,
                (SELECT value_name FROM cstm_list_app cstatus WHERE cstatus.value_code = a.status AND cstatus.type_code = 'case_status_dom') cstatus,
                (SELECT CASE WHEN first_name IS NULL THEN last_name ELSE CONCAT(first_name,last_name) END AS a FROM users WHERE id = a.created_by AND users.deleted = 0) createdby,
                (SELECT CASE WHEN first_name IS NULL THEN last_name ELSE CONCAT(first_name,last_name) END AS a FROM users WHERE id = a.modified_user_id AND users.deleted = 0) modifiedby,
                CONVERT_TZ(a.date_entered,'+00:00','+08:00') createdat,CONVERT_TZ(a.date_modified,'+00:00','+08:00')mofiedat, 
                b.name AS accname,
                CASE WHEN a.state = 'Open' THEN TIMEDIFF(NOW(),CONVERT_TZ(a.date_entered,'+00:00','+08:00'))
                ELSE TIMEDIFF(CONVERT_TZ(a.date_modified,'+00:00','+08:00'),CONVERT_TZ(a.date_entered,'+00:00','+08:00')) END AS fullaging
                FROM cases a
                LEFT JOIN accounts b ON b.id = a.account_id
                WHERE DATE(CONVERT_TZ(a.date_entered,'+00:00','+08:00')) BETWEEN '$startDate' AND '$endDate'
                AND a.deleted = 0 LIMIT $nextSkip,$take"
            );
    }

    /**
     * Send an e-mail report
     * @param  Request  $error
     * @return Response
     */
    protected static function sendEmail($dataReport)
    {
        $transport = (new \Swift_SmtpTransport(
            env('MAIL_EP_HOST', Config::get('constant.mail_ep_host_casb')),
            env('MAIL_EP_PORT', Config::get('constant.mail_ep_port_casb'))
        ))
            ->setEncryption(env('MAIL_EP_ENCRYPTION', Config::get('constant.mail_ep_encryption_casb')))
            ->setUsername(env('MAIL_EP_USERNAME', Config::get('constant.mail_ep_username_casb')))
            ->setPassword(env('MAIL_EP_PASSWORD', Config::get('constant.mail_ep_password_casb')));

        $mailer = app(\Illuminate\Mail\Mailer::class);
        $mailer->setSwiftMailer(new \Swift_Mailer($transport));

        $data = array(
            "to" => ['<EMAIL>'],
            "cc" => ['<EMAIL>'],
            // "to" => ['<EMAIL>'],
            // "cc" => ['<EMAIL>'],
            "subject" => $dataReport['report_group']
        );
        try {
            Mail::send('emails.reportcrmgamuda', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'data' => $dataReport], function ($m) use ($data, $dataReport) {
                $m->from(Config::get('constant.email_sender_casb'), Config::get('constant.email_sender_name_casb'));
                $m->to($data["to"])
                    ->cc($data["cc"])
                    ->subject($data["subject"]);
                //                foreach ($dataReport['report_path'] as $rpt_path) {
                //                    $m->attach($rpt_path);
                //                }
            });
            dump('done send');
        } catch (\Exception $e) {
            Log::error(self::class . ' Error ... ' . __FUNCTION__ . ' ::  ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }
}
