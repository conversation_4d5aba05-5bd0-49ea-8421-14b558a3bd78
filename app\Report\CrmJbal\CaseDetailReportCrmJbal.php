<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Report\CrmJbal;

use Carbon\Carbon;
use DB;
use Log;
use Mail;
use Config;
use App\Migrate\MigrateUtils;
use App\Services\CrmJbalService;

class CaseDetailReportCrmJbal {

    static $query_skip = 500;
    static $query_take = 500;

    public static function CrmJbalService() {
        return new CrmJbalService;
    }

    public static function run($startDate, $endDate, $daily=null) {
        dump(self::class . ' Starting ... ' . __FUNCTION__);
        Log::info(self::class . ' Starting ... ' . __FUNCTION__ . ' Start Date: ' . $startDate . ' End Date: ' . $endDate);
        $dtStartTime = Carbon::now();
        $report_path = storage_path('app/exports/crmjbal') . '/';

        $case_dtl_rpt_name = 'CaseDtl_' . $startDate . '_' . $endDate;
        self::case_detail($startDate, $endDate, $case_dtl_rpt_name, $report_path);

        $dataReport = collect([]);
        $dataReport->put("report_group", 'Case Detail For CRM JBAL ' . $startDate . '_' . $endDate);
        $dataReport->put("report_date", $startDate . '_' . $endDate);
        $dataReport->put("report_name", [$case_dtl_rpt_name]);
        $dataReport->put("file_name", [$case_dtl_rpt_name . '.csv']);
        $dataReport->put("report_path", [$report_path . $case_dtl_rpt_name . '.csv']);
        self::sendEmail($dataReport,$daily);

        dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    public static function case_detail($startDate, $endDate, $rpt_nm, $report_path) {
        $CsvData = array('Bil,Tarikh Kes,Jenis Kes,Kategori Kes,Kategori Kes (2),Diskripsi Kes,Butiran Kes,Medium Aduan,Kategori Pelanggan,'
            . 'Nama Pengadu,No. Telefon,Lokasi,DMA,No. Meter/ No. Akaun,No. Rujukan Aduan,Tindakan Kes Oleh (Seksyen),'
            . 'Maklumbalas(Respond-dalam tempoh 24 jam,Tarikh Pemakluman Kepada Pengguna,Jenis Pembaikan,'
            . 'Tarikh Arahan Pembaikan,Status Pembaikan,Tarikh Pembaikan Siap,Tempoh Pembaikan(Hari),Catatan,Dicipta Oleh (Agent)');

        $start = 0;
        $skip = self::$query_skip;
        $take = self::$query_take;
        $totalRecords = 0;
        $dtStartTimeOP = Carbon::now();

        do {
            $nextSkip = $start++ * $skip;
            $result = self::getCaseDetail($startDate, $endDate, $take, $nextSkip);
            $totalRecords = $totalRecords + count($result);
            $dtStartTimeEachLoop = Carbon::now();
            dump(self::class . ' current totalrecords ' . count($result));
            $no = 1;
            foreach ($result as $obj) {
                $tarikhKes = Carbon::parse($obj->created_date);
                $jenisKes = null;
                $kategoriKes = null;
                $kategoriKes2 = null;
                $butiranKes = null;
                $mediumAduan = null;
                $kategoriPelanggan = null;
                $namaPengadu = null;
                $noTelefon = null;
                $lokasi = null;
                $dma = null;
                $noMeter = null;
                $noRujukanAduan = null;
                $tindakanKes = null;
                $description = null;
                $maklumBalas = 'Kurang 24 jam';
                $tarikhPemakluman = null;
                $jenisPembaikan = null;
                $tarikhArahan = null;
                $statusPembaikan = null;
                $tarikhPembaikanSiap = null;
                $tempohPembaikan = null;
                $catatan = null;
                $agent = null;

                if ($obj->type != '') {
                    $jenisKes = self::CrmJbalService()->getValueLookupCRM('case_type_dom', $obj->type);
                }
                if ($obj->category != '') {
                    $kategoriKes = self::CrmJbalService()->getValueLookupCRM('category_list', $obj->category);
                }
                if ($obj->category_2 != '') {
                    $kategoriKes2 = self::CrmJbalService()->getValueLookupCRM('category_2_list', $obj->category_2);
                }
                if($obj->name != ''){
                    $trimname = preg_replace('!\s+!',' ', trim($obj->name));
                    $trimnamenLen = $trimname;
                    if((strlen($trimname)) > 30000){
                        $trimnamenLen = substr($trimname, 0, 10000); 
                    }
                    $name = preg_replace('/[,]+/', ' ', trim($trimnamenLen));
                    $butiranKes = $name;
                }
                if ($obj->channel != '') {
                    $mediumAduan = self::CrmJbalService()->getValueLookupCRM('channel_list', $obj->channel);
                }
                if ($obj->account_id != '') {
                    $account = self::CrmJbalService()->getAccount($obj->account_id);
                    if ($account) {
                        if($account->account_type != ''){
                            $kategoriPelanggan = self::CrmJbalService()->getValueLookupCRM('account_type_dom', $account->account_type);
                        }
                        $namaPengadu = $account->name;
                        $noTelefon = $account->phone_mobile;
                        $street = preg_replace('!\s+!',' ', trim($account->billing_address_street));
                        $city = preg_replace('!\s+!',' ', trim($account->billing_address_city));
                        $state = preg_replace('!\s+!',' ', trim($account->billing_address_state));
                        $code = preg_replace('!\s+!',' ', trim($account->billing_address_postalcode));
                        $country = preg_replace('!\s+!',' ', trim($account->billing_address_country));
                        $lokasi = preg_replace('/[,]+/', ' ', trim($street)). ' ' .preg_replace('/[,]+/', ' ', trim($city)) 
                                .' ' .preg_replace('/[,]+/', ' ', trim($state)) .' ' .preg_replace('/[,]+/', ' ', trim($code))
                                .' ' .preg_replace('/[,]+/', ' ', trim($country));
                        $noMeter = $account->company_reg_no;
                        if($account->dma != ''){
                            $dma = self::CrmJbalService()->getValueLookupCRM('dma_list', $account->dma);
                        }
                    }
                }
                if($obj->case_number != ''){
                    $noRujukanAduan = $obj->case_number;
                }
                if ($obj->assigned_user_id != '') {
                    $groups = self::CrmJbalService()->getSecurityGroup($obj->assigned_user_id);
                    if($groups){
                        $tindakanKes = $groups->name;
                    }
                }
                if($obj->description != ''){
                    $trimdescription = preg_replace('!\s+!',' ', trim($obj->description));
                    $trimdescriptionLen = $trimdescription;
                    if((strlen($trimdescription)) > 30000){
                        $trimdescriptionLen = substr($trimdescription, 0, 10000); 
                    } 
                    $description = preg_replace('/[,]+/', ' ', trim($trimdescriptionLen)); 
                }
                if ($obj->state == 'Closed') {
                    $dateModified = Carbon::parse($obj->date_modified);
                    $tempohPembaikan = $tarikhKes->diffInDays($dateModified);
                } else {
                    $aging = $tarikhKes->diffInHours(Carbon::now());
                    if ($aging > 24) {
                        $maklumBalas = 'Lebih 24 jam';
                    }
                }
                if($obj->resolution != ''){
                    $trimresolution = preg_replace('!\s+!',' ', trim($obj->resolution));
                    $trimresolutionLen = $trimresolution;
                    if((strlen($trimresolution)) > 30000){
                        $trimresolutionLen = substr($trimresolution, 0, 10000); 
                    }
                    $resolution = preg_replace('/[,]+/', ' ', trim($trimresolutionLen));
                    $catatan = $resolution;
                }
                $task = self::CrmJbalService()->getTask($obj->id);
                if($task){
                    if($task->tarikh_pemakluman != ''){
                        $tarikhPemakluman = Carbon::parse($task->tarikh_pemakluman);
                    }
                    if($task->jenis_pembaikan != ''){
                        $jenisPembaikan = self::CrmJbalService()->getValueLookupCRM('type_of_repair_list', $task->jenis_pembaikan);
                    }
                    if($task->arahan_pembaikan != ''){
                        $tarikhArahan = Carbon::parse($task->arahan_pembaikan);
                    }
                    if($task->pembaikan_siap != ''){
                        $tarikhPembaikanSiap = Carbon::parse($task->pembaikan_siap);
                    }
                    $statusPembaikan = $task->status_pembaikan;
                 }
                 if($obj->created_by != ''){
                     $userCreated = self::CrmJbalService()->getUserDetail($obj->created_by);
                     if ($userCreated) {
                        $agent = $userCreated->first_name . '' . $userCreated->last_name;
                    }
                 }
                $CsvData[] = (
                        $no++ . ',' .
                        $tarikhKes . ',' .
                        $jenisKes . ',' .
                        $kategoriKes . ',' .
                        $kategoriKes2 . ',' .
                        $description .',' .
                        $butiranKes . ',' .
                        $mediumAduan . ',' .
                        $kategoriPelanggan . ',' .
                        $namaPengadu . ',' .
                        $noTelefon . ',' .
                        $lokasi . ',' .
                        $dma . ',' .
                        $noMeter . ',' .
                        $noRujukanAduan . ',' .
                        $tindakanKes . ',' .
                        $maklumBalas . ',' .
                        $tarikhPemakluman . ',' .
                        $jenisPembaikan . ',' .
                        $tarikhArahan . ',' .
                        $statusPembaikan . ',' .
                        $tarikhPembaikanSiap . ',' .
                        $tempohPembaikan . ',' .
                        $catatan .','. 
                        $agent
                        );
            }
            $takentimeeachLoop = array(
                'Counter' => $start,
                'Taken Time per Minutes' => $dtStartTimeEachLoop->diffInMinutes(Carbon::now()),
                'Taken Time per Seconds' => $dtStartTimeEachLoop->diffInSeconds(Carbon::now())
            );
            dump(self::class . '    :: LoopTakenTime >> Time   :   ', [$takentimeeachLoop]);
            dump(self::class . '    :: sum total current  :   ' . $totalRecords);
        } while (count($result) > 0 && count($result) == self::$query_take);

        $takentimeOP = array(
            'Counter' => $start,
            'Taken Time per Minutes' => $dtStartTimeOP->diffInMinutes(Carbon::now()),
            'Taken Time per Seconds' => $dtStartTimeOP->diffInSeconds(Carbon::now())
        );
        dump(self::class . '    :: AllLoopTakenTime >> Time   :   ', [$takentimeOP]);
        dump(self::class . ' queryReport. Total All :  ' . $totalRecords);
        dump(self::class . '--------------------------------------------');
        Log::info(self::class . '    :: AllLoopTakenTime >> Time   :   ', [$takentimeOP]);
        Log::info(self::class . ' queryReport. Total All :  ' . $totalRecords);

        $filename = $rpt_nm . ".csv";
        $file_path = $report_path . $filename;
        $file = fopen($file_path, "w+");
        foreach ($CsvData as $exp_data) {
            fputcsv($file, explode(',', $exp_data));
        }
        fclose($file);

        $dataReport = collect([]);
        $dataReport->put("date_start", $startDate);
        $dataReport->put("date_end", $endDate);
        $dataReport->put("report_name", $rpt_nm);
        $dataReport->put("file_name", $filename);
    }

    public static function getCaseDetail($startDate, $endDate, $take, $nextSkip) {
        dump('DateStart:' . $startDate, 'DateEnd:' . $endDate, 'Take:' . $take, 'Skip:' . $nextSkip);
        return DB::connection('mysql_crm_jbal')
                        ->table('cases')
                        ->where('cases.deleted', 0)
                        ->whereBetween(DB::raw("DATE(CONVERT_TZ(cases.date_entered,'+00:00','+08:00'))"), [
                            $startDate,
                            $endDate
                        ])
                        ->select('cases.*')
                        ->addSelect(DB::raw("DATE(CONVERT_TZ(cases.date_entered,'+00:00','+08:00')) AS created_date"))
                        ->addSelect(DB::raw("DATE(CONVERT_TZ(cases.date_modified,'+00:00','+08:00')) AS modified_date"))
                        ->skip($nextSkip)->take($take)->get();
    }

    /**
     * Send an e-mail report
     * @param  Request  $error
     * @return Response
     */
    protected static function sendEmail($dataReport,$daily) {
        $transport = (new \Swift_SmtpTransport(
                env('MAIL_EP_HOST', Config::get('constant.mail_ep_host_casb')), env('MAIL_EP_PORT', Config::get('constant.mail_ep_port_casb'))))
                ->setEncryption(env('MAIL_EP_ENCRYPTION', Config::get('constant.mail_ep_encryption_casb')))
                ->setUsername(env('MAIL_EP_USERNAME', Config::get('constant.mail_ep_username_casb')))
                ->setPassword(env('MAIL_EP_PASSWORD', Config::get('constant.mail_ep_password_casb')));

        $mailer = app(\Illuminate\Mail\Mailer::class);
        $mailer->setSwiftMailer(new \Swift_Mailer($transport));

        if($daily == true){
            $data = array(
            "to" => ['<EMAIL>'],
            "cc" => ['<EMAIL>','<EMAIL>', '<EMAIL>'],
            "subject" => $dataReport['report_group']
        );
        }else{
            $data = array(
//           "to" => ['<EMAIL>'],
//           "cc" => ['<EMAIL>'],
            "to" => ['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>',
                '<EMAIL>','<EMAIL>'],
            "cc" => ['<EMAIL>', '<EMAIL>','<EMAIL>'],
            "subject" => $dataReport['report_group']
        );
        }
        
        try {
            Mail::send('emails.reportcrmjbal', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'data' => $dataReport], function($m) use ($data, $dataReport) {
                $m->from(Config::get('constant.email_sender_casb'), Config::get('constant.email_sender_name_casb'));
                $m->to($data["to"])
                        ->cc($data["cc"])
                        ->subject($data["subject"]);
                foreach ($dataReport['report_path'] as $rpt_path) {
                    $m->attach($rpt_path);
                }
            });
            dump('done send');
        } catch (\Exception $e) {
            Log::error(self::class . ' Error ... ' . __FUNCTION__ . ' ::  ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }

}
