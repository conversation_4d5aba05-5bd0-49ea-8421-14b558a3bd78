<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Report\CrmSsm;

use Carbon\Carbon;
use DB;
use Log;
use Excel;
use Mail;
use Config;
use App\Migrate\MigrateUtils;
use App\Services\CrmSsmService;

class OpenCaseDetailReport {

    static $query_skip = 500;
    static $query_take = 500;

    public static function CrmSsmService() {
        return new CrmSsmService;
    }

    public static function run() {
        dump(self::class . ' Starting ... ' . __FUNCTION__);
        Log::info(self::class . ' Starting ... ' . __FUNCTION__ );
        $dtStartTime = Carbon::now();
        $report_path = storage_path('app/exports/crmssm') . '/';

        $case_dtl_rpt_name = 'OpenCaseDtl';
        self::case_detail($case_dtl_rpt_name, $report_path);

        $dataReport = collect([]);
        $dataReport->put("report_group", 'Open Case Detail For CRM SSM ');
        $dataReport->put("report_name", [$case_dtl_rpt_name]);
        $dataReport->put("file_name", [$case_dtl_rpt_name . '.csv']);
        $dataReport->put("report_path", [$report_path . $case_dtl_rpt_name . '.csv']);
        self::sendEmail($dataReport);

        dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }
    
    public static function runOpenDaily($dateStart,$dateEnd) {
        dump(self::class . ' Starting ... ' . __FUNCTION__);
        Log::info(self::class . ' Starting ... ' . __FUNCTION__ );
        $dtStartTime = Carbon::now();
        $report_path = storage_path('app/exports/crmssm') . '/';

        $case_dtl_rpt_name = 'DailyOpenCaseDtl';
        self::case_detail($case_dtl_rpt_name, $report_path, $dateStart, $dateEnd);

        $dataReport = collect([]);
        $dataReport->put("report_group", 'Open Case Detail Daily For CRM SSM ');
        $dataReport->put("report_name", [$case_dtl_rpt_name]);
        $dataReport->put("file_name", [$case_dtl_rpt_name . '.csv']);
        $dataReport->put("report_path", [$report_path . $case_dtl_rpt_name . '.csv']);
        self::sendEmail($dataReport);

        dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    public static function case_detail($rpt_nm, $report_path, $dateStart=null, $dateEnd=null) {
        $CsvData = array('CASE NUMBER,SUBJECT,DESCRIPTION,RESOLUTION,CHANNEL,EMAIL,OFFICE PHONE,PHONE MOBILE,PHONE ALTERNATE,'
            . 'PRIORITY,CASE TYPE,CATEGORY,CATEGORY 2,CATEGORY 3,CATEGORY 4,CATEGORY 5,CATEGORY 6,STATE,STATUS,CREATED BY NAME,'
            . 'MODIFIED BY NAME,CREATED DATE,MODIFIED DATE,TASK CREATED DATE (LEVEL 2),TASK ACKNOWLEDGE TIME (LEVEL 2),TASK MODIFIED DATE (LEVEL 2),ACCOUNT NAME,INTERNAL UPDATE,ASSIGN TO,CASE UPDATES THREADED,'
            . 'TASK NAME(LEVEL 2),TASK STATUS (LEVEL 2),TASK CREATED BY (LEVEL 2), TASK MODIFIED BY (LEVEL 2),TASK ACKNOWLEDGE BY (LEVEL 2),'
            . 'CASE ASSIGNED BY LEVEL 1 TO LEVEL 2 (hh:mm:ss), CASE COMPLETED BY LEVEL 2 (hh:mm:ss), CASE ASSIGNED BY LEVEL 2 TO LEVEL 3 (hh:mm:ss),CASE COMPLETED BY LEVEL 3 (hh:mm:ss), TOTAL AGING (hh:mm:ss)');

        $start = 0;
        $skip = self::$query_skip;
        $take = self::$query_take;
        $totalRecords = 0;
        $dtStartTimeOP = Carbon::now();

        do {
            $nextSkip = $start++ * $skip;
            $result = self::getCaseDetail($take, $nextSkip, $dateStart, $dateEnd);
            $totalRecords = $totalRecords + count($result);
            $dtStartTimeEachLoop = Carbon::now();
            dump(self::class . ' current totalrecords ' . count($result));

            foreach ($result as $obj) {
                $emailAddress = null;
                $phoneOffice = null;
                $phoneMobile = null;
                $phoneAlternate = null;
                $assignTo = null;
                $caseThread = null;
                $description = null;
                $resolution = null;
                $subject = null;
                $accName = null;
                $createdBy = null;
                $modifiedBy = null;
                $category = null;
                $category2 = null;
                $category3 = null;
                $category4 = null;
                $category5 = null;
                $category6 = null;
                $taskCreatedBy = null;
                $taskModifiedBy = null;
                $taskAcknowledgeBy = null;
                $internalUpdate = null;

                if ($obj->name != '') {
                    $caseName = $obj->name;
                    $subject = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($caseName));
                }
                if ($obj->description != '') {
                    $desc = strip_tags($obj->description);
                    $trimdescription = preg_replace('!\s+!', ' ', trim($desc));
                    $trimdescriptionLen = '';
                    if ((strlen($trimdescription)) > 30000) {
                        $trimdescriptionLen = substr($trimdescription, 0, 10000);
                    } else {
                        $trimdescriptionLen = $trimdescription;
                    }
                    $description = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($trimdescriptionLen));
                }

                if ($obj->resolution != '') {
                    $res = strip_tags($obj->resolution);
                    $trimres = preg_replace('!\s+!', ' ', trim($res));
                    $trimresLen = '';
                    if ((strlen($trimres)) > 30000) {
                        $trimresLen = substr($trimres, 0, 10000);
                    } else {
                        $trimresLen = $trimres;
                    }
                    $resolution = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($trimresLen));
                }
                if ($obj->cstatus != '' && $obj->cstatus != 'New') {
                    $thread = self::CrmSsmService()->getCaseThreaded($obj->id);
                    if (count($thread) > 0) {
                        $listGroupName = $thread->pluck('notename');
                        $caseThread = implode(";", $listGroupName->toArray());
                    }
                }
                if ($obj->phone_office != '') {
                    $phoneOffice = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($obj->phone_office));
                }
                if ($obj->phone_mobile != '') {
                    $phoneMobile = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($obj->phone_mobile));
                }
                if ($obj->phone_alternate != '') {
                    $phoneAlternate = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($obj->phone_alternate));
                }
                if ($obj->email != '') { //   /[,]+/
                    $emailAddress = trim($obj->email); //preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($obj->email));
                }
                if ($obj->assigned_user_id != '') {
                    $group = self::CrmSsmService()->getSecurityGroup($obj->assigned_user_id);
                    if (count($group) > 0) {
                        $listGroupName = $group->pluck('name');
                        $assignGroup = implode(";", $listGroupName->toArray());
                        $assignTo = preg_replace('/[^a-zA-Z0-9\']/', ' ', $assignGroup);
                    }
                }
                
                if ($obj->accname != '') {
                    $accName = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($obj->accname));
                }
                if ($obj->createdby != '') {
                    $createdBy = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($obj->createdby));
                }
                if ($obj->modifiedby != '') {
                    $modifiedBy = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($obj->modifiedby));
                }
                if ($obj->cat != '') {
                    $category = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($obj->cat));
                }
                if ($obj->cat2 != '') {
                    $category2 = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($obj->cat2));
                }
                if ($obj->cat3 != '') {
                    $category3 = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($obj->cat3));
                }
                if ($obj->cat4 != '') {
                    $category4 = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($obj->cat4));
                }
                if ($obj->cat5 != '') {
                    $category5 = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($obj->cat5));
                }
                if ($obj->cat6 != '') {
                    $category6 = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($obj->cat6));
                }
                if ($obj->taskcreatedby != '') {
                    $taskCreatedBy = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($obj->taskcreatedby));
                }
                if ($obj->taskmodifiedby != '') {
                    $taskModifiedBy = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($obj->taskmodifiedby));
                }
                if ($obj->taskacknowledgeby != '') {
                    $taskAcknowledgeBy = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($obj->taskacknowledgeby));
                }
                if($obj->id != '') {
                    $caseUpdate = self::CrmSsmService()->getCaseUpdate($obj->id);
                    if($caseUpdate) {
                        $internalUpdate = 'internal_update';
                    } 
                }
                $CsvData[] = (
                        $obj->case_number . ',' .
                        $subject . ',' .
                        $description . ',' .
                        $resolution . ',' .
                        $obj->channel . ',' .
                        $emailAddress . ',' .
                        $phoneOffice . ',' .
                        $phoneMobile . ',' .
                        $phoneAlternate . ',' .
                        $obj->priority . ',' .
                        $obj->casetype . ',' .
                        $category . ',' .
                        $category2 . ',' .
                        $category3 . ',' .
                        $category4 . ',' .
                        $category5 . ',' .
                        $category6 . ',' .
                        $obj->state . ',' .
                        $obj->cstatus . ',' .
                        $createdBy . ',' .
                        $modifiedBy . ',' .
                        $obj->createdat . ',' .
                        $obj->mofiedat . ',' .
                        $obj->taskcreated . ',' .
                        $obj->taskacknowledgetime . ',' .
                        $obj->taskmodified . ',' .
                        $accName . ',' .
                        $internalUpdate . ',' .
                        $assignTo . ',' .
                        $caseThread . ',' .
                        $obj->taskname . ',' .
                        $obj->taskstatus . ',' .
                        $taskCreatedBy . ',' .
                        $taskModifiedBy . ',' .
                        $taskAcknowledgeBy .',' .
                        $obj->lvl1to2 . ',' .
                        $obj->lvl2complete . ',' .
                        $obj->lvl2to3 . ',' .
                        $obj->lvl3complete . ',' .
                        $obj->fullaging 
                        );
            }
            $takentimeeachLoop = array(
                'Counter' => $start,
                'Taken Time per Minutes' => $dtStartTimeEachLoop->diffInMinutes(Carbon::now()),
                'Taken Time per Seconds' => $dtStartTimeEachLoop->diffInSeconds(Carbon::now())
            );
            dump(self::class . '    :: LoopTakenTime >> Time   :   ', [$takentimeeachLoop]);
            dump(self::class . '    :: sum total current  :   ' . $totalRecords);
        } while (count($result) > 0 && count($result) == self::$query_take);

        $takentimeOP = array(
            'Counter' => $start,
            'Taken Time per Minutes' => $dtStartTimeOP->diffInMinutes(Carbon::now()),
            'Taken Time per Seconds' => $dtStartTimeOP->diffInSeconds(Carbon::now())
        );
        dump(self::class . '    :: AllLoopTakenTime >> Time   :   ', [$takentimeOP]);
        dump(self::class . ' queryReport. Total All :  ' . $totalRecords);
        dump(self::class . '--------------------------------------------');
        Log::info(self::class . '    :: AllLoopTakenTime >> Time   :   ', [$takentimeOP]);
        Log::info(self::class . ' queryReport. Total All :  ' . $totalRecords);

        $filename = $rpt_nm . ".csv";
        $file_path = $report_path . $filename;
        $file = fopen($file_path, "w+");
        foreach ($CsvData as $exp_data) {
            fputcsv($file, explode(',', $exp_data));
        }
        fclose($file);

        $dataReport = collect([]);
        $dataReport->put("report_name", $rpt_nm);
        $dataReport->put("file_name", $filename);
    }

    public static function getCaseDetail($take, $nextSkip, $dateStart, $dateEnd) {
        dump('Take:' . $take, 'Skip:' . $nextSkip);
        
        $whereDate = '';
        if($dateStart != null && $dateEnd != null){
            $whereDate = " AND DATE(CONVERT_TZ(a.date_entered,'+00:00','+08:00')) BETWEEN '$dateStart' AND '$dateEnd'";
        }
        return DB::connection('mysql_crm_ssm')
                        ->select("SELECT a.id, a.assigned_user_id,
                                    a.case_number,a.name,a.description,a.resolution,a.channel,
                                    (SELECT bb.email_address FROM email_addr_bean_rel aa,email_addresses bb WHERE aa.email_address_id = bb.id 
                                    AND aa.bean_id = b.id AND aa.deleted = 0 AND bb.deleted = 0 LIMIT 0,1) 'email',
                                    b.phone_office,b.phone_mobile,b.phone_alternate,a.priority,
                                    (SELECT value_name FROM cstm_list_app casetype WHERE casetype.value_code = a.type AND casetype.type_code = 'case_type_dom') casetype,
                                    (SELECT value_name FROM cstm_list_app cat WHERE cat.value_code = a.category AND cat.type_code = 'category_list') cat,
                                    (SELECT value_name FROM cstm_list_app cat2 WHERE cat2.value_code = a.category_2 AND cat2.type_code = 'category_2_list') cat2,
                                    (SELECT value_name FROM cstm_list_app cat3 WHERE cat3.value_code = a.category_3 AND cat3.type_code = 'category_3_list') cat3,
                                    (SELECT value_name FROM cstm_list_app cat4 WHERE cat4.value_code = a.category_4 AND cat4.type_code = 'category_4_list') cat4,
                                    (SELECT value_name FROM cstm_list_app cat5 WHERE cat5.value_code = a.category_5 AND cat5.type_code = 'category_5_list') cat5,
                                    (SELECT value_name FROM cstm_list_app cat6 WHERE cat6.value_code = a.category_6 AND cat6.type_code = 'category_6_list') cat6,
                                    a.state,
                                    (SELECT value_name FROM cstm_list_app cstatus WHERE cstatus.value_code = a.status AND cstatus.type_code = 'case_status_dom') cstatus,
                                    (SELECT CASE WHEN first_name IS NULL THEN last_name ELSE CONCAT(first_name,last_name) END AS a FROM users WHERE id = a.created_by AND users.deleted = 0) createdby,
                                    (SELECT CASE WHEN first_name IS NULL THEN last_name ELSE CONCAT(first_name,last_name) END AS a FROM users WHERE id = a.modified_user_id AND users.deleted = 0) modifiedby,
                                    CONVERT_TZ(a.date_entered,'+00:00','+08:00') createdat,CONVERT_TZ(a.date_modified,'+00:00','+08:00')mofiedat, 
                                    b.name AS accname,
                                    t.name AS taskname,
                                    (SELECT value_name FROM cstm_list_app WHERE cstm_list_app.value_code = t.status AND
                                     cstm_list_app.type_code = 'task_status_dom' AND t.deleted = 0) AS taskstatus, 
                                     CONVERT_TZ(t.date_entered,'+00:00','+08:00') AS taskcreated,
                                     CONVERT_TZ(t.date_modified,'+00:00','+08:00') AS taskmodified,
                                     CONVERT_TZ(t.acknowledge_time,'+00:00','+08:00') AS taskacknowledgetime,
                                    (SELECT CASE WHEN first_name IS NULL THEN last_name ELSE CONCAT(first_name,last_name) END AS a FROM users WHERE id = t.created_by AND users.deleted = 0) taskcreatedby,
                                    (SELECT CASE WHEN first_name IS NULL THEN last_name ELSE CONCAT(first_name,last_name) END AS a FROM users WHERE id = t.modified_user_id AND users.deleted = 0) taskmodifiedby,
                                    (SELECT CASE WHEN first_name IS NULL THEN last_name ELSE CONCAT(first_name,last_name) END AS a FROM users WHERE id = t.acknowledge_by AND users.deleted = 0) taskacknowledgeby,
                                    TIMEDIFF(CONVERT_TZ(t.date_entered,'+00:00','+08:00'),CONVERT_TZ(a.date_entered,'+00:00','+08:00')) AS lvl1to2,
                                    TIMEDIFF(CONVERT_TZ(t.date_modified,'+00:00','+08:00'),CONVERT_TZ(t.date_entered,'+00:00','+08:00')) AS lvl2complete,
                                    (SELECT TIMEDIFF(CONVERT_TZ(tasks.date_entered,'+00:00','+08:00'),CONVERT_TZ(t.date_entered,'+00:00','+08:00')) 
                                    FROM tasks WHERE tasks.parent_id = a.id AND tasks.assigned_user_id = '9c2c7e26-810d-0079-489f-62060612050d' LIMIT 1) AS lvl2to3, -- ssmfollowup
                                    (SELECT TIMEDIFF(CONVERT_TZ(tasks.date_modified,'+00:00','+08:00'),CONVERT_TZ(tasks.date_entered,'+00:00','+08:00')) 
                                    FROM tasks WHERE tasks.parent_id = a.id AND tasks.assigned_user_id = '9c2c7e26-810d-0079-489f-62060612050d' LIMIT 1) AS lvl3complete, -- ssmfollowup
                                    CASE WHEN a.state = 'Open' THEN TIMEDIFF(NOW(),CONVERT_TZ(a.date_entered,'+00:00','+08:00'))
                                    ELSE TIMEDIFF(CONVERT_TZ(a.date_modified,'+00:00','+08:00'),CONVERT_TZ(a.date_entered,'+00:00','+08:00')) END AS fullaging
                                    FROM cases a
                                    LEFT JOIN accounts b ON b.id = a.account_id
                                    LEFT JOIN tasks t ON t.parent_id = a.id AND t.assigned_user_id <> '9c2c7e26-810d-0079-489f-62060612050d' -- ssmemail/ssmurgent
                            WHERE a.state = 'Open'
                            $whereDate
                            AND a.deleted = 0 LIMIT $nextSkip,$take");
    }

    /**
     * Send an e-mail report
     * @param  Request  $error
     * @return Response
     */
    protected static function sendEmail($dataReport) {
        $transport = (new \Swift_SmtpTransport(
                env('MAIL_EP_HOST', Config::get('constant.mail_ep_host_casb')), env('MAIL_EP_PORT', Config::get('constant.mail_ep_port_casb'))))
                ->setEncryption(env('MAIL_EP_ENCRYPTION', Config::get('constant.mail_ep_encryption_casb')))
                ->setUsername(env('MAIL_EP_USERNAME', Config::get('constant.mail_ep_username_casb')))
                ->setPassword(env('MAIL_EP_PASSWORD', Config::get('constant.mail_ep_password_casb')));

        $mailer = app(\Illuminate\Mail\Mailer::class);
        $mailer->setSwiftMailer(new \Swift_Mailer($transport));

        $data = array(
//           "to" => ['<EMAIL>'],
//           "cc" => ['<EMAIL>'],
            "to" => ['<EMAIL>', '<EMAIL>', '<EMAIL>',
                '<EMAIL>','<EMAIL>','<EMAIL>', '<EMAIL>'],
            "cc" => ['<EMAIL>', '<EMAIL>'],
            "subject" => $dataReport['report_group']
        );
        try {
            Mail::send('emails.reportcrmssm', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'data' => $dataReport], function($m) use ($data, $dataReport) {
                $m->from(Config::get('constant.email_sender_casb'), Config::get('constant.email_sender_name_casb'));
                $m->to($data["to"])
                        ->cc($data["cc"])
                        ->subject($data["subject"]);
//                foreach ($dataReport['report_path'] as $rpt_path) {
//                    $m->attach($rpt_path);
//                }
            });
            dump('done send');
        } catch (\Exception $e) {
            Log::error(self::class . ' Error ... ' . __FUNCTION__ . ' ::  ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }

}
