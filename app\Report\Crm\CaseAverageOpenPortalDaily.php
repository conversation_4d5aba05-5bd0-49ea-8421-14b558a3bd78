<?php

namespace App\Report\Crm;

use Carbon\Carbon;
use DB;
use Log;
use Excel;
use Mail;
use Config;
use App\Migrate\MigrateUtils;
use App\Services\CRMService;

class CaseAverageOpenPortalDaily { 

    public function __construct() {
        
    }

    public function crmService() {
        return new CRMService;
    }

    public function run(Carbon $dateReport) {
        $CONTACT_MODE_PORTAL = 'Open Portal';
        $CONTACT_MODE_EMAIL = 'Email';

        $dataArray1 = $this->generateReportByDate($dateReport, $CONTACT_MODE_PORTAL);
        if ($dataArray1 != null) {
            $this->sendSuccessEmail($dataArray1, $CONTACT_MODE_PORTAL);
            dump("Completed! Send CaseAverageOpenPortalDaily to Email");
            Log::info(self::class . ' > ' . __FUNCTION__ . ' Completed!  Send CaseAverageOpenPortalDaily to Email');
        } else {
            dump("Error Send Email > CaseAverageOpenPortalDaily. No data received!");
            Log::info(self::class . ' > ' . __FUNCTION__ . ' Error Send Email > CaseAverageOpenPortalDaily. No data received!');
        }

        $dataArray2 = $this->generateReportByDate($dateReport, $CONTACT_MODE_EMAIL);
        if ($dataArray2 != null) {
            $this->sendSuccessEmail($dataArray2, $CONTACT_MODE_EMAIL);
            dump("Completed! Send CaseAverageEmail to Email");
            Log::info(self::class . ' > ' . __FUNCTION__ . ' Completed!  Send CaseAverageEmail to Email');
        } else {
            dump("Error Send Email > CaseAverageEmail. No data received!");
            Log::info(self::class . ' > ' . __FUNCTION__ . ' Error Send Email > CaseAverageEmail. No data received!');
        }
    }

    public function runTest(Carbon $dateReport) {
        $dataArray = $this->generateReportByDate($dateReport, 'Email');
        if ($dataArray != null) {
            $this->sendSuccessEmail($dataArray);
            dump("Completed! Send CaseAverageEmail to Email");
            Log::info(self::class . ' > ' . __FUNCTION__ . ' Completed!  Send CaseAverageEmail to Email');
        } else {
            dump("Error Send Email. No data received!");
            Log::info(self::class . ' > ' . __FUNCTION__ . ' Error Send Email. No data received!');
        }
    }

    public function generateReportByDate($dateReport, $contactMode) {
        Log::info(self::class . ' starting ..', ['Date' => $dateReport]);
        dump(self::class . ' starting ..', ['Date' => $dateReport]);
        $dtStartTime = Carbon::now();

        try {

            $averageDaily = $this->averageDailyCase($dateReport, $contactMode);
            $averageMonthlyCase = $this->averageMonthlyCase($dateReport, $contactMode);            
        
            $aboveSLADaily = $this->aboveSLADailyCase($dateReport, $contactMode);
            $aboveSLAMonthlyCase = $this->aboveSLAMonthlyCase($dateReport, $contactMode);            
         
            $belowSLADaily = $this->belowSLADailyCase($dateReport, $contactMode);
            $belowSLAMonthlyCase = $this->belowSLAMonthlyCase($dateReport, $contactMode);
            
            if ($averageDaily) {
                $caseAboveSLA = $averageDaily[0]->case_total - $averageDaily[0]->case_below_sla;
                $averageDaily[0]->case_above_sla = $caseAboveSLA;
            }

            if ($averageMonthlyCase) {
                $caseAboveSlaMonth = $averageMonthlyCase[0]->case_total_month - $averageMonthlyCase[0]->case_below_sla_month;
                $averageMonthlyCase[0]->case_above_sla_month = $caseAboveSlaMonth;
            }

            $dataArray = [
                'date' => $dateReport->toFormattedDateString(),
                'dateMonth' => $dateReport->format('F Y'),
                'averageDaily' => $averageDaily,
                'averageMonthlyCase' => $averageMonthlyCase,
                'aboveSLADaily' => $aboveSLADaily,
                'aboveSLAMonthlyCase' => $aboveSLAMonthlyCase,
                'belowSLADaily' => $belowSLADaily,
                'belowSLAMonthlyCase' => $belowSLAMonthlyCase,
                'contactMode' => $contactMode
            ];

            $logsdata = self::class . ' Query Date Start : ' . $dtStartTime . ' , '
                    . 'Query Date End : ' . Carbon::now() . ' , Completed --- Taken Time : ' .
                    json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);

            Log::info($logsdata);
            dump($logsdata);

            return $dataArray;
        } catch (\Exception $exc) {
            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            echo $exc->getTraceAsString();
        }

        return null;
    }

    /**
     * Send an e-mail as Success Logs
     *
     * @param  Request  $logsdata
     * @return Response
     */
    protected function sendSuccessEmail($dataArray) {

        $data = array(
//          "to" => ['<EMAIL>', '<EMAIL>'],
            "to" => ['<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>'],
            "subject" => 'Server (' . env('APP_ENV') . ') > CS Response Time (' . $dataArray['contactMode'] . ') on ' . $dataArray['date']. ' from 00:00am to 18:00pm'
        );
        $dataArray['subject'] = $data["subject"];
        try {
            Mail::send('emails.reportCaseAverageOpenPortalDaily', $dataArray, function($m) use ($data) {
                $m->from('<EMAIL>', 'Pentadbir');
                $m->to($data["to"])->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            echo $e;
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }

    protected function averageDailyCase($dateReport, $contactMode) {
        Log::info('Date :'. $dateReport->toDateString());
        Log::info('Contact Mode :'. $contactMode);
        $result = DB::select("SELECT ROUND(AVG(cs_actual_duration)/60,2) AS avg_time, COUNT(case_number) AS case_total, COUNT(cs_actual_duration <= 900) AS case_below_sla
        FROM poms_cs
        WHERE contact_mode IN (?)
        AND cs_actual_duration IS NOT NULL
        AND STR_TO_DATE(CONVERT_TZ(created_date,'+00:00','+08:00'),'%Y-%m-%d') = ?
        AND TIME_FORMAT(CONVERT_TZ(created_date,'+00:00','+08:00'), '%H:%i:%s') BETWEEN '00:00:00' AND '18:00:00'", array($contactMode,$dateReport->toDateString())); 
        Log::info($result);
        return $result;    
    }

    protected function averageMonthlyCase($dateReport,$contactMode) {
        /* add by asmaa / 19032020 */
        $yesterdayMonth = Carbon::now();
        $currentMonth = date('m');
        $currentYear = date('Y');
        $previousMonth = date_format($yesterdayMonth,"m");
        $previousYear = date_format($yesterdayMonth,"Y");        
        
        if (($previousMonth < $currentMonth) || ($previousYear < $currentYear)) {
            $FirstDateofMonth = Carbon::now()->subMonths(1)->startOfMonth(); 
            $LastDateofMonth = Carbon::now()->subMonths(1)->endOfMonth();
            $result = DB::select("SELECT ROUND(AVG(cs_actual_duration)/60,2) AS avg_time_month, COUNT(case_number) AS case_total_month, COUNT(cs_actual_duration <= 900) AS case_below_sla_month
                            FROM poms_cs
                            WHERE contact_mode IN (?)
                            AND cs_actual_duration IS NOT NULL
                            AND STR_TO_DATE(CONVERT_TZ(created_date,'+00:00','+08:00'),'%Y-%m-%d') BETWEEN ? AND ? + INTERVAL 1 DAY
                            AND TIME_FORMAT(CONVERT_TZ(created_date,'+00:00','+08:00'), '%H:%i:%s') BETWEEN '00:00:00' AND '18:00:00'", array($contactMode, $FirstDateofMonth->toDateString(), $LastDateofMonth->toDateString()));
        
            
        } else {
            $FirstDateofMonth = Carbon::now()->startOfMonth();
            $result = DB::select("SELECT ROUND(AVG(cs_actual_duration)/60,2) AS avg_time_month, COUNT(case_number) AS case_total_month, COUNT(cs_actual_duration <= 900) AS case_below_sla_month
                            FROM poms_cs
                            WHERE contact_mode IN (?)
                            AND cs_actual_duration IS NOT NULL
                            AND STR_TO_DATE(CONVERT_TZ(created_date,'+00:00','+08:00'),'%Y-%m-%d') BETWEEN ? AND ? + INTERVAL 1 DAY
                            AND TIME_FORMAT(CONVERT_TZ(created_date,'+00:00','+08:00'), '%H:%i:%s') BETWEEN '00:00:00' AND '18:00:00'", array($contactMode, $FirstDateofMonth->toDateString(), $dateReport->toDateString()));
        
        }

        return $result;
    }
    
    protected function aboveSLADailyCase($dateReport, $contactMode) {
        /* add by asmaa / 19032020 */
        $result = DB::select("SELECT COUNT(case_number) AS above_sla_daily
        FROM poms_cs
        WHERE contact_mode IN (?)
        AND cs_actual_duration > cs_available_duration
        AND cs_actual_duration IS NOT NULL
        AND STR_TO_DATE(CONVERT_TZ(created_date,'+00:00','+08:00'),'%Y-%m-%d') = ?
        AND TIME_FORMAT(CONVERT_TZ(created_date,'+00:00','+08:00'), '%H:%i:%s') BETWEEN '00:00:00' AND '18:00:00'", array($contactMode,$dateReport->toDateString()));
        return $result; 
    }   
    
     protected function aboveSLAMonthlyCase($dateReport, $contactMode) {
       /* add by asmaa / 19032020 */

        $yesterdayMonth = Carbon::now();
        $currentMonth = date('m');
        $currentYear = date('Y');
        $previousMonth = date_format($yesterdayMonth,"m");
        $previousYear = date_format($yesterdayMonth,"Y");

        if (($previousMonth < $currentMonth) || ($previousYear < $currentYear)) {
            $FirstDateofMonth = Carbon::now()->subMonths(1)->startOfMonth();
            $LastDateofMonth = Carbon::now()->subMonths(1)->endOfMonth();
            $result = DB::select("SELECT DISTINCT COUNT(*) AS above_sla_monthly
                                FROM poms_cs
                                WHERE contact_mode IN (?)
                                AND cs_actual_duration > cs_available_duration
                                AND cs_actual_duration IS NOT NULL
                                AND STR_TO_DATE(CONVERT_TZ(created_date,'+00:00','+08:00'),'%Y-%m-%d') BETWEEN ? AND ? + INTERVAL 1 DAY
                                AND TIME_FORMAT(CONVERT_TZ(created_date,'+00:00','+08:00'), '%H:%i:%s') BETWEEN '00:00:00' AND '18:00:00'", array($contactMode, $FirstDateofMonth->toDateString(), $LastDateofMonth->toDateString()));
        
            
        } else {
            $FirstDateofMonth = Carbon::now()->startOfMonth();
            $result = DB::select("SELECT DISTINCT COUNT(*) AS above_sla_monthly
                                FROM poms_cs
                                WHERE contact_mode IN (?)
                                AND cs_actual_duration > cs_available_duration
                                AND cs_actual_duration IS NOT NULL
                                AND STR_TO_DATE(CONVERT_TZ(created_date,'+00:00','+08:00'),'%Y-%m-%d') BETWEEN ? AND ? + INTERVAL 1 DAY
                                AND TIME_FORMAT(CONVERT_TZ(created_date,'+00:00','+08:00'), '%H:%i:%s') BETWEEN '00:00:00' AND '18:00:00'", array($contactMode, $FirstDateofMonth->toDateString(), $dateReport->toDateString()));
        
        }

        return $result;
    }

    protected function belowSLADailyCase($dateReport, $contactMode) {
        /* add by asmaa / 19032020 */
        $result = DB::select("SELECT COUNT(case_number) AS below_sla_daily
        FROM poms_cs
        WHERE contact_mode IN (?)
        AND cs_actual_duration <= cs_available_duration
        AND cs_actual_duration IS NOT NULL
        AND STR_TO_DATE(CONVERT_TZ(created_date,'+00:00','+08:00'),'%Y-%m-%d') = ?
        AND TIME_FORMAT(CONVERT_TZ(created_date,'+00:00','+08:00'), '%H:%i:%s') BETWEEN '00:00:00' AND '18:00:00'", array($contactMode,$dateReport->toDateString()));
        return $result;
        
        
    }   
    
     protected function belowSLAMonthlyCase($dateReport, $contactMode) {
        /* add by asmaa / 19032020 */
         
        $yesterdayMonth = Carbon::now(); 
        $currentMonth = date('m');
        $currentYear = date('Y');
        $previousMonth = date_format($yesterdayMonth,"m");
        $previousYear = date_format($yesterdayMonth,"Y");
        
         if (($previousMonth < $currentMonth) || ($previousYear < $currentYear)) {
           $FirstDateofMonth = Carbon::now()->subMonths(1)->startOfMonth(); 
           $LastDateofMonth = Carbon::now()->subMonths(1)->endOfMonth();
           $result = DB::select("SELECT DISTINCT COUNT(*) AS below_sla_monthly
                                FROM poms_cs
                                WHERE contact_mode IN (?)
                                AND cs_actual_duration <= cs_available_duration
                                AND cs_actual_duration IS NOT NULL
                                AND STR_TO_DATE(CONVERT_TZ(created_date,'+00:00','+08:00'),'%Y-%m-%d') BETWEEN ? AND ? + INTERVAL 1 DAY
                                AND TIME_FORMAT(CONVERT_TZ(created_date,'+00:00','+08:00'), '%H:%i:%s') BETWEEN '00:00:00' AND '18:00:00'", array($contactMode, $FirstDateofMonth->toDateString(), $LastDateofMonth->toDateString()));
        
           
         } else {
            $FirstDateofMonth = Carbon::now()->startOfMonth();
            $result = DB::select("SELECT DISTINCT COUNT(*) AS below_sla_monthly
                                    FROM poms_cs
                                    WHERE contact_mode IN (?)
                                    AND cs_actual_duration <= cs_available_duration
                                    AND cs_actual_duration IS NOT NULL
                                    AND STR_TO_DATE(CONVERT_TZ(created_date,'+00:00','+08:00'),'%Y-%m-%d') BETWEEN ? AND ? + INTERVAL 1 DAY
                                    AND TIME_FORMAT(CONVERT_TZ(created_date,'+00:00','+08:00'), '%H:%i:%s') BETWEEN '00:00:00' AND '18:00:00'", array($contactMode, $FirstDateofMonth->toDateString(), $dateReport->toDateString()));
        
         }
         

        return $result;
    }
}
