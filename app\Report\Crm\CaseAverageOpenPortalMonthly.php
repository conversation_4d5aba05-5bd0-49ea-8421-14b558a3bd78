<?php

namespace App\Report\Crm;

use Carbon\Carbon;
use DB;
use Log;
use Excel;
use Mail;
use Config;
use App\Migrate\MigrateUtils;
use App\Services\CRMService;

class CaseAverageOpenPortalMonthly {

    static $report_sheet = "Total Cases";
    static $query_skip = 500;
    static $query_take = 500;

    public function __construct() {
        
    }

    public function crmService() {
        return new CRMService;
    }

    public function run(Carbon $dateReport) {
        $CONTACT_MODE_PORTAL = 'Open Portal';
        $CONTACT_MODE_EMAIL = 'Email';
        $dataArrayExcel1 = $this->generateReportExcel($dateReport, $CONTACT_MODE_PORTAL);
        $dataArray1 = $this->generateReportByDate($dateReport, $CONTACT_MODE_PORTAL);
        if ($dataArray1 != null || $dataArrayExcel1 != null) {
            sleep(10);
            $this->sendSuccessEmail($dataArray1, $dateReport, $CONTACT_MODE_PORTAL);
            dump("Completed! Send CaseAverageOpenPortal to Email");
            Log::info(self::class . ' > ' . __FUNCTION__ . ' Completed!  Send CaseAverageOpenPortal to Email');
        } else {
            dump("Error Send Email > CaseAverageOpenPortal. No data received!");
            Log::info(self::class . ' > ' . __FUNCTION__ . ' Error Send Email > CaseAverageOpenPortal. No data received!');
        }
        $dataArrayExcel2 = $this->generateReportExcel($dateReport, $CONTACT_MODE_EMAIL);
        $dataArray2 = $this->generateReportByDate($dateReport, $CONTACT_MODE_EMAIL);
        if ($dataArray2 != null || $dataArrayExcel2 != null) {
            sleep(10);
            $this->sendSuccessEmail($dataArray2, $dateReport, $CONTACT_MODE_EMAIL);
            dump("Completed! Send CaseAverageEmail to Email");
            Log::info(self::class . ' > ' . __FUNCTION__ . ' Completed!  Send CaseAverageEmail to Email');
        } else {
            dump("Error Send Email > CaseAverageEmail. No data received!");
            Log::info(self::class . ' > ' . __FUNCTION__ . ' Error Send Email > CaseAverageEmail. No data received!');
        }
    }

    public static function generateReportExcel($dateReport, $contactMode) {

        $start = 0;
        $totalRecords = 0;

        $dtStartTimeOP = Carbon::now();
        $dateQueryReport = $dateReport->toDateString();

        $CsvData = array('Case Created On, Case Number, Case Status, Case Sub Status, Contact Mode, Request Type, Incident Type, Category, Sub Category, Sub Category 2, Available Duration, Actual Duration');

        do {

            $data = self::getQuery($dateReport, $contactMode);
            $totalRecords = $totalRecords + count($data);

            if (count($data) > 0) {
                foreach ($data as $obj) {

                    $CsvData[] = (
                            $obj->date_Log_Case . ',' .
                            $obj->caseNo . ',' .
                            $obj->caseState . ',' .
                            $obj->caseStatus . ',' .
                            $obj->contactMode . ',' .
                            $obj->requestType . ',' .
                            $obj->incidentService . ',' .
                            $obj->caseCategory . ',' .
                            $obj->caseSubCategory . ',' .
                            $obj->caseSubCategory2 . ',' .
                            $obj->cs_available_duration . ',' .
                            $obj->cs_actual_duration . ','
                            );
                }
            }
        } while (count($data) > 0 && count($data) == self::$query_take);

        $takentimeOP = array(
            'Counter' => $start,
            'Taken Time per Minutes' => $dtStartTimeOP->diffInMinutes(Carbon::now()),
            'Taken Time per Seconds' => $dtStartTimeOP->diffInSeconds(Carbon::now())
        );
        dump(self::class . ' ' . $dateQueryReport . '    :: AllLoopTakenTime >> Time   :   ', [$takentimeOP]);
        dump(self::class . ' queryReport. Total All :  ' . $totalRecords);
        dump(self::class . '--------------------------------------------');
        $year = $dateReport->format('Y');
        $month = $dateReport->format('m');
        $filename = 'TotalCasesCreatedFor' . $contactMode . '_on_' . $month . '_' . $year . ".csv";
        $file_path = storage_path() . '/app/exports/cases/' . $filename;
        $file = fopen($file_path, "w+");
        foreach ($CsvData as $exp_data) {
            fputcsv($file, explode(',', $exp_data));
        }
        fclose($file);

        $dataReport = collect([]);
        $dataReport->put("date_start", $dateQueryReport);
        $dataReport->put("report_name", 'Total Average Cases Created in CRM');
        $dataReport->put("file_name", $filename);
    }

    protected static function getQuery($dateQueryReport, $contactMode) {
        $year = $dateQueryReport->format('Y');
        $month = $dateQueryReport->format('m');
        $result = DB::select("SELECT DISTINCT
                            `c`.`case_number`              AS `caseNo`,
                            DATE(CONVERT_TZ(c.`date_entered`,'+00:00','+08:00'))             AS `date_Log_Case`,
                            `cc`.`request_type_c` AS `requestType`,
                            `cc`.`incident_service_type_c` AS `incidentService`,
                            `c`.`state`                   AS `caseState`,
                            `c`.`status`                   AS `caseStatus`,
                            `cc`.`contact_mode_c`          AS `contactMode`,
                            cc.`category_desc_c` AS caseCategory,
                            cc.`sub_category_desc_c` AS caseSubCategory,
                            cc.`sub_category_2_desc_c` AS caseSubCategory2,
                            c.`name` AS caseSubject,
                            (CASE WHEN (`c`.`cntc_mode_sla_startdate` IS NOT NULL) THEN TIMESTAMPDIFF(SECOND,CONVERT_TZ(`c`.`cntc_mode_sla_startdate`,'+00:00','+08:00'),CONVERT_TZ(`c`.`cntc_mode_sla_duedate`,'+00:00','+08:00')) END) AS `cs_available_duration`,
                            (CASE WHEN (`c`.`cntc_mode_sla_startdate` IS NOT NULL) THEN TIMESTAMPDIFF(SECOND,CONVERT_TZ(`c`.`cntc_mode_sla_startdate`,'+00:00','+08:00'),CONVERT_TZ(`c`.`cntc_mode_executiondate`,'+00:00','+08:00')) END) AS `cs_actual_duration`
                          FROM ((`cases` `c`
                              JOIN `cases_cstm` `cc`
                                ON ((`c`.`id` = `cc`.`id_c`)))
                             LEFT JOIN `cstm_list_app` `cmode`
                                ON (((`cc`.`contact_mode_c` = `cmode`.`value_code`)
                                    AND (TRIM(`cmode`.`value_code`) <> '')
                                    AND (`cmode`.`type_code` = 'cdc_contact_mode_list'))))
                          WHERE ((`c`.`date_entered` >= '2020-01-01')
                                 AND (`c`.`created_by` IN('1be60b16-974a-11c0-cb30-591eb0147a82','96fa954b-8288-4bef-ac39-5338a5b5c9c0'))
                                 AND  (cc.category_c NOT IN (10715,10722,10721,10719,10720))
                                 AND (YEAR(CONVERT_TZ(c.date_entered,'+00:00','+08:00')) = ?)
                                 AND (MONTH(CONVERT_TZ(c.date_entered,'+00:00','+08:00')) = ?)
                                 AND (`c`.`cntc_mode_sla_duedate` IS NOT NULL)
                                 AND (`c`.`cntc_mode_executiondate` IS NOT NULL)
                                 AND (`cc`.`contact_mode_c`  = ?)
                                 AND (`c`.`deleted` = 0))", array($year, $month, $contactMode));
        return $result;
    }

    public function generateReportByDate($dateReport, $contactMode) {
        Log::info(self::class . ' starting ..', ['Date' => $dateReport]);
        dump(self::class . ' starting ..', ['Date' => $dateReport]);
        $dtStartTime = Carbon::now();

        try {

            $averageMonthlyCase = $this->averageMonthlyCase($dateReport, $contactMode);
            $aboveSLAMonthlyCase = $this->aboveSLAMonthlyCase($dateReport, $contactMode);
            $belowSLAMonthlyCase = $this->belowSLAMonthlyCase($dateReport, $contactMode);

            if ($averageMonthlyCase) {
                $caseAboveSlaMonth = $averageMonthlyCase[0]->case_total_month - $averageMonthlyCase[0]->case_below_sla_month;
                $averageMonthlyCase[0]->case_above_sla_month = $caseAboveSlaMonth;
            } 
            $dataArray = [
                'date' => $dateReport->format('F Y'),
                'dateMonth' => $dateReport->format('F Y'),
                'averageMonthlyCase' => $averageMonthlyCase,
                'aboveSLAMonthlyCase' => $aboveSLAMonthlyCase,
                'belowSLAMonthlyCase' => $belowSLAMonthlyCase,
                'contactMode' => $contactMode
            ];

            $logsdata = self::class . ' Query Date Start : ' . $dtStartTime . ' , '
                    . 'Query Date End : ' . Carbon::now() . ' , Completed --- Taken Time : ' .
                    json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);

            Log::info($logsdata);
            dump($logsdata);

            return $dataArray;
        } catch (\Exception $exc) {
            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            echo $exc->getTraceAsString();
        }

        return null;
    }

    /**
     * Send an e-mail as Success Logs
     *
     * @param  Request  $logsdata
     * @return Response
     */
    protected function sendSuccessEmail($dataArray, $dateReport, $contactMode) {
        $dateGenerateReport = $dateReport->toDateString();
        $year = $dateReport->format('Y');
        $month = $dateReport->format('m');
        $filename = 'TotalCasesCreatedFor' . $contactMode . '_on_' . $month . '_' . $year . ".csv";
        $file_path = storage_path() . '/app/exports/cases/' . $filename;

        $data = array(
//            "to" => ['<EMAIL>',],
            "to" => ['<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>','<EMAIL>'],
            "subject" => 'Server (' . env('APP_ENV') . ') > CS Response Time (' . $dataArray['contactMode'] . ') on ' . $dataArray['date'],
            "att" => $file_path
        );
        $dataArray['subject'] = $data["subject"];
        try {
            Mail::send('emails.reportCaseAverageOpenPortalMonthly', $dataArray, function($m) use ($data) {
                $m->from('<EMAIL>', 'Pentadbir');
                $m->to($data["to"])->subject($data["subject"]);
                $m->attach($data["att"]);
            });
        } catch (\Exception $e) {
            $msg = '*[ALERT]* Failed send email for cs response time report.Please Check!!';
            self::notifyWhatsapp($msg);
            echo $e;
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }

    protected function notifyWhatsapp($msg) {
        DB::connection('mysql_ep_notify')
                ->insert('insert into notification  
                    (message,receiver_group,process,status,sent,date_entered,retry,source_app,source_class,source_remark) 
                    values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', [
                    $msg,
                    'CRM_REPORT_INTEGRATION',
                    'notify', 0, 0,
                    Carbon::now(), 0,
                    'CrmIntegration',
                    __CLASS__,
                    'This alert to notify error send report cs response time through email'
        ]);
    }

    protected function averageMonthlyCase($dateReport, $contactMode) { 
        $year = $dateReport->format('Y');
        $month = $dateReport->format('m');
        $result = DB::select("SELECT ROUND(AVG(cs_actual_duration)/60,2) AS avg_time_month, COUNT(case_number) AS case_total_month, COUNT(cs_actual_duration <= 900) AS case_below_sla_month
                            FROM poms_cs
                            WHERE contact_mode IN (?)
                            AND cs_actual_duration IS NOT NULL
                            AND YEAR(CONVERT_TZ(created_date,'+00:00','+08:00')) = ? 
                            AND MONTH(CONVERT_TZ(created_date,'+00:00','+08:00')) = ?", array($contactMode, $year, $month));


        return $result;
    }

    protected function aboveSLAMonthlyCase($dateReport, $contactMode) {
        $year = $dateReport->format('Y');
        $month = $dateReport->format('m');
        $result = DB::select("SELECT DISTINCT COUNT(*) AS above_sla_monthly
                                FROM poms_cs
                                WHERE contact_mode IN (?)
                                AND cs_actual_duration > cs_available_duration
                                AND cs_actual_duration IS NOT NULL
                                AND YEAR(CONVERT_TZ(created_date,'+00:00','+08:00')) = ?
                                AND MONTH(CONVERT_TZ(created_date,'+00:00','+08:00')) = ?", array($contactMode, $year, $month));


        return $result;
    }

    protected function belowSLAMonthlyCase($dateReport, $contactMode) { 
        $year = $dateReport->format('Y');
        $month = $dateReport->format('m'); 
        $result = DB::select("SELECT DISTINCT COUNT(*) AS below_sla_monthly
                                    FROM poms_cs
                                    WHERE contact_mode IN (?)
                                    AND cs_actual_duration <= cs_available_duration
                                    AND cs_actual_duration IS NOT NULL
                                    AND YEAR(CONVERT_TZ(created_date,'+00:00','+08:00')) = ?
                                    AND MONTH(CONVERT_TZ(created_date,'+00:00','+08:00')) = ?", array($contactMode, $year, $month));



        return $result;
    }

}
