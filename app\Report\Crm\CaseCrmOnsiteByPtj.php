<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Report\Crm;

use App\Services\CRMService;
use Carbon\Carbon;
use DB;
use Log;
use Excel;
use Mail;
use Config;
use App\Migrate\MigrateUtils;

class CaseCrmOnsiteByPtj {

    static $report_sheet = "Case Detail Onsite Support";
    static $query_skip = 500;
    static $query_take = 500;

    public static function crmService() {
        return new CRMService;
    }

    public static function run($dateStart, $dateEnd) {
        Log::debug(self::class . ' Starting ... Generate Case Detail Onsite Support');
        $dtStartTime = Carbon::now();
        self::createExcelReport($dateStart, $dateEnd);

        Log::debug(self::class . ' Completed Report Case Detail Onsite Support --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    public static function createExcelReport($dateStart, $dateEnd) {
        $dtStartTime = Carbon::now();

        self::generateReport($dateStart, $dateEnd);

        $durationTime = MigrateUtils::getTakenTime($dtStartTime);

        Log::debug(self::class . ' Taken Time :', ['Time' => $durationTime]);
    }

    public static function generateReport($dateStart, $dateEnd) {

        $start = 0;
        $totalRecords1 = 0;
        $totalRecords2 = 0;

        $dtStartTimeOP = Carbon::now();

        $CsvData = array('VISIT NO.,STATE,VISIT DATE,TIME,CRM No. (Ref),Top PTJ 500,PTJ CODE,PTJ NAME,MINISTRY,ACTIVITY LOCATION,ACTIVITY TYPE,MODULE,DESCRIPTION,RESOLUTION,USER NAME,TOTAL VISIT,ROLE IN EP,USER DESGINATION,USER CONTACT DETAILS,USER EMAIL,PAX,ONSITE (PIC),STATUS,MONTH');

        $datas = array();

        $data1 = self::getQuery1($dateStart, $dateEnd);
        $data2 = self::getQuery2($dateStart, $dateEnd);
        $totalRecords1 = $totalRecords1 + count($data1);
        $totalRecords2 = $totalRecords2 + count($data2);

        $totalRecords = $totalRecords1 + $totalRecords2;

        array_push($datas, $data1, $data2);
        $uniqueData = array_unique($datas);
        
        foreach ($uniqueData as $data) {

            foreach ($data as $obj) {
                $onsiteState = '';
                $onsiteDate = '';
                $onsiteTime = '';
                $caseNumberRelate = '';
                $topPtj = 'No';
                $ptjCode = '';
                $ptjName = '';
                $kementerian = '';
                $kementerianName = '';
                $onsiteLocation = '';
                $activityType = '';
                $module = '';
                $description = '';
                $resolution = '';
                $ptjRole = '';
                $ptjDesignation = '';
                $ptjPhone = '';
                $ptjEmail = '';
                $createdBy = '';
                $statusCase = '';
                $totalVisit = '';

                if ($obj->onsite_state_c != '') {
                    $onsiteState = self::crmService()->getValueLookupCRM('onsite_state_list', $obj->onsite_state_c);
                }

                if ($obj->onsite_date_c != '') {
                    $onsiteDate = Carbon::parse($obj->onsite_date_c)->format('Y-m-d');
                    $onsiteTime = Carbon::parse($obj->onsite_date_c)->format('H:i');
                } else {
                    $onsiteDate = Carbon::parse($obj->visit_start_date_c)->format('Y-m-d');
                    $onsiteTime = Carbon::parse($obj->visit_start_date_c)->format('H:i');
                }

                if ($obj->acase_id_c != '') {
                    $caseRelate = self::crmService()->getCaseRelate($obj->acase_id_c);

                    if ($caseRelate) {
                        $caseNumberRelate = $caseRelate->case_number;
                    }
                }

                if ($obj->account_id != '') {
                    $account = self::crmService()->getDetailAccountCRM($obj->account_id);

                    if ($account) {
                        if ($account->top_ptj_500 != '') {
                            $topPtj = 'Yes';
                        }

                        $accountType = $account->account_type;
                        if ($accountType == 'GOVERNMENT') {

                            $ptjCode = $account->org_gov_code;
                            $ptjName = preg_replace('/[^a-zA-Z0-9]/', ' ', trim($account->name));

                            if ($account->org_gov_type != '') {

                                if ($account->org_gov_type == '5') { //As PTJ
                                    $accountHirarchy = self::crmService()->getDetailAccountByPTJ($account->id);
                                    if ($accountHirarchy) {
                                        $kementerian = $accountHirarchy->kementerian_name;
                                        $pegawaiPengawal = $accountHirarchy->pegawaipengawal_name;
                                        $kumpulanPtj = $accountHirarchy->kumpulanptj_name;
                                    }
                                }
                                if ($account->org_gov_type == '4') { //As Kumpulan PTJ
                                    $accountHirarchy = self::crmService()->getDetailAccountByKumpulanPTJ($account->id);
                                    if ($accountHirarchy) {
                                        $kementerian = $accountHirarchy->kementerian_name;
                                        $pegawaiPengawal = $accountHirarchy->pegawaipengawal_name;
                                        $kumpulanPtj = $account->name;
                                    }
                                }
                                if ($account->org_gov_type == '3') { // As Pegawai Pengawal
                                    $accountHirarchy = self::crmService()->getDetailAccountPtjToKementerianCRMCaseReport($account->id);
                                    if ($accountHirarchy) {
                                        $kementerian = $accountHirarchy->kementerian_name;
                                        $pegawaiPengawal = $account->name;
                                    }
                                }
                                if ($account->org_gov_type == '2') { // As Kementerian
                                    $kementerian = $account->name;
                                }
                            }
                        }
                    }

                    $kementerianName = preg_replace('/[^a-zA-Z0-9]/', ' ', trim($kementerian));

                    if ($obj->onsite_location_c != '') {
                        $onsiteLocation = preg_replace('/[^a-zA-Z0-9]/', ' ', trim($obj->onsite_location_c));
                    }
                    if ($obj->sub_category_c != '') {
                        $activityType = self::crmService()->getValueLookupCRM('cdc_sub_category_list', $obj->sub_category_c);
                    }

                    if ($obj->sub_category_2_c != '') {
                        $module = self::crmService()->getValueLookupCRM('cdc_sub_category_2_list', $obj->sub_category_2_c);
                    }

                    $trimdescription = preg_replace('!\s+!', ' ', trim($obj->description));
                    $descriptionLen = '';
                    if ((strlen($trimdescription)) > 30000) {
                        $descriptionLen = substr($trimdescription, 0, 10000);
                    }
                    $description = preg_replace('/[,]+/', ' ', trim($descriptionLen));

                    $trimResolution = '';
                    if ((strlen($obj->resolution)) > 30000) {
                        $trimResolution = substr($obj->resolution, 0, 10000);
                    }
                    $resolution = preg_replace('/[^a-zA-Z0-9]/', ' ', trim($trimResolution));
                }

                if ($obj->created_by != '') {
                    $createdBy = self::crmService()->getNameUserCRM($obj->created_by);
                }

                if ($obj->status != '') {
                    $statusCase = self::crmService()->getValueLookupCRM('case_status_dom', $obj->status);
                }

                $contact = self::crmService()->getDetailContactCRM($obj->contact_id_c);

                $arrayPtj = '';
                if ($contact) {

                    $ptjRole = preg_replace('/[^a-zA-Z0-9]/', ' ', trim($contact->role_nextgen));
                    $ptjDesignation = preg_replace('/[^a-zA-Z0-9]/', ' ', trim($contact->designation_nextgen));
                    $ptjPhone = $contact->phone_mobile;
                    $ptjEmail = self::crmService()->getEmailCRM("Contacts", $contact->id);
                    $totalVisit = self::crmService()->getTotalVisitCntc($contact->id);
                    $cntc = preg_replace('/[^a-zA-Z0-9]/', ' ', trim($contact->first_name));
                }

                $searchForValue = ',';
                
                if (strpos($obj->ptj_user_name, $searchForValue) !== false) {
                    $arrayPtj = explode(',', $obj->ptj_user_name);
                }

                if ($arrayPtj != '') {
                    foreach ($arrayPtj as $ctc) {

                        $ptjDetails = self::crmService()->getDetailContactByFirstnameCRM(trim($ctc), $obj->account_id);
                        
                        if ($ptjDetails) {
                            if ($ptjDetails->first_name != $contact->first_name) {
                                // Log::info($obj->case_number .' ' .$ptjDetails->first_name .' ' .$contact->first_name);
                                $ptjRole = preg_replace('/[^a-zA-Z0-9]/', ' ', trim($ptjDetails->role_nextgen));
                                $ptjDesignation = preg_replace('/[^a-zA-Z0-9]/', ' ', trim($ptjDetails->designation_nextgen));
                                $ptjPhone = $ptjDetails->phone_mobile;
                                $ptjEmail = self::crmService()->getEmailCRM("Contacts", $ptjDetails->id);
                                $totalVisit = self::crmService()->getTotalVisitAddCntc($ptjDetails->first_name);
//                                Log::info($ptjDetails->first_name .' ' .$totalVisit);
                                $additionalCntc = preg_replace('/[^a-zA-Z0-9]/', ' ', trim($ptjDetails->first_name));

                                $CsvData[] = (
                                        $obj->case_number . ',' .
                                        $onsiteState . ',' .
                                        $onsiteDate . ',' .
                                        $onsiteTime . ',' .
                                        $caseNumberRelate . ',' .
                                        $topPtj . ',' .
                                        $ptjCode . ',' .
                                        $ptjName . ',' .
                                        $kementerianName . ',' .
                                        $onsiteLocation . ',' .
                                        $activityType . ',' .
                                        $module . ',' .
                                        $description . ',' .
                                        $resolution . ',' .
                                        $additionalCntc . ',' .
                                        $totalVisit . ',' .
                                        $ptjRole . ',' .
                                        $ptjDesignation . ',' .
                                        $ptjPhone . ',' .
                                        $ptjEmail . ',' .
                                        $obj->no_of_pax . ',' .
                                        $createdBy . ',' .
                                        $statusCase . ',' .
                                        Carbon::parse($onsiteDate)->format('m')
                                        );
                            }
                        }
                    }
                }

                $CsvData[] = (
                        $obj->case_number . ',' .
                        $onsiteState . ',' .
                        $onsiteDate . ',' .
                        $onsiteTime . ',' .
                        $caseNumberRelate . ',' .
                        $topPtj . ',' .
                        $ptjCode . ',' .
                        $ptjName . ',' .
                        $kementerianName . ',' .
                        $onsiteLocation . ',' .
                        $activityType . ',' .
                        $module . ',' .
                        $description . ',' .
                        $resolution . ',' .
                        $cntc . ',' .
                        $totalVisit . ',' .
                        $ptjRole . ',' .
                        $ptjDesignation . ',' .
                        $ptjPhone . ',' .
                        $ptjEmail . ',' .
                        $obj->no_of_pax . ',' .
                        $createdBy . ',' .
                        $statusCase . ',' .
                        Carbon::parse($onsiteDate)->format('m')
                        );
            }
        }

        $takentimeOP = array(
            'Counter' => $start,
            'Taken Time per Minutes' => $dtStartTimeOP->diffInMinutes(Carbon::now()),
            'Taken Time per Seconds' => $dtStartTimeOP->diffInSeconds(Carbon::now())
        );
        dump(self::class . '    :: AllLoopTakenTime >> Time   :   ', [$takentimeOP]);
        dump(self::class . ' queryReport. Total All :  ' . $totalRecords);
        dump(self::class . '--------------------------------------------');

        $filename = 'CaseDetailOnsiteSupport' . $dateStart . '_to_' . $dateEnd . ".csv";
        $file_path = storage_path() . '/app/exports/cases/' . $filename;
        $file = fopen($file_path, "w+");
        foreach ($CsvData as $exp_data) {
            fputcsv($file, explode(',', $exp_data));
        }
        fclose($file);

        $dataReport = collect([]);
        $dataReport->put("date_start", $dateStart);
        $dataReport->put("date_end", $dateEnd);
        $dataReport->put("report_name", 'Case Detail Onsite Support');
        $dataReport->put("file_name", $filename);

         self::sendEmail($dataReport);
    }

    protected static function getQuery1($dateStart, $dateEnd) {
        dump('getQuery1 DateStart:' . $dateStart, 'DateEnd:' . $dateEnd);
        $onsiteCategory = array('10719', '10720','10721','10722');

        $sql = DB::table('cases')
                ->join('cases_cstm', 'cases.id', '=', 'cases_cstm.id_c')
                ->where('cases.deleted', 0)
                ->whereIn('cases_cstm.category_c', $onsiteCategory);
        $sql->whereBetween(DB::raw("DATE(CONVERT_TZ(cases.`onsite_date_c`, '+00:00', '+08:00'))"), [
            $dateStart,
            $dateEnd
        ]);

        $sql->select('cases.*', 'cases_cstm.*');
        $sql->addSelect(DB::raw("CONVERT_TZ(cases.date_entered,'+00:00','+08:00') AS created_date"));
        $sql->addSelect(DB::raw("CONVERT_TZ(cases.date_modified,'+00:00','+08:00') AS modified_date"));
//        $sql->skip($nextSkip)->take($take);

        $data = array(
            "sql" => $sql->toSql(),
            "parameter" => $sql->getBindings()
        );

        dump(self::class . ' :: getQuery >> SQL   :   ', [$data]);
        return $result = $sql->get();
    }

    protected static function getQuery2($dateStart, $dateEnd) {
        dump('getQuery2 DateStart:' . $dateStart, 'DateEnd:' . $dateEnd);
        $onsiteCategory = array('10719', '10720','10721','10722');

        $sql = DB::table('cases')
                ->join('cases_cstm', 'cases.id', '=', 'cases_cstm.id_c')
                ->where('cases.deleted', 0)
                ->whereNull('cases.onsite_date_c')
                ->whereIn('cases_cstm.category_c', $onsiteCategory);
        $sql->whereBetween(DB::raw("DATE(CONVERT_TZ(cases_cstm.`visit_start_date_c`, '+00:00', '+08:00'))"), [
            $dateStart,
            $dateEnd
        ]);
        $sql->select('cases.*', 'cases_cstm.*');
        $sql->addSelect(DB::raw("CONVERT_TZ(cases.date_entered,'+00:00','+08:00') AS created_date"));
        $sql->addSelect(DB::raw("CONVERT_TZ(cases.date_modified,'+00:00','+08:00') AS modified_date"));
//        $sql->skip($nextSkip)->take($take);

        $data = array(
            "sql" => $sql->toSql(),
            "parameter" => $sql->getBindings()
        );

        dump(self::class . ' :: getQuery >> SQL   :   ', [$data]);
        return $result = $sql->get();
    }

    protected static function sendEmail($dataReport) {

        $data = array(
//            "to" => ['<EMAIL>','<EMAIL>'],
            "to" => ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>'],
            "subject" => "Server (" . env('APP_ENV') . ") > CRM Report : " . $dataReport->get('report_name') . " pada " . $dataReport->get('date_start') . " sehingga " . $dataReport->get('date_end'),
        );
        try {
            Mail::send('emails.generate_report_crm', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'data' => $dataReport], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])->subject($data["subject"]);
            });
            dump('done send');
        } catch (\Exception $e) {
            echo $e;
            Log::error(self::class . ' Error ... ' . __FUNCTION__ . ' ::  ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            dump('error' . $e);
            return $e;
        }
    }

}
