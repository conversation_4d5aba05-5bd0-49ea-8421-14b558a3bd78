<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Report\Crm;

use Carbon\Carbon;
use DB;
use Log;
use Excel;
use Mail;
use Config;
use App\Migrate\MigrateUtils;
use App\Services\CRMService;

class CaseDetailAllTask {
    
    public static function crmService() {
        return new CRMService;
    }

    public static function run($dateStart,$dateEnd) {
        dump(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();
        self::generateReportExcel($dateStart,$dateEnd);
        //self::export();
        
        dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }
 
    public static function getStartDate() {
        //return $date = Carbon::now()->month(2)->startOfMonth()->format('Y-m-d');
        return $date = Carbon::now()->startOfMonth()->format('Y-m-d');
        //return $date = Carbon::now()->format('Y-m-d');
    }
        
    public static function getEndDate() {
        //return $date = Carbon::now()->month(2)->endOfMonth()->format('Y-m-d');
        return $date = Carbon::now()->endOfMonth()->format('Y-m-d');
        //return $date = Carbon::now()->format('Y-m-d');
    }
    
    protected static function generateReportExcel($dateStart,$dateEnd) {
        $fileName = 'CaseDetailAllTask_'.$dateStart.'_to_'.$dateEnd;
        dump('filename : '.$fileName);
        Excel::create($fileName, function($excel) use ($dateStart,$dateEnd) {
            $excel->setTitle('Cases Detail All Task');
            
            //Run on 2018-04-04 10.30 AM
            $excel->sheet('Run on '.Carbon::now()->format('Y-m-d g.i A'), function($sheet) use ($dateStart,$dateEnd) {
                $sheet->setOrientation('landscape');

                $sheet->setStyle(array(
                    'font' => array(
                        'name' => 'Calibri',
                        'size' => 10,
                        'bold' => false
                    )
                ));
                
                $sheet->row(1, array(
                    'Kementerian','Kumpulan PTJ/Jabatan','PTJ/Organisasi','PTJ Code','Organisation Type',
                    'Company Name','MOF No','Leads Name','SSM No','Customer Type',
                    'Subject','Case Created','Case Modified','Case Modified By','Case Created By','Description',
                    'Assigned User Id','Case Number','Case Status','Case Resolution','Case State','Case Flag',
                    'Contact Mode Execution Date','Contact Mode Start Date','Contact Mode End Date', 'Portal Category',
                    'Onsite Date','Onsite Location','Source Reference','Onsite State','No of Pax','Pickup DateTime','Pickup By',
                    'Meeting Description','Meeting Month','Activity Type','CDC Representative 1','CDC Representative 2',
                    'Chairperson Designation','Chairperson Name','Deputy Director Head Account','Deputy Director Head Account Contact No',
                    'Deputy Director Head Account Email','Director Head Account','Director Head Account Contact No','Director Head Account Email',
                    'Meeting Venue','Mof Representative','Mof Representative 2','No of Attendance','No of Visit','PTJ Contact Detail',
                    'PTJ Designation','PTJ Email','PTJ Role','PTJ User Name','Secretary Contact Detail','Secretary Email','Secretary Name',
                    'CPTPP Enquiry','Category','Sub Category','Sub Sub Category','Contact Mode','Epc Location','Incident/Service Type','Request Type',
                    'Case Information','Visit Start Date','Visit End Date','Executive Name','Team Leader Name','Manager Name',
                    'Task Name','Task Created','Task Modified','Task Modified By','Task Created By','Task Description','Task Assigned User Id',
                    'Task Status','Date Start','Date Due','Redmine Number','Task Justification','Contract Quantity','Resolution Category',
                    'Task Severity','Batch Indicator','E-Kontrak Status','Reassign Time','Date Approve Redmine','Redmine Solution','Implementation Issue',
                    'Acknowledge Time','Assign Group','Task Resolution','SLA Start','SLA End','Acknowledge By','Task Number','GM Site','Date Execution',
                    'Sla Task Flag','Task Duration','Task Combine','Task Factor','Category Factor','Task Combine','Task Duration Day',
                    'Task Duration Hour','Task Reserved By','Poms Inserted'
                ));
                
                $count = 2;
                
                $start = 0;
                $skip = 1500;
                $take = 1500;
                $totalFirtSheet = 0;

                $dtStartTimeOP = Carbon::now();
                do {               
                    $dtStartTimeEachLoop = Carbon::now();
                    $nextSkip = $start++ * $skip;
                    $firstResults = self::queryReport($dateStart,$dateEnd,$take,$nextSkip);
                    $totalFirtSheet = $totalFirtSheet+count($firstResults);

                    foreach ($firstResults as $obj) {
                        
                        //Find Account
                        $account = null;
                        $kementerian = '';
                        $pegawaiPengawal = '';
                        $kumpulanPtj = '';
                        $orgName = '';
                        $supplierName = '';
                        $orgCode = '';
                        $mofno = '';
                        $ssmNo = '';
                        $accountType = '';
                        $organizationType = '';
                        $stateAccount = '';
                        
                        if($obj->account_id != ''){
                            $account = self::crmService()->getDetailAccountCRM($obj->account_id);
                            if($account){
                                $accountType = $account->account_type;
                            
                                if($account->billing_address_state != ''){
                                    $stateAccount = self::crmService()->getValueLookupCRM('state_list', $account->billing_address_state);
                                }

                                if($accountType == 'GOVERNMENT'){
                                    $orgName = $account->name;
                                    $orgCode = $account->org_gov_code;
                                    if($account->org_gov_type != ''){
                                        $organizationType = self::crmService()->getValueLookupCRM('accounts_nextgen_org_gov_code', $account->org_gov_type);
                                        
                                        if($account->org_gov_type == '5'){ //As PTJ
                                            $accountHirarchy = self::crmService()->getDetailAccountByPTJ($account->id);
                                            if($accountHirarchy){
                                                $kementerian =  $accountHirarchy->kementerian_name;
                                                $pegawaiPengawal =  $accountHirarchy->pegawaipengawal_name;
                                                $kumpulanPtj =  $accountHirarchy->kumpulanptj_name;
                                            }
                                        }
                                        if($account->org_gov_type == '4'){ //As Kumpulan PTJ
                                            $accountHirarchy = self::crmService()->getDetailAccountByKumpulanPTJ($account->id);
                                            if($accountHirarchy){
                                                $kementerian =  $accountHirarchy->kementerian_name;
                                                $pegawaiPengawal =  $accountHirarchy->pegawaipengawal_name;
                                                $kumpulanPtj =  $account->name; 
                                            }
                                        }
                                        if($account->org_gov_type == '3'){ // As Pegawai Pengawal
                                            $accountHirarchy = self::crmService()->getDetailAccountPtjToKementerianCRM($account->id);
                                            if($accountHirarchy){
                                                $kementerian =  $accountHirarchy->kementerian_name;
                                                $pegawaiPengawal =  $account->name;
                                            }
                                        }
                                        if($account->org_gov_type == '2'){ // As Kementerian
                                            $kementerian =  $account->name; 
                                        }
                                    }else {
                                        //org_gov_type is null (UPS)
                                        $accountHirarchy = self::crmService()->getDetailAccountByPTJ($account->id);
                                        if ($accountHirarchy) {
                                            $kementerian = $accountHirarchy->kementerian_name;
                                            $pegawaiPengawal = $accountHirarchy->pegawaipengawal_name;
                                            $kumpulanPtj = $accountHirarchy->kumpulanptj_name;
                                        }
                                    }
                                }else{
                                    $supplierName = $account->name;
                                    $mofno = $account->mof_no;
                                    $ssmNo = $account->registration_no;
                                } 
                            } 
                        }
                        
                        //Find Lead 
                        $lead = null;
                        $leadName = '';
                        if($account == null){
                            $lead = self::crmService()->getDetailLeadCRM($obj->id);
                            if($lead){
                                $leadName = $lead->first_name;
                                $ssmNo = $account->ssm_np_c;
                                $accountType = 'LEADS';

                                if($lead->primary_address_state != ''){
                                    $stateAccount = self::crmService()->getValueLookupCRM('state_list', $lead->primary_address_state);
                                } 
                            }
                            
                        }
                        $trimdescription = preg_replace('!\s+!',' ', trim($obj->description));
                        $trimdescriptionLen = '';
                        if((strlen($trimdescription)) > 30000){
                            $trimdescriptionLen = substr($trimdescription, 0, 10000); 
                        }else{
                            $trimdescriptionLen = $trimdescription;
                        }
                        $subject = preg_replace('/[,]+/', ' ', trim($obj->name));
                        $description = preg_replace('/[,]+/', ' ', trim($trimdescriptionLen));
                        $trimResolution = ''; 
                        $strLenReso = (strlen($obj->resolution));
                        if($strLenReso > 30000){
                            $trimResolution = substr($obj->resolution, 0, 10000); 
                        }else{
                            $trimResolution = $obj->resolution;
                        }
                        $resolution = preg_replace('/[^a-zA-Z0-9]/', ' ', trim($trimResolution));
                        
                        //Find User AssignTo
                        $assignTo = '';
                        if($obj->assigned_user_id != ''){
                            $assignTo = self::crmService()->getNameUserCRM($obj->assigned_user_id);
                        }
                        
                        $taskAssign = '';
                        if($obj->taskassigned != ''){
                            $taskAssign = self::crmService()->getNameUserCRM($obj->taskassigned);
                        }
                        
                        //Find User CreateBy
                        $createdBy = '';
                        if($obj->created_by != ''){
                            $createdBy = self::crmService()->getNameUserCRM($obj->created_by);
                        }
                        
                        //Find Group for CreatedBy
                        //getListDetailGroupCRM
                        $groupCreatedBy = '';
                        if($obj->created_by != ''){
                            $listGroup= self::crmService()->getListDetailGroupCRM($obj->created_by);
                            if(count($listGroup) > 0){
                                $listGroupName = $listGroup->pluck('name');
                                $groupCreatedBy = implode(",",$listGroupName->toArray());
                            }
                        }
                        
                        //Find User ModifiedBy
                        $modifiedBy = '';
                        if($obj->modified_user_id != ''){
                            $modifiedBy = self::crmService()->getNameUserCRM($obj->modified_user_id);
                        }
                        
                        $taskCreatedBy = '';
                        if($obj->taskcreatedby != ''){
                            $taskCreatedBy = self::crmService()->getNameUserCRM($obj->taskcreatedby);
                        }
                        
                        $taskModifiedBy = '';
                        if($obj->taskmodifiedby != ''){
                            $taskModifiedBy = self::crmService()->getNameUserCRM($obj->taskmodifiedby);
                        }
                        
                        $taskJustification = '';
                        if($obj->task_justification != ''){
                            $taskJustification = self::crmService()->getValueLookupCRM('task_justification', $obj->task_justification);
                        } 
                        
                        $taskResolutionCategory = '';
                        if($obj->resolution_category_c != ''){
                            $taskResolutionCategory = self::crmService()->getValueLookupCRM('task_resolution_category_list', $obj->resolution_category_c);
                        } 
                        
                        $taskRedmineSolution = '';
                        if($obj->redmine_solution != ''){
                            $taskRedmineSolution = self::crmService()->getValueLookupCRM('task_redmine_solution', $obj->redmine_solution);
                        } 
                        
                        $taskImplementationIssue = '';
                        if($obj->implementation_issue != ''){
                            $taskImplementationIssue = self::crmService()->getValueLookupCRM('task_implementation_issue', $obj->implementation_issue);
                        } 
                        
                        $acknowledgeBy = '';
                        if($obj->acknowledge_by_userid_c != ''){
                            $acknowledgeBy = self::crmService()->getNameUserCRM($obj->acknowledge_by_userid_c);
                        }   
                        
                        $gmSite = ''; 
                        if($obj->gm_site_c != ''){
                            $gmSite = self::crmService()->getValueLookupCRM('task_gm_site', $obj->gm_site_c);
                        }    
                        
                        $taskCategoryFactor = '';
                        if($obj->category_factor_c != ''){
                            $taskCategoryFactor = self::crmService()->getValueLookupCRM('task_factor_list', $obj->category_factor_c);
                        } 
                        
                        $taskCombine = ''; 
                        if($obj->tasks_combine_c != ''){
                            $taskCombine = self::crmService()->getValueLookupCRM('combine_subcategory2_list', $obj->tasks_combine_c);
                        }
                        
                        $taskReserve = '';
                        if($obj->reserve_task_userid != ''){
                            $taskReserve = self::crmService()->getNameUserCRM($obj->reserve_task_userid);
                        }
                        
                        //Find Contact RequestedBy
                        $requestedBy = '';
                        $emailContact = '';
                        if($obj->contact_id_c != ''){
                            $requestedBy = self::crmService()->getNameContactCRM($obj->contact_id_c);
                            $emailContact = self::crmService()->getEmailCRM("Contacts", $obj->contact_id_c);
                        }
                        $portalCategory = '';
                        if($obj->portal_category != ''){
                            $portalCategory = self::crmService()->getValueLookupCRM('portal_category_list', $obj->portal_category);
                        }
                        
                        $sourceReference = '';
                        if($obj->source_reference_c != ''){
                            $sourceReference = self::crmService()->getValueLookupCRM('source_of_reference_list', $obj->source_reference_c);
                        }
                        
                        $onsiteState = '';
                        if($obj->onsite_state_c != ''){
                            $onsiteState = self::crmService()->getValueLookupCRM('onsite_state_list', $obj->onsite_state_c);
                        }
                        
                        $pickupBy = '';
                        if($obj->pickupby_id != ''){
                            $pickupBy = self::crmService()->getNameUserCRM($obj->pickupby_id);
                        }
                        
                        $epcLocation = '';
                        if($obj->epc_location_c != ''){
                            $epcLocation = self::crmService()->getValueLookupCRM('epc_location_list', $obj->epc_location_c);
                        }

                        $category = '';
                        if($obj->category_c != ''){
                            $category = self::crmService()->getValueLookupCRM('category_list', $obj->category_c);
                        }
                        
                        $subCategory = '';
                        if($obj->sub_category_c != ''){
                            $subCategory = self::crmService()->getValueLookupCRM('cdc_sub_category_list', $obj->sub_category_c);
                        }
                        
                        $subSubCategory = '';
                        if($obj->sub_category_2_c != ''){
                            $subSubCategory = self::crmService()->getValueLookupCRM('cdc_sub_category_2_list', $obj->sub_category_2_c);
                        }
                        
                        $incidentServiceType = '';
                        if($obj->incident_service_type_c != ''){
                            $incidentServiceType = self::crmService()->getValueLookupCRM('incident_service_type_list', $obj->incident_service_type_c);
                        }
                        
                        $requestType = '';
                        if($obj->request_type_c != ''){
                            $requestType = self::crmService()->getValueLookupCRM('request_type_list', $obj->request_type_c);
                        }
                        
                        $priority = '';
                        if($obj->priority != ''){
                            $priority = self::crmService()->getValueLookupCRM('case_priority_dom', $obj->priority);
                        }
                        
                        $statusCase = '';
                        if($obj->casestatus != ''){
                            $statusCase = self::crmService()->getValueLookupCRM('case_status_dom', $obj->casestatus);
                        }
                        
                        $stateCase = '';
                        if($obj->state != ''){
                            $stateCase = self::crmService()->getValueLookupCRM('case_state_dom', $obj->state);
                        }
                        
                        $information = '';
                        if($obj->case_info_c != ''){
                            $information = self::crmService()->getValueLookupCRM('case_info_list', $obj->case_info_c);
                        }
                        
                        $contactMode = '';
                        if($obj->contact_mode_c != ''){
                            $contactMode = self::crmService()->getValueLookupCRM('cdc_contact_mode_list', $obj->contact_mode_c);
                        }
                        $cptpp = $obj->cptpp_flag;
                        $cptppValue = 'No';
                        if ($cptpp == 1) {
                            $cptppValue = 'Yes';
                        }
                        $sheet->row($count, array(
                            trim($kementerian),trim($kumpulanPtj),trim($orgName),$orgCode,$organizationType,trim($supplierName),$mofno,trim($leadName),
                            $ssmNo,$accountType,$subject,Carbon::parse($obj->date_entered)->addHour(8),Carbon::parse($obj->date_modified)->addHour(8),
                            $modifiedBy,$createdBy,$description,$assignTo,$obj->case_number,$statusCase,$resolution,$stateCase,$obj->sla_flag,
                            Carbon::parse($obj->cntc_mode_executiondate)->addHour(8),Carbon::parse($obj->cntc_mode_sla_startdate)->addHour(8),
                            Carbon::parse($obj->cntc_mode_sla_duedate)->addHour(8),$portalCategory,Carbon::parse($obj->onsite_date_c)->addHour(8),
                            $obj->onsite_location_c,$sourceReference,$onsiteState,$obj->no_of_pax,Carbon::parse($obj->pickup_datetime)->addHour(8),$pickupBy,
                            $obj->meeting_desc,$obj->meeting_month,$obj->activity_type,$obj->cdc_representative,$obj->cdc_representative_2,
                            $obj->chairperson_designation,$obj->chairperson_name,$obj->deputy_director_head_account,$obj->deputy_director_head_account_contact_num,
                            $obj->deputy_director_head_account_email,$obj->director_head_account,$obj->director_head_account_contact_num,
                            $obj->director_head_account_email,$obj->meeting_venue,$obj->mof_representative,$obj->mof_representative_2,$obj->no_of_att,
                            $obj->no_of_visit,$obj->ptj_cntc_detail,$obj->ptj_designation,$obj->ptj_email,$obj->ptj_role,$obj->ptj_user_name,
                            $obj->sec_cntc_detail,$obj->sec_email,$obj->sec_email,$cptppValue,$category,$subCategory,$subSubCategory,$contactMode,
                            $epcLocation,$incidentServiceType,$requestType,$information,Carbon::parse($obj->visit_start_date_c)->addHour(8),
                            Carbon::parse($obj->visit_end_date_c)->addHour(8),$obj->exec_name_c,$obj->tl_name_c,$obj->manager_name_c,
                            $obj->taskname,Carbon::parse($obj->taskentered)->addHour(8),Carbon::parse($obj->taskmodified)->addHour(8),
                            $taskCreatedBy,$taskModifiedBy,$obj->taskdescription,$taskAssign,$obj->taskstatus,Carbon::parse($obj->date_start)->addHour(8),
                            Carbon::parse($obj->date_due)->addHour(8),$obj->case_redmine_number,$taskJustification,$obj->task_contract_quantity,
                            $taskResolutionCategory,$obj->task_severity,$obj->task_batch_indicator,$obj->econtract_status,
                            Carbon::parse($obj->reassign_time_c)->addHour(8),Carbon::parse($obj->date_approved_redmine)->addHour(8),
                            $taskRedmineSolution,$taskImplementationIssue,Carbon::parse($obj->acknowledge_time_c)->addHour(8),
                            $obj->assign_group_c,$obj->resolution_c,$obj->sla_start,$obj->sla_stop,$acknowledgeBy,
                            $obj->task_number_c,$gmSite,Carbon::parse($obj->date_execution_time_c)->addHour(8),$obj->sla_task_flag_c,$obj->task_duration_c,
                            $obj->task_combine_c,$obj->task_factor_c,$taskCategoryFactor,$taskCombine,$obj->task_duration_sla_day_c,
                            $obj->task_duration_sla_hour_c,$taskReserve,Carbon::parse($obj->poms_inserted_date)->addHour(8)                           
                            )
                        );
                        $count++;
                    }
                    
                    $takentimeeachLoop = array(
                        'Counter'                => $start,
                        'Taken Time per Minutes' => $dtStartTimeEachLoop->diffInMinutes(Carbon::now()),
                        'Taken Time per Seconds' => $dtStartTimeEachLoop->diffInSeconds(Carbon::now())
                    );
                    dump(self::class.'    :: LoopTakenTime >> Time   :   ',[$takentimeeachLoop]);
                    dump(self::class.'    :: sum total current  :   '. $totalFirtSheet);

                    
                
                } while (count($firstResults) > 0 && count($firstResults) == $take);
                
                $takentimeOP = array(
                        'Counter'                => $start,
                        'Taken Time per Minutes' => $dtStartTimeOP->diffInMinutes(Carbon::now()),
                        'Taken Time per Seconds' => $dtStartTimeOP->diffInSeconds(Carbon::now())
                    );
                dump(self::class.'    :: AllLoopTakenTime >> Time   :   ',[$takentimeOP]);
                dump(self::class . ' generateFirstSheet > end  queryReport. Total All :  '.$totalFirtSheet);

                
                
            });         
            
         })->store('xlsx', storage_path('app/exports/cases'));
         
        $dataReport = collect([]);
        $dataReport->put("date_start", $dateStart);
        $dataReport->put("date_end",$dateEnd);
        $dataReport->put("report_name",'Case Details');
        $dataReport->put("report_path",storage_path('app/exports/cases').'/'.$fileName.'.xlsx');
        
        self::sendEmail($dataReport);
           
    }

    protected static function queryReport($dateStart,$dateEnd,$take,$nextSkip){

        $sql = DB::table('cases')
                ->join('cases_cstm', 'cases.id', '=', 'cases_cstm.id_c')
                ->leftJoin('tasks', 'tasks.parent_id', '=', 'cases.id')
                ->leftJoin('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id')
                ->where('cases.deleted',0)
                ->where('cases.case_number',4378699);
        $sql->whereBetween(DB::raw("DATE(CONVERT_TZ(cases.date_entered,'+00:00','+08:00'))"), 
                    [
                        $dateStart, 
                        $dateEnd
                    ]);
        $sql->select('cases.*','cases_cstm.*','tasks.*','tasks_cstm.*');
        $sql->addSelect(DB::raw("CONVERT_TZ(cases.date_entered,'+00:00','+08:00') AS created_date"));
        $sql->addSelect(DB::raw("CONVERT_TZ(cases.date_modified,'+00:00','+08:00') AS modified_date"));
        $sql->addSelect('tasks.name as taskname','tasks.date_entered as taskentered','tasks.date_modified as taskmodified');
        $sql->addSelect('tasks.modified_user_id as taskmodifiedby','tasks.created_by as taskcreatedby','tasks.description as taskdescription');
        $sql->addSelect('tasks.assigned_user_id as taskassigned','tasks.status as taskstatus','cases.status as casestatus');
        $sql->addSelect(DB::raw("CASE WHEN tasks_cstm.sla_task_flag_c = '2' THEN sla_start_15min_c
	    WHEN tasks_cstm.sla_task_flag_c IN ('3','s1','s2','s3') THEN sla_start_4hr_c 
	    WHEN tasks_cstm.sla_task_flag_c IN ('4') THEN sla_start_approver_c 
	    ELSE NULL END AS sla_start"));
        $sql->addSelect(DB::raw("CASE WHEN tasks_cstm.sla_task_flag_c = '2' THEN sla_stop_15min_c
	    WHEN tasks_cstm.sla_task_flag_c IN ('3','s1','s2','s3') THEN sla_stop_4hr_c 
	    WHEN tasks_cstm.sla_task_flag_c IN ('4') THEN sla_stop_approver_c 
	    ELSE NULL END AS sla_stop"));
        $sql->skip($nextSkip) ->take($take);
        
        $data = array(
            "sql"   => $sql->toSql(),
            "parameter" => $sql->getBindings()
        );

        dump(self::class.' :: getFirstQuery >> SQL   :   ',[$data]); 
        return $result = $sql->get();
    }

    /**
    * Send an e-mail report
    * @param  Request  $error
    * @return Response
    */
   protected static function  sendEmail($dataReport) {
       $data = array(
           "to" => ['<EMAIL>'],
           "subject" => 'Report CRM Generated : '.$dataReport['report_name']
       );
       try {
           Mail::send('emails.report', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'data' => $dataReport], function($m) use ($data,$dataReport) {
               $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
               $m->to($data["to"])
                       //->cc($data["cc"])
                    ->attach($dataReport['report_path'])
                    ->subject($data["subject"]);
           });
           dump('done send');
       } catch (\Exception $e) {
           Log::error(self::class . ' Error ... ' . __FUNCTION__.' ::  ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
           return $e;
       }
   }
}
