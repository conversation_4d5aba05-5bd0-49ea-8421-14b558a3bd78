<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Report\Crm;

use Carbon\Carbon;
use DB;
use Log;
use Excel;
use Mail;
use Config;
use App\Migrate\MigrateUtils;
use App\Services\CRMService;

class CaseDetailEnquiry {

    static $report_sheet_incident = "Enquiry";
    static $query_skip = 500;
    static $query_take = 500;

    public static function crmService() {
        return new CRMService;
    }

    public static function run($dateStart, $dateEnd) {
        Log::debug(self::class . ' Starting ... Generate Case Detail Enquiry');
        $dtStartTime = Carbon::now();
        self::createExcelReport($dateStart, $dateEnd);

        Log::debug(self::class . ' Completed ReportByExcel --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    public static function createExcelReport($dateStart, $dateEnd) {
        $dtStartTime = Carbon::now();

        self::generateReport($dateStart, $dateEnd);

        $durationTime = MigrateUtils::getTakenTime($dtStartTime);

        Log::debug(self::class . ' Taken Time :', ['Time' => $durationTime]);
    }

    public static function generateReport($dateStart, $dateEnd) {
        $start = 0;
        $skip = self::$query_skip;
        $take = self::$query_take;
        $totalRecords = 0;

        $dtStartTimeOP = Carbon::now();

        $CsvData = array('Case No, Redmine No, Kementerian, Kumpulan PTJ / Jabatan, PTJ / Organisasi, PTJ Code, Organisation Type, Company Name, MOF No, Leads Name, SSM No, Customer Type, Request Type, CPTPP Enquiry, Category, Sub-Category, Sub-Category2, Subject, Problem Details, Document Number, Resolution, Priority, Created Date, Requested By, Owner Name, Owner Location, User Group(s), Status, Sub Status, Information, Date Modified, Modified By, Contact Mode, Ageing (Day), Duration to Close, e-mail, State, Task Number, Task Status, Task Assign Group, GM Site');

        do {
            $nextSkip = $start++ * $skip;

                $dataSub1 = self::getQuery($dateStart,$dateEnd,$take,$nextSkip);
                $totalRecords = $totalRecords + count($dataSub1);

                $dtStartTimeEachLoop = Carbon::now();

                dump(self::class .' current totalrecords ' .count($dataSub1));
                
                foreach ($dataSub1 as $obj) {
                    $account = null;
                    $kementerian = '';
                    $pegawaiPengawal = '';
                    $kumpulanPtj = '';
                    $orgName = '';
                    $supplierName = '';
                    $orgCode = '';
                    $mofno = '';
                    $ssmNo = '';
                    $accountType = '';
                    $organizationType = '';
                    $emailContact = '';
                    $stateAccount = '';
                    if ($obj->account_id != '') {
                        $account = self::crmService()->getDetailAccountCRM($obj->account_id);
                        if ($account) {
                            $accountType = $account->account_type;

                            if ($account->billing_address_state != '') {
                                $stateAccount = self::crmService()->getValueLookupCRM('state_list', $account->billing_address_state);
                            }

                            if ($accountType == 'GOVERNMENT') {
                                $orgName = $account->name;
                                $orgCode = $account->org_gov_code;
                                if ($account->org_gov_type != '') {
                                    $organizationType = self::crmService()->getValueLookupCRM('accounts_nextgen_org_gov_code', $account->org_gov_type);

                                    if ($account->org_gov_type == '5') { //As PTJ
                                        $accountHirarchy = self::crmService()->getDetailAccountByPTJ($account->id);
                                        if ($accountHirarchy) {
                                            $kementerian = $accountHirarchy->kementerian_name;
                                            $pegawaiPengawal = $accountHirarchy->pegawaipengawal_name;
                                            $kumpulanPtj = $accountHirarchy->kumpulanptj_name;
                                        }
                                    }
                                    if ($account->org_gov_type == '4') { //As Kumpulan PTJ
                                        $accountHirarchy = self::crmService()->getDetailAccountByKumpulanPTJ($account->id);
                                        if ($accountHirarchy) {
                                            $kementerian = $accountHirarchy->kementerian_name;
                                            $pegawaiPengawal = $accountHirarchy->pegawaipengawal_name;
                                            $kumpulanPtj = $account->name;
                                        }
                                    }
                                    if ($account->org_gov_type == '3') { // As Pegawai Pengawal
                                        $accountHirarchy = self::crmService()->getDetailAccountPtjToKementerianCRMCaseReport($account->id);
                                        if ($accountHirarchy) {
                                            $kementerian = $accountHirarchy->kementerian_name;
                                            $pegawaiPengawal = $account->name;
                                        }
                                    }
                                    if ($account->org_gov_type == '2') { // As Kementerian
                                        $kementerian = $account->name;
                                    }
                                }else {
                                    //org_gov_type is null (UPS)
                                    $accountHirarchy = self::crmService()->getDetailAccountByPTJ($account->id);
                                    if ($accountHirarchy) {
                                        $kementerian = $accountHirarchy->kementerian_name;
                                        $pegawaiPengawal = $accountHirarchy->pegawaipengawal_name;
                                        $kumpulanPtj = $accountHirarchy->kumpulanptj_name;
                                    }
                                }
                            } else {
                                $supplierName = $account->name;
                                $mofno = $account->mof_no;
                                $ssmNo = $account->registration_no;
                            }
                        }
                    }

                    //Find Lead 
                    $lead = null;
                    $leadName = '';
                    if ($account == null) {
                        $lead = self::crmService()->getDetailLeadCRM($obj->id);
                        if ($lead) {
                            $leadName = $lead->first_name . $lead->last_name;
                            $ssmNo = $lead->ssm_no_c;
                            $accountType = 'LEADS';
                            $emailContact = self::crmService()->getEmailCRM("Leads", $lead->id);

                            if ($lead->primary_address_state != '') {
                                $stateAccount = self::crmService()->getValueLookupCRM('state_list', $lead->primary_address_state);
                            }
                        }
                    }

                    //Find Latest Task
                    $taskNumber = '';
                    $taskStatus = '';
                    $taskAssignGroup = '';
                    $gmSite = '';
                    if ($obj->request_type_c == 'incident' || $obj->request_type_c == 'service') {
                        $task = self::crmService()->getDetailTaskLatestCRM($obj->id);
                        if ($task) {
                            $taskNumber = $task->task_number_c;
                            if ($task->status != '') {
                                $taskStatus = self::crmService()->getValueLookupCRM('cdc_task_status_list', $task->status);
                            }
                            $taskAssignGroup = '';
                            if ($task->assigned_user_id != '') {
                                $taskAssignGroup = self::crmService()->getNameUserCRM($task->assigned_user_id);
                            }
                            if ($task->gm_site_c != '') {
                                $gmSite = self::crmService()->getValueLookupCRM('task_gm_site', $task->gm_site_c);
                            }
                        }
                    }

                    //Find User AssignTo
                    $assignTo = '';
                    if ($obj->assigned_user_id != '') {
                        $assignTo = self::crmService()->getNameUserCRM($obj->assigned_user_id);
                    }

                    //Find User CreateBy
                    $createdBy = '';
                    $createdByLocation = '';
                    if ($obj->created_by != '') {
                        $createdBy = self::crmService()->getNameUserCRM($obj->created_by);
                        $createdByLocation = self::crmService()->getLocationUserCRM($obj->created_by); 
                    }

                    //Find Group for CreatedBy
                    //getListDetailGroupCRM
                    $groupCreatedBy = '';
                    if ($obj->created_by != '') {
                        $listGroup = self::crmService()->getListDetailGroupCRM($obj->created_by);
                        if (count($listGroup) > 0) {
                            $listGroupName = $listGroup->pluck('name');
                            $groupCreatedBy = implode(",", $listGroupName->toArray());
                        }
                    }

                    //Find User ModifiedBy
                    $modifiedBy = '';
                    if ($obj->modified_user_id != '') {
                        $modifiedBy = self::crmService()->getNameUserCRM($obj->modified_user_id);
                    }

                    //Find Contact RequestedBy
                    $requestedBy = '';
                    if ($obj->contact_id_c != '') {
                        $requestedBy = self::crmService()->getNameContactCRM($obj->contact_id_c);
                        $emailContact = self::crmService()->getEmailCRM("Contacts", $obj->contact_id_c);
                    }

                    $category = '';
                    if ($obj->category_c != '') {
                        $category = self::crmService()->getValueLookupCRM('category_list', $obj->category_c);
                    }
                    
                    $subCategory = '';
                    if ($obj->sub_category_c != null) {
                        $subCategory = self::crmService()->getValueLookupCRM('cdc_sub_category_list', $obj->sub_category_c);
                    }

                    $subSubCategory = '';
                    if ($obj->sub_category_2_c != '') {
                        $subSubCategory = self::crmService()->getValueLookupCRM('cdc_sub_category_2_list', $obj->sub_category_2_c);
                    }

                    $incidentServiceType = '';
                    if ($obj->incident_service_type_c != '') {
                        $incidentServiceType = self::crmService()->getValueLookupCRM('incident_service_type_list', $obj->incident_service_type_c);
                    }

                    $requestType = '';
                    if ($obj->request_type_c != null) {
                        $requestType = self::crmService()->getValueLookupCRM('request_type_list', $obj->request_type_c);
                    }

                    $priority = '';
                    if ($obj->priority != '') {
                        $priority = self::crmService()->getValueLookupCRM('case_priority_dom', $obj->priority);
                    }
                    
                    $stateCase = '';
                    if ($obj->state != '') {
                        $stateCase = self::crmService()->getValueLookupCRM('case_state_dom', $obj->state);
                    }

                    $statusCase = '';
                    if ($obj->status != '') {
                        $statusCase = self::crmService()->getValueLookupCRM('case_status_dom', $obj->status);
                    }

                    $information = '';
                    if ($obj->case_info_c != '') {
                        $information = self::crmService()->getValueLookupCRM('case_info_list', $obj->case_info_c);
                    }

                    $contactMode = '';
                    if ($obj->contact_mode_c != '') {
                        $contactMode = self::crmService()->getValueLookupCRM('cdc_contact_mode_list', $obj->contact_mode_c);
                    }

                    $ageingDay = '';
                    if ($obj->status == 'Open_New' || $obj->status == 'Open_Assigned' || $obj->status == 'In_Progress' || $obj->status == 'Open_Pending Input') {
                        $dateCreated = Carbon::parse($obj->created_date);
                        $ageingDay = $dateCreated->diffInDays(Carbon::now());
                    }
                    $durationDayToClose = '';
                    if ($obj->status == 'Closed_Closed' || $obj->status == 'Closed_Rejected' || $obj->status == 'Closed_Rejected_Eaduan' || $obj->status == 'Closed_Verified_Eaduan' || $obj->status == 'Closed_Duplicate' ||
                            $obj->status == 'Pending_User_Verification' || $obj->status == 'Open_Resolved' || $obj->status == 'Closed_Cancelled_Eaduan') {
                        $dateCreated = Carbon::parse($obj->created_date);
                        $dateModified = Carbon::parse($obj->modified_date);
                        $durationDayToClose = $dateCreated->diffInDays($dateModified);
                    }

                    $kementerianName = preg_replace('/[,]+/', ' ', trim($kementerian));
                    $kumpulanPtjName = preg_replace('/[,]+/', ' ', trim($kumpulanPtj));
                    $orgNameName = preg_replace('/[,]+/', ' ', trim($orgName));
                    $supplierNameName = preg_replace('/[,]+/', ' ', trim($supplierName));
                    $leadNameName = preg_replace('/[,]+/', ' ', trim($leadName));
                    $subject = preg_replace('/[,]+/', ' ', trim($obj->name));
                    $trimdescription = preg_replace('!\s+!',' ', trim($obj->description));
                    $trimdescriptionLen = '';
                        if((strlen($trimdescription)) > 30000){
                            $trimdescriptionLen = substr($trimdescription, 0, 10000); 
                        }else{
                            $trimdescriptionLen = $trimdescription;
                        }
                    $description = preg_replace('/[,]+/', ' ', trim($trimdescriptionLen));
                    $docNo = preg_replace('/[,]+/', ' ', trim($obj->doc_no));
                    $strLen = (strlen($obj->resolution));
                    $trimResolution = ''; 
                        if($strLen > 30000){
                            $trimResolution = substr($obj->resolution, 0, 10000); 
                        }else{
                            $trimResolution = $obj->resolution;
                        }
                    $resolution = preg_replace('/[^a-zA-Z0-9]/', ' ', trim($trimResolution));
                    $accSsmNo = preg_replace('/[,]+/', ' ', trim($ssmNo));
                    $redmineNo = preg_replace('/[,]+/', ' ', trim($obj->redmine_number));
                    $caseCategory = preg_replace('/[,]+/', ' ', trim($category));
                    $caseSubCategory = preg_replace('/[,]+/', ' ', trim($subCategory));
                    $caseSubSubCategory = preg_replace('/[,]+/', ' ', trim($subSubCategory));
                    $caseGroupCreatedBy = preg_replace('/[,]+/', ' ', trim($groupCreatedBy)); 
                    $caseRequestedBy = preg_replace('/[,]+/', ' ', trim($requestedBy));
                    $caseCreatedBy = preg_replace('/[,]+/', ' ', trim($createdBy));
                    $cptpp = $obj->cptpp_flag;
                    $cptppValue = 'No';
                    if ($cptpp == 1) {
                        $cptppValue = 'Yes';
                    }
                    
                    $CsvData[] = (
                            $obj->case_number . ',' .
                            $redmineNo . ',' .
                            $kementerianName . ',' .
                            $kumpulanPtjName . ',' .
                            $orgNameName . ',' .
                            $orgCode . ',' .
                            $organizationType . ',' .
                            $supplierNameName . ',' .
                            $mofno . ',' .
                            $leadNameName . ',' .
                            $accSsmNo . ',' .
                            $accountType . ',' .
                            $requestType .',' .
                            $cptppValue .',' .
                            $caseCategory .',' .
                            $caseSubCategory . ',' .
                            $caseSubSubCategory . ',' .
                            $subject . ',' .
                            $description . ',' .
                            $docNo . ',' .
                            $resolution . ',' .
                            $priority . ',' .
                            Carbon::parse($obj->created_date) . ',' . 
                            $caseRequestedBy . ',' .
                            $caseCreatedBy . ',' .
                            $createdByLocation . ',' .
                            $caseGroupCreatedBy . ',' .
                            $stateCase . ',' .
                            $statusCase . ',' .
                            $information . ',' .
                            Carbon::parse($obj->modified_date) . ',' . 
                            $modifiedBy . ',' .
                            $contactMode . ',' .
                            $ageingDay . ',' .
                            $durationDayToClose . ',' .
                            $emailContact . ',' .
                            $stateAccount . ',' .
                            $taskNumber . ',' .
                            $taskStatus . ',' .
                            $taskAssignGroup . ',' .
                            $gmSite);
                }
                
                $takentimeeachLoop = array(
                'Counter' => $start,
                'Taken Time per Minutes' => $dtStartTimeEachLoop->diffInMinutes(Carbon::now()),
                'Taken Time per Seconds' => $dtStartTimeEachLoop->diffInSeconds(Carbon::now())
            );
            dump(self::class . '    :: LoopTakenTime >> Time   :   ', [$takentimeeachLoop]);
            dump(self::class . '    :: sum total current  :   ' . $totalRecords);
            
        } while (count($dataSub1) > 0 && count($dataSub1) == self::$query_take);

        $takentimeOP = array(
            'Counter' => $start,
            'Taken Time per Minutes' => $dtStartTimeOP->diffInMinutes(Carbon::now()),
            'Taken Time per Seconds' => $dtStartTimeOP->diffInSeconds(Carbon::now())
        );
        dump(self::class . '    :: AllLoopTakenTime >> Time   :   ', [$takentimeOP]);
        dump(self::class . ' queryReport. Total All :  ' . $totalRecords);
        dump(self::class . '--------------------------------------------');

        $filename = 'CaseDetailEnquiry' . $dateStart . '_to_' . $dateEnd. ".csv";
        $file_path = storage_path() . '/app/exports/cases/' . $filename;
        $file = fopen($file_path, "w+");
        foreach ($CsvData as $exp_data) {
            fputcsv($file, explode(',', $exp_data));
        }
        fclose($file);

        $dataReport = collect([]);
        $dataReport->put("date_start", $dateStart);
        $dataReport->put("date_end",$dateEnd);
        $dataReport->put("report_name",'Case Details Enquiry');
        $dataReport->put("file_name",$filename);
        
        self::sendEmail($dataReport);
        
        //$headers = ['Content-Type' => 'application/csv'];
        //return response()->download($file_path, $filename, $headers);
    }

    protected static function getQuery($dateStart,$dateEnd,$take,$nextSkip) {
        dump('DateStart:'.$dateStart,'DateEnd:'.$dateEnd,'Take:'.$take,'Skip:'.$nextSkip);
        $financeSubCategory = array('10712_15034', '10714_15842', '10713_15534');
        $onsiteCategory = array('10719','10720','10721','10722');
        $outboundSubCategory = array('10715_15858');
        $sql = DB::table('cases')
                ->join('cases_cstm', 'cases.id', '=', 'cases_cstm.id_c')
                ->where('cases.deleted', 0)
                ->where('cases_cstm.request_type_c', 'enquiry')
                ->whereNotIn('cases_cstm.category_c', $onsiteCategory)
                ->whereNotIn('cases_cstm.sub_category_c', $financeSubCategory)
                ->whereNotIn('cases_cstm.sub_category_c', $outboundSubCategory);
        $sql->whereBetween(DB::raw("DATE(CONVERT_TZ(cases.date_entered,'+00:00','+08:00'))"), [
            $dateStart,
            $dateEnd
        ]);

        $sql->select('cases.*', 'cases_cstm.*');
        $sql->addSelect(DB::raw("CONVERT_TZ(cases.date_entered,'+00:00','+08:00') AS created_date"));
        $sql->addSelect(DB::raw("CONVERT_TZ(cases.date_modified,'+00:00','+08:00') AS modified_date"));
        $sql->skip($nextSkip)->take($take);

        $data = array(
            "sql" => $sql->toSql(),
            "parameter" => $sql->getBindings()
        );

        //dump(self::class . ' :: getQuery >> SQL   :   ', [$data]);
        return $result = $sql->get();
    }
    
    protected static function  sendEmail($dataReport) {
        
        $data = array(
//            "to" => ['<EMAIL>'],
            "to" => ['<EMAIL>','<EMAIL>'],
            "subject" => "Server (".env('APP_ENV').") > CRM Report : ".$dataReport->get('report_name')." pada ".$dataReport->get('date_start')." sehingga ".$dataReport->get('date_end'),
         
        );
        try {
            Mail::send('emails.generate_report_crm',['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'data' => $dataReport], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])->subject($data["subject"]);
            });
            dump('done send');
        } catch (\Exception $e) {
            $reportName = $dataReport->get('report_name');
            $msg = '*[ALERT]* Failed send report ' .$reportName .'.Please Check!!';
            self::crmService()->notifyWhatsapp($msg, 'CRM_REPORT_INTEGRATION', 'CrmIntegration', 'Failed Send Email Report');
            echo $e;
            Log::error(self::class . ' Error ... ' . __FUNCTION__.' ::  ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            dump('error' .$e);
            return $e;
        }
   }

}
