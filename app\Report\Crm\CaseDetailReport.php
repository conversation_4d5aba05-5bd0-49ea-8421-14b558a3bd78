<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Report\Crm;

use Carbon\Carbon;
use DB;
use Log;
use Excel;
use Mail;
use Config;
use App\Migrate\MigrateUtils;
use App\Services\CRMService;

class CaseDetailReport {
    
    public static function crmService() {
        return new CRMService;
    }

    public static function run($dataParameter) {
        dump(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();
        self::generateReportExcel($dataParameter);
        //self::export();
        
        dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }
 
    public static function getStartDate() {
        //return $date = Carbon::now()->month(2)->startOfMonth()->format('Y-m-d');
        return $date = Carbon::now()->startOfMonth()->format('Y-m-d');
        //return $date = Carbon::now()->format('Y-m-d');
    }
        
    public static function getEndDate() {
        //return $date = Carbon::now()->month(2)->endOfMonth()->format('Y-m-d');
        return $date = Carbon::now()->endOfMonth()->format('Y-m-d');
        //return $date = Carbon::now()->format('Y-m-d');
    }
    
    protected static function generateReportExcel($dataParameter) {
        $fileName = 'CaseDetailReport_'.$dataParameter->get('date_start').'_to_'.$dataParameter->get('date_end');
        dump('filename : '.$fileName);
        Excel::create($fileName, function($excel) use ($dataParameter) {
            $excel->setTitle('Cases Details');
            
            //Run on 2018-04-04 10.30 AM
            $excel->sheet('Run on '.Carbon::now()->format('Y-m-d g.i A'), function($sheet) use ($dataParameter) {
                $sheet->setOrientation('landscape');

                $sheet->setStyle(array(
                    'font' => array(
                        'name' => 'Calibri',
                        'size' => 10,
                        'bold' => false
                    )
                ));
                
                // https://laravel-excel.maatwebsite.nl/docs/2.1/export/autosize
                $sheet->setColumnFormat(array(
                    'A' => '@','B' => '@','C' => '@',
                    'D' => '@','E' => '@','F' => '@',
                    'G' => '@','H' => '@','I' => '@',
                    'J' => '@','K' => '@','L' => '@',
                    'M' => '@','N' => '@','O' => '@',
                    'P' => '@','Q' => '@','R' => '@',
                    'S' => '@','T' => '@',
                    'U' => '@',
                    'V' => '@','W' => '@','X' => '@',
                    'Y' => '@','Z' => '@','AA' => '@',
                    'AB' => '@','AC' => '@','AD' => '@',
                    'AE' => '@','AF' => '@','AG' => '@',
                    'AH' => '@','AI' => '@','AJ' => '@',
                    'AK' => '@','AL' => '@','AM' => '@',
                ));
                  
                $sheet->row(1, function($row) {
                    // call cell manipulation methods
                    $row->setBackground('#684E49');
                });
                
                // Disable auto size for columns
                $sheet->setAutoSize(array(
                    'R', 'S', 'T'
                ));

                $sheet->cells('A1:AM1', function($cells) {
                    // manipulate the range of cells
                    // Set with font color
                    $cells->setFontColor('#fff');

                    // Set font family
                    $cells->setFontFamily('Calibri');

                    // Set font size
                    $cells->setFontSize(10);

                    // Set font weight to bold
                    $cells->setFontWeight('bold');

                    // Set all borders (top, right, bottom, left)
                    $cells->setBorder('solid', 'solid', 'solid', 'solid');
                });
                //$sheet->setSize('A' . $intRowNumber, 25, 18);
                
                $sheet->row(1, array(
                    'Case No', 'Redmine No', 
                    'Kementerian','Kumpulan PTJ / Jabatan','PTJ/ Organisasi','PTJ Code','Organisation Type',
                    'Company Name','MOF No','Leads Name','SSM No','Customer Type',
                    'Request Type','Incident / Service Type',
                    'Category','Sub Category','Sub Sub Category',
                    'Subject','Problem Details','Resolution','Priority',
                    'Requested By','Created Date','Owner Name','User Group(s)',
                    'Assigned To','Status','Information',
                    'Modified Date','Modified By','Contact Mode',
                    'Ageing (Day)','Duration to Close',
                    'e-mail','State',
                    'Task Number','Task Status','Task Assign Group','GM Site'
                ));
                
                $count = 2;
                
                $start = 0;
                $skip = 1500;
                $take = 1500;
                $totalFirtSheet = 0;

                $dtStartTimeOP = Carbon::now();
                do {               
                    $dtStartTimeEachLoop = Carbon::now();
                    $nextSkip = $start++ * $skip;
                    $firstResults = self::queryReport($dataParameter,$take,$nextSkip);
                    $totalFirtSheet = $totalFirtSheet+count($firstResults);

                    foreach ($firstResults as $obj) {
                        
                        //Find Account
                        $account = null;
                        $kementerian = '';
                        $pegawaiPengawal = '';
                        $kumpulanPtj = '';
                        $orgName = '';
                        $supplierName = '';
                        $orgCode = '';
                        $mofno = '';
                        $ssmNo = '';
                        $accountType = '';
                        $organizationType = '';
                        $stateAccount = '';
                        if($obj->account_id != ''){
                            $account = self::crmService()->getDetailAccountCRM($obj->account_id);
                            if($account){
                                $accountType = $account->account_type;
                            
                                if($account->billing_address_state != ''){
                                    $stateAccount = self::crmService()->getValueLookupCRM('state_list', $account->billing_address_state);
                                }

                                if($accountType == 'GOVERNMENT'){
                                    $orgName = $account->name;
                                    $orgCode = $account->org_gov_code;
                                    if($account->org_gov_type != ''){
                                        $organizationType = self::crmService()->getValueLookupCRM('accounts_nextgen_org_gov_code', $account->org_gov_type);
                                        
                                        if($account->org_gov_type == '5'){ //As PTJ
                                            $accountHirarchy = self::crmService()->getDetailAccountByPTJ($account->id);
                                            if($accountHirarchy){
                                                $kementerian =  $accountHirarchy->kementerian_name;
                                                $pegawaiPengawal =  $accountHirarchy->pegawaipengawal_name;
                                                $kumpulanPtj =  $accountHirarchy->kumpulanptj_name;
                                            }
                                        }
                                        if($account->org_gov_type == '4'){ //As Kumpulan PTJ
                                            $accountHirarchy = self::crmService()->getDetailAccountByKumpulanPTJ($account->id);
                                            if($accountHirarchy){
                                                $kementerian =  $accountHirarchy->kementerian_name;
                                                $pegawaiPengawal =  $accountHirarchy->pegawaipengawal_name;
                                                $kumpulanPtj =  $account->name; 
                                            }
                                        }
                                        if($account->org_gov_type == '3'){ // As Pegawai Pengawal
                                            $accountHirarchy = self::crmService()->getDetailAccountPtjToKementerianCRM($account->id);
                                            if($accountHirarchy){
                                                $kementerian =  $accountHirarchy->kementerian_name;
                                                $pegawaiPengawal =  $account->name;
                                            }
                                        }
                                        if($account->org_gov_type == '2'){ // As Kementerian
                                            $kementerian =  $account->name; 
                                        }
                                    }
                                }else{
                                    $supplierName = $account->name;
                                    $mofno = $account->mof_no;
                                    $ssmNo = $account->registration_no;
                                } 
                            } 
                        }
                        
                        //Find Lead 
                        $lead = null;
                        $leadName = '';
                        if($account == null){
                            $lead = self::crmService()->getDetailLeadCRM($obj->id);
                            if($lead){
                                $leadName = $lead->first_name;
                                $ssmNo = $account->ssm_np_c;
                                $accountType = 'LEADS';

                                if($lead->primary_address_state != ''){
                                    $stateAccount = self::crmService()->getValueLookupCRM('state_list', $lead->primary_address_state);
                                } 
                            }
                            
                        }
                        
                        //Find Latest Task
                        $taskNumber = '';
                        $taskStatus = '';
                        $taskAssignGroup = '';
                        $gmSite = '';
                        if($obj->request_type_c  == 'incident' || $obj->request_type_c  == 'service' ){
                            $task = self::crmService()->getDetailTaskLatestCRM($obj->id);
                            if($task){
                                $taskNumber = $task->task_number_c;
                                if($task->status != ''){
                                    $taskStatus = self::crmService()->getValueLookupCRM('cdc_task_status_list', $task->status);
                                }
                                $taskAssignGroup = '';
                                if($task->assigned_user_id != ''){
                                    $taskAssignGroup = self::crmService()->getNameUserCRM($task->assigned_user_id);
                                }
                                if($task->gm_site_c != ''){
                                    $gmSite = self::crmService()->getValueLookupCRM('task_gm_site', $task->gm_site_c);
                                }
                            }
                        }
                        
                        //Find User AssignTo
                        $assignTo = '';
                        if($obj->assigned_user_id != ''){
                            $assignTo = self::crmService()->getNameUserCRM($obj->assigned_user_id);
                        }
                        
                        //Find User CreateBy
                        $createdBy = '';
                        if($obj->created_by != ''){
                            $createdBy = self::crmService()->getNameUserCRM($obj->created_by);
                        }
                        
                        //Find Group for CreatedBy
                        //getListDetailGroupCRM
                        $groupCreatedBy = '';
                        if($obj->created_by != ''){
                            $listGroup= self::crmService()->getListDetailGroupCRM($obj->created_by);
                            if(count($listGroup) > 0){
                                $listGroupName = $listGroup->pluck('name');
                                $groupCreatedBy = implode(",",$listGroupName->toArray());
                            }
                        }
                        
                        //Find User ModifiedBy
                        $modifiedBy = '';
                        if($obj->modified_user_id != ''){
                            $modifiedBy = self::crmService()->getNameUserCRM($obj->modified_user_id);
                        }
                        
                        //Find Contact RequestedBy
                        $requestedBy = '';
                        $emailContact = '';
                        if($obj->contact_id_c != ''){
                            $requestedBy = self::crmService()->getNameContactCRM($obj->contact_id_c);
                            $emailContact = self::crmService()->getEmailCRM("Contacts", $obj->contact_id_c);
                        }

                        $category = '';
                        if($obj->category_c != ''){
                            $category = self::crmService()->getValueLookupCRM('category_list', $obj->category_c);
                        }
                        
                        $subCategory = '';
                        if($obj->sub_category_c != ''){
                            $subCategory = self::crmService()->getValueLookupCRM('cdc_sub_category_list', $obj->sub_category_c);
                        }
                        
                        $subSubCategory = '';
                        if($obj->sub_category_2_c != ''){
                            $subSubCategory = self::crmService()->getValueLookupCRM('cdc_sub_category_2_list', $obj->sub_category_2_c);
                        }
                        
                        $incidentServiceType = '';
                        if($obj->incident_service_type_c != ''){
                            $incidentServiceType = self::crmService()->getValueLookupCRM('incident_service_type_list', $obj->incident_service_type_c);
                        }
                        
                        $requestType = '';
                        if($obj->request_type_c != ''){
                            $requestType = self::crmService()->getValueLookupCRM('request_type_list', $obj->request_type_c);
                        }
                        
                        $priority = '';
                        if($obj->priority != ''){
                            $priority = self::crmService()->getValueLookupCRM('case_priority_dom', $obj->priority);
                        }
                        
                        $statusCase = '';
                        if($obj->status != ''){
                            $statusCase = self::crmService()->getValueLookupCRM('case_status_dom', $obj->status);
                        }
                        
                        $information = '';
                        if($obj->case_info_c != ''){
                            $information = self::crmService()->getValueLookupCRM('case_info_list', $obj->case_info_c);
                        }
                        
                        $contactMode = '';
                        if($obj->contact_mode_c != ''){
                            $contactMode = self::crmService()->getValueLookupCRM('cdc_contact_mode_list', $obj->contact_mode_c);
                        }
                        
                        $ageingDay = '';
                        if($obj->state == 'Open'){
                          $dateCreated = Carbon::parse($obj->created_date);
                          $ageingDay = $dateCreated->diffInDays(Carbon::now());
                        }
                        $durationDayToClose = '';
                        if($obj->state == 'Closed'){
                          $dateCreated = Carbon::parse($obj->created_date);
                          $dateModified = Carbon::parse($obj->modified_date);
                          $durationDayToClose = $dateCreated->diffInDays($dateModified);
                        }


                        $sheet->row($count, array(
                                $obj->case_number,
                                $obj->redmine_number,
                                trim($kementerian),
                                trim($kumpulanPtj),
                                trim($orgName),
                                $orgCode,
                                $organizationType,
                                trim($supplierName),
                                $mofno,
                                trim($leadName),
                                $ssmNo,
                                $accountType,
                                $requestType,
                                $incidentServiceType,
                                $category,
                                $subCategory,
                                $subSubCategory,
                                trim($obj->name),
                                trim($obj->description),
                                trim($obj->resolution),
                                $priority,
                                $requestedBy,
                                $obj->created_date,
                                $createdBy,
                                $groupCreatedBy,
                                $assignTo,
                                $statusCase,
                                $information,
                                $obj->modified_date,
                                $modifiedBy,
                                $contactMode,
                                $ageingDay,
                                $durationDayToClose,
                                $emailContact,
                                $stateAccount,
                                $taskNumber,
                                $taskStatus,
                                $taskAssignGroup,
                                $gmSite
                            )
                        );
                        $count++;
                    }
                    
                    $takentimeeachLoop = array(
                        'Counter'                => $start,
                        'Taken Time per Minutes' => $dtStartTimeEachLoop->diffInMinutes(Carbon::now()),
                        'Taken Time per Seconds' => $dtStartTimeEachLoop->diffInSeconds(Carbon::now())
                    );
                    dump(self::class.'    :: LoopTakenTime >> Time   :   ',[$takentimeeachLoop]);
                    dump(self::class.'    :: sum total current  :   '. $totalFirtSheet);

                    
                
                } while (count($firstResults) > 0 && count($firstResults) == $take);
                
                $takentimeOP = array(
                        'Counter'                => $start,
                        'Taken Time per Minutes' => $dtStartTimeOP->diffInMinutes(Carbon::now()),
                        'Taken Time per Seconds' => $dtStartTimeOP->diffInSeconds(Carbon::now())
                    );
                dump(self::class.'    :: AllLoopTakenTime >> Time   :   ',[$takentimeOP]);
                dump(self::class . ' generateFirstSheet > end  queryReport. Total All :  '.$totalFirtSheet);

                
                
            });         
            
         })->store('xlsx', storage_path('app/exports/cases'));
         
        $dataReport = collect([]);
        $dataReport->put("date_start", $dataParameter->get('date_start'));
        $dataReport->put("date_end",$dataParameter->get('date_end'));
        $dataReport->put("report_name",'Case Details');
        $dataReport->put("report_path",storage_path('app/exports/cases').'/'.$fileName.'.xlsx');
        
        self::sendEmail($dataReport);
           
    }

    protected static function queryReport($dataParameter,$take,$nextSkip){

        $sql = DB::table('cases')
                ->join('cases_cstm', 'cases.id', '=', 'cases_cstm.id_c')
                ->where('cases.deleted',0);
        $sql->whereBetween(DB::raw("DATE(CONVERT_TZ(cases.date_entered,'+00:00','+08:00'))"), 
                    [
                        $dataParameter->get('date_start'), 
                        $dataParameter->get('date_end')
                    ]);
        if($dataParameter->get('status') != null){
            $sql->where('cases.status',$dataParameter->get('status'));
        }
        if($dataParameter->get('request_type') != null){
            $sql->where('cases_cstm.request_type_c',$dataParameter->get('request_type'));
        }
        $sql->select('cases.*','cases_cstm.*');
        $sql->addSelect(DB::raw("CONVERT_TZ(cases.date_entered,'+00:00','+08:00') AS created_date"));
        $sql->addSelect(DB::raw("CONVERT_TZ(cases.date_modified,'+00:00','+08:00') AS modified_date"));
        $sql->skip($nextSkip) ->take($take);
        
        $data = array(
            "sql"   => $sql->toSql(),
            "parameter" => $sql->getBindings()
        );

        dump(self::class.' :: getFirstQuery >> SQL   :   ',[$data]); 
        return $result = $sql->get();
    }

    /**
    * Send an e-mail report
    * @param  Request  $error
    * @return Response
    */
   protected static function  sendEmail($dataReport) {
       $data = array(
           "to" => ['<EMAIL>'],
           "subject" => 'Report CRM Generated : '.$dataReport['report_name']
       );
       try {
           Mail::send('emails.report', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'data' => $dataReport], function($m) use ($data,$dataReport) {
               $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
               $m->to($data["to"])
                       //->cc($data["cc"])
                    ->attach($dataReport['report_path'])
                    ->subject($data["subject"]);
           });
           dump('done send');
       } catch (\Exception $e) {
           Log::error(self::class . ' Error ... ' . __FUNCTION__.' ::  ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
           return $e;
       }
   }
}
