<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Report\Crm;

use Carbon\Carbon;
use DB;
use Log;
use Excel;
use Mail;
use Config;
use App\Migrate\MigrateUtils;
use App\Services\CRMService;

class CaseRejectedEaduan {

    static $report_sheet_incident = "Incident";
    static $report_sheet_service = "Service";
    static $query_skip = 500;
    static $query_take = 500;

    public static function crmService() {
        return new CRMService;
    }

    public static function run($dateStart, $dateEnd) {
        Log::debug(self::class . ' Starting ... Generate Case Detail Incident Service');
        $dtStartTime = Carbon::now();
        self::createExcelReport($dateStart, $dateEnd);

        Log::debug(self::class . ' Completed ReportByExcel --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    } 
    
    public static function createExcelReport($dateStart, $dateEnd) {
        $dtStartTime = Carbon::now();

        $dateReport = self::generateReport($dateStart, $dateEnd);
        self::sendEmail($dateReport);
        
        $durationTime = MigrateUtils::getTakenTime($dtStartTime);

        Log::debug(self::class . ' Taken Time :', ['Time' => $durationTime]);
    }

    public static function generateReport($dateStart, $dateEnd, $status = null) {
        $start = 0;
        $skip = self::$query_skip;
        $take = self::$query_take;
        $totalRecords = 0;

        $dtStartTimeOP = Carbon::now();

        $CsvData = array('Case No,Redmine No,Kementerian,Kumpulan PTJ/Jabatan,PTJ/Organisasi,PTJ Code,Organisation Type,Company Name,MOF No,'
            . 'Leads Name,SSM No,Customer Type,Phone No.,Request Type,Incident/Service Type,CPTPP Enquiry,Sub Category,Sub Sub Category,Subject,Problem Details,Document Number,Resolution,'
            . 'Reject Remark,Created Date,Date Modified,Owner Name,Owner Location,Status,Contact Mode,Ageing,Duration to Close,e-mail, State,Task Status,Task Assign Group,Resolution Category');
        
//        $CsvData = array('Case No,Redmine No,Kementerian,Kumpulan PTJ/Jabatan,PTJ/Organisasi,PTJ Code,Organisation Type,Company Name,MOF No,Leads Name,SSM No,Customer Type,Phone No.,Sub Category,Sub Sub Category,Subject,Problem Details,Document Number,Resolution,Created Date,Date Modified,Owner Name,Status,Contact Mode,Ageing,Duration to Close,e-mail, State,Task Status,Task Assign Group,Resolution Category,Severity');
        do {
            $nextSkip = $start++ * $skip;

//            $begin = new DateTime($dateStart);
//            $end = new DateTime($dateEnd);
//
//            $interval = DateInterval::createFromDateString('1 day');
//            $period = new DatePeriod($begin, $interval, $end);

//            foreach ($period as $dt) {

                $dataSub1 = self::getQuery($dateStart,$dateEnd,$take,$nextSkip,$status);
                $totalRecords = $totalRecords + count($dataSub1);

                $dtStartTimeEachLoop = Carbon::now();

                var_dump(self::class .' current totalrecords ' .count($dataSub1));
                
                foreach ($dataSub1 as $obj) {
                    $account = null;
                    $kementerian = '';
                    $pegawaiPengawal = '';
                    $kumpulanPtj = '';
                    $orgName = '';
                    $supplierName = '';
                    $orgCode = '';
                    $mofno = '';
                    $ssmNo = '';
                    $accountType = '';
                    $phoneNoAcc = '';
                    $phoneNoContact = '';
                    $phoneNo = '';
                    $organizationType = '';
                    $emailContact = '';
                    $stateAccount = '';
                    
                    if ($obj->account_id != '') {
                        $account = self::crmService()->getDetailAccountCRM($obj->account_id);
                        if ($account) {
                            $accountType = $account->account_type;

                            if ($account->billing_address_state != '') {
                                $stateAccount = self::crmService()->getValueLookupCRM('state_list', $account->billing_address_state);
                            }

                            if ($accountType == 'GOVERNMENT') {
                                $orgName = $account->name;
                                $orgCode = $account->org_gov_code;
                                if ($account->org_gov_type != '') {
                                    $organizationType = self::crmService()->getValueLookupCRM('accounts_nextgen_org_gov_code', $account->org_gov_type);

                                    if ($account->org_gov_type == '5') { //As PTJ
                                        $accountHirarchy = self::crmService()->getDetailAccountByPTJ($account->id);
                                        if ($accountHirarchy) {
                                            $kementerian = $accountHirarchy->kementerian_name;
                                            $pegawaiPengawal = $accountHirarchy->pegawaipengawal_name;
                                            $kumpulanPtj = $accountHirarchy->kumpulanptj_name;
                                        }
                                    }
                                    if ($account->org_gov_type == '4') { //As Kumpulan PTJ
                                        $accountHirarchy = self::crmService()->getDetailAccountByKumpulanPTJ($account->id);
                                        if ($accountHirarchy) {
                                            $kementerian = $accountHirarchy->kementerian_name;
                                            $pegawaiPengawal = $accountHirarchy->pegawaipengawal_name;
                                            $kumpulanPtj = $account->name;
                                        }
                                    }
                                    if ($account->org_gov_type == '3') { // As Pegawai Pengawal
                                        $accountHirarchy = self::crmService()->getDetailAccountPtjToKementerianCRMCaseReport($account->id);
                                        if ($accountHirarchy) {
                                            $kementerian = $accountHirarchy->kementerian_name;
                                            $pegawaiPengawal = $account->name;
                                        }
                                    }
                                    if ($account->org_gov_type == '2') { // As Kementerian
                                        $kementerian = $account->name;
                                    }
                                }else {
                                    //org_gov_type is null (UPS)
                                    $accountHirarchy = self::crmService()->getDetailAccountByPTJ($account->id);
                                    if ($accountHirarchy) {
                                        $kementerian = $accountHirarchy->kementerian_name;
                                        $pegawaiPengawal = $accountHirarchy->pegawaipengawal_name;
                                        $kumpulanPtj = $accountHirarchy->kumpulanptj_name;
                                    }
                                }
                            } else {
                                $supplierName = $account->name;
                                $mofno = $account->mof_no;
                                $ssmNo = $account->registration_no;
                            }  
                            
                            if ($account->phone_office != '') {
                            $phoneNoAcc = $account->phone_office;
                            } else {
                                $phoneNoAcc = $account->phone_alternate;
                            }
                    }
                    }

                    //Find Lead 
                    $lead = null;
                    $leadName = '';
                    if ($account == null) {
                        $lead = self::crmService()->getDetailLeadCRM($obj->id);
                        if ($lead) {
                            $leadName = $lead->first_name . $lead->last_name;
                            $ssmNo = $lead->ssm_no_c;
                            $accountType = 'LEADS';
                            $emailContact = self::crmService()->getEmailCRM("Leads", $lead->id);

                            if ($lead->primary_address_state != '') {
                                $stateAccount = self::crmService()->getValueLookupCRM('state_list', $lead->primary_address_state);
                            }
                            
                            if($lead->phone_work != ''){
                                $phoneNoContact = $lead->phone_work;
                            } else if($lead->phone_mobile != '') {
                                $phoneNoContact = $lead->phone_mobile;
                            } else if ($lead->phone_home != ''){
                                $phoneNoContact = $lead->phone_home;
                            } else {
                                $phoneNoContact = $lead->phone_other;
                            }
                        }
                    }

                    
                    $taskNumber = '';
                    $taskStatus = '';
                    $taskAssignGroup = '';
                    $gmSite = '';
                    $resolutionCategory = '';
                    $severity = '';
                    if ($obj->request_type_c == 'incident' || $obj->request_type_c == 'service') {
                        $task = self::crmService()->getDetailTaskCaseOwnerCRM($obj->id);
                        if ($task) {
                            $taskNumber = $task->task_number_c;
                            if ($task->status != '') {
                                $taskStatus = self::crmService()->getValueLookupCRM('cdc_task_status_list', $task->status);
                            }
                            $taskAssignGroup = '';
                            if ($task->assigned_user_id != '') {
                                $taskAssignGroup = self::crmService()->getNameUserCRM($task->assigned_user_id);
                            }
                            if ($task->gm_site_c != '') {
                                $gmSite = self::crmService()->getValueLookupCRM('task_gm_site', $task->gm_site_c);
                            }
                            if ($task->resolution_category_c != '') {
                                $resolutionCategory = self::crmService()->getValueLookupCRM('task_resolution_category_list', $task->resolution_category_c);
                            }
                        }
                        
                        if($resolutionCategory === null || $resolutionCategory === ''){
                            $previoustask = self::crmService()->getDetailTaskCRM($obj->id);
                            if ($previoustask) {
                                $resolutionCategory = self::crmService()->getValueLookupCRM('task_resolution_category_list', $previoustask->resolution_category_c);
                            }
                        }
                        $taskSeverity = self::crmService()->getDetailTaskSeverityCRM($obj->id);
                        if($taskSeverity){
                            $severity = self::crmService()->getValueLookupCRM('task_severity_list', $taskSeverity->task_severity);
                        }
                    }

                    //Find User AssignTo
                    $assignTo = '';
                    if ($obj->assigned_user_id != '') {
                        $assignTo = self::crmService()->getNameUserCRM($obj->assigned_user_id);
                    }

                    //Find User CreateBy
                    $createdBy = '';
                    $createdByLocation = '';
                    if ($obj->created_by != '') {
                        $createdBy = self::crmService()->getNameUserCRM($obj->created_by);
                        $createdByLocation = self::crmService()->getLocationUserCRM($obj->created_by); 
                    }

                    //Find Group for CreatedBy
                    //getListDetailGroupCRM
                    $groupCreatedBy = '';
                    if ($obj->created_by != '') {
                        $listGroup = self::crmService()->getListDetailGroupCRM($obj->created_by);
                        if (count($listGroup) > 0) {
                            $listGroupName = $listGroup->pluck('name');
                            $groupCreatedBy = implode(",", $listGroupName->toArray());
                        }
                    }

                    //Find User ModifiedBy
                    $modifiedBy = '';
                    if ($obj->modified_user_id != '') {
                        $modifiedBy = self::crmService()->getNameUserCRM($obj->modified_user_id);
                    }

                    //Find Contact RequestedBy
                    $requestedBy = '';
                    if ($obj->contact_id_c != '') {
                    $requestedBy = self::crmService()->getNameContactCRM($obj->contact_id_c);
                    $emailContact = self::crmService()->getEmailCRM("Contacts", $obj->contact_id_c);

                    $contact = self::crmService()->getDetailContactCRM($obj->contact_id_c);

                    if ($contact) {
                        if ($contact->phone_work != '') {
                            $phoneNoContact = $contact->phone_work;
                        } else if ($contact->phone_mobile != '') {
                            $phoneNoContact = $contact->phone_mobile;
                        } else if ($contact->phone_home != '') {
                            $phoneNoContact = $contact->phone_home;
                        } else {
                            $phoneNoContact = $contact->phone_other;
                        }
                    }
                }

                $category = '';
                    if ($obj->category_c != '') {
                        $category = self::crmService()->getValueLookupCRM('category_list', $obj->category_c);
                    }

                    if ($obj->sub_category_c != null) {
                        $subCategory = self::crmService()->getValueLookupCRM('cdc_sub_category_list', $obj->sub_category_c);
                    }

                    $subSubCategory = '';
                    if ($obj->sub_category_2_c != '') {
                        $subSubCategory = self::crmService()->getValueLookupCRM('cdc_sub_category_2_list', $obj->sub_category_2_c);
                    }

                    $incidentServiceType = '';
                    if ($obj->incident_service_type_c != '') {
                        $incidentServiceType = self::crmService()->getValueLookupCRM('incident_service_type_list', $obj->incident_service_type_c);
                    }

                    $requestType = '';
                    if ($obj->request_type_c != null) {
                        $requestType = self::crmService()->getValueLookupCRM('request_type_list', $obj->request_type_c);
                    }

                    $priority = '';
                    if ($obj->priority != '') {
                        $priority = self::crmService()->getValueLookupCRM('case_priority_dom', $obj->priority);
                    }

                    $statusCase = '';
                    if ($obj->status != '') {
                        $statusCase = self::crmService()->getValueLookupCRM('case_status_dom', $obj->status);
                    }

                    $information = '';
                    if ($obj->case_info_c != '') {
                        $information = self::crmService()->getValueLookupCRM('case_info_list', $obj->case_info_c);
                    }

                    $contactMode = '';
                    if ($obj->contact_mode_c != '') {
                        $contactMode = self::crmService()->getValueLookupCRM('cdc_contact_mode_list', $obj->contact_mode_c);
                    }

                    $ageingDay = '';
                    if ($obj->status == 'Open_New' || $obj->status == 'Open_Assigned' || $obj->status == 'In_Progress' || $obj->status == 'Open_Pending Input') {
                        $dateCreated = Carbon::parse($obj->created_date);
                        $ageingDay = $dateCreated->diffInDays(Carbon::now());
                    }
                    $durationDayToClose = '';
                    if ($obj->status == 'Closed_Closed' || $obj->status == 'Closed_Rejected' || $obj->status == 'Closed_Rejected_Eaduan' || $obj->status == 'Closed_Verified_Eaduan' || $obj->status == 'Closed_Duplicate' ||
                            $obj->status == 'Pending_User_Verification' || $obj->status == 'Open_Resolved' || $obj->status == 'Closed_Cancelled_Eaduan') {
                        $dateCreated = Carbon::parse($obj->created_date);
                        $dateModified = Carbon::parse($obj->modified_date);
                        $durationDayToClose = $dateCreated->diffInDays($dateModified);
                    }

                    $kementerianName = preg_replace('/[,]+/', ' ', trim($kementerian));
                    $kumpulanPtjName = preg_replace('/[,]+/', ' ', trim($kumpulanPtj));
                    $orgNameName = preg_replace('/[,]+/', ' ', trim($orgName));
                    $supplierNameName = preg_replace('/[,]+/', ' ', trim($supplierName));
                    $leadNameName = preg_replace('/[,]+/', ' ', trim($leadName));
                    $subject = preg_replace('/[,]+/', ' ', trim($obj->name));
                    $trimdescription = preg_replace('!\s+!',' ', trim($obj->description));
                    $trimdescriptionLen = '';
                    if((strlen($trimdescription)) > 30000){
                        $trimdescriptionLen = substr($trimdescription, 0, 10000); 
                    }else{
                        $trimdescriptionLen = $trimdescription;
                    }
                    $description = preg_replace('/[,]+/', ' ', trim($trimdescriptionLen));
                    $docNo = preg_replace('/[,"]+/', ' ', trim($obj->doc_no));
                    $strLen = (strlen($obj->resolution));
                    $trimResolution = ''; 
                    if($strLen > 30000){
                        $trimResolution = substr($obj->resolution, 0, 10000); 
                    }else{
                        $trimResolution = $obj->resolution;
                    }
                    $resolution = preg_replace('/[^a-zA-Z0-9]/', ' ', trim($trimResolution));
                    $accSsmNo = preg_replace('/[,]+/', ' ', trim($ssmNo));
                    $redmineNo = preg_replace('/[,]+/', ' ', trim($obj->redmine_number));
                    $caseSubCategory = preg_replace('/[,]+/', ' ', trim($subCategory));
                    $caseSubSubCategory = preg_replace('/[,]+/', ' ', trim($subSubCategory));
                    $emailAdd = preg_replace('/[,]+/', ' ', trim($emailContact));
                    
                    
                    if($phoneNoAcc != '') {
                        $phoneNo = $phoneNoAcc;
                    } else {
                        $phoneNo = $phoneNoContact;
                    }
                    
                    $phoneNumber = preg_replace('/[,]+/', ' ', trim($phoneNo));
                    $cptpp = $obj->cptpp_flag;
                    $cptppValue = 'No';
                    if ($cptpp == 1) {
                        $cptppValue = 'Yes';
                    }
                    $rejectRemark = '';
                    if (($pos = strpos($resolution, " Reject Remark")) !== FALSE) {
                        $rejectRemarks = substr($resolution, $pos+15);
                        $rejectRemark = preg_replace('/[^a-zA-Z0-9]/', ' ', trim($rejectRemarks));
                    }
                    
                $CsvData[] = (
                            $obj->case_number . ',' .
                            $redmineNo . ',' .
                            $kementerianName . ',' .
                            $kumpulanPtjName . ',' .
                            $orgNameName . ',' .
                            $orgCode . ',' .
                            $organizationType . ',' .
                            $supplierNameName . ',' .
                            $mofno . ',' .
                            $leadNameName . ',' .
                            $accSsmNo . ',' .
                            $accountType . ',' .
                            $phoneNumber .',' .
                            $requestType .',' .
                            $incidentServiceType .',' .
                            $cptppValue .',' .
                            $caseSubCategory . ',' .
                            $caseSubSubCategory . ',' .
                            $subject . ',' .
                            $description . ',' .
                            $docNo . ',' .
                            $resolution . ',' .
                            $rejectRemark . ',' .
                            Carbon::parse($obj->created_date) . ',' . 
                            Carbon::parse($obj->modified_date) . ',' .
                            $createdBy . ',' .
                            $createdByLocation .','.
                            $statusCase . ',' .
                            $contactMode . ',' .
                            $ageingDay . ',' .
                            $durationDayToClose . ',' .
                            $emailAdd . ',' .
                            $stateAccount . ',' .
                            $taskStatus . ',' .
                            $taskAssignGroup.','.
                            $resolutionCategory
//                            .','.$severity
                            );
                }
                
                $takentimeeachLoop = array(
                'Counter' => $start,
                'Taken Time per Minutes' => $dtStartTimeEachLoop->diffInMinutes(Carbon::now()),
                'Taken Time per Seconds' => $dtStartTimeEachLoop->diffInSeconds(Carbon::now())
            );
            var_dump(self::class . '    :: LoopTakenTime >> Time   :   ', [$takentimeeachLoop]);
            var_dump(self::class . '    :: sum total current  :   ' . $totalRecords);
            
        } while (count($dataSub1) > 0 && count($dataSub1) == self::$query_take);

        $takentimeOP = array(
            'Counter' => $start,
            'Taken Time per Minutes' => $dtStartTimeOP->diffInMinutes(Carbon::now()),
            'Taken Time per Seconds' => $dtStartTimeOP->diffInSeconds(Carbon::now())
        );
        var_dump(self::class . '    :: AllLoopTakenTime >> Time   :   ', [$takentimeOP]);
        var_dump(self::class . ' queryReport. Total All :  ' . $totalRecords);
        var_dump(self::class . '--------------------------------------------');

        $filename = 'CaseRejectedEaduan_' . $dateStart . '_to_' . $dateEnd. ".csv";
        $file_path = storage_path() . '/app/exports/cases/' . $filename;
        $file = fopen($file_path, "w+");
        foreach ($CsvData as $exp_data) {
            fputcsv($file, explode(',', $exp_data));
        }
        fclose($file);

        $dataReport = collect([]);
        $dataReport->put("date_start", $dateStart);
        $dataReport->put("date_end",$dateEnd);
        $dataReport->put("report_name",'Case Rejected Eaduan');
        $dataReport->put("file_name",$filename);
        
        return $dataReport;
        
        //$headers = ['Content-Type' => 'application/csv'];
        //return response()->download($file_path, $filename, $headers);
    }

    protected static function getQuery($dateStart,$dateEnd,$take,$nextSkip,$status) {
        var_dump('DateStart:'.$dateStart,'DateEnd:'.$dateEnd,'Take:'.$take,'Skip:'.$nextSkip);
        $financeSubCategory = array('10712_15034', '10714_15842', '10713_15534');
        $reqtype = array('incident', 'service');
        $onsiteCategory = array('10719','10720','10721','10722');

        $sql = DB::table('cases')
                ->join('cases_cstm', 'cases.id', '=', 'cases_cstm.id_c')
                ->where('cases.deleted', 0)
                ->where('cases.status','Closed_Rejected_Eaduan')
                ->whereIn('cases_cstm.request_type_c', $reqtype)
                ->whereNotIn('cases_cstm.category_c', $onsiteCategory)
                ->whereNotIn('cases_cstm.sub_category_c', $financeSubCategory);
        
        $columnDate = 'cases.date_entered';
        if($status != ''){
            $sql->where('cases.state',$status);
            if($status == 'Closed'){
                $columnDate = 'cases.date_modified';
            }
        }
        $sql->whereBetween(DB::raw("DATE(CONVERT_TZ($columnDate,'+00:00','+08:00'))"), [
            $dateStart,
            $dateEnd
        ]);

        $sql->select('cases.*', 'cases_cstm.*');
        $sql->addSelect(DB::raw("CONVERT_TZ(cases.date_entered,'+00:00','+08:00') AS created_date"));
        $sql->addSelect(DB::raw("CONVERT_TZ(cases.date_modified,'+00:00','+08:00') AS modified_date"));
        $sql->skip($nextSkip)->take($take);

        $data = array(
            "sql" => $sql->toSql(),
            "parameter" => $sql->getBindings()
        );

//        dump(self::class . ' :: getQuery >> SQL   :   ', [$data]);
        return $result = $sql->get();
    }
    
    protected static function  sendEmail($dataReport) {
        
        $data = array(
             "to" => ['<EMAIL>','<EMAIL>'],
            "subject" => "Server (".env('APP_ENV').") > CRM Report : ".$dataReport->get('report_name')." pada ".$dataReport->get('date_start')." sehingga ".$dataReport->get('date_end'),
         
        );
        try {
            Mail::send('emails.generate_report_crm',['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'data' => $dataReport], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])->subject($data["subject"]);
                // ->setBody('<a href="'.url('/crm/casedetail/download/'.$fileName).'" target =" blank"> Download Report </a>', 'text/html');
            });
            var_dump('done send');
        } catch (\Exception $e) {
            echo $e;
            Log::error(self::class . ' Error ... ' . __FUNCTION__.' ::  ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            var_dump('error' .$e);
            return $e;
        }
   }
     
}
