<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Report\Crm;

use Carbon\Carbon;
use DB;
use Log;
use Excel;
use Mail;
use Config;
use App\Migrate\MigrateUtils;
use App\Services\CRMService;

class CaseStatisticReport {

    public function __construct() {
    }
    
    public  function crmService() {
        return new CRMService;
    }

    /*
    Group IT Coordinator  			bd305f97-902e-e186-f506-58997eeecc12
    Archissoft Build Team			15c7bd74-12f3-311b-9e8f-5a810702a1a5
    Group Middleware                            5f919eef-f9cc-4278-b7d1-bbc1aa87ff6e
    Group IT Specialist(Production Support)	d3bf216c-122b-4ce5-9410-899317762b60
    Group IT Specialist(Network Admin)          bb59c521-4a4d-a001-a8aa-58d09f694ae7
    Group IT Specialist(Server Admin)           825b06f5-d0b6-e6da-0d56-58d0a0bca1e4
    Group IT Specialist(Developer)		5dac7b92-18d0-4beb-b600-413957aa4c26
    Group IT Specialist(Security)		86dde498-da2d-4208-a7dd-786980e6827a
    */
    protected $listGroupSelectedID =   "('bd305f97-902e-e186-f506-58997eeecc12',
                                        '15c7bd74-12f3-311b-9e8f-5a810702a1a5',
                                        '5f919eef-f9cc-4278-b7d1-bbc1aa87ff6e',
                                        'd3bf216c-122b-4ce5-9410-899317762b60',
                                        'bb59c521-4a4d-a001-a8aa-58d09f694ae7',
                                        '825b06f5-d0b6-e6da-0d56-58d0a0bca1e4',
                                        '5dac7b92-18d0-4beb-b600-413957aa4c26',
                                        '86dde498-da2d-4208-a7dd-786980e6827a')";
    /*
    protected $listTaskMissing =   "(
                            'Check document status',
                            'Missing Role',
                            'Missing data',
                            'Missing from Task List'
                            )";
    */
    protected $listTaskMissing =   "(
                            'Missing from Task List',
                            'Stuck Task List'
                            )";
    
    protected $listManualContract =   "(
                            'Lampiran A - Contract',
                            'Lampiran C – Items'
                            )";
    
    protected $listIntegration =   "(
                            'Integration-1GFMAS-Internal',
                            'Integration-PHIS-eP',
                            'Integration',
                            'Integration failed',
                            'Integration too long'
                            )";
    /*
    protected $listCaseSubModuleCheck = "('Check document status',
                                                'Missing Role',
                                                'Missing data',
                                                'Missing from Task List',
                                                'Lampiran A - Contract',
                                                'Lampiran C – Items',
                                                'Integration-1GFMAS-Internal',
                                                'Integration-PHIS-eP',
                                                'Integration',
                                                'Integration failed',
                                                'Integration too long')";
    */
    protected $listCaseSubModuleCheck = "(
                                                'Missing from Task List',
                                                'Lampiran A - Contract',
                                                'Lampiran C – Items',
                                                'Integration-1GFMAS-Internal',
                                                'Integration-PHIS-eP',
                                                'Integration',
                                                'Integration failed',
                                                'Integration too long')";
    
    protected $listCaseSubModuleExclude = "(
                                               
                                                'Lampiran A - Contract',
                                                'Lampiran C – Items')";
    
    public function run(Carbon $dateReport) {
        $dataArray = $this->generateReportByDate($dateReport);
        if($dataArray != null){
            $this->sendSuccessEmail($dataArray);
            $this->sendSuccessEmailForBigBoss($dataArray);
            dump("Completed! Send CaseStatisticReport to Email");
            Log::info(self::class . ' > '.__FUNCTION__. ' Completed!  Send CaseStatisticReport to Email');
        }else{
            dump("Error Send Email. No data received!");
            Log::info(self::class . ' > '.__FUNCTION__. ' Error Send Email. No data received!');
        }
        
    }
    
    public function runToSpecificPerson(Carbon $dateReport) {
        $dataArray = $this->generateReportByDate($dateReport);
        if($dataArray != null){
            $this->sendSuccessToSpecificEmail($dataArray);
            dump("Completed!  Send CaseStatisticReport to Email");
            Log::info(self::class . ' > '.__FUNCTION__. ' Completed!  Send CaseStatisticReport to Email');
        }else{
            dump("Error Send Email. No data received!");
            Log::info(self::class . ' > '.__FUNCTION__. ' Error Send Email. No data received!');
        }

    }
    
    public function runMyTest(Carbon $dateReport) {
        $dataArray = $this->generateReportByDate($dateReport);
        if($dataArray != null){
            $this->sendSuccessEmailMyTest($dataArray);
            dump("Completed!  Send CaseStatisticReport to Email");
            Log::info(self::class . ' > '.__FUNCTION__. ' Completed!  Send CaseStatisticReport to Email');
        }else{
            dump("Error Send Email. No data received!");
            Log::info(self::class . ' > '.__FUNCTION__. ' Error Send Email. No data received!');
        }
    }
    
    public function runStatisticUsers(Carbon $dateReport) {
        $dataArray = $this->generateReportByDate($dateReport);
        if($dataArray != null){
            $this->sendSuccessToSpecificEmailReportStatisticUsers($dataArray);
            dump("Completed!  Send runStatisticUsers Case Resolved to Email");
            Log::info(self::class . ' > '.__FUNCTION__. ' Completed!  Send runStatisticUsers Case Resolved  to Email');
        }else{
            dump("Error Send Email. No data received!");
            Log::info(self::class . ' > '.__FUNCTION__. ' Error Send Email. No data received!');
        }
    }
    
    public function generateReportByDate($dateReport) {
        Log::info(self::class . ' starting ..', ['Date' => $dateReport]);
        dump(self::class . ' starting ..', ['Date' => $dateReport ]);
        $dtStartTime = Carbon::now();

        $dateReportFirstDayOfYear = Carbon::now()->firstOfYear();
        //$dateReport = Carbon::now();

        try {

            $totalCloseCases = $this->totalCloseCases($dateReport);
            $totalPendingCases = $this->totalPendingCases($dateReport);
            $totalPendingInputCases = $this->totalPendingInputCases($dateReport);
            $totalInProgressCases = $this->totalInProgressCases($dateReport);
            $totalVerifyCSCases = $this->totalVerifyCSCases($dateReport);

            $totalAllPendingCasesByModuleOther = $this->totalPendingCasesByModuleOther($dateReportFirstDayOfYear, $dateReport, 'CREATED');
            $totalAllPendingCasesByModule = $this->totalPendingCasesByModule($dateReportFirstDayOfYear, $dateReport, 'CREATED');

            /* Start Daily */
            $totalDailyPendingCasesByModuleOther = $this->totalPendingCasesByModuleOther($dateReport, $dateReport, 'MODIFIED');
            $totalDailyPendingCasesByModule = $this->totalPendingCasesByModule($dateReport, $dateReport, 'MODIFIED');


            $totalCreatedCases = $this->totalCreatedCases($dateReport);
            $totalCreatedCasesByModuleOther = $this->totalCreatedCasesByModuleOther($dateReport);
            $totalCreatedCasesByModule = $this->totalCreatedCasesByModule($dateReport);

            $totalResolvedCases = $this->totalResolvedCases($dateReport);
            $totalResolvedCasesByModuleOther = $this->totalResolvedCasesByModuleOther($dateReport);
            $totalResolvedCasesByModule = $this->totalResolvedCasesByModule($dateReport);

            $data = $this->statisticUsersResolved($dateReport);
            $dataStatisticUsersResolveByModuleOther = $this->statisticUsersResolvedByCasesModuleOther($dateReport);
            $dataStatisticUsersResolveByModule = $this->statisticUsersResolvedByCasesModule($dateReport);

            /* End  Daily */



            /** Populate report statistic for cases by case type * */
            $totalStatisticCasesTemp = array();
            $this->populateStatisticReport($totalStatisticCasesTemp, $totalCreatedCases, array('key' => 'case_type', 'load' => 'created'));
            $this->populateStatisticReport($totalStatisticCasesTemp, $totalResolvedCases, array('key' => 'case_type', 'load' => 'resolved'));
            $totalStatisticCases = array_values($totalStatisticCasesTemp);



            /** Populate report statistic for cases by module (others)  * */
            $totalStatisticByOtherModuleTemp = array();
            $this->populateStatisticReport($totalStatisticByOtherModuleTemp, $totalCreatedCasesByModuleOther, array('key' => 'module', 'load' => 'created'));
            $this->populateStatisticReport($totalStatisticByOtherModuleTemp, $totalResolvedCasesByModuleOther, array('key' => 'module', 'load' => 'resolved'));
            $this->populateStatisticReport($totalStatisticByOtherModuleTemp, $totalDailyPendingCasesByModuleOther, array('key' => 'module', 'load' => 'pending'));
            $this->populateStatisticReport($totalStatisticByOtherModuleTemp, $totalAllPendingCasesByModuleOther, array('key' => 'module', 'load' => 'all-pending'));
            $totalStatisticByOtherModule = array_values($totalStatisticByOtherModuleTemp);

            /** Populate report statistic for cases by module (priority)  * */
            $totalStatisticByModuleTemp = array();
            $this->populateStatisticReport($totalStatisticByModuleTemp, $totalCreatedCasesByModule, array('key' => 'module', 'load' => 'created'));
            $this->populateStatisticReport($totalStatisticByModuleTemp, $totalResolvedCasesByModule, array('key' => 'module', 'load' => 'resolved'));
            $this->populateStatisticReport($totalStatisticByModuleTemp, $totalDailyPendingCasesByModule, array('key' => 'module', 'load' => 'pending'));
            $this->populateStatisticReport($totalStatisticByModuleTemp, $totalAllPendingCasesByModule, array('key' => 'module', 'load' => 'all-pending'));
            $totalStatisticByModule = array_values($totalStatisticByModuleTemp);

            $dataArray = [
                'date' => $dateReport->toFormattedDateString(),
                'dateFirstOfYear' => $dateReportFirstDayOfYear->toFormattedDateString(),
                'totalCreated' => $totalCreatedCases,
                'totalCreatedCasesByModuleOther' => $totalCreatedCasesByModuleOther,
                'totalCreatedCasesByModule' => $totalCreatedCasesByModule,
                'totalResolved' => $totalResolvedCases,
                'totalResolvedCasesByModuleOther' => $totalResolvedCasesByModuleOther,
                'totalResolvedCasesByModule' => $totalResolvedCasesByModule,
                'totalCloseCases' => $totalCloseCases,
                'totalPendingCases' => $totalPendingCases,
                'totalPendingInputCases' => $totalPendingInputCases,
                'totalInProgressCases' => $totalInProgressCases,
                'totalVerifyCSCases' => $totalVerifyCSCases,
                'totalStatisticCases' => $totalStatisticCases,
                'totalStatisticByModuleOther' => $totalStatisticByOtherModule,
                'totalStatisticByModule' => $totalStatisticByModule,
                'bigboss' => false,
                'data' => $data,
                'dataStatisticUsersResolveByModuleOther' => $dataStatisticUsersResolveByModuleOther,
                'dataStatisticUsersResolveByModule' => $dataStatisticUsersResolveByModule
            ];
            
            $logsdata = self::class . ' Query Date Start : ' . $dtStartTime . ' , '
                    . 'Query Date End : ' . Carbon::now() . ' , Completed --- Taken Time : ' .
                    json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);

            Log::info($logsdata);
            dump($logsdata);
            
            return $dataArray;
        } catch (\Exception $exc) {
            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            echo $exc->getTraceAsString();
        }
        
        return null;
    }
    
    protected function populateStatisticReport(&$totalStatisticTemp,$listRawStatistic,$parameters){

         foreach ($listRawStatistic as $obj){
             $ob = null;
             if($parameters['key'] == 'module'){
                if(array_key_exists($obj->module, $totalStatisticTemp)){
                    $ob = $totalStatisticTemp[$obj->module];
                }
             }else if($parameters['key'] == 'case_type'){
                if(array_key_exists($obj->case_type, $totalStatisticTemp)){
                    $ob = $totalStatisticTemp[$obj->case_type];
                }
             }
             
             if($ob == null){
                 $ob = objectValue();
                 if($parameters['key'] == 'module'){
                    $ob->module = $obj->module;
                 }else if($parameters['key'] == 'case_type'){
                    $ob->case_type = $obj->case_type;
                 }
                 
                $ob->total_resolved = 0;
                $ob->total_created = 0;
                $ob->total_pending = 0;
                $ob->total_all_pending = 0;
             }

             if($parameters['load'] == 'created'){
                if($ob->total_created  >= 0  ){
                    $ob->total_created = $ob->total_created + $obj->total;
                }
             }else if($parameters['load'] == 'resolved'){
                if($ob->total_resolved  >= 0  ){
                    $ob->total_resolved = $ob->total_resolved + $obj->total;
                }
             }else if($parameters['load'] == 'pending'){
                if($ob->total_pending  >= 0  ){
                    $ob->total_pending = $ob->total_pending + $obj->total;
                }
             }else if($parameters['load'] == 'all-pending'){
                if($ob->total_all_pending  >= 0  ){
                    $ob->total_all_pending = $ob->total_all_pending + $obj->total;
                }
             }

             if($parameters['key'] == 'module'){
                $totalStatisticTemp[$obj->module] = $ob;
             }else if($parameters['key'] == 'case_type'){
                $totalStatisticTemp[$obj->case_type] = $ob;
             }
         }
    }
    
    protected function totalCloseCases(Carbon $dateReport){
        $dateReportFirstDayOfYear = Carbon::now()->firstOfYear();
           $totalPendingCases = DB::select("SELECT 
                CASE
                    WHEN b.incident_service_type_c = 'incident_it' THEN 'IT INCIDENT' 
                    WHEN b.incident_service_type_c = 'service_it' THEN 'IT SERVICE'  
                    ELSE 'N/A' 
                END AS case_type, COUNT(a.id) AS total 
                FROM cases a, cases_cstm b 
                WHERE a.id=b.id_c AND b.incident_service_type_c IN ('incident_it','service_it') 
                AND a.status IN ('Closed_Closed') 
                AND  DATE(CONVERT_TZ(a.date_entered, '+00:00', '+08:00'))
                BETWEEN STR_TO_DATE(?,'%Y-%m-%d') AND STR_TO_DATE(?,'%Y-%m-%d') GROUP BY b.incident_service_type_c", 
                array($dateReportFirstDayOfYear->toDateString(),$dateReport->toDateString())); 
           return $totalPendingCases;
    }
    
    protected function totalPendingInputCases(Carbon $dateReport){
            $dateReportFirstDayOfYear = Carbon::now()->firstOfYear();
            $totalPendingInputCases = DB::select("SELECT 
                COUNT(a.id) AS total 
                FROM cases a, cases_cstm b 
                WHERE a.id=b.id_c  
                AND a.status = 'Open_Pending Input'  
                AND a.state IN ('OPEN')   
                AND a.account_id IS NOT NULL 
                AND b.`contact_id_c` IS NOT NULL 
                AND a.deleted = 0 
                AND  DATE(CONVERT_TZ(a.date_entered, '+00:00', '+08:00'))
                BETWEEN STR_TO_DATE(?,'%Y-%m-%d') AND STR_TO_DATE(?,'%Y-%m-%d') ", 
                array($dateReportFirstDayOfYear->toDateString(),$dateReport->toDateString())); 
           return $totalPendingInputCases;
    }
    
    protected function totalPendingCases(Carbon $dateReport){
            $dateReportFirstDayOfYear = Carbon::now()->firstOfYear();
            $totalPendingCases = DB::select("SELECT 
                CASE
                    WHEN b.incident_service_type_c = 'incident_it' THEN 'IT INCIDENT' 
                    WHEN b.incident_service_type_c = 'service_it' THEN 'IT SERVICE'  
                    ELSE 'N/A' 
                END AS case_type, COUNT(a.id) AS total 
                FROM cases a, cases_cstm b , tasks c , users d  
                WHERE a.id=b.id_c AND a.id = c.parent_id AND c.assigned_user_id = d.id 
                AND c.date_entered IN ( SELECT MAX(date_entered) FROM tasks WHERE parent_id = a.id ) 
                AND c.assigned_user_id IN $this->listGroupSelectedID  
                AND b.incident_service_type_c IN ('incident_it','service_it') 
                AND a.status IN ('open_assigned','In_Progress') 
                AND a.redmine_number IS NULL 
                AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('PTJ Onsite')) AS tm 
		    WHERE   tm.value_code = b.sub_category_c ) 
                AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('Onsite Support')) AS tm 
		    WHERE   tm.value_code = b.sub_category_2_c ) 
                AND  DATE(CONVERT_TZ(a.date_entered, '+00:00', '+08:00'))
                BETWEEN STR_TO_DATE(?,'%Y-%m-%d') AND STR_TO_DATE(?,'%Y-%m-%d') GROUP BY b.incident_service_type_c", 
                array($dateReportFirstDayOfYear->toDateString(),$dateReport->toDateString())); 
           return $totalPendingCases;
    }
    
    protected function totalInProgressCases(Carbon $dateReport){
            $dateReportFirstDayOfYear = Carbon::now()->firstOfYear();
            $totalInProgressCases = DB::select("SELECT 
                CASE
                    WHEN b.incident_service_type_c = 'incident_it' THEN 'IT INCIDENT' 
                    WHEN b.incident_service_type_c = 'service_it' THEN 'IT SERVICE'  
                    ELSE 'N/A' 
                END AS case_type, COUNT(a.id) AS total 
                FROM cases a, cases_cstm b , tasks c , users d  
                WHERE a.id=b.id_c AND a.id = c.parent_id AND c.assigned_user_id = d.id 
                AND c.date_entered IN ( SELECT MAX(date_entered) FROM tasks WHERE parent_id = a.id ) 
                AND c.assigned_user_id IN $this->listGroupSelectedID  
                AND b.incident_service_type_c IN ('incident_it','service_it') 
                AND a.status  in ('open_assigned','In_Progress')  
                AND a.redmine_number IS NOT NULL  
                AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('PTJ Onsite')) AS tm 
		    WHERE   tm.value_code = b.sub_category_c ) 
                AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('Onsite Support')) AS tm 
		    WHERE   tm.value_code = b.sub_category_2_c ) 
                AND  DATE(CONVERT_TZ(a.date_entered, '+00:00', '+08:00'))
                BETWEEN STR_TO_DATE(?,'%Y-%m-%d') AND STR_TO_DATE(?,'%Y-%m-%d') GROUP BY b.incident_service_type_c", 
                array($dateReportFirstDayOfYear->toDateString(),$dateReport->toDateString())); 
           return $totalInProgressCases;
    }
    
    /**
     * All cases for Last task name : Assigned to Case Owner 
     * @param Carbon $dateReport
     * @return type
     */
    protected function totalVerifyCSCases(Carbon $dateReport){
            $dateReportFirstDayOfYear = Carbon::now()->firstOfYear();
            $totalVerifyCSCases = DB::select("SELECT 
                CASE
                WHEN cc.incident_service_type_c = 'incident_it' THEN 'IT INCIDENT' 
                WHEN cc.incident_service_type_c = 'service_it' THEN 'IT SERVICE' 
                ELSE 'N/A' 
                END AS case_type, COUNT(cc.incident_service_type_c) AS total 
              FROM
                cases c,
                cases_cstm cc,
                tasks t,
                tasks_cstm tc,
                users u 
              WHERE c.id = cc.id_c 
                AND t.parent_id = c.id 
                AND t.parent_type = 'Cases' 
                AND t.id = tc.id_c 
                AND t.created_by = u.id 
                AND t.name = ?
                AND cc.incident_service_type_c IN ('incident_it','service_it')
                AND c.status  <> 'Closed_Closed'
                AND t.status <> ('Completed') 
                AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('PTJ Onsite')) AS tm 
		    WHERE   tm.value_code = cc.sub_category_c ) 
                AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('Onsite Support')) AS tm 
		    WHERE   tm.value_code = cc.sub_category_2_c ) 
                AND DATE(CONVERT_TZ(t.date_entered, '+00:00', '+08:00'))
                       BETWEEN STR_TO_DATE(?,'%Y-%m-%d') AND STR_TO_DATE(?,'%Y-%m-%d')
              GROUP BY cc.incident_service_type_c  ", 
                array('Assigned to Case Owner',$dateReportFirstDayOfYear->toDateString(),$dateReport->toDateString())) ; 
           return $totalVerifyCSCases;
    }
    
    protected function totalCreatedCases(Carbon $dateReport){
           $totalCreatedCases = DB::select("SELECT CASE
                    WHEN b.incident_service_type_c = 'incident_it' THEN 'IT INCIDENT' 
                    WHEN b.incident_service_type_c = 'service_it' THEN 'IT SERVICE' 
                    ELSE 'N/A' 
                    END AS case_type, COUNT(a.id) AS total 
                    FROM cases a, cases_cstm b , tasks c , users d  
                    WHERE a.id=b.id_c AND a.id = c.parent_id AND c.assigned_user_id = d.id 
                    AND c.date_entered IN ( SELECT MAX(date_entered) FROM tasks WHERE parent_id = a.id ) 
                    AND c.assigned_user_id IN $this->listGroupSelectedID  
                    AND b.incident_service_type_c IN ('incident_it','service_it') 
                    AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('PTJ Onsite')) AS tm 
                        WHERE   tm.value_code = b.sub_category_c ) 
                    AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('Onsite Support')) AS tm 
                        WHERE   tm.value_code = b.sub_category_2_c ) 
                    AND  DATE(CONVERT_TZ(a.date_entered, '+00:00', '+08:00'))
                    BETWEEN STR_TO_DATE(?,'%Y-%m-%d') AND STR_TO_DATE(?,'%Y-%m-%d') GROUP BY b.incident_service_type_c", 
                array($dateReport->toDateString(),$dateReport->toDateString())); 
           return $totalCreatedCases;
    }
              
    protected function totalPendingCasesByModule(Carbon $startDate,Carbon $endDate,$typeDate){
        $query = "SELECT 
                    m.value_name AS module,
                    COUNT(a.id) AS total 
                    FROM cases a, cases_cstm b  , tasks c , users d , 
                    cstm_list_app m  
                    WHERE a.id=b.id_c 
                    AND a.id = c.parent_id AND c.assigned_user_id = d.id 
                    AND c.date_entered IN ( SELECT MAX(date_entered) FROM tasks WHERE parent_id = a.id ) 
                    AND c.assigned_user_id IN $this->listGroupSelectedID  
                    AND b.incident_service_type_c IN ('incident_it','service_it') 
                    AND b.sub_category_c = m.value_code 
                    AND  NOT EXISTS
                            (
                            SELECT  NULL 
                            FROM    (
                                            SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN $this->listCaseSubModuleCheck ) AS tm 
                            WHERE   tm.value_code = b.sub_category_2_c 
                            )
                    AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('PTJ Onsite')) AS tm 
                        WHERE   tm.value_code = b.sub_category_c ) 
                    AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('Onsite Support')) AS tm 
                        WHERE   tm.value_code = b.sub_category_2_c ) 
                    AND a.status  IN ('open_assigned','In_Progress') ";
        if($typeDate == 'MODIFIED'){
            $query = $query. " AND  DATE(CONVERT_TZ(a.date_modified, '+00:00', '+08:00'))
                    BETWEEN STR_TO_DATE(?,'%Y-%m-%d') AND STR_TO_DATE(?,'%Y-%m-%d') GROUP BY m.value_name ";
        }else{
           $query = $query. " AND  DATE(CONVERT_TZ(a.date_entered, '+00:00', '+08:00'))
                    BETWEEN STR_TO_DATE(?,'%Y-%m-%d') AND STR_TO_DATE(?,'%Y-%m-%d') GROUP BY m.value_name "; 
        }
        $data = DB::select($query, 
             array($startDate->toDateString(),$endDate->toDateString())); 
        return $data;
    }
    
    protected function totalPendingCasesByModuleOther(Carbon $startDate,Carbon $endDate,$typeDate){
        $query = "SELECT 
                    tm.module,
                    COUNT(a.id) AS total 
                    FROM cases a, cases_cstm b , tasks c , users d  ,
                    (
                            SELECT 'Missing Tasklist' AS module,value_code,value_name FROM cstm_list_app WHERE value_name  IN $this->listTaskMissing 
                            UNION
                            SELECT 'Integration' AS module,value_code,value_name FROM cstm_list_app WHERE value_name  IN $this->listIntegration 
                            UNION
                            SELECT 'Manual Contract' AS module,value_code,value_name FROM cstm_list_app WHERE value_name  IN $this->listManualContract 
                            
                    ) AS tm 
                    WHERE a.id=b.id_c 
                    AND a.id = c.parent_id AND c.assigned_user_id = d.id 
                    AND c.date_entered IN ( SELECT MAX(date_entered) FROM tasks WHERE parent_id = a.id ) 
                    AND c.assigned_user_id IN $this->listGroupSelectedID  
                    AND b.incident_service_type_c IN ('incident_it','service_it') 
                    AND tm.value_code = b.sub_category_2_c  
                    AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('PTJ Onsite')) AS tm 
                        WHERE   tm.value_code = b.sub_category_c ) 
                    AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('Onsite Support')) AS tm 
                        WHERE   tm.value_code = b.sub_category_2_c ) 
                    AND a.status  IN ('open_assigned','In_Progress')  ";
        if($typeDate == 'MODIFIED'){
            $query = $query. " AND  DATE(CONVERT_TZ(a.date_modified, '+00:00', '+08:00'))
                    BETWEEN STR_TO_DATE(?,'%Y-%m-%d') AND STR_TO_DATE(?,'%Y-%m-%d') GROUP BY  tm.module";
        }else{
           $query = $query. " AND  DATE(CONVERT_TZ(a.date_entered, '+00:00', '+08:00'))
                    BETWEEN STR_TO_DATE(?,'%Y-%m-%d') AND STR_TO_DATE(?,'%Y-%m-%d') GROUP BY  tm.module"; 
        }
        $data = DB::select($query, 
                array($startDate->toDateString(),$endDate->toDateString())); 
        return $data;
    }
    
    protected function totalCreatedCasesByModuleOther(Carbon $dateReport){
           $totalCreatedCasesByModuleOther = DB::select("SELECT 
                        tm.module,
                        COUNT(a.id) AS total 
                        FROM cases a, cases_cstm b  , tasks c , users d  ,
                        (

                                SELECT 'Missing Tasklist' AS module,value_code,value_name FROM cstm_list_app WHERE value_name  IN $this->listTaskMissing 
                                UNION
                                SELECT 'Integration' AS module,value_code,value_name FROM cstm_list_app WHERE value_name  IN $this->listIntegration 
                                UNION
                                SELECT 'Manual Contract' AS module,value_code,value_name FROM cstm_list_app WHERE value_name  IN $this->listManualContract 
                                
                        ) AS tm 
                        WHERE a.id=b.id_c 
                        AND a.id = c.parent_id AND c.assigned_user_id = d.id 
                        AND c.date_entered IN ( SELECT MAX(date_entered) FROM tasks WHERE parent_id = a.id ) 
                        AND c.assigned_user_id IN $this->listGroupSelectedID                         
                        AND b.incident_service_type_c IN ('incident_it','service_it') 
                        AND tm.value_code = b.sub_category_2_c  
                        AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('PTJ Onsite')) AS tm 
                            WHERE   tm.value_code = b.sub_category_c ) 
                        AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('Onsite Support')) AS tm 
                            WHERE   tm.value_code = b.sub_category_2_c ) 
                        AND  DATE(CONVERT_TZ(a.date_entered, '+00:00', '+08:00'))
                        BETWEEN STR_TO_DATE(?,'%Y-%m-%d') AND STR_TO_DATE(?,'%Y-%m-%d') GROUP BY tm.module", 
                array($dateReport->toDateString(),$dateReport->toDateString())); 
           return $totalCreatedCasesByModuleOther;
    }
    
    protected function totalCreatedCasesByModule(Carbon $dateReport){
           $totalCreatedCasesByModule = DB::select("SELECT 
                        m.value_name AS module,
                        COUNT(a.id) AS total 
                        FROM cases a, cases_cstm b , tasks c , users d , 
                        cstm_list_app m  
                        WHERE a.id=b.id_c 
                        AND a.id = c.parent_id AND c.assigned_user_id = d.id 
                        AND c.date_entered IN ( SELECT MAX(date_entered) FROM tasks WHERE parent_id = a.id ) 
                        AND c.assigned_user_id IN $this->listGroupSelectedID  
                        AND b.incident_service_type_c IN ('incident_it','service_it') 
                        AND b.sub_category_c = m.value_code 
                        AND  NOT EXISTS
                                (
                                SELECT  NULL 
                                FROM    (
                                                SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN $this->listCaseSubModuleCheck 
                                        ) AS tm 
                                WHERE   tm.value_code = b.sub_category_2_c 
                                )
                        AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('PTJ Onsite')) AS tm 
                            WHERE   tm.value_code = b.sub_category_c ) 
                        AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('Onsite Support')) AS tm 
                            WHERE   tm.value_code = b.sub_category_2_c )         
                        AND  DATE(CONVERT_TZ(a.date_entered, '+00:00', '+08:00'))
                        BETWEEN STR_TO_DATE(?,'%Y-%m-%d') AND STR_TO_DATE(?,'%Y-%m-%d') GROUP BY m.value_name", 
                array($dateReport->toDateString(),$dateReport->toDateString())); 
           return $totalCreatedCasesByModule;
    }
   
    /**
     * Cases as resolved as Task Name: 'Assigned to Case Owner','Task Rejected'
     * @param Carbon $dateReport
     * @return type
     */
    protected function totalResolvedCases(Carbon $dateReport){
           $totalResolvedCases = DB::select("SELECT 
                    CASE
                    WHEN cc.incident_service_type_c = 'incident_it' THEN 'IT INCIDENT' 
                    WHEN cc.incident_service_type_c = 'service_it' THEN 'IT SERVICE' 
                    ELSE 'N/A' 
                    END AS case_type, COUNT(cc.incident_service_type_c) AS total 
                  FROM
                    cases c,
                    cases_cstm cc,
                    tasks t,
                    tasks_cstm tc,
                    users u 
                  WHERE c.id = cc.id_c 
                    AND t.parent_id = c.id 
                    AND t.parent_type = 'Cases' 
                    AND t.id = tc.id_c 
                    AND t.created_by = u.id 
                    AND t.name in ('Assigned to Case Owner','Task Rejected')   
                    AND cc.incident_service_type_c IN ('incident_it','service_it') 
                    AND  NOT EXISTS
                                (
                                SELECT  NULL 
                                FROM    (
                                                SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN $this->listCaseSubModuleExclude 
                                        ) AS tm 
                                WHERE   tm.value_code = cc.sub_category_2_c 
                                ) 
                    AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('PTJ Onsite')) AS tm 
                        WHERE   tm.value_code = cc.sub_category_c ) 
                    AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('Onsite Support')) AS tm 
                        WHERE   tm.value_code = cc.sub_category_2_c ) 
                    AND DATE(CONVERT_TZ(t.date_entered, '+00:00', '+08:00'))
                           BETWEEN STR_TO_DATE(?,'%Y-%m-%d') AND STR_TO_DATE(?,'%Y-%m-%d')
                  GROUP BY cc.incident_service_type_c ", 
                array($dateReport->toDateString(),$dateReport->toDateString())); 
           return $totalResolvedCases;
    }
    
    protected function totalResolvedCasesByModuleOther(Carbon $dateReport){
           $totalResolvedCasesByModuleOther = DB::select("SELECT 
                    tm.module, 
                    COUNT(*) AS total 
                  FROM
                    cases c,
                    cases_cstm cc,
                    tasks t,
                    tasks_cstm tc,
                    users u ,
                    (
                        SELECT 'Missing Tasklist' AS module,value_code,value_name FROM cstm_list_app WHERE value_name  IN $this->listTaskMissing 
                        UNION
                        SELECT 'Integration' AS module,value_code,value_name FROM cstm_list_app WHERE value_name  IN $this->listIntegration     
                        UNION
                        SELECT 'Manual Contract' AS module,value_code,value_name FROM cstm_list_app WHERE value_name  IN $this->listManualContract 
                        
                ) AS tm 
                  WHERE c.id = cc.id_c 
                    AND t.parent_id = c.id 
                    AND t.parent_type = 'Cases' 
                    AND t.id = tc.id_c 
                    AND t.created_by = u.id 
                    AND t.name  in ('Assigned to Case Owner','Task Rejected') 
                    AND tm.value_code = cc.sub_category_2_c 
                    AND cc.incident_service_type_c IN ('incident_it','service_it') 
                    AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('PTJ Onsite')) AS tm 
                        WHERE   tm.value_code = cc.sub_category_c ) 
                    AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('Onsite Support')) AS tm 
                        WHERE   tm.value_code = cc.sub_category_2_c ) 
                    AND DATE(CONVERT_TZ(t.date_entered, '+00:00', '+08:00'))
                           BETWEEN STR_TO_DATE(?,'%Y-%m-%d') AND STR_TO_DATE(?,'%Y-%m-%d')
                  GROUP BY tm.module ", 
                array($dateReport->toDateString(),$dateReport->toDateString())); 
           return $totalResolvedCasesByModuleOther;
    }
    
    protected function totalResolvedCasesByModule(Carbon $dateReport){
           $totalResolvedCasesByModule = DB::select("SELECT 
                    m.value_name AS module, 
                    COUNT(*) AS total 
                  FROM
                    cases c,
                    cases_cstm cc,
                    tasks t,
                    tasks_cstm tc,
                    users u ,
                    cstm_list_app m  
                  WHERE c.id = cc.id_c 
                    AND t.parent_id = c.id 
                    AND t.parent_type = 'Cases' 
                    AND t.id = tc.id_c 
                    AND t.created_by = u.id 
                    AND t.name  in ('Assigned to Case Owner','Task Rejected')  
                    AND cc.sub_category_c = m.value_code 
                        AND  NOT EXISTS
                        (
                        SELECT  NULL 
                        FROM    (
                                        SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN $this->listCaseSubModuleCheck 
                                ) AS tm 
                        WHERE   tm.value_code = cc.sub_category_2_c 
                        )
                    AND cc.incident_service_type_c IN ('incident_it','service_it') 
                    AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('PTJ Onsite')) AS tm 
                        WHERE   tm.value_code = cc.sub_category_c ) 
                    AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('Onsite Support')) AS tm 
                        WHERE   tm.value_code = cc.sub_category_2_c ) 
                    AND DATE(CONVERT_TZ(t.date_entered, '+00:00', '+08:00'))
                           BETWEEN STR_TO_DATE(?,'%Y-%m-%d') AND STR_TO_DATE(?,'%Y-%m-%d')
                  GROUP BY m.value_name ", 
                array($dateReport->toDateString(),$dateReport->toDateString())); 
           return $totalResolvedCasesByModule;
    }
    
    protected function statisticUsersResolved(Carbon $dateReport){
           $data = DB::select("SELECT 
                t.created_by as username,
                u.first_name as fullname,
                COUNT(*) as total
                FROM
                cases c,
                cases_cstm cc,
                tasks t,
                tasks_cstm tc,
                users u 
                WHERE c.id = cc.id_c 
                AND t.parent_id = c.id 
                AND t.parent_type = 'Cases' 
                AND t.id = tc.id_c 
                AND t.created_by = u.id 
                AND t.name  in ('Assigned to Case Owner','Task Rejected') 
                AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('PTJ Onsite')) AS tm 
		    WHERE   tm.value_code = cc.sub_category_c ) 
                AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('Onsite Support')) AS tm 
		    WHERE   tm.value_code = cc.sub_category_2_c ) 
                AND DATE(CONVERT_TZ(t.date_entered, '+00:00', '+08:00'))
                           BETWEEN STR_TO_DATE(?,'%Y-%m-%d') AND STR_TO_DATE(?,'%Y-%m-%d')
                GROUP BY t.created_by,u.first_name ORDER BY total DESC", 
                array($dateReport->toDateString(),$dateReport->toDateString()));   
           return $data;
    }
    
    protected function statisticUsersResolvedByCasesModuleOther(Carbon $dateReport){
           $data = DB::select("SELECT 
                        tm.module,
                        u.user_name,
                        u.first_name AS fullname,
                        COUNT(*) AS total
                        FROM
                        cases c,
                        cases_cstm cc,
                        tasks t,
                        tasks_cstm tc,
                        users u ,
                        (
                                SELECT 'Missing Tasklist' AS module,value_code,value_name FROM cstm_list_app WHERE value_name  IN $this->listTaskMissing
                                UNION
                                SELECT 'Integration' AS module,value_code,value_name FROM cstm_list_app WHERE value_name  IN $this->listIntegration     
                                UNION
                                SELECT 'Manual Contract' AS module,value_code,value_name FROM cstm_list_app WHERE value_name  IN $this->listManualContract 
                                
                        ) AS tm 
                        WHERE c.id = cc.id_c 
                        AND t.parent_id = c.id 
                        AND t.parent_type = 'Cases' 
                        AND t.id = tc.id_c 
                        AND t.created_by = u.id 
                        AND tm.value_code = cc.sub_category_2_c 
                        AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('PTJ Onsite')) AS tm 
                            WHERE   tm.value_code = cc.sub_category_c ) 
                        AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('Onsite Support')) AS tm 
                            WHERE   tm.value_code = cc.sub_category_2_c ) 
                        AND t.name  in ('Assigned to Case Owner','Task Rejected') 
                        AND DATE(CONVERT_TZ(t.date_entered, '+00:00', '+08:00'))
                                   BETWEEN STR_TO_DATE(?,'%Y-%m-%d') AND STR_TO_DATE(?,'%Y-%m-%d')
                        GROUP BY tm.module,u.user_name,u.first_name ORDER BY tm.module,user_name DESC", 
                array($dateReport->toDateString(),$dateReport->toDateString()));   
           return $data;
    }
    
    protected function statisticUsersResolvedByCasesModule(Carbon $dateReport){
           $data = DB::select("SELECT 
                        m.value_name AS module,
                        u.user_name,
                        u.first_name AS fullname,
                        COUNT(*) AS total
                        FROM
                        cases c,
                        cases_cstm cc,
                        tasks t,
                        tasks_cstm tc,
                        users u ,
                        cstm_list_app m

                        WHERE c.id = cc.id_c 
                        AND t.parent_id = c.id 
                        AND t.parent_type = 'Cases' 
                        AND t.id = tc.id_c 
                        AND t.created_by = u.id 
                        AND cc.sub_category_c = m.value_code 
                        AND  NOT EXISTS
                                (
                                SELECT  NULL 
                                FROM    (
                                                SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN $this->listCaseSubModuleCheck 
                                        ) AS tm 
                                WHERE   tm.value_code = cc.sub_category_2_c 
                                )
                        AND t.name  in ('Assigned to Case Owner','Task Rejected')   
                        AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('PTJ Onsite')) AS tm 
                            WHERE   tm.value_code = cc.sub_category_c ) 
                        AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('Onsite Support')) AS tm 
                            WHERE   tm.value_code = cc.sub_category_2_c ) 
                        AND DATE(CONVERT_TZ(t.date_entered, '+00:00', '+08:00'))
                                   BETWEEN STR_TO_DATE(?,'%Y-%m-%d') AND STR_TO_DATE(?,'%Y-%m-%d')
                        GROUP BY m.value_name,u.user_name,u.first_name ORDER BY m.value_name,user_name DESC", 
                array($dateReport->toDateString(),$dateReport->toDateString()));   
           return $data;
    }
    
    /**
     * Send an e-mail as Success Logs
     *
     * @param  Request  $logsdata
     * @return Response
     */
    protected function sendSuccessEmailMyTest($dataArray) {
        
        $data = array(
            "to" => ['<EMAIL>',
                 //'<EMAIL>','<EMAIL>','<EMAIL>'
                ],
            "subject" => 'Server ('.env('APP_ENV').') > Statistik Penyelesaian Kes (IT) CRM pada '.$dataArray['date']
        );
        $dataArray['subject'] = $data["subject"];
        try {
            Mail::send('emails.reportCaseResolveUsers', $dataArray, function($m) use ($data) {
                $m->from('<EMAIL>', 'Pentadbir');
                $m->to($data["to"])->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            echo $e;
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }
    
    /**
     * Send an e-mail as Success Logs
     *
     * @param  Request  $logsdata
     * @return Response
     */
    protected function sendSuccessToSpecificEmailReportStatisticUsers($dataArray) {
        
        $data = array(
            "to" => ['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>'],
            "subject" => 'Server ('.env('APP_ENV').') > Statistik Technical Support - Penyelesaian Kes (IT) CRM pada '.$dataArray['date']
        );
        $dataArray['subject'] = $data["subject"];
        try {
            Mail::send('emails.reportCaseResolveUsers', $dataArray, function($m) use ($data) {
                $m->from('<EMAIL>', 'Pentadbir');
                $m->to($data["to"])->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            echo $e;
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }
    
    
    /**
     * Send an e-mail as Success Logs
     *
     * @param  Request  $logsdata
     * @return Response
     */
    protected function sendSuccessToSpecificEmail($dataArray) {
 
        $data = array(
            "to" => [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>'],
            "subject" => 'Server ('.env('APP_ENV').') > Statistik Penyelesaian Kes (IT) CRM pada '.$dataArray['date']
        );
        $dataArray['subject'] = $data["subject"];
        try {
            Mail::send('emails.reportCaseResolve', $dataArray, function($m) use ($data) {
                $m->from('<EMAIL>', 'Pentadbir');
                $m->to($data["to"])->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            echo $e;
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }
    
    /**
     * Send an e-mail as Success Logs
     *
     * @param  Request  $logsdata
     * @return Response
     */
    protected function sendSuccessEmail($dataArray) {
        
        $data = array(
            "to" => ['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>'],
            "subject" => 'Server ('.env('APP_ENV').') > Statistik Penyelesaian Kes (IT) CRM pada '.$dataArray['date']
        );
        $dataArray['subject'] = $data["subject"];
        try {
            Mail::send('emails.reportCaseResolve', $dataArray, function($m) use ($data) {
                $m->from('<EMAIL>', 'Pentadbir');
                $m->to($data["to"])->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            echo $e;
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }
    
    /**
     * Send an e-mail as Success Logs
     *
     * @param  Request  $logsdata
     * @return Response
     */
    protected function sendSuccessEmailForBigBoss(
            $dataArray) {
        $data = array(
            //"to" => ['<EMAIL>','<EMAIL>'],
            //"to" => ['<EMAIL>'],
            "to" => ['<EMAIL>'],
            "subject" => 'Server ('.env('APP_ENV').') > Statistik Penyelesaian Kes (IT) CRM pada '.$dataArray['date']
        );
        $dataArray['subject'] = $data["subject"];
        try {
            Mail::send('emails.reportCaseResolve', $dataArray, function($m) use ($data) {
                $m->from('<EMAIL>', 'Pentadbir');
                $m->to($data["to"])->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            echo $e;
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }
    
     /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => 'Server ('.env('APP_ENV').') - Error executing  data fixed for field created_by and modified_user_id '
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toFormattedDateString(), 'error' => $error], function($m) use ($data) {
                $m->from('<EMAIL>', 'Pentadbir');
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }

}
