<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Report\Crm;

use Carbon\Carbon;
use DB;
use Log;
use Mail;
use Config;
use App\Migrate\MigrateUtils;
use App\Services\CRMService;

class DailyCollectionCdciReport {

    static $report_sheet = "Daily Collection Report";
    static $query_skip = 500;
    static $query_take = 500;

    public static function crmService() {
        return new CRMService;
    }

    public static function run($dateStart, $dateEnd) {
        Log::debug(self::class . ' Starting ... Generate Daily Collection Report');
        $dtStartTime = Carbon::now();
        self::createExcelReport($dateStart, $dateEnd);

        Log::debug(self::class . ' Completed ReportByExcel --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    public static function createExcelReport($dateStart, $dateEnd) {
        $dtStartTime = Carbon::now();

        self::generateReport($dateStart, $dateEnd);

        $durationTime = MigrateUtils::getTakenTime($dtStartTime);

        Log::debug(self::class . ' Taken Time :', ['Time' => $durationTime]);
    }

    public static function generateReport($dateStart, $dateEnd) {
        $start = 0;
        $totalRecords = 0;
        $dtStartTimeOP = Carbon::now();

        $CsvData = array('CENTRE,MODULE,DATE,COMPANY REG. NO.,MOF NO.,COMPANY NAME,PAYMENT TYPE,REFERENCE NO.,RECEIPT NO.,SALE PERSON,STAFF NAME,LEADS NAME,SSM NO.,CREATED BY,EP NO.,STATE,Combo1_eP_QT_T2u,Combo2_eP_DP_CM_Sen_29%Disc,Combo3_eP_Intensif_T2u_15%Disc,TOTAL (RM),DISCOUNT (RM),GST (RM),SALES ROUNDING ADJUSTMENT (RM),GRAND TOTAL INCLUDING GST (RM)');

        $data = self::getQuery($dateStart, $dateEnd);
        $totalRecords = $totalRecords + count($data);

        dump(self::class . ' current totalrecords ' . count($data));

        foreach ($data as $value) {

            $location = '';
            $module = '';
            $invoiceDate = '';
            $regNo = '';
            $mofNo = '';
            $companyName = '';
            $paymentType = '';
            $referenceNo = '';
            $receiptNo = '';
            $salePerson = '';
            $createdBy = '';
            $staffName = '';
            $leadsName = '';
            $ssmNo = '';
            $epNo = '';
            $onsiteState = '';
            $combo1 = 0;
            $combo2 = 0;
            $combo3 = 0;
            $total = 0;
            $discount = 0;
            $gst = 0;
            $roundingSale = 0;
            $grandTotal = 0;

            if ($value->location) {
                $location = $value->location;
            }

            if ($value->module) {
                $module = $value->module;
            }

            if ($value->invdate) {
                $invoiceDate = Carbon::parse($value->invdate);
            }

            if ($value->compregno) {
                $regNo = preg_replace('/[,]+/', ' ', trim($value->compregno));
            }

            if ($value->mofno) {
                $mofNo = preg_replace('/[,]+/', ' ', trim($value->mofno));
            }

            if ($value->companyname) {
                $companyName = preg_replace('/[,]+/', ' ', trim($value->companyname));
            }

            if ($value->paymentType) {
                $paymentType = $value->paymentType;
            }

            if ($value->referenceno) {
                $referenceNo = $value->referenceno;
            }

            if ($value->receiptno) {
                $receiptNo = $value->receiptno;
            }

            if ($value->saleperson) {
                $salePerson = $value->saleperson;
            }

            if ($value->staffname) {
                $staffName = $value->staffname;
            }

            if ($value->leadsName) {
                $leadsName = $value->leadsName;
            }

            if ($value->ssmNo) {
                $ssmNo = $value->ssmNo;
            }

            if ($value->createdby) {
                $createdBy = $value->createdby;
            }

            if ($value->epno) {
                $epNo = $value->epno;
            }

            if ($value->onsitestate) {
                $onsiteState = $value->onsitestate;
            }

            if ($value->combo1) {
                $combo1 = $value->combo1;
            }

            if ($value->combo2) {
                $combo2 = $value->combo2;
            }

            if ($value->combo3) {
                $combo3 = $value->combo3;
            }

            if ($value->total) {

                if ($value->total) {
                    $total = $value->total;
                }

                if ($value->discount) {
                    $discount = $value->discount;
                }

                if ($value->gst) {
                    $gst = $value->gst;
                }

                if ($value->roundingsale) {
                    $roundingSale = $value->roundingsale;
                }

                $grandTotal = $grandTotal + $total + $discount + $gst + $roundingSale;
            }

            $CsvData[] = (
                    $location . ',' .
                    $module . ',' .
                    $invoiceDate . ',' .
                    $regNo . ',' .
                    $mofNo . ',' .
                    $companyName . ',' .
                    $paymentType . ',' .
                    $referenceNo . ',' .
                    $receiptNo . ',' .
                    $salePerson . ',' .
                    $staffName . ',' .
                    $leadsName . ',' .
                    $ssmNo . ',' .
                    $createdBy . ',' .
                    $epNo . ',' .
                    $onsiteState . ',' .
                    $combo1 . ',' .
                    $combo2 . ',' .
                    $combo3 . ',' .
                    $total . ',' .
                    $discount . ',' .
                    $gst . ',' .
                    $roundingSale . ',' .
                    $grandTotal);
        }

        $takentimeOP = array(
            'Counter' => $start,
            'Taken Time per Minutes' => $dtStartTimeOP->diffInMinutes(Carbon::now()),
            'Taken Time per Seconds' => $dtStartTimeOP->diffInSeconds(Carbon::now())
        );
        dump(self::class . '    :: AllLoopTakenTime >> Time   :   ', [$takentimeOP]);
        dump(self::class . ' queryReport. Total All :  ' . $totalRecords);
        dump(self::class . '--------------------------------------------');

        $filename = 'DailyCollectionReport' . $dateStart . '_to_' . $dateEnd . ".csv";
        $file_path = storage_path() . '/app/exports/cases/' . $filename;
        $file = fopen($file_path, "w+");
        foreach ($CsvData as $exp_data) {
            fputcsv($file, explode(',', $exp_data));
        }
        fclose($file);

        $dataReport = collect([]);
        $dataReport->put("date_start", $dateStart);
        $dataReport->put("date_end", $dateEnd);
        $dataReport->put("report_name", 'Daily Collection Report');
        $dataReport->put("file_name", $filename);

        if($totalRecords > 0){
            self::sendEmail($dataReport);
        }
        
    }

    public static function getQuery($dateStart, $dateEnd) {
        dump('DateStart:' . $dateStart, 'DateEnd:' . $dateEnd);

        $sql = DB::select("SELECT
                    location.`value_name` AS location,
                    CASE
                            WHEN (aos_invoices.`billing_account_id` IS NULL) THEN 'Leads'
                            WHEN (aos_invoices.`billing_account_id` = '') THEN 'Leads'
                            ELSE 'Account'
                          END 				     AS module,
                    DATE(CONVERT_TZ(aos_invoices_cstm.`payment_date_c`, '+00:00', '+08:00')) AS invdate,
                    accounts_cstm.`ssm_no_c` AS compregno,
                    accounts_cstm.`mof_no_c` AS mofno,
                    UPPER(accounts.`name`) AS companyname,
                    paymenttype.`value_name` AS paymentType,
                    CONCAT_WS('  ', aos_invoices_cstm.`payment_reference_no_c`, bank.value_name) AS referenceno,
                    aos_invoices_cstm.`receipt_generate_no_c` AS receiptno,
                    UPPER(CONCAT_WS(' ',saleperson.first_name,saleperson.last_name)) AS saleperson,
                    UPPER(users.`first_name`) AS staffname,
                    leads.`first_name` AS leadsName,
                    leads_cstm.`ssm_no_c` AS ssmNo,
                    CONCAT_WS(' ',createdby.first_name, createdby.last_name) AS createdby,
                    accounts.`ep_no` AS epno,
                    saleperson.user_location AS onsitestate,
                    SUM(IF(aos_products.`name` = 'Combo1_eP_QT_T2u' ,ROUND(aos_products_quotes.`product_list_price` * aos_products_quotes.`product_qty`,2),0)) AS combo1,
                    SUM(IF(aos_products.`name` = 'Combo2_eP_DP_CM_Sen_29%Disc' ,ROUND(aos_products_quotes.`product_list_price` * aos_products_quotes.`product_qty`,2),0)) AS combo2,
                    SUM(IF(aos_products.`name` = 'Combo3_eP_Intensif_T2u_15%Disc' ,ROUND(aos_products_quotes.`product_list_price` * aos_products_quotes.`product_qty`,2),0)) AS combo3,
                    SUM(ROUND((aos_products_quotes.`product_list_price` * aos_products_quotes.`product_qty`),2)) AS total,
                    SUM(ROUND(aos_invoices.`discount_amount`,2)) AS discount,
                    SUM(ROUND(aos_products_quotes.`vat_amt`,2)) AS gst,
                    ROUND(aos_invoices_cstm.`rounding_price_c`,2) AS roundingsale
                    FROM aos_invoices
                    JOIN aos_invoices_cstm ON aos_invoices_cstm.`id_c` = aos_invoices.`id`
                    JOIN aos_products_quotes ON (aos_invoices.`id` = aos_products_quotes.`parent_id` AND aos_products_quotes.`deleted` = 0 AND aos_products_quotes.`name` NOT IN (''))
                    JOIN aos_products ON (aos_products.`id` = aos_products_quotes.`product_id`)
                    LEFT JOIN cstm_list_app AS location ON (location.`value_code` = aos_invoices_cstm.`payment_channel_c` AND location.`type_code` = 'epc_location_list' AND location.value_name NOT IN ('Please Select'))
                    LEFT JOIN cstm_list_app AS paymenttype ON (paymenttype.`value_code` = aos_invoices_cstm.`payment_type_c` AND paymenttype.`type_code` = 'payment_type_list' AND paymenttype.value_name NOT IN ('None',''))
                    LEFT JOIN cstm_list_app AS bank ON (bank.value_code = aos_invoices_cstm.`bank_c` AND bank.`type_code` = 'bank_code_list')
                    LEFT JOIN accounts ON (accounts.`id` = aos_invoices.`billing_account_id` AND accounts.`deleted` = 0)
                    LEFT JOIN accounts_cstm ON accounts_cstm.`id_c` = accounts.`id`
                    LEFT JOIN users ON (users.`id` = aos_invoices.`modified_user_id` AND users.`deleted` = 0)
                    LEFT JOIN leads ON (aos_invoices.`billing_lead_id` = leads.`id` AND leads.deleted = 0)
                    LEFT JOIN leads_cstm ON (leads_cstm.`id_c` = leads.`id`)
                    LEFT JOIN users saleperson ON (saleperson.id = aos_invoices.sale_person_id AND saleperson.deleted = 0)
                    LEFT JOIN users createdby ON (createdby.id = aos_invoices.`created_by`)
                    WHERE aos_invoices.deleted = 0 AND aos_invoices.`status` = 'Paid'
                    AND DATE(CONVERT_TZ(aos_invoices_cstm.`payment_date_c`, '+00:00', '+08:00'))
                              BETWEEN STR_TO_DATE(?,'%Y-%m-%d') AND STR_TO_DATE(?,'%Y-%m-%d')
                    AND aos_products.`name` IN ('Combo1_eP_QT_T2u','Combo2_eP_DP_CM_Sen_29%Disc','Combo3_eP_Intensif_T2u_15%Disc')
                    GROUP BY location,module,invdate,compregno,mofno,companyname,paymentType,
                    referenceno,receiptno,saleperson,staffname,leadsName,ssmNo,
                    createdby,epno,onsitestate,roundingsale", array($dateStart, $dateEnd));
        return $sql;
    }

    protected static function sendEmail($dataReport) {

        $data = array(
//            "to" => ['<EMAIL>'],
            "to" => ['<EMAIL> ','<EMAIL>','<EMAIL>'],
            "subject" => "Server (" . env('APP_ENV') . ") > CRM Report : " . $dataReport->get('report_name') . " pada " . $dataReport->get('date_start') . " sehingga " . $dataReport->get('date_end'),
        );
        try {
            Mail::send('emails.generate_report_crm', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'data' => $dataReport], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])->subject($data["subject"]);
            });
            dump('done send');
        } catch (\Exception $e) {
            echo $e;
            Log::error(self::class . ' Error ... ' . __FUNCTION__ . ' ::  ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            dump('error' . $e);
            return $e;
        }
    }

}
