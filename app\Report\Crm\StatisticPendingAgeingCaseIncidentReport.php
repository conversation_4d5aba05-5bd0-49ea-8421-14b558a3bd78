<?php

namespace App\Report\Crm;

use Carbon\Carbon;
use DB;
use Log;
use Excel;
use Mail;
use Config;
use App\Migrate\MigrateUtils;
use App\Services\CRMService;
use DateTime;

class StatisticPendingAgeingCaseIncidentReport { 
    
    static $report_sheet = "Ageing - Details Cases Pending";
    static $SKIP_QUERY = 500;
    static $TAKE_QUERY = 500;
    
    public function __construct() {
        
    }

    public function crmService() {
        return new CRMService;
    }

    public function run() {
        $dataArray = $this->generateReport();
        $dataArrayCsv = $this->generateReportCsv();
        if ($dataArray != null || $dataArrayCsv != null) {            
            $this->sendSuccessEmail($dataArray,$dataArrayCsv);           
            MigrateUtils::logDump(self::class . ' > ' . __FUNCTION__ . ' Completed!  Send  Email');
        } else {
            MigrateUtils::logDump(self::class . ' > ' . __FUNCTION__ . ' Error Send Email. No data received!');
        }
    }
    
    /**
     * Return Collection 
     */
    public static function generateReportCsv() {
        
        $filename = 'StatisticAgeingPendingCaseIncident' .'_on_'.Carbon::now()->format('Ymd'). ".csv";
        $filePath = storage_path() . '/app/exports/cases/' . $filename;
        $file = fopen($filePath, "w+");

        $totalRecord = 0;
        $count = 1;
        $start = 0;
        $skip = self::$SKIP_QUERY;
        $take = self::$TAKE_QUERY;
        $isHeaderCreated = false;
        do { 
            MigrateUtils::logDump(__CLASS__.' > '.__FUNCTION__. ' >> counter : '.$count); 
            $nextSkip = $start++ * $skip; 
            $resultLimit = self::getQueryData($nextSkip, $take);
            if($resultLimit->count() > 0){
                 if($isHeaderCreated == false){
                     $csvData = self::getHeaderCSV($resultLimit);
                     fputcsv($file, $csvData);
                     $isHeaderCreated = true;
                 }
                 
                 foreach ($resultLimit as $row){
                     fputcsv($file, collect($row)->toArray());
                     $totalRecord++;
                 }
                 $count++;
            }
         } while (count($resultLimit) > 0 && count($resultLimit) == $take);
         
         MigrateUtils::logDump(__CLASS__.' > '.__FUNCTION__. ' >> Completed >> counter : '.$count. ' , Total Record: '.$totalRecord); 

        fclose($file);

        $dataReport = collect([]);
        $dataReport->put("report_name", 'Statisic Ageing Pending Case Incident');
        $dataReport->put("file_name", $filename);  
        $dataReport->put("file_path", $filePath);       
        
        return $dataReport;
    }
    
    protected static function getHeaderCSV($collection) {
        return array_keys(get_object_vars($collection->first()));
    }

    protected static function getQueryData($skip, $take) {
        $query = "SELECT 
            c.`case_number`,
            c.`redmine_number`,
            CONVERT_TZ(c.date_entered,'+00:00','+08:00') AS date_created,
            c.status AS case_status,
            cc.`incident_service_type_c` AS incident_service_type,
            cc.`request_type_c` AS request_type,
            (SELECT m.value_name 
                FROM cstm_list_app m  
			    WHERE m.name = 'cdc_sub_category_list' AND m.value_code =  cc.sub_category_c ) AS module,  
            ( SELECT CASE 
							-- WHEN (pp.sla_task_flag_c IS NULL OR pp.sla_task_flag_c = '') THEN p.task_severity 
							WHEN p.case_redmine_number IS NOT NULL THEN 's4' 
							ELSE pp.sla_task_flag_c 
							END AS a  
			    FROM tasks p , tasks_cstm pp 
			    WHERE  p.id = pp.id_c AND p.parent_id = c.id 
			    AND p.date_entered IN (SELECT MAX(date_entered) FROM tasks r , tasks_cstm rr WHERE  r.id = rr.id_c AND r.parent_id = p.parent_id  ) 
			) AS severity,
            -- t.`id`, tt.`task_number_c`,
            -- t.name, 
            -- tt.sla_task_flag_c,
            -- CONVERT_TZ(t.`date_entered`,'+00:00','+08:00') as task_created, 
            CONVERT_TZ(t.date_start,'+00:00','+08:00') AS itspec_start_datetime ,
            DATEDIFF(NOW(),CONVERT_TZ(c.date_entered,'+00:00','+08:00')) AS ageing_day_case  ,
            DATEDIFF(NOW(),CONVERT_TZ(t.date_start,'+00:00','+08:00')) AS ageing_day_specialist 
            FROM cases c ,cases_cstm cc,
            tasks t , tasks_cstm tt
            WHERE 
            c.id = cc.id_c 
            AND c.id = t.`parent_id` 
            AND t.id = tt.id_c 
            AND c.`status` = 'Open_Assigned' 
            AND cc.incident_service_type_c = 'incident_it'
            AND cc.request_type_c = 'incident' 
            AND t.name IN ('Assigned to Production Support','Assigned to Group IT Specialist(Production Support)')
            AND tt.sla_task_flag_c = '3'   -- Confirmation task as under RIT
            /** to exclude task not category as FINANCE **/
            AND  NOT EXISTS (SELECT 1 FROM cstm_list_app WHERE value_code   = cc.sub_category_c AND value_code  IN ('10712_15034','10714_15842','10713_15534') )
            AND c.`deleted` = 0 
            AND t.`deleted` = 0";
        if($skip >= 0 && $take > 0 ){
            $query = $query.' LIMIT '.$skip.','.$take;
        }else{
            $query = $query.' LIMIT 0,100';
        }
        $result = DB::select($query);
        return collect($result);        
    }

    public function generateReport() {
        MigrateUtils::logDump(__CLASS__.' > '.__FUNCTION__. ' >> entering .. ');
        $dtStartTime = Carbon::now();

        try {
            $listData = self::getQueryReportData();
            $dataArray = [
                //'dateReport' => Carbon::now()->toFormattedDateString(),
                'dateReport' => Carbon::now()->format('d/m/Y H:i'),
                'dateMonth' => Carbon::now()->format('F Y'),
                'listData'=> $listData 
            ];

            $logsdata = self::class . ' Query  Completed --- Taken Time : ' .
                    json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);

            MigrateUtils::logDump($logsdata);    
           

            return $dataArray;
        } catch (\Exception $exc) {
            MigrateUtils::logErrorDump(__CLASS__.' > '.__FUNCTION__.' > '.' ERROR'. $exc->getMessage());
            MigrateUtils::logErrorDump($exc->getTraceAsString());
        }

        return null; 
    }

    protected static function getQueryReportData() {
        $result = DB::select("SELECT ageing_day_specialist, COUNT(*) AS total_cases FROM (
            SELECT 
            c.`case_number`,
            c.`redmine_number`,
            CONVERT_TZ(c.date_entered,'+00:00','+08:00') AS date_created,
            c.status AS case_status,
            cc.`incident_service_type_c` AS incident_service_type,
            cc.`request_type_c` AS request_type,
            -- t.`id`, tt.`task_number_c`,
            -- t.name, 
            -- tt.sla_task_flag_c,
            -- CONVERT_TZ(t.`date_entered`,'+00:00','+08:00') as task_created, 
            CONVERT_TZ(t.date_start,'+00:00','+08:00') AS itspec_start_datetime ,
            DATEDIFF(NOW(),CONVERT_TZ(c.date_entered,'+00:00','+08:00')) AS ageing_day_case  ,
            DATEDIFF(NOW(),CONVERT_TZ(t.date_start,'+00:00','+08:00')) AS ageing_day_specialist 
            FROM cases c ,cases_cstm cc,
            tasks t , tasks_cstm tt
            WHERE 
            c.id = cc.id_c 
            AND c.id = t.`parent_id` 
            AND t.id = tt.id_c 
            AND c.`status` = 'Open_Assigned' 
            AND cc.incident_service_type_c = 'incident_it'
            AND cc.request_type_c = 'incident' 
            AND t.name IN ('Assigned to Production Support','Assigned to Group IT Specialist(Production Support)')
            AND tt.sla_task_flag_c = '3'   -- Confirmation task as under RIT
            /** to exclude task not category as FINANCE **/
            AND  NOT EXISTS (SELECT 1 FROM cstm_list_app WHERE value_code   = cc.sub_category_c AND value_code  IN ('10712_15034','10714_15842','10713_15534') )
            AND c.`deleted` = 0 
            AND t.`deleted` = 0
            ) a 
            GROUP BY a.ageing_day_specialist 
            ORDER BY 1 ASC
                ");
        return $result;        
    }

    /**
     * Send an e-mail as Success Logs
     *
     * @param  Request  $logsdata
     * @return Response
     */
    protected function sendSuccessEmail($dataArray, $dateReport) {

        $fileName = $dateReport->get('file_name');
        $filePath = $dateReport->get('file_path');
        $data = array(
            //"to" => ['<EMAIL>'],
            "to" => ['<EMAIL>','<EMAIL>','<EMAIL>'],
            "subject" => 'Server (' . env('APP_ENV') . ') > CRM: Statisic Ageing Pending Case Incident on ' . $dataArray['dateReport'],
            "att" => $filePath
        );
        $dataArray['subject'] = $data["subject"];
        try {
            Mail::send('emails.reportStatisticAgeingPendingCaseIncident', $dataArray, function($m) use ($data) {
                $m->from('<EMAIL>', 'Pentadbir');
                $m->to($data["to"])->subject($data["subject"]);
                $m->attach($data["att"]);
            });
        } catch (\Exception $e) {
            MigrateUtils::logErrorDump(__CLASS__.' > '.__FUNCTION__.' > '.' ERROR'. $e->getMessage());
            MigrateUtils::logErrorDump($e->getTraceAsString());
        }
    }

        
    
}
