<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Report\Crm;

use App\Services\CRMService;
use Carbon\Carbon;
use DB;
use Log;
use Excel;
use Mail;
use Config;
use DateTime;
use App\Migrate\MigrateUtils;
use App\Http\Controllers\ReportMonitoringController;

class statisticPerformanceAgentReport {

    static $report_sheet = "Agent Performance";
    static $query_skip = 500;
    static $query_take = 500;

    public static function crmService() {
        return new CRMService;
    }

    public static function run($date = null, $command = null) {
        Log::debug(self::class . ' Starting ... Generate Statistic Agent Performance Report',['Query Start Date' => $date, 'Query End Date' => $date]);
        $dtStartTime = Carbon::now();
        
        $reportDate = Carbon::yesterday()->format('Y-m-d');
        if($date){
            $reportDate = $date;
        } 
        
        self::createExcelReport($reportDate, $command);

        Log::debug(self::class . ' Completed Report Statistic Agent Performance --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    public static function createExcelReport($yesterday, $command) {
        $dtStartTime = Carbon::now();
        
        Log::info(self::class . ' Date Yesterday : ' . $yesterday);
        self::generateReport($yesterday, $command);

        $durationTime = MigrateUtils::getTakenTime($dtStartTime);

        Log::debug(self::class . ' Taken Time :', ['Time' => $durationTime]);
    }

    public static function generateReport($yesterday, $command) {

        $start = 0;
        $totalRecords = 0;

        $dtStartTimeOP = Carbon::now();

        $CsvData = array('Case Number,Case Status, Request Type, Incident Type, Category, Sub Category, Sub Category 2, Contact Mode, Date Case Created, Agent Name, Agent Group, Agent Pickup Time, Duration ( < 15 Minutes), Duration ( > 15 Minutes)');

        do {

            $data = self::getQuery($yesterday);
            $totalRecords = $totalRecords + count($data);

            if (count($data) > 0) {
                foreach ($data as $obj) {
                    
                      $CSName = ''; 
                      $GroupName = '';
                      $CSTimePickupCase = '';
                      $ResultdurationWithin = '';
                      $ResultdurationExceed = '';
                      
                      $incidentType = $obj->incidentType;                    
                
                        if ($obj->requestType == 'enquiry') {
                        $incidentType = 'enquiry';
                    }


                    if ($obj->CSName != '') {
                        $CSName = $obj->CSName;
                        $GroupName = $obj->specGroupCSName;
                        $CSTimePickupCase = Carbon::parse($obj->CSNamePickupTime)->addHour(8)->format("Y-m-d H:i");
                        $DatecaseCreated = Carbon::parse($obj->caseDateCreated)->addHour(8)->format("Y-m-d H:i");
                        $DatecasePickup = Carbon::parse($CSTimePickupCase)->addHour(8)->format("Y-m-d H:i");
                        $dateDiff = (new DateTime($DatecasePickup))->diff(new DateTime($DatecaseCreated));
    
                        $durationMin = $dateDiff->i;
                        $durationHour = $dateDiff->h;

                        if($durationHour <= 8 && $durationMin <= 15){
                            $ResultdurationWithin = $durationMin .' min ';
                        }else{
                            $ResultdurationExceed = $durationHour - 8 .'hour ' . ' ' .$durationMin .'min ';
                        }
                    } else {
                        if ($obj->requestType == 'enquiry') {
                            $CSName = $obj->CSNama;
                            $GroupName = $obj->specGroupCSNama;
                            $CSTimePickupCase = Carbon::parse($obj->CSNamaPickupTime)->addHour(8)->format("Y-m-d H:i");
                            $DatecaseCreated = Carbon::parse($obj->caseDateCreated)->addHour(8)->format("Y-m-d H:i");
                            $DatecasePickup = Carbon::parse($CSTimePickupCase)->addHour(8)->format("Y-m-d H:i");
                            $dateDiff = (new DateTime($DatecasePickup))->diff(new DateTime($DatecaseCreated));
                            $durationMin = $dateDiff->i;
                            $durationHour = $dateDiff->h;
                            if($durationHour <= 8 && $durationMin <= 15){
                            $ResultdurationWithin = $durationMin .' min ';
                            }else{
                                $ResultdurationExceed = $durationHour - 8 .'hour ' . ' ' .$durationMin .'min ';
                            }
                        } else {
                            $pickupTime = $obj->CSPickNameTime;
                            if($obj->CSPickNameTime == '') {
                                $pickupTime = $obj->CSNamaPickupTime;
                            } 
                            $CSName = $obj->CSPickName;
                            $GroupName = $obj->specGroupCSPickName;
                            $CSTimePickupCase = Carbon::parse($pickupTime)->addHour(8)->format("Y-m-d H:i");
                            $DatecaseCreated = Carbon::parse($obj->caseDateCreated)->addHour(8)->format("Y-m-d H:i");
                            $DatecasePickup = Carbon::parse($CSTimePickupCase)->addHour(8)->format("Y-m-d H:i");
                            $dateDiff = (new DateTime($DatecasePickup))->diff(new DateTime($DatecaseCreated));
                            $durationMin = $dateDiff->i;
                            $durationHour = $dateDiff->h;
                            if ($incidentType == null || $incidentType == '') {
                                if ($durationHour <= 8 && $durationMin <= 15) {
                                    $ResultdurationWithin = $durationMin . ' min ';
                                } else {
                                    $ResultdurationExceed = 0;
                                }
                            } else {
                                if ($durationHour <= 8 && $durationMin <= 15) {
                                    $ResultdurationWithin = $durationMin . ' min ';
                                } else {
                                    $ResultdurationExceed = $durationHour - 8 . 'hour ' . ' ' . $durationMin . 'min ';
                                }
                            }
                        }
                    }
                    
                    $ActualDateCaseCreated = Carbon::parse($obj->caseDateCreated)->addHour(8)->format("Y-m-d H:i");
                    
                    $CsvData[] = (
                            $obj->case_number  . ',' .
                            $obj->caseStatus  . ',' .
                            $obj->requestType  . ',' .
                            $incidentType  . ',' .
                            $obj->Category  . ',' .
                            $obj->subCategory  . ',' .
                            $obj->subCategory2  . ',' .                            
                            $obj->contact_mode  . ',' .
                            $ActualDateCaseCreated . ',' .
                            $CSName  . ',' .
                            $GroupName  . ',' .
                            $CSTimePickupCase . ',' .
                            $ResultdurationWithin . ',' .
                            $ResultdurationExceed
                            );
                    
                }
            }
        } while (count($data) > 0 && count($data) == self::$query_take);

        $takentimeOP = array(
            'Counter' => $start,
            'Taken Time per Minutes' => $dtStartTimeOP->diffInMinutes(Carbon::now()),
            'Taken Time per Seconds' => $dtStartTimeOP->diffInSeconds(Carbon::now())
        );
        dump(self::class . ' '.$yesterday.'    :: AllLoopTakenTime >> Time   :   ', [$takentimeOP]);
        dump(self::class . ' queryReport. Total All :  ' . $totalRecords);
        dump(self::class . '--------------------------------------------');

        $filename = 'StatisticAgentPerformanceReport' .'_on_' . $yesterday . ".csv";
        $file_path = storage_path() . '/app/exports/cases/' . $filename;
        $file = fopen($file_path, "w+");
        foreach ($CsvData as $exp_data) {
            fputcsv($file, explode(',', $exp_data));
        }
        fclose($file);

        $dataReport = collect([]);
        $dataReport->put("date_start", $yesterday);
        $dataReport->put("report_name", 'Statistic Agent Performance');
        $dataReport->put("file_name", $filename);
        $dataReport->put("file_path", $file_path);
        
        self::sendEmail($dataReport,$command);
    }
    
    protected static function getQuery($yesterday) {
        $result = DB::select("SELECT DISTINCT c.case_number    AS case_number,
                            c.status 			 AS caseStatus,
                            cc.request_type_c                AS requestType,
                            cc.incident_service_type_c       AS incidentType,
                            (SELECT GROUP_CONCAT(cstm.`value_name` SEPARATOR '/')  FROM cstm_list_app cstm  WHERE cstm.value_code=cc.category_c)    AS Category,
                            (SELECT GROUP_CONCAT(clist.`value_name` SEPARATOR '/')  FROM cstm_list_app clist  WHERE clist.value_code=cc.sub_category_c)    AS subCategory,
                            (SELECT GROUP_CONCAT(clistt.`value_name` SEPARATOR '/') FROM cstm_list_app clistt WHERE clistt.value_code=cc.sub_category_2_c) AS subCategory2,
                            (SELECT cmode.`value_name` FROM cstm_list_app cmode  WHERE (cc.contact_mode_c = cmode.value_code) AND (TRIM(cmode.value_code) <> '') AND (cmode.type_code = 'cdc_contact_mode_list')) AS contact_mode,
                            c.date_entered			 AS caseDateCreated,
                            (SELECT u.first_name  FROM users u  WHERE u.id=c.pickupby_id)            AS CSName,
                            (SELECT GROUP_CONCAT(sg.name SEPARATOR '/') 
                             FROM securitygroups sg 
                             JOIN securitygroups_users sgu ON sg.id = sgu.securitygroup_id 
                             JOIN users u ON u.id = sgu.user_id
                             WHERE sgu.deleted = 0 AND u.id = c.pickupby_id 
                             ORDER BY sg.name ) 						         AS specGroupCSName,
                            c.pickup_datetime 						         AS CSNamePickupTime,
                            (SELECT us.first_name  FROM users us  WHERE us.id=c.modified_user_id)    AS CSNama,
                            (SELECT GROUP_CONCAT(sg1.name SEPARATOR '/') 
                             FROM securitygroups sg1 
                             JOIN securitygroups_users sgu1 ON sg1.id = sgu1.securitygroup_id 
                             JOIN users us ON us.id = sgu1.user_id
                             WHERE sgu1.deleted = 0 AND us.id = c.modified_user_id 
                             ORDER BY sg1.name ) 						         AS specGroupCSNama,
                            c.date_modified 							 AS CSNamaPickupTime,
                            (SELECT GROUP_CONCAT(tu.first_name SEPARATOR '/') 
                             FROM users tu  
                             JOIN tasks t ON ( tu.id = t.created_by)
                             WHERE t.parent_id = c.id AND t.name LIKE '%nitial%' AND t.deleted = 0)  AS CSPickName,
                            (SELECT GROUP_CONCAT(s.name SEPARATOR '/') AS sGroup
                                    FROM securitygroups s, securitygroups_users su, users u , tasks t
                                    WHERE su.securitygroup_id = s.id
                                    AND su.user_id = u.id
                                   AND t.created_by=u.id
                                   AND t.parent_id= c.id
                                    AND t.name LIKE '%nitial%' AND t.deleted = 0
                                    AND  su.`deleted` = 0
                                    AND u.status = 'Active'
                                    AND u.is_group = 0
                                  ORDER BY s.name ASC) AS specGroupCSPickName,
                             (SELECT GROUP_CONCAT(t.date_entered SEPARATOR '/') 
                             FROM users tu  
                             JOIN tasks t ON ( tu.id = t.created_by)
                            WHERE t.parent_id = c.id AND t.name LIKE '%nitial%' AND t.deleted = 0)  AS CSPickNameTime  
                            FROM cases c
                            JOIN cases_cstm cc ON c.id = cc.id_c		
                            WHERE STR_TO_DATE(CONVERT_TZ(c.date_entered,'+00:00','+08:00'),'%Y-%m-%d') = ?
                            AND  cc.category_c NOT IN (10715,10722,10721,10719,10720)
                            AND c.deleted = 0", array($yesterday));
        return $result;        
    }

    protected static function  sendEmail($dataReport,$command) {
        $data = array(
            // "to" => ['<EMAIL>'],
            "to" => ['<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>'],
            "subject" => "Server (".env('APP_ENV').") > CRM Report : ".$dataReport->get('report_name')." pada ".$dataReport->get('date_start'),
            "att" => $dataReport->get('file_path')
        );
        try {
            sleep(60);
            Mail::send('emails.generate_report_agent_performance',['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'data' => $dataReport], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])->subject($data["subject"]);
                $m->attach($data["att"]);
            });
            dump('done send');
        } catch (\Exception $e) {
            $reportName = $dataReport->get('report_name');
            $msg = '*[ALERT]* Failed send report ' .$reportName .'.Please Check!!';
            self::crmService()->notifyWhatsapp($msg, 'CRM_REPORT_INTEGRATION', 'CrmIntegration', 'Failed Send Email Report');
            echo $e;
            Log::error(self::class . ' Error ... ' . __FUNCTION__.' ::  ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            dump('error' .$e);
            ReportMonitoringController::saveErrorSendReport($dataReport->get('report_name'),$dataReport->get('date_start'),$command,$e->getMessage());
            return $e;
        }
   }
}
