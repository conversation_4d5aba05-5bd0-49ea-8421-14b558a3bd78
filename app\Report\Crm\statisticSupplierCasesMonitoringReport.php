<?php

namespace App\Report\Crm;

use Carbon\Carbon;
use DB;
use Log;
use Excel;
use Mail;
use Config;
use App\Migrate\MigrateUtils;
use App\Services\CRMService;
use DateTime;

class statisticSupplierCasesMonitoringReport { 
    
    static $report_sheet = "Supplier Cases Monitoring";
    static $query_skip = 500;
    static $query_take = 500;
    
    public function __construct() {
        
    }

    public function crmService() {
        return new CRMService;
    }

    public function runTest(Carbon $dateReport) {
        $dataArray = $this->generateReportByDate($dateReport);
        $dataArrayExcel = $this->generateReportExcel($dateReport);
        if ($dataArray != null || $dataArrayExcel != null) {            
            $this->sendSuccessEmail($dataArray,$dateReport);           
            dump("Completed! Send StatisticTopSupplierCaseMonitoring to Email"); 
            Log::info(self::class . ' > ' . __FUNCTION__ . ' Completed!  Send StatisticTopSupplierCaseMonitoring to Email');
        } else {
            dump("Error Send Email. No data received!");
            Log::info(self::class . ' > ' . __FUNCTION__ . ' Error Send Email. No data received!');
        }
    }
    
    public static function generateReportExcel($dateReport) {
        
        $start = 0;
        $totalRecords = 0;

        $dtStartTimeOP = Carbon::now();
        $dateQueryReport = $dateReport->toDateString();

        $CsvData = array('Company Name, eP No , Case Created On, Case Number, Case Status, Case Sub Status, Contact Mode, Request Type, Incident Type, Category, Sub Category, Sub Category 2, Subject, Description, Resolution, Case Resolved On');

        do {

            $data = self::getQuery($dateQueryReport);
            $totalRecords = $totalRecords + count($data);

            if (count($data) > 0) {
                foreach ($data as $obj) {
                    
                    $CsvData[] = (
                            $obj->companyName  . ',' .
                            $obj->epNo  . ',' .
                            $obj->date_Log_Case  . ',' .
                            $obj->caseNo . ',' .
                            $obj->caseState  . ',' .
                            $obj->caseStatus  . ',' .
                            $obj->contactMode  . ',' .
                            $obj->requestType  . ',' .
                            $obj->incidentService  . ',' .
                            $obj->caseCategory  . ',' .
                            $obj->caseSubCategory  . ',' .
                            $obj->caseSubCategory2  . ',' .
                            $obj->caseSubject  . ',' .
                            $obj->caseDesc  . ',' .
                            $obj->caseReso  . ',' .
                            $obj->date_Resolved  . ',' 
                            );
                    
                }
            }
        } while (count($data) > 0 && count($data) == self::$query_take);

        $takentimeOP = array(
            'Counter' => $start,
            'Taken Time per Minutes' => $dtStartTimeOP->diffInMinutes(Carbon::now()),
            'Taken Time per Seconds' => $dtStartTimeOP->diffInSeconds(Carbon::now())
        );
        dump(self::class . ' '.$dateQueryReport.'    :: AllLoopTakenTime >> Time   :   ', [$takentimeOP]);
        dump(self::class . ' queryReport. Total All :  ' . $totalRecords);
        dump(self::class . '--------------------------------------------');

        $filename = 'TopSupplierCasesMonitoringReport' .'_on_'.$dateQueryReport. ".csv";
        $file_path = storage_path() . '/app/exports/cases/' . $filename;
        $file = fopen($file_path, "w+");
        foreach ($CsvData as $exp_data) {
            fputcsv($file, explode(',', $exp_data));
        }
        fclose($file);

        $dataReport = collect([]);
        $dataReport->put("date_start", $dateQueryReport);
        $dataReport->put("report_name", 'Top Supplier Cases Monitoring');
        $dataReport->put("file_name", $filename);      
        
        
    }
    
    protected static function getQuery($dateQueryReport) {
        $result = DB::select("SELECT ac.`name` AS companyName
                            ,ac.`ep_no` AS epNo
                            ,CONVERT_TZ(c.date_entered,'+00:00','+08:00') AS date_Log_Case
                            ,c.`case_number` AS caseNo
                            ,c.`state` AS caseState
                            ,c.`status` AS caseStatus
                            ,cc.`contact_mode_c` AS contactMode
                            ,cc.`request_type_c` AS requestType
                            ,cc.`incident_service_type_c`AS incidentService
                            ,cc.`category_desc_c` AS caseCategory
                            ,cc.`sub_category_desc_c` AS caseSubCategory
                            ,cc.`sub_category_2_desc_c` AS caseSubCategory2
                            ,c.`name` AS caseSubject
                            ,c.`description` AS caseDesc
                            ,c.`resolution` AS caseReso
                            ,CONVERT_TZ(c.`date_modified`,'+00:00','+08:00') AS date_Resolved
                            FROM cases c
                            LEFT JOIN cases_cstm cc ON c.`id`= cc.`id_c`
                            LEFT JOIN accounts ac ON ac.`id`= c.`account_id`
                            LEFT JOIN accounts_cstm acc ON acc.`id_c`=ac.`id`
                            WHERE STR_TO_DATE(CONVERT_TZ(`c`.`date_entered`,'+00:00','+08:00'),'%Y-%m-%d') = ?
                            AND ac.`account_type` = 'SUPPLIER'
                            AND c.`deleted` =0
                            AND ac.`deleted`=0   
                            AND ac.`name` IN ('PHARMANIAGA LOGISTICS SDN BHD'
                                              ,'PRIMABUMI SDN BHD'
                                              ,'M.S. ALLY PHARMA SDN. BHD.'
                                              ,'GITN SDN. BERHAD'
                                              ,'TARGET MEGA MARINE SDN BHD'
                                              ,'TELEKOM MALAYSIA BERHAD'
                                              ,'QUALITY REPUTATION SDN BHD'
                                              ,'ALAM MEDIK SDN BHD'
                                              ,'GADING KASTURI SDN. BHD.'
                                              ,'DESATERA SDN BHD'
                                              ,'DUOPHARMA MARKETING SDN. BHD.'
                                              ,'MUTIARA MURNI SDN BHD'
                                              ,'MEDILIANCE (M) SDN. BHD.'
                                              ,'TERAJU FARMA SDN. BHD.'
                                              ,'KOMATRA SAJIAN SDN BHD')", array($dateQueryReport));
        return $result;        
    }

    public function generateReportByDate($dateReport) {
        Log::info(self::class . ' starting Statistic Top Supplier Cases Monitoring..', ['Date' => $dateReport]);
        dump(self::class . ' starting Statistic Top Supplier Cases Monitoring..', ['Date' => $dateReport]);
        $dtStartTime = Carbon::now();

        try {
            
            $countPharmaNiaga =0;
            $countPharmaNiagaPending =0;
            $countPharmaNiagaResolved =0;
            $companyNamePharmaNiaga = 'PHARMANIAGA LOGISTICS SDN BHD';
            
            $countPribumi =0;
            $countPriBumiPending =0;
            $countPriBumiResolved =0;
            $companyNamePribumi = 'PRIMABUMI SDN BHD';
            
            $countAlly = 0;
            $countAllyPending = 0;
            $countAllyResolved = 0;
            $companyNameAlly = 'M.S. ALLY PHARMA SDN. BHD.';

            $countgitn =0;
            $countgitnPending =0;
            $countgitnResolved =0;
            $companyNamegitn = 'GITN SDN. BERHAD';
                        
            $countMegaMarine =0;
            $countMegaMarinePending =0;
            $countMegaMarineResolved =0;
            $companyNameMegaMarine = 'TARGET MEGA MARINE SDN BHD';
            
            $countTelekom =0;
            $countTelekomPending =0;
            $countTelekomResolved =0;
            $companyNameTelekom = 'TELEKOM MALAYSIA BERHAD';
            
            $countQuality =0;
            $countQualityPending =0;
            $countQualityResolved =0;
            $companyNameQuality = 'QUALITY REPUTATION SDN BHD';
            
            $countAlamMedik =0;
            $countAlamMedikPending =0;
            $countAlamMedikResolved =0;
            $companyNameAlamMedik = 'ALAM MEDIK SDN BHD';
            
            $countGading =0;
            $countGadingPending =0;
            $countGadingResolved =0;
            $companyNameGading = 'GADING KASTURI SDN. BHD.';
            
            $countDesatera =0;
            $countDesateraPending =0;
            $countDesateraResolved =0;
            $companyNameDesatera = 'DESATERA SDN BHD';
            
            $countDuoPharma =0;
            $countDuoPharmaPending =0;
            $countDuoPharmaResolved =0;
            $companyNameDuoPharma = 'DUOPHARMA MARKETING SDN. BHD.';
            
            $countMutiaraMurni =0;
            $countMutiaraMurniPending =0;
            $countMutiaraMurniResolved =0;
            $companyNameMutiaraMurni = 'MUTIARA MURNI SDN BHD';
            
            $countMediliance =0;
            $countMediliancePending =0;
            $countMedilianceResolved =0;
            $companyNameMediliance = 'MEDILIANCE (M) SDN. BHD.';
            
            $countTerajuFarma =0;
            $countTerajuFarmaPending =0;
            $countTerajuFarmaResolved =0;
            $companyNameTerajuFarma = 'TERAJU FARMA SDN. BHD.';
            
            $countKomatra =0;
            $countKomatraPending =0;
            $countKomatraResolved =0;
            $companyNameKomatra = 'KOMATRA SAJIAN SDN BHD';
            
            $EPPharmaNiaga = 'eP-1008I02CC';
            $EPPribumi = 'eP-1400I0293';
            $EPAlly = 'eP-1400I03BM';
            $EPgitn = 'eP-1400I02VH';
            $EPMegaMarine = 'eP-1008I06NB';
            $EPTelekom = 'eP-1400J000E';
            $EPQuality = 'eP-1008I04B4';
            $EPAlamMedik = 'eP-1400I03GQ';
            $EPGading = 'eP-1002I005L';
            $EPDesatera = 'eP-1006I00S6';
            $EPDuoPharma = 'eP-1006I00MH';
            $EPMutiaraMurni = 'eP-1008I01XT';
            $EPMediliance = 'eP-1008I03E4';
            $EPTerajuFarma = 'eP-1008I02Z2';
            $EPKomatra = 'eP-1000I00RR';

            
            $listTasks = $this->SupplierCasesMonitoring($dateReport);
            $listData = collect($listTasks);
            
            log::info('Check Count: '.count($listData));
            if (count($listTasks) > 0) {
                log::info('Masuk Conditon Asmaa 1');
                foreach ($listData as $tasks) {  

                    if ($tasks->epNo == $EPPharmaNiaga) {
                        $countPharmaNiaga++;
                        if ($tasks->state == 'Open' && ($tasks->status == 'Open_Assigned' || $tasks->status == 'Open_New' || $tasks->status == 'Open_Pending Input' || $tasks->status == 'In_Progress' || $tasks->status == 'Open_Pending_Approval')) {
                            $countPharmaNiagaPending++;
                        }
                        if (($tasks->state == 'Closed' || $tasks->state == 'Open') && ($tasks->status == 'Closed_Closed' || $tasks->status == 'Open_Resolved' || $tasks->status == 'Pending_User_Verification' || $tasks->status == 'Closed_Approved' || $tasks->status == 'Closed_Verified_Eaduan' || $tasks->status == 'Closed_Cancelled_Eaduan' || $tasks->status == 'Closed_Rejected_Eaduan' || $tasks->status == 'Closed_Rejected' || $tasks->status == 'Closed_Duplicated')) {
                            $countPharmaNiagaResolved++;
                        }
                    }
                    if ($tasks->epNo == $EPPribumi) {
                        $countPribumi++;
                        if ($tasks->state == 'Open' && ($tasks->status == 'Open_Assigned' || $tasks->status == 'Open_New' || $tasks->status == 'Open_Pending Input' || $tasks->status == 'In_Progress' || $tasks->status == 'Open_Pending_Approval')) {
                            $countPriBumiPending++;
                        }
                        if (($tasks->state == 'Closed' || $tasks->state == 'Open') && ($tasks->status == 'Closed_Closed' || $tasks->status == 'Open_Resolved' || $tasks->status == 'Pending_User_Verification' || $tasks->status == 'Closed_Approved' || $tasks->status == 'Closed_Verified_Eaduan' || $tasks->status == 'Closed_Cancelled_Eaduan' || $tasks->status == 'Closed_Rejected_Eaduan' || $tasks->status == 'Closed_Rejected' || $tasks->status == 'Closed_Duplicated')) {
                            $countPriBumiResolved++;
                        }
                    }
                    if ($tasks->epNo == $EPAlly) {
                        $countAlly++;
                        if ($tasks->state == 'Open' && ($tasks->status == 'Open_Assigned' || $tasks->status == 'Open_New' || $tasks->status == 'Open_Pending Input' || $tasks->status == 'In_Progress' || $tasks->status == 'Open_Pending_Approval')) {
                            $countAllyPending++;
                        }
                        if (($tasks->state == 'Closed' || $tasks->state == 'Open') && ($tasks->status == 'Closed_Closed' || $tasks->status == 'Open_Resolved' || $tasks->status == 'Pending_User_Verification' || $tasks->status == 'Closed_Approved' || $tasks->status == 'Closed_Verified_Eaduan' || $tasks->status == 'Closed_Cancelled_Eaduan' || $tasks->status == 'Closed_Rejected_Eaduan' || $tasks->status == 'Closed_Rejected' || $tasks->status == 'Closed_Duplicated')) {
                            $countAllyResolved++;
                        }
                    }
                    if ($tasks->epNo == $EPgitn) {
                        $countgitn++;
                        if ($tasks->state == 'Open' && ($tasks->status == 'Open_Assigned' || $tasks->status == 'Open_New' || $tasks->status == 'Open_Pending Input' || $tasks->status == 'In_Progress' || $tasks->status == 'Open_Pending_Approval')) {
                            $countgitnPending++;
                        }
                        if (($tasks->state == 'Closed' || $tasks->state == 'Open') && ($tasks->status == 'Closed_Closed' || $tasks->status == 'Open_Resolved' || $tasks->status == 'Pending_User_Verification' || $tasks->status == 'Closed_Approved' || $tasks->status == 'Closed_Verified_Eaduan' || $tasks->status == 'Closed_Cancelled_Eaduan' || $tasks->status == 'Closed_Rejected_Eaduan' || $tasks->status == 'Closed_Rejected' || $tasks->status == 'Closed_Duplicated')) {
                            $countgitnResolved++;
                        }
                    }
                    if ($tasks->epNo == $EPMegaMarine) {
                        $countMegaMarine++;
                        if ($tasks->state == 'Open' && ($tasks->status == 'Open_Assigned' || $tasks->status == 'Open_New' || $tasks->status == 'Open_Pending Input' || $tasks->status == 'In_Progress' || $tasks->status == 'Open_Pending_Approval')) {
                            $countMegaMarinePending++;
                        }
                        if (($tasks->state == 'Closed' || $tasks->state == 'Open') && ($tasks->status == 'Closed_Closed' || $tasks->status == 'Open_Resolved' || $tasks->status == 'Pending_User_Verification' || $tasks->status == 'Closed_Approved' || $tasks->status == 'Closed_Verified_Eaduan' || $tasks->status == 'Closed_Cancelled_Eaduan' || $tasks->status == 'Closed_Rejected_Eaduan' || $tasks->status == 'Closed_Rejected' || $tasks->status == 'Closed_Duplicated')) {
                            $countMegaMarineResolved++;
                        }
                    }
                    if ($tasks->epNo == $EPTelekom) {
                        $countTelekom++;
                        if ($tasks->state == 'Open' && ($tasks->status == 'Open_Assigned' || $tasks->status == 'Open_New' || $tasks->status == 'Open_Pending Input' || $tasks->status == 'In_Progress' || $tasks->status == 'Open_Pending_Approval')) {
                            $countTelekomPending++;
                        }
                        if (($tasks->state == 'Closed' || $tasks->state == 'Open') && ($tasks->status == 'Closed_Closed' || $tasks->status == 'Open_Resolved' || $tasks->status == 'Pending_User_Verification' || $tasks->status == 'Closed_Approved' || $tasks->status == 'Closed_Verified_Eaduan' || $tasks->status == 'Closed_Cancelled_Eaduan' || $tasks->status == 'Closed_Rejected_Eaduan' || $tasks->status == 'Closed_Rejected' || $tasks->status == 'Closed_Duplicated')) {
                            $countTelekomResolved++;
                        }
                    }
                    if ($tasks->epNo == $EPQuality) {
                        $countQuality++;
                        if ($tasks->state == 'Open' && ($tasks->status == 'Open_Assigned' || $tasks->status == 'Open_New' || $tasks->status == 'Open_Pending Input' || $tasks->status == 'In_Progress' || $tasks->status == 'Open_Pending_Approval')) {
                            $countQualityPending++;
                        }
                        if (($tasks->state == 'Closed' || $tasks->state == 'Open') && ($tasks->status == 'Closed_Closed' || $tasks->status == 'Open_Resolved' || $tasks->status == 'Pending_User_Verification' || $tasks->status == 'Closed_Approved' || $tasks->status == 'Closed_Verified_Eaduan' || $tasks->status == 'Closed_Cancelled_Eaduan' || $tasks->status == 'Closed_Rejected_Eaduan' || $tasks->status == 'Closed_Rejected' || $tasks->status == 'Closed_Duplicated')) {
                            $countQualityResolved++;
                        }
                    }
                    if ($tasks->epNo == $EPAlamMedik) {
                        $countAlamMedik++;
                        if ($tasks->state == 'Open' && ($tasks->status == 'Open_Assigned' || $tasks->status == 'Open_New' || $tasks->status == 'Open_Pending Input' || $tasks->status == 'In_Progress' || $tasks->status == 'Open_Pending_Approval')) {
                            $countAlamMedikPending++;
                        }
                        if (($tasks->state == 'Closed' || $tasks->state == 'Open') && ($tasks->status == 'Closed_Closed' || $tasks->status == 'Open_Resolved' || $tasks->status == 'Pending_User_Verification' || $tasks->status == 'Closed_Approved' || $tasks->status == 'Closed_Verified_Eaduan' || $tasks->status == 'Closed_Cancelled_Eaduan' || $tasks->status == 'Closed_Rejected_Eaduan' || $tasks->status == 'Closed_Rejected' || $tasks->status == 'Closed_Duplicated')) {
                            $countAlamMedikResolved++;
                        }
                    }
                    if ($tasks->epNo == $EPGading) {
                        $countGading++;
                        if ($tasks->state == 'Open' && ($tasks->status == 'Open_Assigned' || $tasks->status == 'Open_New' || $tasks->status == 'Open_Pending Input' || $tasks->status == 'In_Progress' || $tasks->status == 'Open_Pending_Approval')) {
                            $countGadingPending++;
                        }
                        if (($tasks->state == 'Closed' || $tasks->state == 'Open') && ($tasks->status == 'Closed_Closed' || $tasks->status == 'Open_Resolved' || $tasks->status == 'Pending_User_Verification' || $tasks->status == 'Closed_Approved' || $tasks->status == 'Closed_Verified_Eaduan' || $tasks->status == 'Closed_Cancelled_Eaduan' || $tasks->status == 'Closed_Rejected_Eaduan' || $tasks->status == 'Closed_Rejected' || $tasks->status == 'Closed_Duplicated')) {
                            $countGadingResolved++;
                        }
                    }
                    if ($tasks->epNo == $EPDesatera) {
                        $countDesatera++;
                        if ($tasks->state == 'Open' && ($tasks->status == 'Open_Assigned' || $tasks->status == 'Open_New' || $tasks->status == 'Open_Pending Input' || $tasks->status == 'In_Progress' || $tasks->status == 'Open_Pending_Approval')) {
                            $countDesateraPending++;
                        }
                        if (($tasks->state == 'Closed' || $tasks->state == 'Open') && ($tasks->status == 'Closed_Closed' || $tasks->status == 'Open_Resolved' || $tasks->status == 'Pending_User_Verification' || $tasks->status == 'Closed_Approved' || $tasks->status == 'Closed_Verified_Eaduan' || $tasks->status == 'Closed_Cancelled_Eaduan' || $tasks->status == 'Closed_Rejected_Eaduan' || $tasks->status == 'Closed_Rejected' || $tasks->status == 'Closed_Duplicated')) {
                            $countDesateraResolved++;
                        }
                    }
                    if ($tasks->epNo == $EPDuoPharma) {
                        $countDuoPharma++;
                        if ($tasks->state == 'Open' && ($tasks->status == 'Open_Assigned' || $tasks->status == 'Open_New' || $tasks->status == 'Open_Pending Input' || $tasks->status == 'In_Progress' || $tasks->status == 'Open_Pending_Approval')) {
                            $countDuoPharmaPending++;
                        }
                        if (($tasks->state == 'Closed' || $tasks->state == 'Open') && ($tasks->status == 'Closed_Closed' || $tasks->status == 'Open_Resolved' || $tasks->status == 'Pending_User_Verification' || $tasks->status == 'Closed_Approved' || $tasks->status == 'Closed_Verified_Eaduan' || $tasks->status == 'Closed_Cancelled_Eaduan' || $tasks->status == 'Closed_Rejected_Eaduan' || $tasks->status == 'Closed_Rejected' || $tasks->status == 'Closed_Duplicated')) {
                            $countDuoPharmaResolved++;
                        }
                    }
                    if ($tasks->epNo == $EPMutiaraMurni) {
                        $countMutiaraMurni++;
                        if ($tasks->state == 'Open' && ($tasks->status == 'Open_Assigned' || $tasks->status == 'Open_New' || $tasks->status == 'Open_Pending Input' || $tasks->status == 'In_Progress' || $tasks->status == 'Open_Pending_Approval')) {
                            $countMutiaraMurniPending++;
                        }
                        if (($tasks->state == 'Closed' || $tasks->state == 'Open') && ($tasks->status == 'Closed_Closed' || $tasks->status == 'Open_Resolved' || $tasks->status == 'Pending_User_Verification' || $tasks->status == 'Closed_Approved' || $tasks->status == 'Closed_Verified_Eaduan' || $tasks->status == 'Closed_Cancelled_Eaduan' || $tasks->status == 'Closed_Rejected_Eaduan' || $tasks->status == 'Closed_Rejected' || $tasks->status == 'Closed_Duplicated')) {
                            $countMutiaraMurniResolved++;
                        }
                    }
                    if ($tasks->epNo == $EPMediliance) {
                        $countMediliance++;
                        if ($tasks->state == 'Open' && ($tasks->status == 'Open_Assigned' || $tasks->status == 'Open_New' || $tasks->status == 'Open_Pending Input' || $tasks->status == 'In_Progress' || $tasks->status == 'Open_Pending_Approval')) {
                            $countMediliancePending++;
                        }
                        if (($tasks->state == 'Closed' || $tasks->state == 'Open') && ($tasks->status == 'Closed_Closed' || $tasks->status == 'Open_Resolved' || $tasks->status == 'Pending_User_Verification' || $tasks->status == 'Closed_Approved' || $tasks->status == 'Closed_Verified_Eaduan' || $tasks->status == 'Closed_Cancelled_Eaduan' || $tasks->status == 'Closed_Rejected_Eaduan' || $tasks->status == 'Closed_Rejected' || $tasks->status == 'Closed_Duplicated')) {
                            $countMedilianceResolved++;
                        }
                    }
                    if ($tasks->epNo == $EPTerajuFarma) {
                        $countTerajuFarma++;
                        if ($tasks->state == 'Open' && ($tasks->status == 'Open_Assigned' || $tasks->status == 'Open_New' || $tasks->status == 'Open_Pending Input' || $tasks->status == 'In_Progress' || $tasks->status == 'Open_Pending_Approval')) {
                            $countTerajuFarmaPending++;
                        }
                        if (($tasks->state == 'Closed' || $tasks->state == 'Open') && ($tasks->status == 'Closed_Closed' || $tasks->status == 'Open_Resolved' || $tasks->status == 'Pending_User_Verification' || $tasks->status == 'Closed_Approved' || $tasks->status == 'Closed_Verified_Eaduan' || $tasks->status == 'Closed_Cancelled_Eaduan' || $tasks->status == 'Closed_Rejected_Eaduan' || $tasks->status == 'Closed_Rejected' || $tasks->status == 'Closed_Duplicated')) {
                            $countTerajuFarmaResolved++;
                        }
                    }
                    if ($tasks->epNo == $EPKomatra) {
                        $countKomatra++;
                        if ($tasks->state == 'Open' && ($tasks->status == 'Open_Assigned' || $tasks->status == 'Open_New' || $tasks->status == 'Open_Pending Input' || $tasks->status == 'In_Progress' || $tasks->status == 'Open_Pending_Approval')) {
                            $countKomatraPending++;
                        }
                        if (($tasks->state == 'Closed' || $tasks->state == 'Open') && ($tasks->status == 'Closed_Closed' || $tasks->status == 'Open_Resolved' || $tasks->status == 'Pending_User_Verification' || $tasks->status == 'Closed_Approved' || $tasks->status == 'Closed_Verified_Eaduan' || $tasks->status == 'Closed_Cancelled_Eaduan' || $tasks->status == 'Closed_Rejected_Eaduan' || $tasks->status == 'Closed_Rejected' || $tasks->status == 'Closed_Duplicated')) {
                            $countKomatraResolved++;
                        }
                    }
                    
                    
                }
            }

            $dataArray = [
                'date' => $dateReport->toFormattedDateString(),
                'dateMonth' => $dateReport->format('F Y'),
                'countPharmaNiaga'=>$countPharmaNiaga ,
                'countPharmaNiagaPending'=>$countPharmaNiagaPending ,
                'countPharmaNiagaResolved'=>$countPharmaNiagaResolved ,
                'companyNamePharmaNiaga'=>$companyNamePharmaNiaga,
                'epNoPharmaNiaga'=>$EPPharmaNiaga,
                'countPribumi'=>$countPribumi ,
                'countPribumiPending'=>$countPriBumiPending ,
                'countPribumiResolved'=>$countPriBumiResolved ,
                'companyNamePribumi'=>$companyNamePribumi,
                'epNoPribumi'=>$EPPribumi,
                'countAlly'=>$countAlly ,
                'countAllyPending'=>$countAllyPending ,
                'countAllyResolved'=>$countAllyResolved ,
                'companyNameAlly'=>$companyNameAlly,
                'epNoAlly'=>$EPAlly,
                'countgitn'=>$countgitn ,
                'countgitnPending'=>$countgitnPending ,
                'countgitnResolved'=>$countgitnResolved , 
                'companyNamegitn'=>$companyNamegitn,
                'epNogitn'=>$EPgitn,
                'countMegaMarine'=>$countMegaMarine ,
                'countMegaMarinePending'=>$countMegaMarinePending ,
                'countMegaMarineResolved'=>$countMegaMarineResolved ,
                'companyNameMegaMarine'=>$companyNameMegaMarine,
                'epNoMegaMarine'=>$EPMegaMarine,
                'countTelekom'=>$countTelekom ,
                'countTelekomPending'=>$countTelekomPending ,
                'countTelekomResolved'=>$countTelekomResolved ,
                'companyNameTelekom'=>$companyNameTelekom,
                'epNoTelekom'=>$EPTelekom,
                'countQuality'=>$countQuality ,
                'countQualityPending'=>$countQualityPending ,
                'countQualityResolved'=>$countQualityResolved ,
                'companyNameQuality'=>$companyNameQuality,
                'epNoQuality'=>$EPQuality,
                'countAlamMedik'=>$countAlamMedik ,
                'countAlamMedikPending'=>$countAlamMedikPending ,
                'countAlamMedikResolved'=>$countAlamMedikResolved ,
                'companyNameAlamMedik'=>$companyNameAlamMedik,
                'epNoAlamMedik'=>$EPAlamMedik,
                'countGading'=>$countGading ,
                'countGadingPending'=>$countGadingPending ,
                'countGadingResolved'=>$countGadingResolved ,
                'companyNameGading'=>$companyNameGading,
                'epNoGading'=>$EPGading,
                'countDesatera'=>$countDesatera ,
                'countDesateraPending'=>$countDesateraPending ,
                'countDesateraResolved'=>$countDesateraResolved ,
                'companyNameDesatera'=>$companyNameDesatera,
                'epNoDesatera'=>$EPDesatera,
                'countDuoPharma'=>$countDuoPharma ,
                'countDuoPharmaPending'=>$countDuoPharmaPending ,
                'countDuoPharmaResolved'=>$countDuoPharmaResolved ,
                'companyNameDuoPharma'=>$companyNameDuoPharma,
                'epNoDuoPharma'=>$EPDuoPharma,
                'countMutiaraMurni'=>$countMutiaraMurni ,
                'countMutiaraMurniPending'=>$countMutiaraMurniPending ,
                'countMutiaraMurniResolved'=>$countMutiaraMurniResolved ,
                'companyNameMutiaraMurni'=>$companyNameMutiaraMurni,
                'epNoMutiaraMurni'=>$EPMutiaraMurni,
                'countMediliance'=>$countMediliance ,
                'countMediliancePending'=>$countMediliancePending ,
                'countMedilianceResolved'=>$countMedilianceResolved ,
                'companyNameMediliance'=>$companyNameMediliance,
                'epNoMediliance'=>$EPMediliance,
                'countTerajuFarma'=>$countTerajuFarma ,
                'countTerajuFarmaPending'=>$countTerajuFarmaPending ,
                'countTerajuFarmaResolved'=>$countTerajuFarmaResolved ,
                'companyNameTerajuFarma'=>$companyNameTerajuFarma,
                'epNoTerajuFarma'=>$EPTerajuFarma,
                'countKomatra'=>$countKomatra ,
                'countKomatraPending'=>$countKomatraPending ,
                'countKomatraResolved'=>$countKomatraResolved,
                'companyNameKomatra'=>$companyNameKomatra,
                'epNoKomatra'=>$EPKomatra
            ];

            $logsdata = self::class . ' Query Date Start : ' . $dtStartTime . ' , '
                    . 'Query Date End : ' . Carbon::now() . ' , Completed --- Taken Time : ' .
                    json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);

            Log::info($logsdata);
            dump($logsdata);       
           

            return $dataArray;
        } catch (\Exception $exc) {
            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            echo $exc->getTraceAsString();
        }

        return null; 
    }

    /**
     * Send an e-mail as Success Logs
     *
     * @param  Request  $logsdata
     * @return Response
     */
    protected function sendSuccessEmail($dataArray, $dateReport) {

        $dateGenerateReport = $dateReport->toDateString();

        $filename = 'TopSupplierCasesMonitoringReport_on_' . $dateGenerateReport . ".csv";
        $file_path = storage_path() . '/app/exports/cases/' . $filename;
        $data = array(
         //   "to" => ['<EMAIL>','<EMAIL>'],
            "to" => ['<EMAIL>',
                '<EMAIL>'],
            "subject" => 'Server (' . env('APP_ENV') . ') > Top 15 Supplier Cases Monitoring on ' . $dataArray['date'],
            "att" => $file_path
        );
        $dataArray['subject'] = $data["subject"];
        try {
            Mail::send('emails.reportStatisticSupplierCasesMonitoring', $dataArray, function($m) use ($data) {
                $m->from('<EMAIL>', 'Pentadbir');
                $m->to($data["to"])->subject($data["subject"]);
                $m->attach($data["att"]);
            });
        } catch (\Exception $e) {
            echo $e;
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }

    protected function SupplierCasesMonitoring($dateReport) {
        Log::info('Date :'. $dateReport->toDateString()); 
        $result = DB::select("SELECT ac.`name` AS companyName
                            ,ac.`ep_no` AS epNo
                            ,CONVERT_TZ(c.date_entered,'+00:00','+08:00') AS date_Log_Case
                            ,c.`case_number`
                            ,c.`state` AS state
                            ,c.`status` AS status
                            ,cc.`contact_mode_c`
                            ,cc.`request_type_c`
                            ,cc.`incident_service_type_c`
                            ,cc.`category_desc_c`
                            ,cc.`sub_category_desc_c`
                            ,cc.`sub_category_2_desc_c`
                            ,c.`name`
                            ,c.`resolution`
                            ,CONVERT_TZ(c.`date_modified`,'+00:00','+08:00') AS date_Resolved
                            FROM cases c
                            LEFT JOIN cases_cstm cc ON c.`id`= cc.`id_c`
                            LEFT JOIN accounts ac ON ac.`id`= c.`account_id`
                            LEFT JOIN accounts_cstm acc ON acc.`id_c`=ac.`id`
                            WHERE STR_TO_DATE(CONVERT_TZ(`c`.`date_entered`,'+00:00','+08:00'),'%Y-%m-%d') = ?
                            AND ac.`account_type` = 'SUPPLIER'
                            AND c.`deleted` =0
                            AND ac.`deleted`=0   
                            AND ac.`name` IN ('PHARMANIAGA LOGISTICS SDN BHD'
                                              ,'PRIMABUMI SDN BHD'
                                              ,'M.S. ALLY PHARMA SDN. BHD.'
                                              ,'GITN SDN. BERHAD'
                                              ,'TARGET MEGA MARINE SDN BHD'
                                              ,'TELEKOM MALAYSIA BERHAD'
                                              ,'QUALITY REPUTATION SDN BHD'
                                              ,'ALAM MEDIK SDN BHD'
                                              ,'GADING KASTURI SDN. BHD.'
                                              ,'DESATERA SDN BHD'
                                              ,'DUOPHARMA MARKETING SDN. BHD.'
                                              ,'MUTIARA MURNI SDN BHD'
                                              ,'MEDILIANCE (M) SDN. BHD.'
                                              ,'TERAJU FARMA SDN. BHD.'
                                              ,'KOMATRA SAJIAN SDN BHD')", array($dateReport->toDateString()));
        Log::info($result);
        return $result;    
    }
            
    
}
