<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Report\MyTvAltel;

use Carbon\Carbon;
use DB;
use Log;
use Excel;
use Mail;
use Config;
use App\Migrate\MigrateUtils;
use App\Services\CasbService;

class CaseDetailReport {

    static $query_skip = 500;
    static $query_take = 500;

    public static function CrmCasbService() {
        return new CasbService;
    }

    public static function run($startDate, $endDate) {
        dump(self::class . ' Starting ... ' . __FUNCTION__);
        Log::info(self::class . ' Starting ... ' . __FUNCTION__ . ' Start Date: ' . $startDate . ' End Date: ' . $endDate);
        $dtStartTime = Carbon::now();
        $report_path = storage_path('app/exports/mytvaltel') . '/';

        $daily_cntct_rpt_name = 'Statistic_' . $startDate . '_' . $endDate;
        $case_dtl_rpt_name = 'CaseDtl_' . $startDate . '_' . $endDate;
        self::daily_contact_mode($startDate, $endDate, $daily_cntct_rpt_name, $report_path);
        self::case_detail($startDate, $endDate, $case_dtl_rpt_name, $report_path);

        $dataReport = collect([]);
        $dataReport->put("report_group", 'Case Detail For Mytv/Altel ' . $startDate . '_' . $endDate);
        $dataReport->put("report_date", $startDate . '_' . $endDate);
        $dataReport->put("report_name", [$daily_cntct_rpt_name, $case_dtl_rpt_name]);
        $dataReport->put("report_path", [$report_path . $daily_cntct_rpt_name . '.csv', $report_path . $case_dtl_rpt_name . '.csv']);
        self::sendEmail($dataReport);

        dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    public static function daily_contact_mode($startDate, $endDate, $rpt_nm, $report_path) {

        $CsvData = array('CONTACT MODE,TOTAL ALTEL,TOTAL MYTV');
        $result = self::getDailyContactMode($startDate, $endDate);
        $totalRecords = count($result);
        $dtStartTimeOP = Carbon::now();
        foreach ($result as $obj) {
            $CsvData[] = (
                    $obj->value_code . ',' .
                    $obj->total_altel . ',' .
                    $obj->total_mytv
                    );
        }
        $takentimeOP = array(
            'Statistic : Taken Time per Minutes' => $dtStartTimeOP->diffInMinutes(Carbon::now()),
            'Statistic : Taken Time per Seconds' => $dtStartTimeOP->diffInSeconds(Carbon::now())
        );
        Log::info(self::class . ' Statistic : LoopTakenTime >> Time   :   ', [$takentimeOP]);
        Log::info(self::class . ' Statistic : queryReport. Total All :  ' . $totalRecords);

        $filename = $rpt_nm . ".csv";
        $file_path = $report_path . $filename;
        $file = fopen($file_path, "w+");
        foreach ($CsvData as $exp_data) {
            fputcsv($file, explode(',', $exp_data));
        }
        fclose($file);

        $dataReport = collect([]);
        $dataReport->put("date_start", $startDate);
        $dataReport->put("date_end", $endDate);
        $dataReport->put("report_name", $rpt_nm);
        $dataReport->put("file_name", $filename);
    }

    public static function getDailyContactMode($startDate, $endDate) {
        $sql = "select lists.value_code, altel.total AS total_altel, mytv.total AS total_mytv from                
                    (select value_code from cstm_list_app 
                    where name = 'contact_mode_list'
                    and value_code <> ''
                    and status = 1) lists
                left join 
                    (SELECT COUNT(*) AS total, contact_mode, domain_id,
                    (SELECT NAME FROM securitygroups WHERE id = cases.`domain_id`) grp
                    FROM cases WHERE deleted = 0 
                    AND cases.`domain_id` = '52801af6-9904-d25a-386d-606935483167'
                    AND DATE(CONVERT_TZ(cases.`date_entered`, '+00:00', '+08:00'))
                    BETWEEN STR_TO_DATE(?,'%Y-%m-%d') AND STR_TO_DATE(?,'%Y-%m-%d')
                    GROUP BY domain_id, contact_mode
                    ORDER BY domain_id, contact_mode) altel on lists.value_code = altel.contact_mode
                left join 
                    (SELECT COUNT(*) AS total, contact_mode, domain_id,
                    (SELECT NAME FROM securitygroups WHERE id = cases.`domain_id`) grp
                    FROM cases
                    WHERE deleted = 0 
                    AND cases.`domain_id` = 'd020956b-a913-00ac-4f42-60693594e3ba'
                    AND DATE(CONVERT_TZ(cases.`date_entered`, '+00:00', '+08:00'))
                    BETWEEN STR_TO_DATE(?,'%Y-%m-%d') AND STR_TO_DATE(?,'%Y-%m-%d')
                    GROUP BY domain_id, contact_mode
                    ORDER BY domain_id, contact_mode) mytv on lists.value_code = mytv.contact_mode";

        return DB::connection('mysql_casb')->select($sql, array($startDate, $endDate, $startDate, $endDate));
    }

    public static function case_detail($startDate, $endDate, $rpt_nm, $report_path) {
        $CsvData = array(
            'ACCOUNT TYPE,CASE NUMBER,NAME,RESOLUTION,CONTACT MODE,CATEGORY,SUBCATEGORY 1,SUBCATEGORY 2,SUBCATEGORY 3,SUBCATEGORY 4,'
             . 'DESCRIPTION,COMPANY REG. NO,PHONE WORK,PHONE MOBILE,CONTACT NAME,IDENTIFICATION NO,PRODUCT TYPE,DISTRICT AREA,TRANSMITTER (DTT),DTH AREA / BLINDSPOT,UPSELL MYTV,STATE,'
             . 'STATUS,CREATED BY NAME,CREATED DATE,MODIFIED DATE,AGING,IDENTIFICATION TYPE,CUSTOMER EMAIL ADDRESS,ADDRESS 1,'
             . 'ADDRESS 2,CITY,ESCALATION DEPARTMENT,SELL TYPE,SUCCESS TYPE,HAPPY CALL,STATES,TYPE OF PROPERTY');

        $start = 0;
        $skip = self::$query_skip;
        $take = self::$query_take;
        $totalRecords = 0;
        $dtStartTimeOP = Carbon::now();

        do {
            $nextSkip = $start++ * $skip;
            $result = self::getCaseDetail($startDate, $endDate, $take, $nextSkip);
            $totalRecords = $totalRecords + count($result);
            $dtStartTimeEachLoop = Carbon::now();
            dump(self::class . ' Case Detail : current totalrecords ' . count($result));

            foreach ($result as $obj) {
                $userGroup = null;
                $contactMode = null;
                $category = null;
                $subCategory1 = null;
                $subCategory2 = null;
                $subCategory3 = null;
                $subCategory4 = null;
                $productType = null;
                $district_area = null;
                $transmitter_dtt = null;
                $dth_area = null;
                $upsell_mytv = null;
                $state = null;
                $status = null;
                $createdByName = null;
                $aging = 0;
                $identificationType = null;
                $escalationDept = null;
                $sellType = null;
                $successType = null;
                $happyCall = null;
                $stateList = null;
                $emailContact = null;
                $address1 = null;
                $companyRegNo = null; 
                $phoneWork = null; 
                $phoneMobile = null; 
                $name = null; 
                $identificationNo = null;
                $subject = null;
                $typeOfProperty = null;

                if($obj->name != ''){
                    $subject = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($obj->name));
                }
                $desc = strip_tags($obj->description);
                $trimdescription = preg_replace('!\s+!', ' ', trim($desc));
                $trimdescriptionLen = '';
                if ((strlen($trimdescription)) > 30000) {
                    $trimdescriptionLen = substr($trimdescription, 0, 10000);
                } else {
                    $trimdescriptionLen = $trimdescription;
                }
                $description = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($trimdescriptionLen));

                $res = strip_tags($obj->resolution);
                $trimres = preg_replace('!\s+!', ' ', trim($res));
                $trimresLen = '';
                if ((strlen($trimres)) > 30000) {
                    $trimresLen = substr($trimres, 0, 10000);
                } else {
                    $trimresLen = $trimres;
                }
                $resolution = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($trimresLen));
 
                $createdByName = $obj->user_first_name .$obj->user_last_name;
                if ($obj->state == 'Open') {
                    $dateCreated = Carbon::parse($obj->created_date);
                    $aging = $dateCreated->diffInDays(Carbon::now());
                }
                if($obj->company_reg_no != ''){
                    $companyRegNo = $obj->company_reg_no;
                } 
                if($obj->phone_work != ''){
                    $phoneWork = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($obj->phone_work));
                } 
                if($obj->phone_mobile != ''){
                    $phoneMobile = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($obj->phone_mobile));
                } 
                if($obj->first_name != ''){
                    $names = $obj->first_name . $obj->last_name;
                    $name = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($names));
                } 
                $groupName = self::CrmCasbService()->getSecurityGroup($obj->domain_id);
                if ($groupName) {
                    $userGroup = $groupName->name;
                }
                if ($obj->contact_mode != '') {
                    $contactMode = self::CrmCasbService()->getValueLookupCRM('contact_mode_list', $obj->contact_mode);
                }
                if ($obj->category != '') {
                    $objCategory = self::CrmCasbService()->getValueLookupCRM('category_list', $obj->category);
                    $category = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($objCategory));
                }
                if ($obj->sub_category != '') {
                    $objSubCategory1 = self::CrmCasbService()->getValueLookupCRM('sub_category_list', $obj->sub_category);
                    $subCategory1 = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($objSubCategory1));
                }
                if ($obj->sub_sub_category != '') {
                    $objSubCategory2 = self::CrmCasbService()->getValueLookupCRM('sub_sub_category_list', $obj->sub_sub_category);
                    $subCategory2 = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($objSubCategory2));
                }
                if ($obj->sub_sub_sub_category != '') {
                    $objSubCategory3 = self::CrmCasbService()->getValueLookupCRM('sub_sub_sub_category_list', $obj->sub_sub_sub_category);
                    $subCategory3 = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($objSubCategory3));
                }
                if ($obj->sub_sub_sub_sub_category != '') {
                    $objSubCategory4 = self::CrmCasbService()->getValueLookupCRM('sub_sub_sub_sub_category_list', $obj->sub_sub_sub_sub_category);
                    $subCategory4 = preg_replace('/[^a-zA-Z0-9\']/', ' ', trim($objSubCategory4));
                }
                if ($obj->product_type != '') {
                    $productType = self::CrmCasbService()->getValueLookupCRM('product_type_list', $obj->product_type);
                }
                if ($obj->district_area != '') {
                    $district_area = self::CrmCasbService()->getValueLookupCRM('district_area_list', $obj->district_area);
                }
                if ($obj->transmitter_dtt != '') {
                    $transmitter_dtt = self::CrmCasbService()->getValueLookupCRM('transmitter_dtt_list', $obj->transmitter_dtt);
                }
                if ($obj->dth_area != '') {
                    $dth_area = self::CrmCasbService()->getValueLookupCRM('dth_area_list', $obj->dth_area);
                }
                if ($obj->upsell_mytv != '') {
                    $upsell_mytv = self::CrmCasbService()->getValueLookupCRM('upsell_mytv_list', $obj->upsell_mytv);
                }
                if ($obj->state != '') {
                    $state = self::CrmCasbService()->getValueLookupCRM('case_state_dom', $obj->state);
                }
                if ($obj->status != '') {
                    $status = self::CrmCasbService()->getValueLookupCRM('case_status_dom', $obj->status);
                }
                if ($obj->identification_type != '') {
                    $identificationType = self::CrmCasbService()->getValueLookupCRM('identification_type_list', $obj->identification_type);
                }
                if ($obj->escalation_dept != '') {
                    $escalationDept = self::CrmCasbService()->getValueLookupCRM('escalation_dept_list', $obj->escalation_dept);
                }
                if ($obj->sell_type != '') {
                    $sellType = self::CrmCasbService()->getValueLookupCRM('sell_type_list', $obj->sell_type);
                }
                if ($obj->success_type != '') {
                    $successType = self::CrmCasbService()->getValueLookupCRM('success_list', $obj->success_type);
                }
                if ($obj->happy_call != '') {
                    $happyCall = self::CrmCasbService()->getValueLookupCRM('happy_call_list', $obj->happy_call);
                }
                if ($obj->state_case != '') {
                    $stateList = self::CrmCasbService()->getValueLookupCRM('state_list', $obj->state_case);
                }
                if ($obj->contact_id != '') {
                    $emailContact = self::CrmCasbService()->getEmail($obj->contact_id, 'Contacts');
                }
                if ($obj->primary_address_street != '') {
                    $address1 = $obj->primary_address_street;
                } else {
                    $address1 = $obj->alt_address_street;
                }
                if ($obj->primary_address_street != '') {
                    $address2 = $obj->primary_address_street;
                } else {
                    $address2 = $obj->alt_address_street;
                }
                if ($obj->primary_address_city != '') {
                    $address3 = $obj->primary_address_city;
                } else {
                    $address3 = $obj->alt_address_city;
                }
                if ($obj->type_of_property != '') {
                    $typeOfProperty = self::CrmCasbService()->getValueLookupCRM('type_of_property_list', $obj->type_of_property);
                }
                $CsvData[] = (
                        $userGroup . ',' .
                        $obj->case_number . ',' .
                        $subject . ',' .
                        $resolution . ',' .
                        $contactMode . ',' .
                        $category . ',' .
                        $subCategory1 . ',' .
                        $subCategory2 . ',' .
                        $subCategory3 . ',' .
                        $subCategory4 . ',' .
                        $description . ',' .
                        $companyRegNo . ',' .
                        $phoneWork . ',' .
                        $phoneMobile . ',' .
                        $name . ',' .
                        $identificationNo . ',' .
                        $productType . ',' .
                        $district_area . ',' .
                        $transmitter_dtt . ',' .
                        $dth_area . ',' .
                        $upsell_mytv . ',' .
                        $state . ',' .
                        $status . ',' .
                        $createdByName . ',' .
                        $obj->created_date . ',' .
                        $obj->modified_date . ',' .
                        $aging . ',' .
                        $identificationType . ',' .
                        $emailContact . ',' .
                        $address1 . ',' .
                        $address2 . ',' .
                        $address3 . ',' .
                        $escalationDept . ',' .
                        $sellType . ',' .
                        $successType . ',' .
                        $happyCall . ',' .
                        $stateList . ',' . 
                        $typeOfProperty
                        );
            }
            $takentimeeachLoop = array(
                'Case Detail : Counter' => $start,
                'Case Detail : Taken Time per Minutes' => $dtStartTimeEachLoop->diffInMinutes(Carbon::now()),
                'Case Detail : Taken Time per Seconds' => $dtStartTimeEachLoop->diffInSeconds(Carbon::now())
            );
            dump(self::class . ' Case Detail : LoopTakenTime >> Time   :   ', [$takentimeeachLoop]);
            dump(self::class . ' Case Detail : sum total current  :   ' . $totalRecords);
        } while (count($result) > 0 && count($result) == self::$query_take);
        $takentimeOP = array(
            'Case Detail : Counter' => $start,
            'Case Detail : Taken Time per Minutes' => $dtStartTimeOP->diffInMinutes(Carbon::now()),
            'Case Detail : Taken Time per Seconds' => $dtStartTimeOP->diffInSeconds(Carbon::now())
        );
        dump(self::class . ' Case Detail : AllLoopTakenTime >> Time   :   ', [$takentimeOP]);
        dump(self::class . ' Case Detail : queryReport. Total All :  ' . $totalRecords);
        dump(self::class . '--------------------------------------------');
        Log::info(self::class . ' Case Detail : AllLoopTakenTime >> Time   :   ', [$takentimeOP]);
        Log::info(self::class . ' Case Detail : queryReport. Total All :  ' . $totalRecords);

        $filename =$rpt_nm. ".csv";
        $file_path = $report_path . $filename;
        $file = fopen($file_path, "w+");
        foreach ($CsvData as $exp_data) {
            fputcsv($file, explode(',', $exp_data));
        }
        fclose($file);

        $dataReport = collect([]);
        $dataReport->put("date_start", $startDate);
        $dataReport->put("date_end", $endDate);
        $dataReport->put("report_name", $rpt_nm);
        $dataReport->put("file_name", $filename);
    }

    public static function getCaseDetail($startDate, $endDate, $take, $nextSkip) {

        return DB::connection('mysql_casb')
                ->table('cases')
                ->join('accounts', 'accounts.id', '=', 'cases.account_id')
                ->join('contacts', 'contacts.id', '=', 'cases.contact_id')
                ->join('users', 'users.id', '=', 'cases.created_by')
                ->where('cases.deleted', 0)
                ->where('accounts.deleted', 0)
                ->where('contacts.deleted', 0)
                ->whereBetween(DB::raw("DATE(CONVERT_TZ(cases.date_entered,'+00:00','+08:00'))"), [$startDate,$endDate])
                ->select('cases.*', 'accounts.company_reg_no', 'contacts.phone_work', 'contacts.phone_mobile', 
                        'contacts.first_name', 'contacts.last_name', 'contacts.identification_no', 'contacts.id', 
                        'contacts.identification_type', 'contacts.primary_address_street', 'contacts.alt_address_street', 
                        'contacts.primary_address_postalcode', 'contacts.alt_address_postalcode', 'contacts.primary_address_city', 
                        'contacts.alt_address_city', 'users.first_name as user_first_name', 'users.last_name as user_last_name')
                ->addSelect(DB::raw("CONVERT_TZ(cases.date_entered,'+00:00','+08:00') AS created_date"))
                ->addSelect(DB::raw("CONVERT_TZ(cases.date_modified,'+00:00','+08:00') AS modified_date"))
                ->skip($nextSkip)->take($take)->get();
    }

    /**
     * Send an e-mail report
     * @param  Request  $error
     * @return Response
     */
    protected static function sendEmail($dataReport) {
        $transport = (new \Swift_SmtpTransport(
                env('MAIL_EP_HOST', Config::get('constant.mail_ep_host_casb')), env('MAIL_EP_PORT', Config::get('constant.mail_ep_port_casb'))))
                ->setEncryption(env('MAIL_EP_ENCRYPTION', Config::get('constant.mail_ep_encryption_casb')))
                ->setUsername(env('MAIL_EP_USERNAME', Config::get('constant.mail_ep_username_casb')))
                ->setPassword(env('MAIL_EP_PASSWORD', Config::get('constant.mail_ep_password_casb')));

        $mailer = app(\Illuminate\Mail\Mailer::class);
        $mailer->setSwiftMailer(new \Swift_Mailer($transport));
            
        $data = array(
//             "to" => ['<EMAIL>'],
//             "cc" => ['<EMAIL>'],
            // "to" => ['<EMAIL>'],
            // "cc" => ['<EMAIL>'],
            "to" => ['<EMAIL>', '<EMAIL>', '<EMAIL>','<EMAIL>'],
            "cc" => ['<EMAIL>', '<EMAIL>','<EMAIL>','<EMAIL>'],
            "subject" => $dataReport['report_group']
        );
        try {
            Mail::send('emails.reportcasb001', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'data' => $dataReport], function($m) use ($data, $dataReport) {
                $m->from(Config::get('constant.email_sender_casb'), 
                        Config::get('constant.email_sender_name_casb'));
                $m->to($data["to"])
                        ->cc($data["cc"])
                        ->subject($data["subject"]);
                foreach ($dataReport['report_path'] as $rpt_path) {
                    $m->attach($rpt_path);
                }
            });
            dump('done send');
        } catch (\Exception $e) {
            Log::error(self::class . ' Error ... ' . __FUNCTION__ . ' ::  ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }

}
