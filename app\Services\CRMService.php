<?php

namespace App\Services;

use DB;
use Carbon\Carbon;

/**
 * Description of Profile Management PMServiceTrait
 *
 * <AUTHOR>
 */
class CRMService
{
    public static $DB_CONNECTION = 'mysql_crm';


    public function getDetailLookupCRM($type, $value)
    {
        $query = DB::table('cstm_list_app');
        $query->where('type_code', $type);
        $query->where('value_code', $value);
        $data = $query->first();
        return $data;
    }

    public function getDetailUserCRM($id)
    {
        $query = DB::table('users');
        $query->where('id', $id);
        $data = $query->first();
        return $data;
    }

    public function getDetailUserCRMByUsername($username)
    {
        $query = DB::connection($this::$DB_CONNECTION)->table('users');
        $query->join('securitygroups_users', 'users.id', '=', 'securitygroups_users.user_id');
        $query->join('securitygroups', 'securitygroups.id', '=', 'securitygroups_users.securitygroup_id');
        $query->join('email_addr_bean_rel as eabr', 'users.id', '=', 'eabr.bean_id');
        $query->join('email_addresses as ea', 'eabr.email_address_id', '=', 'ea.id');
        // Add join to get modifier user's username
        $query->leftJoin('users as modifier', 'users.modified_user_id', '=', 'modifier.id');
        $query->where('users.user_name', $username);
        $query->where('eabr.deleted', 0);
        $query->where('ea.deleted', 0);
        $query->select(
            'users.*', 
            DB::raw('CONVERT_TZ(users.date_modified, "+00:00", "+08:00") as date_modified'),
            'ea.email_address', 
            DB::raw('GROUP_CONCAT(securitygroups.name) as securitygroup_names'),
            'modifier.user_name as modified_user_name'
        );
        $query->groupBy(['users.id', 'ea.email_address', 'modifier.user_name']);
        $data = $query->first();
        return $data;
    }

    public function getDetailUserCRMByName($name)
    {
        $query = DB::connection($this::$DB_CONNECTION)->table('users');
        $query->join('securitygroups_users', 'users.id', '=', 'securitygroups_users.user_id');
        $query->join('securitygroups', 'securitygroups.id', '=', 'securitygroups_users.securitygroup_id');
        $query->join('email_addr_bean_rel as eabr', 'users.id', '=', 'eabr.bean_id');
        $query->join('email_addresses as ea', 'eabr.email_address_id', '=', 'ea.id');
        // Add join to get modifier user's username
        $query->leftJoin('users as modifier', 'users.modified_user_id', '=', 'modifier.id');
        $query->whereRaw("CONCAT(users.first_name, ' ', users.last_name) LIKE ?", ["%{$name}%"]);
        $query->select(
            'users.*', 
            DB::raw('CONVERT_TZ(users.date_modified, "+00:00", "+08:00") as date_modified'),
            'ea.email_address', 
            DB::raw('GROUP_CONCAT(securitygroups.name) as securitygroup_names'),
            'modifier.user_name as modified_user_name'
        );
        $query->where('eabr.deleted', 0);
        $query->where('ea.deleted', 0);
        $query->groupBy(['users.id', 'ea.email_address', 'modifier.user_name']);
        $data = $query->get();
        return $data;
    }
    
    public function getDetailUserCRMByEmail($email)
    {
        $query = DB::connection($this::$DB_CONNECTION)->table('email_addresses as ea');
        $query->join('email_addr_bean_rel as eabr', 'ea.id', '=', 'eabr.email_address_id');
        $query->join('users as u', 'eabr.bean_id', '=', 'u.id');
        $query->join('securitygroups_users', 'u.id', '=', 'securitygroups_users.user_id');
        $query->join('securitygroups', 'securitygroups.id', '=', 'securitygroups_users.securitygroup_id');
        // Add join to get modifier user's username
        $query->leftJoin('users as modifier', 'u.modified_user_id', '=', 'modifier.id');
        $query->where('ea.email_address', $email);
        $query->where('eabr.bean_module', 'Users');
        $query->where('eabr.deleted', 0);
        $query->where('ea.deleted', 0);
        $query->select(
            'u.*', 
            DB::raw('CONVERT_TZ(u.date_modified, "+00:00", "+08:00") as date_modified'),
            'ea.email_address', 
            DB::raw('GROUP_CONCAT(securitygroups.name) as securitygroup_names'),
            'modifier.user_name as modified_user_name'
        );
        $query->groupBy(['u.id', 'ea.email_address', 'modifier.user_name']);
        $data = $query->first();
        return $data;
    }

    public function deactivateUserEPSS($userId, $ticketHelpdesk, $remark, $authUserName)
    {
        $user = DB::connection($this::$DB_CONNECTION)->table('users')->where('id', $userId)->first();
        
        // Find auth user ID by username, ensuring the user is active
        $authUser = DB::connection($this::$DB_CONNECTION)->table('users')
            ->where('user_name', $authUserName)
            ->where('status', 'Active')
            ->first();
        $modifiedUserId = $authUser ? $authUser->id : 1; // Default to admin (id = 1) if not found
        
        $description = $user->description ?? '';
        $description .= " (Ticket Helpdesk: $ticketHelpdesk, Remark: $remark)";
        
        $query = DB::connection($this::$DB_CONNECTION)->table('users');
        $query->where('id', $userId);
        $affectedRows = $query->update([
            'status' => 'Inactive',
            'description' => $description,
            'modified_user_id' => $modifiedUserId,
            'date_modified' => Carbon::now('UTC')->toDateTimeString()
        ]);
        
        if ($affectedRows > 0) {
            return true;
        } else {
            return false;
        }
    }

    public function getDetailContactCRM($id)
    {
        $query = DB::table('contacts');
        $query->where('id', $id);
        $data = $query->first();
        return $data;
    }

    public function getDetailAccountCRM($id)
    {
        $query = DB::table('accounts');
        $query->where('id', $id);
        $data = $query->first();
        return $data;
    }

    public function getDetailAccountPtjToKementerianCRM($id)
    {
        $query = DB::table('accounts as ptj');
        $query->join('accounts as kumpulanptj', 'ptj.parent_id', '=', 'kumpulanptj.id');
        $query->join('accounts as pegawaipengawal', 'kumpulanptj.parent_id', '=', 'pegawaipengawal.id');
        $query->join('accounts as kementerian', 'pegawaipengawal.parent_id', '=', 'kementerian.id');
        $query->where('ptj.id', $id);
        $query->where('ptj.deleted', 0);
        $query->select('ptj.name as ptj_name', 'kumpulanptj.name as kumpulanptj_name', 'pegawaipengawal.name as pegawaipengawal_name', 'kementerian.name as kementerian_name');
        $data = $query->first();
        return $data;
    }

    public function getDetailAccountKumpulanPtjToKementerianCRM($id)
    {
        $query = DB::table('accounts as kumpulanptj');
        $query->join('accounts as pegawaipengawal', 'kumpulanptj.parent_id', '=', 'pegawaipengawal.id');
        $query->join('accounts as kementerian', 'pegawaipengawal.parent_id', '=', 'kementerian.id');
        $query->where('kumpulanptj.id', $id);
        $query->where('kumpulanptj.deleted', 0);
        $query->select('kumpulanptj.name as kumpulanptj_name', 'pegawaipengawal.name as pegawaipengawal_name', 'kementerian.name as kementerian_name');
        $data = $query->first();
        return $data;
    }

    public function getDetailAccountPegawaiPengawalToKementerianCRM($id)
    {
        $query = DB::table('accounts as pegawaipengawal');
        $query->join('accounts as kementerian', 'pegawaipengawal.parent_id', '=', 'kementerian.id');
        $query->where('pegawaipengawal.id', $id);
        $query->where('pegawaipengawal.deleted', 0);
        $query->select('pegawaipengawal.name as pegawaipengawal_name', 'kementerian.name as kementerian_name');
        $data = $query->first();
        return $data;
    }

    public function getDetailTaskLatestCRM($caseId)
    {
        $query = DB::table('tasks');
        $query->join('tasks_cstm', 'tasks.id', '=', 'tasks_cstm.id_c');
        $query->where('tasks.parent_id', $caseId);
        $query->where('tasks.deleted', 0);
        $query->where('tasks.parent_type', 'Cases');
        $query->orderBy('tasks_cstm.task_number_c', 'desc');
        $data = $query->first();
        return $data;
    }

    public function getDetailTaskCaseOwnerCRM($caseId)
    {
        $query = DB::table('tasks');
        $query->join('tasks_cstm', 'tasks.id', '=', 'tasks_cstm.id_c');
        $query->where('tasks.parent_id', $caseId);
        $query->where('tasks.deleted', 0);
        $query->where('tasks.parent_type', 'Cases');
        $query->where('tasks.name', 'Assigned to Case Owner');
        $query->orderBy('tasks_cstm.task_number_c', 'desc');
        $data = $query->first();
        return $data;
    }

    public function getDetailTaskLatestFlagCRM($caseId)
    {
        $query = DB::table('tasks');
        $query->join('tasks_cstm', 'tasks.id', '=', 'tasks_cstm.id_c');
        $query->where('tasks.parent_id', $caseId);
        $query->where('tasks.parent_type', 'Cases');
        $query->where('tasks.status', 'Completed');
        $query->whereNotNull('tasks_cstm.sla_task_flag_c');
        $query->whereNotIn('tasks_cstm.sla_task_flag_c', ['']);
        $query->where('tasks.deleted', 0);
        $query->select('tasks.*', 'tasks_cstm.*');
        $query->orderBy('tasks_cstm.task_number_c', 'desc');
        $data = $query->first();

        return $data;
    }

    public function getDetailTaskCRM($caseId)
    {
        $query = DB::table('tasks');
        $query->join('tasks_cstm', 'tasks.id', '=', 'tasks_cstm.id_c');
        $query->where('tasks.parent_id', $caseId);
        $query->where('tasks.deleted', 0);
        $query->where('tasks.parent_type', 'Cases');
        $query->whereNotNull('tasks.resolution_category_c');
        $query->orderBy('tasks.date_entered', 'desc');
        $data = $query->first();
        return $data;
    }

    public function getDetailTaskSeverityCRM($caseId)
    {
        $query = DB::table('tasks');
        $query->join('tasks_cstm', 'tasks.id', '=', 'tasks_cstm.id_c');
        $query->where('tasks.parent_id', $caseId);
        $query->where('tasks.parent_type', 'Cases');
        $query->whereNotNull('tasks.task_severity');
        $query->orderBy('tasks.date_entered', 'desc');
        $data = $query->first();
        return $data;
    }

    public function getDetailTaskById($taskId)
    {
        $query = DB::table('tasks');
        $query->join('tasks_cstm', 'tasks.id', '=', 'tasks_cstm.id_c');
        $query->where('tasks.id', $taskId);
        $query->where('tasks.deleted', 0);
        $query->orderBy('tasks_cstm.task_number_c', 'desc');
        $data = $query->first();
        return $data;
    }

    public function getListDetailGroupCRM($userId)
    {
        $query = DB::table('securitygroups');
        $query->join('securitygroups_users', 'securitygroups.id', '=', 'securitygroups_users.securitygroup_id');
        $query->where('securitygroups_users.user_id', $userId);
        $query->where('securitygroups_users.deleted', 0);
        $data = $query->get();
        return $data;
    }

    public function getEmailDetail($module, $moduleId)
    {
        $query = DB::table('email_addresses');
        $query->join('email_addr_bean_rel', 'email_addresses.id', '=', 'email_addr_bean_rel.email_address_id');
        $query->where('email_addr_bean_rel.bean_module', $module);
        $query->where('email_addr_bean_rel.bean_id', $moduleId);
        $query->where('email_addr_bean_rel.deleted', 0);
        $data = $query->first();
        return $data;
    }

    public function getDetailLeadCRM($id)
    {
        $query = DB::table('leads');
        $query->join('leads_cases_1_c', 'leads.id', '=', 'leads_cases_1_c.leads_cases_1leads_ida');
        $query->join('leads_cstm', 'leads.id', '=', 'leads_cstm.id_c');
        $query->where('leads_cases_1_c.leads_cases_1cases_idb', $id);
        $data = $query->first();
        return $data;
    }

    public function getValueLookupCRM($type, $value)
    {
        $data = $this->getDetailLookupCRM($type, $value);
        if ($data) {
            return $data->value_name;
        }
    }

    public function getNameUserCRM($id)
    {
        $data = $this->getDetailUserCRM($id);
        if ($data) {
            return $data->first_name . $data->last_name;
        }
    }

    public function getLocationUserCRM($id)
    {
        $data = $this->getDetailUserCRM($id);
        if ($data) {
            return $data->user_location;
        }
    }

    public function getNameContactCRM($id)
    {
        $data = $this->getDetailContactCRM($id);
        if ($data) {
            return $data->first_name;
        }
    }

    public function getEmailCRM($module, $moduleId)
    {
        $data = $this->getEmailDetail($module, $moduleId);
        if ($data) {
            return $data->email_address;
        }
    }

    public function getDetailAccountPtjToKementerianCRMCaseReport($id)
    {
        $query = DB::table('accounts as ptj');
        $query->join('accounts as kumpulanptj', 'ptj.parent_id', '=', 'kumpulanptj.id');
        $query->join('accounts as pegawaipengawal', 'kumpulanptj.parent_id', '=', 'pegawaipengawal.id');
        $query->join('accounts as kementerian', 'pegawaipengawal.parent_id', '=', 'kementerian.id');
        $query->where('ptj.id', $id);
        $query->select('ptj.name as ptj_name', 'kumpulanptj.name as kumpulanptj_name', 'pegawaipengawal.name as pegawaipengawal_name', 'kementerian.name as kementerian_name');
        $query->orderBy('ptj.date_modified', 'desc');
        $data = $query->first();
        return $data;
    }

    public function getDetailAccountByPTJ($id)
    {
        $query = DB::table('accounts as ptj');
        $query->join('accounts as kumpulanptj', 'ptj.parent_id', '=', 'kumpulanptj.id');
        $query->join('accounts as pegawaipengawal', 'kumpulanptj.parent_id', '=', 'pegawaipengawal.id');
        $query->join('accounts as kementerian', 'pegawaipengawal.parent_id', '=', 'kementerian.id');
        $query->where('ptj.id', $id);
        $query->select('ptj.name as ptj_name', 'kumpulanptj.name as kumpulanptj_name', 'pegawaipengawal.name as pegawaipengawal_name', 'kementerian.name as kementerian_name');
        $query->orderBy('ptj.date_modified', 'desc');
        $data = $query->first();
        return $data;
    }

    public function getDetailAccountByKumpulanPTJ($id)
    {
        $query = DB::table('accounts as kumpulanptj');
        $query->join('accounts as pegawaipengawal', 'kumpulanptj.parent_id', '=', 'pegawaipengawal.id');
        $query->join('accounts as kementerian', 'pegawaipengawal.parent_id', '=', 'kementerian.id');
        $query->where('kumpulanptj.id', $id);
        $query->select('kumpulanptj.name as kumpulanptj_name', 'pegawaipengawal.name as pegawaipengawal_name', 'kementerian.name as kementerian_name');
        $query->orderBy('kumpulanptj.date_modified', 'desc');
        $data = $query->first();
        return $data;
    }

    public function getDetailLeads($id)
    {
        $query = DB::table('leads');
        $query->join('leads_cstm', 'leads.id', '=', 'leads_cstm.id_c');
        $query->where('leads.id', $id);
        $data = $query->first();
        return $data;
    }

    public function getCaseRelate($id)
    {
        $query = DB::table('cases as onsite');
        $query->join('cases', 'cases.id', '=', 'onsite.acase_id_c');
        $query->where('onsite.acase_id_c', $id);
        $data = $query->first();
        return $data;
    }

    public function getDetailContactByFirstnameCRM($firstName, $accId)
    {
        $query = DB::table('contacts');
        $query->where('first_name', $firstName);
        $query->where('account_reference_id', $accId);
        $query->orderBy('date_modified', 'desc');
        $data = $query->first();
        return $data;
    }

    public function getTotalVisitCntc($contactId)
    {
        $query = DB::table('contacts_cases');
        $query->join('cases', 'cases.id', '=', 'contacts_cases.case_id');
        $query->join('cases_cstm', 'cases.id', '=', 'cases_cstm.id_c');
        $query->where('contacts_cases.contact_id', $contactId);
        $query->where('cases.deleted', 0);
        $query->whereIn('cases_cstm.category_c', ['10719', '10720']);
        $data = $query->count();
        return $data;
    }

    public function getTotalVisitAddCntc($firstName)
    {
        $query = DB::table('cases');
        $query->join('cases_cstm', 'cases.id', '=', 'cases_cstm.id_c');
        $query->where('cases.ptj_user_name', 'like', '%' . $firstName . '%');
        $query->where('cases.deleted', 0);
        $query->whereIn('cases_cstm.category_c', ['10719', '10720']);
        $data = $query->count();
        return $data;
    }

    public function getDetailCase($caseNumber, $caseName, $caseResolution)
    {
        $query = DB::table('cases');
        $query->join('cases_cstm', 'cases.id', '=', 'cases_cstm.id_c');
        $query->where('cases.deleted', 0);
        $query->whereNotIn('cases.case_number', [$caseNumber]);
        $query->where('cases.name', $caseName);
        $query->where('cases.resolution', $caseResolution);
        $data = $query->first();
        return $data;
    }

    public function getCaseOwnerTask($caseId)
    {
        $query = DB::table('tasks');
        $query->where('tasks.parent_id', $caseId);
        $query->where('tasks.name', 'Assigned to Case Owner');
        $query->where('tasks.deleted', 0);
        $data = $query->get();
        return $data;
    }

    public function notifyWhatsapp($msg, $receiver, $sourceApp, $sourceRemark) {
        DB::connection('mysql_ep_notify')
                ->insert('insert into notification  
                    (message,receiver_group,process,status,sent,date_entered,retry,source_app,source_class,source_remark) 
                    values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', [
                    $msg,
                    $receiver,
                    'notify', 0, 0,
                    Carbon::now(), 0,
                    $sourceApp,
                    __CLASS__,
                    $sourceRemark
        ]);
    }
}
