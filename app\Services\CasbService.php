<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Services;

use DB;
use Carbon\Carbon;

class CasbService
{
    public static $DB_CONNECTION = 'mysql_casb';

    public function getDetailUserCRMByUsername($username)
    {
        $query = DB::connection($this::$DB_CONNECTION)->table('users');
        $query->join('securitygroups_users', 'users.id', '=', 'securitygroups_users.user_id');
        $query->join('securitygroups', 'securitygroups.id', '=', 'securitygroups_users.securitygroup_id');
        $query->join('email_addr_bean_rel as eabr', 'users.id', '=', 'eabr.bean_id');
        $query->join('email_addresses as ea', 'eabr.email_address_id', '=', 'ea.id');
        // Add join to get modifier user's username
        $query->leftJoin('users as modifier', 'users.modified_user_id', '=', 'modifier.id');
        $query->where('users.user_name', $username);
        $query->where('eabr.deleted', 0);
        $query->where('ea.deleted', 0);
        $query->select(
            'users.*', 
            DB::raw('CONVERT_TZ(users.date_modified, "+00:00", "+08:00") as date_modified'),
            'ea.email_address', 
            DB::raw('GROUP_CONCAT(securitygroups.name) as securitygroup_names'),
            'modifier.user_name as modified_user_name'
        );
        $query->groupBy(['users.id', 'ea.email_address', 'modifier.user_name']);
        $data = $query->first();
        return $data;
    }

    public function getDetailUserCRMByName($name)
    {
        $query = DB::connection($this::$DB_CONNECTION)->table('users');
        $query->join('securitygroups_users', 'users.id', '=', 'securitygroups_users.user_id');
        $query->join('securitygroups', 'securitygroups.id', '=', 'securitygroups_users.securitygroup_id');
        $query->join('email_addr_bean_rel as eabr', 'users.id', '=', 'eabr.bean_id');
        $query->join('email_addresses as ea', 'eabr.email_address_id', '=', 'ea.id');
        // Add join to get modifier user's username
        $query->leftJoin('users as modifier', 'users.modified_user_id', '=', 'modifier.id');
        $query->whereRaw("CONCAT(users.first_name, ' ', users.last_name) LIKE ?", ["%{$name}%"]);
        $query->select(
            'users.*', 
            DB::raw('CONVERT_TZ(users.date_modified, "+00:00", "+08:00") as date_modified'),
            'ea.email_address', 
            DB::raw('GROUP_CONCAT(securitygroups.name) as securitygroup_names'),
            'modifier.user_name as modified_user_name'
        );
        $query->where('eabr.deleted', 0);
        $query->where('ea.deleted', 0);
        $query->groupBy(['users.id', 'ea.email_address', 'modifier.user_name']);
        $data = $query->get();
        return $data;
    }
    
    public function getDetailUserCRMByEmail($email)
    {
        $query = DB::connection($this::$DB_CONNECTION)->table('email_addresses as ea');
        $query->join('email_addr_bean_rel as eabr', 'ea.id', '=', 'eabr.email_address_id');
        $query->join('users as u', 'eabr.bean_id', '=', 'u.id');
        $query->join('securitygroups_users', 'u.id', '=', 'securitygroups_users.user_id');
        $query->join('securitygroups', 'securitygroups.id', '=', 'securitygroups_users.securitygroup_id');
        // Add join to get modifier user's username
        $query->leftJoin('users as modifier', 'u.modified_user_id', '=', 'modifier.id');
        $query->where('ea.email_address', $email);
        $query->where('eabr.bean_module', 'Users');
        $query->where('eabr.deleted', 0);
        $query->where('ea.deleted', 0);
        $query->select(
            'u.*', 
            DB::raw('CONVERT_TZ(u.date_modified, "+00:00", "+08:00") as date_modified'),
            'ea.email_address', 
            DB::raw('GROUP_CONCAT(securitygroups.name) as securitygroup_names'),
            'modifier.user_name as modified_user_name'
        );
        $query->groupBy(['u.id', 'ea.email_address', 'modifier.user_name']);
        $data = $query->first();
        return $data;
    }
    
    public function deactivateUserEPSS($userId, $ticketHelpdesk, $remark, $authUserName)
    {
        $user = DB::connection($this::$DB_CONNECTION)->table('users')->where('id', $userId)->first();
        
        // Find auth user ID by username, ensuring the user is active
        $authUser = DB::connection($this::$DB_CONNECTION)->table('users')
            ->where('user_name', $authUserName)
            ->where('status', 'Active')
            ->first();
        $modifiedUserId = $authUser ? $authUser->id : 1; // Default to admin (id = 1) if not found
        
        $description = $user->description ?? '';
        $description .= " (Ticket Helpdesk: $ticketHelpdesk, Remark: $remark)";
        
        $query = DB::connection($this::$DB_CONNECTION)->table('users');
        $query->where('id', $userId);
        $affectedRows = $query->update([
            'status' => 'Inactive',
            'description' => $description,
            'modified_user_id' => $modifiedUserId,
            'date_modified' => Carbon::now('UTC')->toDateTimeString()
        ]);
        
        if ($affectedRows > 0) {
            return true;
        } else {
            return false;
        }
    }

    public static function getStateCode($stateName)
    {
        return DB::connection('mysql_casb')->table('cstm_list_app')
            ->where('name', 'state_list')
            ->where('status', 1)
            ->where('value_name', $stateName)->select('value_code')
            ->first();
    }

    public static function checkAccount($name, $phone, $type)
    {

        return DB::connection('mysql_casb')->table('accounts')
            ->where('name', $name)
            ->where('phone_office', $phone)
            ->where('account_type', $type)
            ->first();
    }

    public static function checkEmail($email)
    {
        return DB::connection('mysql_casb')->table('email_addresses')
            ->where('email_address', $email)
            ->orderBy('date_created', 'desc')->first();
    }

    public static $stateList = array(
        'JOHOR', 'KEDAH', 'KELANTAN', 'MELAKA', 'NEGERI SEMBILAN', 'PAHANG', 'PERAK',
        'PERLIS', 'PULAU PINANG', 'SABAH', 'SARAWAK', 'SELANGOR', 'TERENGGANU', 'WILAYAH PERSEKUTUAN (KUALA LUMPUR)',
        'WILAYAH PERSEKUTUAN (LABUAN)', 'WILAYAH PERSEKUTUAN (PUTRAJAYA)'
    );
    public static $enArray = array('En', 'En.', 'en', 'en,', 'Encik', 'Encik.', 'encik', 'encik.', 'mr', 'Mr');
    public static $cikArray = array('Cik', 'Cik.', 'cik', 'cik.', 'Ms', 'ms');
    public static $pnArray = array('Puan', 'Puan.', 'puan', 'Pn.', 'Pn', 'pn', 'pn.', 'mdm', 'Mdm', 'Mrs', 'mrs');
    public static $salutationList = array('Ms.', 'Mrs.', 'Mr.');
    public static $domainIdList = array(
        'Altel' =>
        array(
            'id' => '52801af6-9904-d25a-386d-606935483167',
            'name' => 'Group Altel',
        ),
        'Mytv' =>
        array(
            'id' => 'd020956b-a913-00ac-4f42-60693594e3ba',
            'name' => 'Group Group Mytv',
        ),
    );


    public static $caseStatus = array(
        'C' => 'Closed',
        '0' => 'Open',
    );

    public static function getListCode($listName, $listType)
    {
        $result =  DB::connection('mysql_casb')->table('cstm_list_app')
            ->where('name', $listType)
            ->where('status', 1)
            ->where('value_name', $listName)->select('value_code')
            ->first();

        if ($result) $result = $result->value_code;

        return $result;
    }

    public static function checkCases($name, $category, $sub_category, $domainId)
    {

        return DB::connection('mysql_casb')->table('cases')
            ->where('name', $name)
            ->where('category', $category)
            ->where('sub_category', $sub_category)
            ->where('domain_id', $domainId)
            ->first();
    }

    public static function findAccount($customerName, $contactNo)
    {
        return DB::connection('mysql_casb')->table('accounts')
            ->where('name', $customerName)
            ->where('phone_office', $contactNo)
            ->first();
    }

    public static function findContact($customerName, $contactNo)
    {
        return DB::connection('mysql_casb')->table('contacts')
            ->where('first_name', $customerName)
            ->where('phone_mobile', $contactNo)
            ->first();
    }

    public static function getSecurityGroup($id)
    {
        return DB::connection('mysql_casb')->table('securitygroups')
            ->where('id', $id)
            ->first();
    }

    public static function getValueLookupCRM($type, $value)
    {
        $data = DB::connection('mysql_casb')
            ->table('cstm_list_app')
            ->where('type_code', $type)
            ->where('value_code', $value)
            ->where('deleted', 0)
            ->first();

        if ($data) {
            return $data->value_name;
        }
    }

    public static function getEmail($id, $module)
    {
        return DB::connection('mysql_casb')
            ->table('email_addr_bean_rel')
            ->join('email_addresses', 'email_addresses.id', 'email_addr_bean_rel.email_address_id')
            ->join('emails_beans', 'emails_beans.email_id', '=', 'email_addr_bean_rel.email_address_id')
            ->where('email_addr_bean_rel.bean_id', $id)
            ->where('email_addr_bean_rel.bean_module', $module)
            ->where('email_addresses.deleted', 0)
            ->where('emails_beans.deleted', 0)
            ->select('email_addresses.email_address')->first();
    }
}
