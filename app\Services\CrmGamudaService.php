<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Services;

use DB;
use Carbon\Carbon;

class CrmGamudaService
{

    public static $DB_CONNECTION = 'mysql_crm_gamuda';

    public function getDetailUserCRMByUsername($username)
    {
        $query = DB::connection($this::$DB_CONNECTION)->table('users');
        $query->join('securitygroups_users', 'users.id', '=', 'securitygroups_users.user_id');
        $query->join('securitygroups', 'securitygroups.id', '=', 'securitygroups_users.securitygroup_id');
        $query->join('email_addr_bean_rel as eabr', 'users.id', '=', 'eabr.bean_id');
        $query->join('email_addresses as ea', 'eabr.email_address_id', '=', 'ea.id');
        // Add join to get modifier user's username
        $query->leftJoin('users as modifier', 'users.modified_user_id', '=', 'modifier.id');
        $query->where('users.user_name', $username);
        $query->where('eabr.deleted', 0);
        $query->where('ea.deleted', 0);
        $query->select(
            'users.*', 
            DB::raw('CONVERT_TZ(users.date_modified, "+00:00", "+08:00") as date_modified'),
            'ea.email_address', 
            DB::raw('GROUP_CONCAT(securitygroups.name) as securitygroup_names'),
            'modifier.user_name as modified_user_name'
        );
        $query->groupBy(['users.id', 'ea.email_address', 'modifier.user_name']);
        $data = $query->first();
        return $data;
    }

    public function getDetailUserCRMByName($name)
    {
        $query = DB::connection($this::$DB_CONNECTION)->table('users');
        $query->join('securitygroups_users', 'users.id', '=', 'securitygroups_users.user_id');
        $query->join('securitygroups', 'securitygroups.id', '=', 'securitygroups_users.securitygroup_id');
        $query->join('email_addr_bean_rel as eabr', 'users.id', '=', 'eabr.bean_id');
        $query->join('email_addresses as ea', 'eabr.email_address_id', '=', 'ea.id');
        // Add join to get modifier user's username
        $query->leftJoin('users as modifier', 'users.modified_user_id', '=', 'modifier.id');
        $query->whereRaw("CONCAT(users.first_name, ' ', users.last_name) LIKE ?", ["%{$name}%"]);
        $query->select(
            'users.*', 
            DB::raw('CONVERT_TZ(users.date_modified, "+00:00", "+08:00") as date_modified'),
            'ea.email_address', 
            DB::raw('GROUP_CONCAT(securitygroups.name) as securitygroup_names'),
            'modifier.user_name as modified_user_name'
        );
        $query->where('eabr.deleted', 0);
        $query->where('ea.deleted', 0);
        $query->groupBy(['users.id', 'ea.email_address', 'modifier.user_name']);
        $data = $query->get();
        return $data;
    }
    
    public function getDetailUserCRMByEmail($email)
    {
        $query = DB::connection($this::$DB_CONNECTION)->table('email_addresses as ea');
        $query->join('email_addr_bean_rel as eabr', 'ea.id', '=', 'eabr.email_address_id');
        $query->join('users as u', 'eabr.bean_id', '=', 'u.id');
        $query->join('securitygroups_users', 'u.id', '=', 'securitygroups_users.user_id');
        $query->join('securitygroups', 'securitygroups.id', '=', 'securitygroups_users.securitygroup_id');
        // Add join to get modifier user's username
        $query->leftJoin('users as modifier', 'u.modified_user_id', '=', 'modifier.id');
        $query->where('ea.email_address', $email);
        $query->where('eabr.bean_module', 'Users');
        $query->where('eabr.deleted', 0);
        $query->where('ea.deleted', 0);
        $query->select(
            'u.*', 
            DB::raw('CONVERT_TZ(u.date_modified, "+00:00", "+08:00") as date_modified'),
            'ea.email_address', 
            DB::raw('GROUP_CONCAT(securitygroups.name) as securitygroup_names'),
            'modifier.user_name as modified_user_name'
        );
        $query->groupBy(['u.id', 'ea.email_address', 'modifier.user_name']);
        $data = $query->first();
        return $data;
    }

    public function deactivateUserEPSS($userId, $ticketHelpdesk, $remark, $authUserName)
    {
        $user = DB::connection($this::$DB_CONNECTION)->table('users')->where('id', $userId)->first();
        
        // Find auth user ID by username, ensuring the user is active
        $authUser = DB::connection($this::$DB_CONNECTION)->table('users')
            ->where('user_name', $authUserName)
            ->where('status', 'Active')
            ->first();
        $modifiedUserId = $authUser ? $authUser->id : 1; // Default to admin (id = 1) if not found
        
        $description = $user->description ?? '';
        $description .= " (Ticket Helpdesk: $ticketHelpdesk, Remark: $remark)";
        
        $query = DB::connection($this::$DB_CONNECTION)->table('users');
        $query->where('id', $userId);
        $affectedRows = $query->update([
            'status' => 'Inactive',
            'description' => $description,
            'modified_user_id' => $modifiedUserId,
            'date_modified' => Carbon::now('UTC')->toDateTimeString()
        ]);
        
        if ($affectedRows > 0) {
            return true;
        } else {
            return false;
        }
    }

    public function getCaseThreaded($caseId)
    {
        return DB::connection(self::$DB_CONNECTION)
            ->table('notes as a')
            ->join('aop_case_updates as b', 'a.parent_id', '=', 'b.id')
            ->join('cases as c', 'c.id', '=', 'b.case_id')
            ->where('b.deleted', 0)
            ->where('c.id', $caseId)
            ->whereNotIn('b.created_by', [1])
            ->select('a.name as notename')
            ->get();
    }

    public function getSecurityGroup($userId)
    {
        return DB::connection(self::$DB_CONNECTION)
            ->table('securitygroups')
            ->join('securitygroups_users', 'securitygroups.id', '=', 'securitygroups_users.securitygroup_id')
            ->where('securitygroups_users.user_id', $userId)
            ->where('securitygroups_users.deleted', 0)
            ->get();
    }

    public static function getCaseUpdate($caseId)
    {
        return DB::connection(self::$DB_CONNECTION)
            ->table('aop_case_updates')
            ->where('aop_case_updates.deleted', 0)
            ->where('aop_case_updates.case_id', $caseId)
            ->first();
    }
}
