<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Services;

use DB;
use Carbon\Carbon;

class CrmSsmService
{

    public static $DB_CONNECTION = 'mysql_crm_ssm';
    public static $DB_MIGRATION_CONNECTION = 'mysql_crm_ssm_migration';

    public function getDetailUserCRMByUsername($username)
    {
        $query = DB::connection($this::$DB_CONNECTION)->table('users');
        $query->join('securitygroups_users', 'users.id', '=', 'securitygroups_users.user_id');
        $query->join('securitygroups', 'securitygroups.id', '=', 'securitygroups_users.securitygroup_id');
        $query->join('email_addr_bean_rel as eabr', 'users.id', '=', 'eabr.bean_id');
        $query->join('email_addresses as ea', 'eabr.email_address_id', '=', 'ea.id');
        // Add join to get modifier user's username
        $query->leftJoin('users as modifier', 'users.modified_user_id', '=', 'modifier.id');
        $query->where('users.user_name', $username);
        $query->where('eabr.deleted', 0);
        $query->where('ea.deleted', 0);
        $query->select(
            'users.*', 
            DB::raw('CONVERT_TZ(users.date_modified, "+00:00", "+08:00") as date_modified'),
            'ea.email_address', 
            DB::raw('GROUP_CONCAT(securitygroups.name) as securitygroup_names'),
            'modifier.user_name as modified_user_name'
        );
        $query->groupBy(['users.id', 'ea.email_address', 'modifier.user_name']);
        $data = $query->first();
        return $data;
    }

    public function getDetailUserCRMByName($name)
    {
        $query = DB::connection($this::$DB_CONNECTION)->table('users');
        $query->join('securitygroups_users', 'users.id', '=', 'securitygroups_users.user_id');
        $query->join('securitygroups', 'securitygroups.id', '=', 'securitygroups_users.securitygroup_id');
        $query->join('email_addr_bean_rel as eabr', 'users.id', '=', 'eabr.bean_id');
        $query->join('email_addresses as ea', 'eabr.email_address_id', '=', 'ea.id');
        // Add join to get modifier user's username
        $query->leftJoin('users as modifier', 'users.modified_user_id', '=', 'modifier.id');
        $query->whereRaw("CONCAT(users.first_name, ' ', users.last_name) LIKE ?", ["%{$name}%"]);
        $query->select(
            'users.*', 
            DB::raw('CONVERT_TZ(users.date_modified, "+00:00", "+08:00") as date_modified'),
            'ea.email_address', 
            DB::raw('GROUP_CONCAT(securitygroups.name) as securitygroup_names'),
            'modifier.user_name as modified_user_name'
        );
        $query->where('eabr.deleted', 0);
        $query->where('ea.deleted', 0);
        $query->groupBy(['users.id', 'ea.email_address', 'modifier.user_name']);
        $data = $query->get();
        return $data;
    }
    
    public function getDetailUserCRMByEmail($email)
    {
        $query = DB::connection($this::$DB_CONNECTION)->table('email_addresses as ea');
        $query->join('email_addr_bean_rel as eabr', 'ea.id', '=', 'eabr.email_address_id');
        $query->join('users as u', 'eabr.bean_id', '=', 'u.id');
        $query->join('securitygroups_users', 'u.id', '=', 'securitygroups_users.user_id');
        $query->join('securitygroups', 'securitygroups.id', '=', 'securitygroups_users.securitygroup_id');
        // Add join to get modifier user's username
        $query->leftJoin('users as modifier', 'u.modified_user_id', '=', 'modifier.id');
        $query->where('ea.email_address', $email);
        $query->where('eabr.bean_module', 'Users');
        $query->where('eabr.deleted', 0);
        $query->where('ea.deleted', 0);
        $query->select(
            'u.*', 
            DB::raw('CONVERT_TZ(u.date_modified, "+00:00", "+08:00") as date_modified'),
            'ea.email_address', 
            DB::raw('GROUP_CONCAT(securitygroups.name) as securitygroup_names'),
            'modifier.user_name as modified_user_name'
        );
        $query->groupBy(['u.id', 'ea.email_address', 'modifier.user_name']);
        $data = $query->first();
        return $data;
    }

    public function deactivateUserEPSS($userId, $ticketHelpdesk, $remark, $authUserName)
    {
        $user = DB::connection($this::$DB_CONNECTION)->table('users')->where('id', $userId)->first();
        
        // Find auth user ID by username, ensuring the user is active
        $authUser = DB::connection($this::$DB_CONNECTION)->table('users')
            ->where('user_name', $authUserName)
            ->where('status', 'Active')
            ->first();
        $modifiedUserId = $authUser ? $authUser->id : 1; // Default to admin (id = 1) if not found
        
        $description = $user->description ?? '';
        $description .= " (Ticket Helpdesk: $ticketHelpdesk, Remark: $remark)";
        
        $query = DB::connection($this::$DB_CONNECTION)->table('users');
        $query->where('id', $userId);
        $affectedRows = $query->update([
            'status' => 'Inactive',
            'description' => $description,
            'modified_user_id' => $modifiedUserId,
            'date_modified' => Carbon::now('UTC')->toDateTimeString()
        ]);
        
        if ($affectedRows > 0) {
            return true;
        } else {
            return false;
        }
    }

    public static function getValueCode($value_name, $type_code)
    {
        return DB::connection(self::$DB_CONNECTION)->table('cstm_list_app')
            ->where('type_code', $type_code)
            ->where('status', 1)
            ->where('value_name', $value_name)->select('value_code')
            ->first();
    }

    public static function checkAccount($customerName, $registrationNo, $emailAddress, $contactNo)
    {
        $query = DB::connection(self::$DB_CONNECTION)->table('accounts');
        if ($emailAddress !== null) {
            $query->join('email_addr_bean_rel', 'email_addr_bean_rel.bean_id', 'accounts.id')
                ->join('email_addresses', 'email_addresses.id', 'email_addr_bean_rel.email_address_id')
                ->where('email_addresses.email_address', $emailAddress)
                ->where('email_addr_bean_rel.deleted', 0)
                ->where('email_addresses.deleted', 0);
        } else if ($registrationNo !== null) {
            $query->where('accounts.company_reg_no', $registrationNo);
        } else if ($contactNo !== null) {
            $query->where('accounts.phone_office', $contactNo);
        }
        $query->where('accounts.name', $customerName)
            ->where('accounts.deleted', 0);
        $data = $query->first();
        return $data;
    }

    public static function findAgent($agentName)
    {
        return DB::connection(self::$DB_CONNECTION)->table('users')
            ->where('first_name', $agentName)
            ->get();
    }

    public static function getAccountId($oldCustomerId)
    {
        return DB::connection(self::$DB_CONNECTION)->table('accounts')
            ->where('company_old_id', $oldCustomerId)
            ->first();
    }

    public static function getAccountName($customerName, $mainContact)
    {
        $res = DB::connection(self::$DB_CONNECTION)->table('accounts')
            ->where('name', $customerName);
        if (isset($mainContact)) {
            $res->where('phone_office', 'like', '%' . $mainContact . '%');
        }

        $data = $res->first();
        return $data;
    }

    public static function getParentCase($caseNo)
    {
        return DB::connection(self::$DB_CONNECTION)
            ->table('cases')
            ->where('cases.name', $caseNo)
            ->first();
    }

    public static function getEmail($name, $email)
    {
        return DB::connection(self::$DB_CONNECTION)
            ->table('accounts')->join('email_addr_bean_rel', 'email_addr_bean_rel.bean_id', 'accounts.id')
            ->join('email_addresses', 'email_addresses.id', 'email_addr_bean_rel.email_address_id')
            ->where('accounts.name', $name)
            ->where('email_addresses.email_address', $email)
            ->select('accounts.id', 'accounts.name')
            ->first();
    }

    public static function getValueLookupCRM($type, $value)
    {
        $data = DB::connection(self::$DB_CONNECTION)
            ->table('cstm_list_app')
            ->where('type_code', $type)
            ->where('value_code', $value)
            ->where('deleted', 0)
            ->first();

        if ($data) {
            return $data->value_name;
        }
    }

    public static function getEmailUser($accountId)
    {
        return DB::connection(self::$DB_CONNECTION)
            ->table('email_addr_bean_rel')
            ->join('email_addresses', 'email_addresses.id', '=', 'email_addr_bean_rel.email_address_id')
            ->where('email_addr_bean_rel.bean_id', $accountId)
            ->where('email_addr_bean_rel.bean_module', '=', 'Accounts')
            ->where('email_addr_bean_rel.deleted', 0)
            ->where('email_addresses.deleted', 0)
            ->first();
    }

    public static function getUserDetail($userId)
    {
        return DB::connection(self::$DB_CONNECTION)
            ->table('users')
            ->where('users.id', $userId)
            ->where('users.deleted', 0)
            ->first();
    }

    public static function getAccount($accountId)
    {
        return DB::connection(self::$DB_CONNECTION)
            ->table('accounts')
            ->where('accounts.id', $accountId)
            ->where('deleted', 0)
            ->first();
    }

    public static function getCaseUpdate($caseId)
    {
        return DB::connection(self::$DB_CONNECTION)
            ->table('aop_case_updates')
            ->where('aop_case_updates.deleted', 0)
            ->where('aop_case_updates.case_id', $caseId)
            ->first();
    }

    public static function getCaseThreaded($caseId)
    {
        return DB::connection(self::$DB_CONNECTION)
            ->table('notes as a')
            ->join('aop_case_updates as b', 'a.parent_id', '=', 'b.id')
            ->join('cases as c', 'c.id', '=', 'b.case_id')
            ->where('b.deleted', 0)
            ->where('c.id', $caseId)
            ->whereNotIn('b.created_by', [1])
            ->select('a.name as notename')
            ->get();
    }

    public static function getSecurityGroup($userId)
    {
        $query = DB::connection(self::$DB_CONNECTION)->table('securitygroups');
        $query->join('securitygroups_users', 'securitygroups.id', '=', 'securitygroups_users.securitygroup_id');
        $query->where('securitygroups_users.user_id', $userId);
        $query->where('securitygroups_users.deleted', 0);
        $data = $query->get();
        return $data;
    }

    public static function getCaseDetails($caseNumber)
    {
        $query = DB::connection('mysql_crm_ssm')
            ->table('cases');
        $query->where('case_number', $caseNumber);
        $data = $query->first();
        return $data;
    }

    public static function getTaskDetails($caseId)
    {
        $query = DB::connection('mysql_crm_ssm')
            ->table('tasks');
        $query->where('parent_id', $caseId);
        $data = $query->first();
        return $data;
    }

    public static function updateCaseDetails($casesId)
    {
        return DB::connection('mysql_crm_ssm')
            ->table('cases')
            ->where('id', $casesId)
            ->update([
                'state' => 'Open',
                'status' => 'Open_New'
            ]);
    }

    public static function updateTaskDetails($tasksId)
    {
        return DB::connection('mysql_crm_ssm')
            ->table('tasks')
            ->where('id', $tasksId)
            ->update([
                'deleted' => '1',
            ]);
    }

    public static function getAssignedTask($dateStart, $dateEnd, $take, $nextSkip)
    {
        return DB::connection('mysql_crm_ssm')
            ->table('cases')
            ->join('tasks', 'tasks.parent_id', '=', 'cases.id')
            ->whereNotIn('tasks.status', ['Completed'])
            ->where('cases.status', 'Open_Assigned')
            ->whereBetween(DB::raw("DATE(CONVERT_TZ(tasks.date_entered,'+00:00','+08:00'))"), [
                $dateStart,
                $dateEnd
            ])
            ->select('cases.id as caseId', 'tasks.status as taskStatus', 'tasks.modified_user_id as taskModifiedBy', 'cases.*', 'tasks.*')
            ->skip($nextSkip)->take($take)
            ->get();
    }

    public static function closeAssignedCase($caseId)
    {
        return DB::connection('mysql_crm_ssm')
            ->table('cases')
            ->join('tasks', 'tasks.parent_id', '=', 'cases.id')
            ->where('cases.id', $caseId)
            ->update([
                'cases.state' => 'Closed',
                'cases.status' => 'Closed_Closed_By_Ssm_No_Email',
                'cases.date_modified' => Carbon::now()->subHour(8)->format("Y-m-d H:i:s"),
                'tasks.status' => 'Completed',
                'tasks.date_modified' => Carbon::now()->subHour(8)->format("Y-m-d H:i:s"),
            ]);
    }

    //migration
    public static function getListProfiles($nextSkip, $take)
    {
        return DB::connection(self::$DB_MIGRATION_CONNECTION)
            ->table('ssm_customer_profile_2022') // ssm_customer_profile_2022
            ->skip($nextSkip)->take($take)
            ->get();
    }

    public static function getListCases($nextSkip, $take)
    {

        return DB::connection(self::$DB_MIGRATION_CONNECTION)
            ->table('ssm_case')
            ->whereDate('date_created', '>=', '2021-10-01')
            //                        ->whereIn('case_no',$arrayCase)
            ->skip($nextSkip)->take($take)
            ->get();
    }

    public static function getListCaseHistory($nextSkip, $take)
    {
        $arrayCase = array('TT1659391');
        return DB::connection(self::$DB_MIGRATION_CONNECTION)
            ->table('ssm_case_history')
            //                        ->whereDate('ssm_case_history.date_created','>=','2021-10-01')
            ->whereIn('case_no', $arrayCase)
            ->skip($nextSkip)->take($take)
            ->get();
    }

    public static function findCustomerProfile($customerId)
    {
        $results = DB::connection(self::$DB_MIGRATION_CONNECTION)->select(
            "SELECT * FROM ssm_customer_profile 
                WHERE customer_id = $customerId
                UNION
                SELECT * FROM ssm_customer_profile_2022
                WHERE customer_id = $customerId"
        );
        return $results;
    }

    public static function getLatestJobQue($name)
    {
        $query = DB::connection(self::$DB_CONNECTION)->table('job_queue');
        $query->where('name', $name);
        $query->orderBy('execute_time', 'desc');
        $data = $query->first();
        return $data;
    }

    public static function updateJobQueue($jobId)
    {
        return DB::connection(self::$DB_CONNECTION)->table('job_queue')
            ->where('id', $jobId)
            ->update([
                'status' => 'done',
                'resolution' => 'failed',
                'message' => 'Update via CRM Integration Scheduler'
            ]);
    }

    public static function getInvalidInboundCase($dateStart, $dateEnd, $take, $nextSkip)
    {
        return DB::connection('mysql_crm_ssm')
            ->table('cases')
            ->where('cases.status', 'Open_New')
            ->where('cases.name', '(no subject)')
            ->where('cases.description', '')
            ->where('cases.deleted', 0)
            ->whereBetween(DB::raw("DATE(CONVERT_TZ(cases.date_entered,'+00:00','+08:00'))"), [
                $dateStart,
                $dateEnd
            ])
            ->select('cases.*')
            ->skip($nextSkip)->take($take)
            ->get();
    }

    public static function getEmptyDescCase($dateStart, $dateEnd, $take, $nextSkip)
    {
        return DB::connection('mysql_crm_ssm')
            ->table('cases')
            ->where('cases.status', 'Open_New')
            ->where('cases.channel', 'Email')
            ->where('cases.description', '')
            ->where('cases.deleted', 0)
            ->whereBetween(DB::raw("DATE(CONVERT_TZ(cases.date_entered,'+00:00','+08:00'))"), [
                $dateStart,
                $dateEnd
            ])
            ->select('cases.*')
            ->skip($nextSkip)->take($take)
            ->get();
    }

    public static function getOpenStateWithCloseStatusCase($dateStart, $dateEnd, $take, $nextSkip)
    {
        return DB::connection('mysql_crm_ssm')
            ->table('cases')
            ->where('cases.state', 'Open')
            ->where('cases.status', 'Closed_Closed_No_Email')
            ->where('cases.category', '')
            ->whereNull('cases.category_2')
            ->whereNull('cases.category_3')
            ->whereBetween(DB::raw("DATE(CONVERT_TZ(cases.date_entered,'+00:00','+08:00'))"), [
                $dateStart,
                $dateEnd
            ])
            ->select('cases.*')
            ->skip($nextSkip)->take($take)
            ->get();
    }
}
