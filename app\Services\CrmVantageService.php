<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Services;

use DB;
use Carbon\Carbon;
use Exception;
use Ramsey\Uuid\Uuid;
use Log;

class CrmVantageService
{

    public static $DB_CONNECTION = 'mysql_crm_vantage'; 

    public static function getMigrateAccount($nextSkip, $take)
    {
        return DB::connection(self::$DB_CONNECTION)
            ->table('migrate_accounts')  
            ->skip($nextSkip)->take($take)
            ->get();
    }

    public static function getMigrateContact($nextSkip, $take)
    {
        return DB::connection(self::$DB_CONNECTION)
            ->table('migrate_contacts')  
            ->skip($nextSkip)->take($take)
            ->get();
    }

    public static function getMigrateUser($nextSkip, $take)
    {
        return DB::connection(self::$DB_CONNECTION)
            ->table('migrate_users')  
            ->skip($nextSkip)->take($take)
            ->get();
    }

    public static function getMigrateCase($nextSkip, $take)
    {
        return DB::connection(self::$DB_CONNECTION)
            ->table('migrate_cases')  
            ->skip($nextSkip)->take($take)
            ->get();
    }

    public static function getValueLookupCRM($type, $value)
    {
        $data = DB::connection(self::$DB_CONNECTION)
            ->table('cstm_list_app')
            ->where('type_code', $type)
            ->where('value_code', $value)
            ->where('deleted', 0)
            ->first();

        if ($data) {
            return $data->value_name;
        }
    }

    public static function getValueLookupByNameCRM($type, $value)
    {
        $data = DB::connection(self::$DB_CONNECTION)
            ->table('cstm_list_app')
            ->where('type_code', $type)
            ->where('value_name', $value)
            ->where('deleted', 0)
            ->first();

        if ($data) {
            return $data->value_code;
        }
    }

    public static function checkAccount($companyName, $registrationNo,$companyPhone)
    {
        $query = DB::connection(self::$DB_CONNECTION)->table('accounts');
        if ($registrationNo !== null) {
            $query->where('accounts.company_reg_no', $registrationNo);
        } else if ($companyPhone !== null) {
            $query->where('accounts.phone_office', $companyPhone);
        }
        $query->where('accounts.name', $companyName)
            ->where('accounts.deleted', 0);
        $data = $query->first();
        return $data;
    }
 
    public static function checkContact($email, $phone, $name)
    {  
        $query = DB::connection(self::$DB_CONNECTION)->table('contacts');
        if ($email !== null) {
            $query->join('email_addr_bean_rel', 'email_addr_bean_rel.bean_id', 'contacts.id')
                ->join('email_addresses', 'email_addresses.id', 'email_addr_bean_rel.email_address_id')
                ->where('email_addresses.email_address', $email)
                ->where('email_addr_bean_rel.deleted', 0)
                ->where('email_addresses.deleted', 0);
        }else if ($phone !== null) {
            $query->where('contacts.phone_mobile', $phone);
        } 
        $query->whereRaw("CONCAT(contacts.first_name, ' ', contacts.last_name) LIKE ?", ["%{$name}%"]) 
            ->where('contacts.deleted', 0);
        $data = $query->first();
        return $data;
    }
 

    public static function checkAccountContact($accountId, $contactId)
    {
        $query = DB::connection(self::$DB_CONNECTION)->table('accounts')
                    ->join('accounts_contacts', 'accounts_contacts.account_id','accounts.id')
                    ->join('contacts', 'accounts_contacts.account_id','contacts.id')
                    ->where('accounts.id', $accountId)->where('contacts.id', $contactId)
                    ->where('accounts.deleted', 0)->where('contacts.deleted', 0);
        $data = $query->first();
        return $data;
    }


    // insert function
    public static function insertAccount($isInsert, $accId, $accountDateCreated, $companyName, $companyPhone, $companyAddress, $companyCity, $companyState, $companyRegNo){
        try{
            $dataRecord = [
                'id' => $accId,
                'name' => $companyName,
                'date_entered' => $accountDateCreated,
                'date_modified' => $accountDateCreated,
                'modified_user_id' => 1,
                'created_by' => 1,
                'description' => null,
                'deleted' => 0,
                'assigned_user_id' => 1,
                'phone_office' => $companyPhone,
                'billing_address_street' => $companyAddress,
                'billing_address_city' => $companyCity,
                'billing_address_state' => $companyState,
                'company_reg_no' => $companyRegNo
            ];

            if ($isInsert == true) {
                $result = DB::connection(self::$DB_CONNECTION)->table('accounts')
                    ->insert($dataRecord);
            } else {
                $result = DB::connection(self::$DB_CONNECTION)->table('accounts')
                ->where('id',$accId)->update($dataRecord);
            }         
        }catch(Exception $ex){
            Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > '. $ex->getMessage());
            $result = 'Error';
        }
        return $result;
    }

    public static function insertAccountCstm($isInsert, $accId){
        try{
            $nowDb = Carbon::now()->subHour(8); 
            $dataRecord = [
                'id_c' => $accId
            ];

            if ($isInsert == true) {
                $result = DB::connection(self::$DB_CONNECTION)->table('accounts_cstm')
                           ->insert($dataRecord);
            } else {
                $result = DB::connection(self::$DB_CONNECTION)->table('accounts_cstm')
                        ->where('id_c',$accId)->update($dataRecord);
            }  
        }catch(Exception $ex){
            Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > '. $ex->getMessage());
            $result = 'Error';
        }
        return $result;
    }

    public static function insertContact($isInsert, $contactId, $contactDateCreated, $contactName, $contactPhone, $userType){
        try{
            if (($pos = strpos($contactName, ",")) !== FALSE) {
                $lastName = ltrim(strstr($contactName, ',', true)); 
                $firstName = ltrim(substr($contactName, strpos($contactName, ",") + 1));
            }else{
                $lastName = $contactName;
                $firstName = '';
            }

            $dataRecord = [
                'id' => $contactId,
                'date_entered' => $contactDateCreated,
                'date_modified' => $contactDateCreated,
                'modified_user_id' => 1,
                'created_by' => 1,
                'description' => null,
                'deleted' => 0,
                'assigned_user_id' => 1,
                'first_name' => $firstName,
                'last_name' => $lastName,
                'phone_mobile' => $contactPhone,
                'title' => $userType
            ];
            if ($isInsert == true) {
                $result = DB::connection(self::$DB_CONNECTION)->table('contacts')
                    ->insert($dataRecord);
            } else {
                $result = DB::connection(self::$DB_CONNECTION)->table('contacts')
                            ->where('id',$contactId)->update($dataRecord);
            } 

        }catch(Exception $ex){
            Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > '. $ex->getMessage());
            $result = 'Error';
        }
        return $result;
    }

    public static function insertContactCstm($isInsert, $contactId){
        try{
            
            $dataRecord = [
                'id_c' => $contactId
            ];
            
            if ($isInsert == true) {
                $result = DB::connection(self::$DB_CONNECTION)->table('contacts_cstm')
                    ->insert($dataRecord);
            } else {
                $result= DB::connection(self::$DB_CONNECTION)->table('contacts_cstm')
                            ->where('id_c',$contactId)->update($dataRecord);
            }         
        }catch(Exception $ex){
            Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > '. $ex->getMessage());
            $result = 'Error';
        }
        return $result;
    }

    public static function insertAccountContact($isInsert, $accountId, $contactId, $contactDateCreated){
        try{
            $id = Uuid::uuid4()->toString();
            $dataRecord = [
                            'id' => $id,
                            'contact_id' => $contactId,
                            'account_id' => $accountId,
                            'date_modified' => $contactDateCreated,
                            'deleted' => 0
                    ];   
            if ($isInsert == true) {
                $result = DB::connection(self::$DB_CONNECTION)->table('accounts_contacts')
                            ->insert($dataRecord);
            } else {
                $result= DB::connection(self::$DB_CONNECTION)->table('accounts_contacts')
                            ->where('id',$id)->update($dataRecord);
            }                   
        }catch(Exception $ex){
            Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > '. $ex->getMessage());
            $result = 'Error';
        }
        return $result;
    }

    public static function checkEmail($email, $module){
        $query = DB::connection(self::$DB_CONNECTION)->table('email_addresses') 
            ->join('email_addr_bean_rel','email_addr_bean_rel.email_address_id','email_addresses.id')
            ->where('email_addresses.email_address', $email)
            ->where('email_addr_bean_rel.bean_module', $module)
            ->where('email_addresses.deleted', 0)->where('email_addr_bean_rel.deleted', 0);

        $data = $query->first();
        return $data;
    }

    public static function insertEmail($emailId,$emailAddress,$beanId,$beanModule,$createdDate) {
        try{
            $date = Carbon::now()->subHour(8); 
            if($createdDate != ''){
                $date = $createdDate;
            }
            $emailTable = DB::connection(self::$DB_CONNECTION)->table('email_addresses')
                                ->insertGetId([
                                    'id' => $emailId,
                                    'email_address' => $emailAddress,
                                    'email_address_caps' => trim(strtoupper($emailAddress)),
                                    'date_created' => $date,
                                    'date_modified' => $date,
                                    'invalid_email' => null,
                                    'opt_out' => null,
                                    'deleted' => 0,
                            ]);
                $result = DB::connection(self::$DB_CONNECTION)->table('email_addr_bean_rel')
                            ->insertGetId([
                                'id' => Uuid::uuid4()->toString(),
                                'email_address_id' => $emailId,
                                'bean_id' => $beanId,
                                'bean_module' => $beanModule,
                                'primary_address' => null,
                                'reply_to_address' => null,
                                'date_created' => $date,
                                'date_modified' => $date,
                                'deleted' => 0,
                        ]);

        }catch(Exception $ex){
            Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > '. $ex->getMessage());
            $result = 'Error';
        }
    }

    public static function checkUser($email, $phone, $name)
    {

        $query = DB::connection(self::$DB_CONNECTION)->table('users');
        if ($email !== null) {
            $query->join('email_addr_bean_rel', 'email_addr_bean_rel.bean_id', 'users.id')
                ->join('email_addresses', 'email_addresses.id', 'email_addr_bean_rel.email_address_id')
                ->where('email_addresses.email_address', $email)
                ->where('email_addr_bean_rel.deleted', 0)
                ->where('email_addresses.deleted', 0);
        }else if ($phone !== null) {
            $query->where('users.phone_work', $phone);
        }
        $query->where('first_name', 'like', '%' . $name . '%')
            ->where('users.deleted', 0);
        $data = $query->first();
        return $data;
    }

   public static function insertUser($isInsert, $userId, $userDateCreated, $fullName, $phoneNo, $userType, $userName){
    
        try{
            $nowDb = Carbon::now()->subHour(8);  
            
            $dataRecord = [
                'id' => $userId,
                'user_name' => $userName,
                'user_hash' => '5f4dcc3b5aa765d61d8327deb882cf99', //password
                'is_group' => 0,
                'is_admin' => 0,
                'date_entered' => $nowDb,
                'date_modified' => $nowDb,
                'modified_user_id' => 1,
                'created_by' => 1,
                'description' => null,
                'deleted' => 0,
                'first_name' => $fullName,
                'last_name' => '.',
                'phone_work' => $phoneNo,
                'title' => $userType,
                'status' => 'Active',
                'employee_status' => 'Active'
            ];   
            if ($isInsert == true) {
                $result = DB::connection(self::$DB_CONNECTION)->table('users')
                    ->insert($dataRecord);
            } else {
                $result = DB::connection(self::$DB_CONNECTION)->table('users')
                            ->where('id',$userId)->update($dataRecord);
            }          
        }catch(Exception $ex){
            Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > '. $ex->getMessage());
            $result = 'Error';
        }
        return $result;
    }

    public static function getUserDetail($userName){
        try{
            $query = DB::connection(self::$DB_CONNECTION)->table('users')
                        ->where('user_name',$userName)
                        ->where('users.deleted', 0);
                $result = $query->first();
        }catch(Exception $ex){
            Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > '. $ex->getMessage());
            $result = 'Error';
        }
        return $result;
    }
    
    public static function checkCase($caseTitle,$accountId,$description) {
        $query = DB::connection(self::$DB_CONNECTION)->table('cases') 
        ->where('name', $caseTitle)
        ->where('deleted', 0)
        ->where('account_id', $accountId)
        ->where('description', $description);

        $data = $query->first();
        return $data;
    }

    public static function insertCase($isInsert, $caseId, $caseTitle, $newFormatCaseCreated, $newFormataseCompleted,
        $assignUserId,$createdBy,$contactId,$caseDescription,$caseStatus,$caseResolution,
        $accountId,$caseState,$caseCategoryValue,$caseSubCategoryValue){
        try{
             $dataRecord = [
                            'id' => $caseId,
                            'name' => $caseTitle,
                            'date_entered' => $newFormatCaseCreated,
                            'date_modified' => $newFormataseCompleted,
                            'modified_user_id' => $assignUserId,
                            'created_by' => $createdBy,
                            'contact_created_by_id' => $contactId,
                            'description' => $caseDescription,
                            'deleted' => 0,
                            'assigned_user_id' => $assignUserId,
                            'status' => $caseStatus,
                            'resolution' => $caseResolution,
                            'account_id' => $accountId,
                            'state' => $caseState,
                            'category' => $caseCategoryValue,
                            'category2' => $caseSubCategoryValue,
                            'channel' => 'Email', 
                            ]; 
            if ($isInsert == true) {
                $result = DB::connection(self::$DB_CONNECTION)->table('cases')
                    ->insert($dataRecord);
            } else {
                $result = DB::connection(self::$DB_CONNECTION)->table('cases')
                            ->where('id',$caseId)->update($dataRecord);
            }         
            }catch(Exception $ex){
                Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > '. $ex->getMessage());
            $result = 'Error';
            }

            return $result;
    }

    public static function insertCaseCstm($isInsert, $caseId){
        try{
            $nowDb = Carbon::now()->subHour(8); 
            $dataRecord = ['id_c' => $caseId, ];

            if ($isInsert == true) {
                $result = DB::connection(self::$DB_CONNECTION)->table('cases_cstm')
                    ->insert($dataRecord);
            } else {
                $result = DB::connection(self::$DB_CONNECTION)->table('cases_cstm')
                            ->where('id_c',$caseId)->update($dataRecord);
            }        
        }catch(Exception $ex){
            Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > '. $ex->getMessage());
            $result = 'Error';
        }

        return $result;
    }

    public static function checkTask($caseId){
        try{
            $query = DB::connection(self::$DB_CONNECTION)->table('tasks')
                        ->where('parent_id',$caseId)
                        ->where('deleted', 0);
                $result = $query->first();
        }catch(Exception $ex){
            Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > '. $ex->getMessage());
            $result = 'Error';
        }
        return $result;
    }

    public static function insertTask($isInsert, $caseId,$taskId,$newFormatTaskCreated,$newFormatTaskCompleted,$assignUserId,
    $caseDescription,$newTaskStatus,$caseResolution,$contactId){

        try{
            $nowDb = Carbon::now()->subHour(8); 
            $dataRecord = [
                            'id' => $taskId,
                            'name' => 'Assigned To Group Level 2',
                            'date_entered' => $newFormatTaskCreated,
                            'date_modified' => $newFormatTaskCompleted,
                            'modified_user_id' => $assignUserId,
                            'created_by' => $assignUserId,
                            'description' => $caseDescription,
                            'deleted' => 0,
                            'assigned_user_id' => $assignUserId,
                            'status' => $newTaskStatus,
                            'resolution' => $caseResolution,
                            'contact_id' => $contactId,
                            'parent_id' => $caseId,
                            'parent_type' => 'Cases'
                        ];  
            if ($isInsert == true) {
                $result = DB::connection(self::$DB_CONNECTION)->table('tasks')
                    ->insert($dataRecord);
            } else {
                $result = DB::connection(self::$DB_CONNECTION)->table('tasks')
                            ->where('id',$taskId)->update($dataRecord);
            }    
        }catch(Exception $ex){
            Log::info(__CLASS__ . ' > ' .__FUNCTION__ .' > '. $ex->getMessage());
            $result = 'Error';
        }

        return $result;
    }
} 