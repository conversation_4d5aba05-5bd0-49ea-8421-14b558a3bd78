<?php

namespace App\Services;


/**
 * Description of Profile Management PMServiceTrait
 *
 * <AUTHOR>
 */
class EPService {

    //AS REFERENCE ONLY
    public static $RECORD_STATUS = array(
            '' => '',
            0 => 'RECORD_STATUS_SUSPENDED',
            1 => 'RECORD_STATUS_ACTIVE',
            2 => 'RECORD_STATUS_CANCELLED',
            3 => 'RECORD_STATUS_EXPIRED',
            4 => 'RECORD_STATUS_REJECTED',
            5 => 'RECORD_STATUS_IN_PROGRESS',
            6 => 'RECORD_STATUS_PENDING_RE_APPROVAL',
            7 => 'RECORD_STATUS_7', /** Not sure, Not in document **/
            8 => 'RECORD_STATUS_PENDING_ACTIVATION',
            9 => 'RECORD_STATUS_DELETED'
            );
    
    public static $SOFTCERT_STATUS = array(
            '' => '',
            0 => 'Softcert is not required',
            4 => 'Softcert is required/Pending for softcert request',
            2 => 'Softcert requested',
            3 => 'Softcert processing',
            1 => 'Softcert issued',
            6 => 'Softcert rejected',
            9 => 'Softcert request failed (E.g. No response from spki)',
            5 => 'Softcert revoked',
            7 => 'Softcert ready for renewal',
        );
    
    public static $YES_NO= array(
            '' => '',
            1 => 'Yes',
            0 => 'No',
        );
    
    public static $OSB_STATUS= array(
            'S' => 'Success',
            'F' => 'Failed',
        );

    public static $JPN_STATE= array(
            '' => '',
            '01' => 'Johor',
            '02' => 'Kedah',
            '03' => 'Kelantan',
            '04' => 'Melaka',
            '05' => 'Negeri Sembilan',
            '06' => 'Pahang',
            '07' => 'Pulau Pinang',
            '08' => 'Perak',
            '09' => 'Perlis',
            '10' => 'Selangor',
            '11' => 'Terengganu',
            '12' => 'Sabah',
            '13' => 'Sarawak',
            '14' => 'Wilayah Persekutuan (Kuala Lumpur)',
            '15' => 'Wilayah Persekutuan (Labuan)',
            '16' => 'Wilayah Persekutuan (Putrajaya)',
            '21' => 'Johor',
            '22' => 'Johor',
            '23' => 'Johor',
            '24' => 'Johor',
            '25' => 'Kedah',
            '26' => 'Kedah',
            '27' => 'Kedah',
            '28' => 'Kelantan',
            '29' => 'Kelantan',
            '30' => 'Melaka',
            '31' => 'Negeri Sembilan',
            '32' => 'Pahang',
            '33' => 'Pahang',
            '34' => 'Pulau Pinang',
            '35' => 'Pulau Pinang',
            '36' => 'Perak',
            '37' => 'Perak',
            '38' => 'Perak',
            '39' => 'Perak',
            '40' => 'Perlis',
            '41' => 'Selangor',
            '42' => 'Selangor',
            '43' => 'Selangor',
            '44' => 'Selangor',
            '45' => 'Terengganu',
            '46' => 'Terengganu',
            '47' => 'Sabah',
            '48' => 'Sabah',
            '49' => 'Sabah',
            '50' => 'Sarawak',
            '51' => 'Sarawak',
            '52' => 'Sarawak',
            '53' => 'Sarawak',
            '54' => 'Wilayah Persekutuan (Kuala Lumpur)',
            '55' => 'Wilayah Persekutuan (Kuala Lumpur)',
            '56' => 'Wilayah Persekutuan (Kuala Lumpur)',
            '57' => 'Wilayah Persekutuan (Kuala Lumpur)',
            '58' => 'Wilayah Persekutuan (Labuan)',
            '59' => 'Negeri Sembilan',
            '82' => 'Negeri Tidak Diketahui'
    );

    public static $JPN_GENDER= array(
            '' => '',
            'L' => 'LELAKI',
            'P' => 'PEREMPUAN',
    );

    public static $CATEGORY_IS_APPROVED= array(
            '' => '',
            0 => 'Rejected',
            1 => 'Approved',
    );
    
    public static $TASK_STATUS= array(
            0 => 'Pending Action',
            1 => 'Completed',
            2 => 'Pending Information',
            3 => 'In Progress',
        );
    
    public static $BUSINESS_TYPE = array(
            'A' => 'Individual',
            'B' => 'Limited Liability Partnership (LLP)',
            'C' => 'ROB - Partnership',
            'D' => 'Business Registration in Sabah (partnership)',
            'E' => 'Business Registration in Sarawak (partnership)',
            'F' => 'ROB - Sole-Proprietorship',
            'G' => 'Business Registration in Sabah (Sole-Proprietorship)',
            'H' => 'Business Registration in Sarawak (Sole-Proprietorship)',
            'I' => 'ROC - Sdn Bhd',
            'J' => 'ROC - Bhd',
            'K' => 'ROS - Organization (Registered with Jabatan Pendaftaran Pertubuhan Malaysia)',
            'L' => 'Cooperative (Registered with Suruhanjaya Koperasi Malaysia)',
            'M' => 'Society',
            'O' => 'Organization (Others)',
            'P' => 'Cooperative (Others)',
            'R' => 'Professional Body',
            'Q' => 'G2G Basic (e.g. Federal Statutory Body, State Govt. Local Authority)',
            'T' => 'PTJ Govt. Seller',
    );
    
    public static $BILL_TYPE= array(
            'S' => 'Softcert Fee',
            'R' => 'Registration Fee',
            'P' => 'Processing Fee',
        );

    public static $IS_CHECKED = array(
            0 => '<i class="fa fa-times-circle fa-2x text-danger"></i>',
            1 => '<i class="fa fa-check-circle fa-2x text-success"></i>',
    );

    public static $QT_QUALIFY = array(
            '' => '-',
            'B' => '<span class="badge label-danger"><strong>B</strong></span>',
            'C' => '<span class="badge label-danger"><strong>C</strong></span>',
    );

    public static $QT_BSV_REGISTRATION_STATUS = array(
            1 => 'Active',
            4 => 'Rejected',
            5 => 'In Progress',
    );
    
    public static $TASK_MISSING_STATUS = array(
            '00' => 'PENDING ACTION',
            '11' => 'NEED MORE INFO',
            '22' => 'IN PROGRESS',
            '33' => 'RUNNING, MAY NEED ATTENTION',
            '44' => 'NEED TO CHECK',
            '55' => 'DONE',
            '66' => 'DATA ISSUE',
    );
    public static $TASK_MISSING_CASE_STATUS = array(
            '0' => 'Case Still Open',
            '1' => 'Case Closed',
    );
    public static $TASK_MISSING_MODULE = array(
            'Direct Purchase' => 'Direct Purchase',
            'Fulfillment' => 'Fulfillment',
            'Supplier Management' => 'Supplier Management',
            'Quotation' => 'Quotation',
            'Direct Purchase / Fulfillment' => 'Direct Purchase / Fulfillment',
            'Contract' => 'Contract',
            'Unknown' => 'Unknown',
            'Catalogue' => 'Catalogue',
            'Quotation/Tender' => 'Quotation/Tender',
            'Contract Management' => 'Contract Management',
            'Profile Management' => 'Profile Management'
    );

    public static $CASE_STATUS = array(
    //        'Open_Resolved' => 'Resolved',
    //        'Open_New' => 'New',
    //        'Open_Assigned' => 'Assigned',
    //        'Closed_Closed' => 'Closed',
            'Pending_User_Verification Input' => 'Pending_User_Verification',
    //        'Closed_Rejected' => 'Rejected',
            'In_Progress' => 'In_Progress',
            'Open_Resolved' => 'Open_Resolved',
            //'Open_New' => 'Open_New',
            'Open_Assigned' => 'Open_Assigned',
            'Closed_Closed' => 'Closed_Closed',
            //'Open_Pending Input' => 'Open_Pending Input',
            'Closed_Rejected' => 'Closed_Rejected',
            //'Closed_Duplicate' => 'Closed_Duplicate',
    );
    
    public static $UNSPSC_LEVEL = array(
            '0' => 'Segment',
            '1' => 'Family',
            '2' => 'Class',
            '3' => 'Commodity',
    );
    
    
    public static $USER_PROCESS_PATCHING = array(
            'personnel_softcert_4' =>  
                array(
                    'name' => 'Change Softcert Status To (4)',
                    'description' => 'Program will checking if SoftCert Status (0) or (1) or (3) or (7) will change to (4).',
                ),
            'personnel_softcert_1' => 
                array(
                    'name' => 'Change Softcert Status To (1)',
                    'description' => 'Program will checking if SoftCert Status (0) or(4) or (3) or (7) will change to (1).',
                ),
            'personnel_1' => 
                array(
                    'name' => 'Change Personnel Record Status To (1)',
                    'description' => 'Program will checking if Rekod Status (0) or (9) will change to (1).',
                ),
            'personnel_9' => 
                array(
                    'name' => 'Change Personnel Record Status To (9)',
                    'description' => 'Program will checking if Rekod Status (0) or (1) will change to (9).',
                ),
//            'softcert_request_1' => 
//                array(
//                    'name' => 'Change Softcert Request Record Status To (1)',
//                    'description' => 'Program will checking if Rekod Status (9) will change to (1).',
//                ),
//            'softcert_request_9' => 
//                array(
//                    'name' => 'Change Softcert Request Record Status To (9)',
//                    'description' => 'Program will checking if Rekod Status (1) will change to (9).',
//                ),
//            'personnel_1_role_null' => 
//                array(
//                    'name' => 'Change Personnel Record Status To (1) AND Role to NULL',
//                    'description' => 'Program will checking if record status (8) and Role in personnel is not null',
//                ),
    );

    
    


}
