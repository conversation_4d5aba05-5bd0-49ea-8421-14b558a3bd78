<?php

namespace App\Services\Traits;

use DB;
use Auth;
use Carbon\Carbon;

/**
 * Description of Profile Management PMServiceTrait
 *
 * <AUTHOR>
 */
trait BPMService {

    /**
     * return String docNo
     * @param type $docNo
     * @return String docNo
     */
    protected function getTaskBpmByDocNo($docNo){
        
        $query = DB::connection('oracle_bpm_rpt')->table(DB::raw('WFTASK'));
        
//        if(Auth::user()->user_name == 'mohdshamsul' 
//                || Auth::user()->user_name || 'shahril' 
//                || Auth::user()->user_name || 'moriana'){
//           $query = DB::connection('oracle_bpm_rpt')->table(DB::raw('WFTASK@PRDSOA')); 
//        }

        $query->where('CUSTOMATTRIBUTESTRING1', $docNo);
        $query->select('CREATEDDATE', 'CUSTOMATTRIBUTESTRING1','ACTIVITYNAME','ACQUIREDBY','ASSIGNEES','CREATOR','INSTANCEID','COMPOSITEINSTANCEID','STATE','VERSIONREASON');
        $data = $query->get();
        return $data;
    }
    

}
