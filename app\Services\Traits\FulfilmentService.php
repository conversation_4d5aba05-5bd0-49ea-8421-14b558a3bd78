<?php

namespace App\Services\Traits;

use Log;
use DB;
use Carbon\Carbon;

/**
 * Description of Profile Management PMServiceTrait
 *
 * <AUTHOR>
 */
trait FulfilmentService {
    
    protected function getSupplierInfoByDocNo($docNo){
        $type   = substr($docNo, 0, 2);
        
        $query = DB::connection('oracle_nextgen_rpt')->table('FL_FULFILMENT_REQUEST as A');
        $query->leftJoin('FL_FULFILMENT_ORDER as B', 'A.FULFILMENT_REQ_ID', '=', 'B.FULFILMENT_REQ_ID');
        $query->join('SM_SUPPLIER as C', 'C.SUPPLIER_ID', '=', 'A.SUPPLIER_ID');
        $query->leftJoin('SM_MOF_ACCOUNT as D', 'D.SUPPLIER_ID', '=', 'C.SUPPLIER_ID');
        if($type == 'PR' || $type == 'CR'){
            $query->where('A.DOC_NO', $docNo);
        }
        if($type == 'PO' || $type == 'CO'){
            $query->where('B.DOC_NO', $docNo);
        }
        $query->select('A.DOC_NO AS FR_DOC_NO' ,'A.DOC_TYPE AS FR_DOC_TYPE', 'B.DOC_NO AS FO_DOC_NO' , 'B.DOC_TYPE AS FO_DOC_TYPE');
        $query->addSelect('C.*');
        $query->addSelect('D.MOF_NO');
        return  $query->first();
    }
    
    /**
     * Get Document PR and PO or Document CR and CO.
     * 
     * return String docNo
     * @param type $docNo
     * @return String docNo
     */
    protected function getDocNoPRPOorCRCO($docNo){
        
        $type   = substr($docNo, 0, 2);
        
        $query = DB::connection('oracle_nextgen_rpt')->table('FL_FULFILMENT_REQUEST as a');
        $query->leftJoin('FL_FULFILMENT_ORDER as b', 'a.FULFILMENT_REQ_ID', '=', 'b.FULFILMENT_REQ_ID');
        if($type == 'PR' || $type == 'CR'){
            $query->where('a.DOC_NO', $docNo);
        }
        if($type == 'PO' || $type == 'CO'){
            $query->where('b.DOC_NO', $docNo);
        }
        $query->select('A.DOC_NO AS FR_DOC_NO' ,'A.DOC_TYPE AS FR_DOC_TYPE', 'B.DOC_NO AS FO_DOC_NO' , 'B.DOC_TYPE AS FO_DOC_TYPE');
        return  $query->first();

    }

    /**
     * return String docNo
     * @param type $docNo
     * @return String docNo
     */
    protected function getDocNoByPrCr($docNo){
        $data = $this->getDocNoPRPOorCRCO($docNo);
        if($data != null){
            return $data->fo_doc_no;
        }
        return null;
    }
    protected function getListPurchaseInquiryByPiNoOther($piNo) {
        $query = DB::connection('oracle_nextgen_rpt')->table('SC_PURCHASE_INQUIRY as a');
        $query->join('SC_WORKFLOW_STATUS as b', 'a.PURCHASE_INQUIRY_ID', '=', 'b.DOC_ID');
        $query->join('PM_STATUS as c', 'b.STATUS_ID', '=', 'c.STATUS_ID');
        $query->join('PM_STATUS_DESC as d', 'c.STATUS_ID', '=', 'd.STATUS_ID');
        $query->where('d.LANGUAGE_CODE', 'en');
        $query->where('b.DOC_TYPE', 'PI');
        $query->where('a.PURCHASE_INQUIRY_NO', $piNo);
        $query->select(DB::raw("TO_CHAR (b.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') as fws_date_created"));
        $query->addSelect('a.PURCHASE_INQUIRY_ID,','a.PURCHASE_INQUIRY_NO','a.DATE_TO_RESPONSE');
        $query->addSelect('b.DOC_ID,','d.STATUS_NAME','c.STATUS_ID');
        $query->addSelect('b.IS_CURRENT,','b.CREATED_BY','b.CHANGED_BY');
        $query->orderBy('b.CREATED_DATE','desc');
        return $query->get();
    }
    
    protected function getListPidByPdNo($docNo) {
         $results = DB::connection('oracle_nextgen_rpt')->select(
                " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED,
                    B.DOC_ID,B.DOC_TYPE , A.PID_NO AS DOC_NO,
                    D.STATUS_NAME ,C.STATUS_ID ,B.IS_CURRENT,B.CREATED_BY,B.CHANGED_BY 
                    FROM SC_PID  A,SC_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D
                    WHERE A.PID_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND D.LANGUAGE_CODE = ? 
                    AND B.DOC_TYPE = ?  
                    AND A.PID_NO  =  ?
                    ORDER BY B.CREATED_DATE DESC", array('en','PD',$docNo));
        return $results;
    }
    
    protected function getListPurchaseInquiryByPiNo($docNo) {
         $results = DB::connection('oracle_nextgen_rpt')->select(
                " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED,
                    B.DOC_ID,B.DOC_TYPE ,
                    A.PURCHASE_INQUIRY_NO AS DOC_NO,A.DATE_TO_RESPONSE, 
                    A.TITLE,
                    D.STATUS_NAME ,C.STATUS_ID ,B.IS_CURRENT,B.CREATED_BY,B.CHANGED_BY 
                    FROM SC_PURCHASE_INQUIRY  A,SC_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D
                    WHERE A.PURCHASE_INQUIRY_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND D.LANGUAGE_CODE = ? 
                    AND B.DOC_TYPE = ? 
                    AND A.PURCHASE_INQUIRY_NO = ?
                    ORDER BY B.CREATED_DATE DESC", array('en','PI',$docNo));
        return $results;
    }
    
    protected function getListSimpleQuoteBySqNo($docNo) {
         $results = DB::connection('oracle_nextgen_rpt')->select(
                " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED,
                    B.DOC_ID,B.DOC_TYPE,
                    A.QUOTE_NO as DOC_NO ,A.IS_PANEL,  A.END_DATE,
                    D.STATUS_NAME ,C.STATUS_ID ,B.IS_CURRENT,B.CREATED_BY,B.CHANGED_BY
                    FROM SC_QUOTE  A,SC_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D
                    WHERE A.QUOTE_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND D.LANGUAGE_CODE =?
                    AND B.DOC_TYPE = ?
                    AND A.QUOTE_NO = ?
                    AND TO_CHAR (A.CREATED_DATE, 'YYYY') = ?
                    ORDER BY B.CREATED_DATE DESC", array('en','SQ',$docNo,Carbon::now()->year));
        return $results;
    }
    
    protected function getListRequestNoteByRnNo($docNo) {
         $results = DB::connection('oracle_nextgen_rpt')->select(
                " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED,
                    B.DOC_ID,B.DOC_TYPE,
                    A.REQUEST_NOTE_NO AS DOC_NO,A.SOURCE_METHOD,A.APPROVER_ID,A.USER_GROUP_ID, A.CHANGED_BY RN_CHANGE,A.CREATED_BY RN_CREATED ,
                    D.STATUS_NAME ,C.STATUS_ID ,B.IS_CURRENT,B.CREATED_BY,B.CHANGED_BY 
                    FROM SC_REQUEST_NOTE A,SC_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D
                    WHERE A.REQUEST_NOTE_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND D.LANGUAGE_CODE = ?
                    AND B.DOC_TYPE = ?
                    AND A.REQUEST_NOTE_NO = ?
                    AND TO_CHAR (A.CREATED_DATE, 'YYYY') = ?
                    ORDER BY B.CREATED_DATE DESC", array('en','RN',$docNo,Carbon::now()->year));
        return $results;
    }
    
    protected  function getListFulfilmenRequestByPrCr($docNo) {
         $results = DB::connection('oracle_nextgen_rpt')->select(
                " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED,
                    TO_CHAR (B.CHANGED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CHANGED,
                    B.DOC_ID,B.DOC_TYPE,
                    A.DOC_NO,(SELECT DOC_NO FROM FL_FULFILMENT_ORDER O WHERE O.FULFILMENT_REQ_ID  = A.FULFILMENT_REQ_ID) AS PO_CO,
                    D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT , A.CREATED_BY, A.CHANGED_BY,A.APPROVER_ID, A.CONTRACT_ID , A.AG_APPROVED_DATE , A.CREATED_ORG_PROFILE_ID
                    FROM FL_FULFILMENT_REQUEST A, FL_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D
                    WHERE A.FULFILMENT_REQ_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND D.LANGUAGE_CODE = ?
                    AND B.DOC_TYPE IN ('CR','PR')
                    AND A.DOC_NO = ?
                    AND TO_CHAR (A.CREATED_DATE, 'YYYY') = ?
                    ORDER BY B.CREATED_DATE DESC", array('en',$docNo,Carbon::now()->year));
        return $results;
    }
    
    protected  function getListFulfilmenOrderByPoCoFc($docNo) {
         $results = DB::connection('oracle_nextgen_rpt')->select(
                " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED,
                    B.DOC_ID,B.DOC_TYPE,A.DOC_NO,
                     (SELECT DOC_NO FROM FL_FULFILMENT_REQUEST O WHERE O.FULFILMENT_REQ_ID  = A.FULFILMENT_REQ_ID) AS PO_NO,A.FULFILMENT_REQ_ID ,
                    D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT, B.CREATED_BY,B.CHANGED_BY 
                    FROM FL_FULFILMENT_ORDER A, FL_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D
                    WHERE A.FULFILMENT_ORDER_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND D.LANGUAGE_CODE = ?
                    AND B.DOC_TYPE IN ('PO','CO','FC')   
                    AND A.DOC_NO  = ?
                    ORDER BY B.CREATED_DATE DESC", array('en',$docNo));
        return $results;
    }
    
    protected  function getListDeliveryOrderByDoNo($docNo) {
         $results = DB::connection('oracle_nextgen_rpt')->select(
                " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED,
                    B.DOC_ID,B.DOC_TYPE,
                    A.DELIVERY_ORDER_NO AS DOC_NO,A.SUPPLIER_DO_REF, A.FULFILMENT_REQ_ID, 
                    D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT, A.RECORD_STATUS, B.CREATED_BY,B.CHANGED_BY  
                    FROM FL_DELIVERY_ORDER A, FL_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D
                    WHERE A.DELIVERY_ORDER_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND D.LANGUAGE_CODE = ? 
                    AND B.DOC_TYPE = ?    
                    AND A.DELIVERY_ORDER_NO  = ? 
                    AND TO_CHAR (A.CREATED_DATE, 'YYYY') = ? 
                    ORDER BY   B.CREATED_DATE DESC", array('en','DO', $docNo,Carbon::now()->year));
        return $results;
    }
    
    protected  function getListFulfilmentNoteByFnNo($docNo) {
         $results = DB::connection('oracle_nextgen_rpt')->select(
                " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, 
                    B.DOC_ID,B.DOC_TYPE,
                    A.FULFILMENT_NOTE_NO AS DOC_NO, A.FULFILMENT_REQ_ID, 
                    D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT,A.RECORD_STATUS, B.CREATED_BY,B.CHANGED_BY  
                    FROM FL_FULFILMENT_NOTE A, FL_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D
                    WHERE A.FULFILMENT_NOTE_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND D.LANGUAGE_CODE = ? 
                    AND B.DOC_TYPE = ? 
                    AND A.FULFILMENT_NOTE_NO = ? 
                    ORDER BY B.CREATED_DATE DESC", array('en','FN',$docNo));
        return $results;
    }
    
    protected  function getListStopInstructionBySdNo($docNo) {
         $results = DB::connection('oracle_nextgen_rpt')->select(
                " SELECT TO_CHAR(B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, 
                    B.DOC_ID,B.DOC_TYPE,
                    A.STOP_INSTR_NO as doc_no,A.FULFILMENT_REQ_ID,
                    D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT, A.RECORD_STATUS, B.CREATED_BY,B.CHANGED_BY  
                    FROM FL_STOP_INSTR A, FL_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D
                    WHERE A.STOP_INSTR_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND D.LANGUAGE_CODE = ?
                    AND B.DOC_TYPE = ? 
                    AND A.STOP_INSTR_NO = ?  
                    ORDER BY B.CREATED_DATE DESC", array('en','SD',$docNo));
        return $results;
    }
    
    protected  function getListInvoiceByInvNo($docNo) {
         $results = DB::connection('oracle_nextgen_rpt')->select(
                " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, 
                    B.DOC_ID,B.DOC_TYPE,
                    A.INVOICE_NO AS DOC_NO,A.SUPPLIER_INVOICE_REF, A.FULFILMENT_REQ_ID, 
                    D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT, A.RECORD_STATUS,  B.CREATED_BY,B.CHANGED_BY  , A.*
                    FROM FL_INVOICE A, FL_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D
                    WHERE A.INVOICE_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND D.LANGUAGE_CODE = ? 
                    AND B.DOC_TYPE = ? 
                    AND A.INVOICE_NO = ? 
                    AND TO_CHAR (A.CREATED_DATE, 'YYYY') = ? 
                    ORDER BY B.CREATED_DATE DESC", array('en','IN',$docNo,Carbon::now()->year));
        return $results;
    }
    
    protected  function getListAdjustmentByDocNo($docNo) {
        // C0251062601070003 B0381201011100015
         $results = DB::connection('oracle_nextgen_rpt')->select(
                " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, 
                    B.DOC_ID,B.DOC_TYPE,
                    A.DOC_NO,FULFILMENT_ORDER_ID, A.FULFILMENT_REQ_ID, 
                    D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT , A.RECORD_STATUS,  B.CREATED_BY,B.CHANGED_BY 
                    FROM FL_ADJUSTMENT A, FL_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D
                    WHERE A.ADJUSTMENT_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND D.LANGUAGE_CODE = ? 
                    AND B.DOC_TYPE IN ('CN','DN')
                    AND A.DOC_NO  = ? 
                    ORDER BY B.CREATED_DATE DESC", array('en',$docNo));
        return $results;
    }

    protected  function getListPaymentAdviseByPaNo($docNo) {
         $results = DB::connection('oracle_nextgen_rpt')->select(
                " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, 
                    B.DOC_ID,B.DOC_TYPE,
                    A.PAYMENT_ADVICE_NO AS DOC_NO,
                    A.PAYMENT_REF_NO, A.FULFILMENT_REQ_ID,INVOICE_ID, A.FULFILMENT_ORDER_ID,
                    D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT , A.RECORD_STATUS,  B.CREATED_BY,B.CHANGED_BY 
                    FROM FL_PAYMENT_ADVICE A, FL_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D
                    WHERE A.PAYMENT_ADVICE_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND D.LANGUAGE_CODE = ? 
                    AND B.DOC_TYPE = ? 
                    AND A.PAYMENT_ADVICE_NO = ? 
                    AND TO_CHAR (A.CREATED_DATE, 'YYYY') = ? 
                    ORDER BY B.CREATED_DATE DESC", array('en','PA',$docNo,Carbon::now()->year));
        return $results;
    }
    
    /**
     * Checking on SQ, RN, PR, PO
     * Return  List
     * Can be one RN have many PR
     * @param type $docNo
     * @return type
     */
    protected function getListDocNoFulfillmentSQPOTracking($docNo){
        
        $type   = substr($docNo, 0, 2);
        
        
        $query  = "  SELECT DISTINCT q.QUOTE_ID, q.QUOTE_NO, 
                    rn.REQUEST_NOTE_ID, rn.REQUEST_NOTE_NO, rn.SOURCE_METHOD, 
                    rnd.PURCHASE_REQUEST_ID, rnd.FULFILMENT_TYPE_ID,
                    fr.FULFILMENT_REQ_ID, fr.DOC_TYPE as FR_DOC_TYPE, fr.DOC_NO as FR_DOC_NO,
                    fo.DOC_NO as FO_DOC_NO,fo.DOC_TYPE as FO_DOC_TYPE,fo.FULFILMENT_ORDER_ID 
                  FROM SC_QUOTE q, SC_REQUEST_NOTE rn, SC_REQUEST_NOTE_DTL rnd, FL_FULFILMENT_REQUEST fr, FL_FULFILMENT_ORDER fo  
                  WHERE 
                    q.QUOTE_ID = rn.QUOTE_ID (+)  
                    AND rn.REQUEST_NOTE_ID = rnd.REQUEST_NOTE_ID(+) 
                    AND rnd.PURCHASE_REQUEST_ID = fr.PURCHASE_REQUEST_ID(+)
                    AND fr.FULFILMENT_REQ_ID = fo.FULFILMENT_REQ_ID(+) 
                ";
        if($type == 'SQ'){
            $query = $query. "
                    AND q.QUOTE_NO = ?
                    ";
        }
        else if($type == 'RN'){
            $query = $query. "
                    AND rn.REQUEST_NOTE_NO = ?
                    ";
        }
        else if($type == 'PR'){
            $query = $query. "
                    AND fr.DOC_NO = ?
                    ";
        }
        else if($type == 'PO'){
            $query = $query. "
                    AND fo.DOC_NO = ?
                    ";
        }else{
            return array();
        }
        
        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($docNo));
        return $results;
                
    }
    
    /**
     * Checking on SQ, RN, LOA, CT, CR, CO
     * Return  List
     * Can be one RN have many PR
     * @param type $docNo
     * @return type
     */
    protected function getListDocNoFulfillmentSQCOTracking($docNo){
        
        $type   = substr($docNo, 0, 2);
        
        
        $query  = "  SELECT DISTINCT Q.QUOTE_ID, Q.QUOTE_NO, 
                        RN.REQUEST_NOTE_ID, RN.REQUEST_NOTE_NO, RN.SOURCE_METHOD, 
                        RND.FULFILMENT_TYPE_ID,RND.PURCHASE_REQUEST_ID, 
                        LOI.DOC_TYPE AS LOA_DOC_TYPE, LOA.LOA_NO AS LOA_DOC_NO, 
                        CT.CONTRACT_NO AS CT_DOC_NO,
                        FR.DOC_NO AS FR_DOC_NO, FR.DOC_TYPE AS FR_DOC_TYPE , 
                        FO.DOC_NO AS FO_DOC_NO, FO.DOC_TYPE AS FO_DOC_TYPE 
                             FROM SC_QUOTE Q 
                                  LEFT JOIN SC_REQUEST_NOTE RN ON Q.QUOTE_ID = RN.QUOTE_ID
                                  LEFT JOIN SC_REQUEST_NOTE_DTL RND ON RN.REQUEST_NOTE_ID = RND.REQUEST_NOTE_ID
                                  LEFT JOIN SC_LOI_LOA LOI ON RND.PURCHASE_REQUEST_ID = LOI.DOC_ID
                                  LEFT JOIN SC_LOA LOA ON LOI.LOI_LOA_ID = LOA.LOI_LOA_ID
                                  LEFT JOIN CT_CONTRACT CT ON LOA.LOA_ID = CT.LOA_ID
                                  LEFT JOIN FL_FULFILMENT_REQUEST FR ON CT.CONTRACT_ID = FR.CONTRACT_ID
                                  LEFT JOIN FL_FULFILMENT_ORDER FO ON FR.FULFILMENT_REQ_ID = FO.FULFILMENT_REQ_ID
                            WHERE 
                ";
        if($type == 'SQ'){
            $query = $query. "
                     Q.QUOTE_NO = ?
                    ";
        }
        else if($type == 'RN'){
            $query = $query. "
                     RN.REQUEST_NOTE_NO = ?
                    ";
        }
        else if($type == 'LA'){
            $query = $query. "
                     LOA.LOA_NO = ?
                    ";
        }
        else if($type == 'CT'){
            $query = $query. "
                     CT.CONTRACT_NO = ?
                    ";
        }
        else if($type == 'CR'){
            $query = $query. "
                     FR.DOC_NO = ?
                    ";
        }
        else if($type == 'CO'){
            $query = $query. "
                     FO.DOC_NO = ?
                    ";
        }else{
            return array();
        }
        
        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($docNo));
        return $results;
                
    }
    
    /**
     * Checking on QT, LOA, CT, CR, CO
     * Return  List
     * 
     * @param type $docNo
     * @return type
     */
    protected function getListDocNoFulfillmentQTCOTracking($docNo){
        
        $type   = substr($docNo, 0, 1);
        $type2   = substr($docNo, 0, 2);
        
        
        $query  = " SELECT DISTINCT 
                        QT.QT_NO AS QT_DOC_NO ,  
                        LOI.DOC_TYPE AS LOA_DOC_TYPE, LOA.LOA_NO AS LOA_DOC_NO, 
                        CT.CONTRACT_NO AS CT_DOC_NO,
                        FR.DOC_NO AS FR_DOC_NO, FR.DOC_TYPE AS FR_DOC_TYPE , 
                        FO.DOC_NO AS FO_DOC_NO, FO.DOC_TYPE AS FO_DOC_TYPE 
                    FROM SC_QT QT  
                    LEFT JOIN  SC_LOI_LOA LOI ON LOI.DOC_ID  = QT.QT_ID 
                    LEFT JOIN SC_LOA LOA ON  LOA.LOI_LOA_ID = LOI.LOI_LOA_ID 
                    LEFT JOIN CT_CONTRACT CT ON CT.LOA_ID = LOA.LOA_ID  
                    LEFT JOIN FL_FULFILMENT_REQUEST FR ON  FR.CONTRACT_ID = CT.CONTRACT_ID 
                    LEFT JOIN FL_FULFILMENT_ORDER FO ON FO.FULFILMENT_REQ_ID = FR.FULFILMENT_REQ_ID  
                    WHERE 
                ";
        if($type == 'N' || $type == 'L' || $type == 'W' || $type == 'A'){
            $query = $query. "
                     LOA.LOA_NO = ?
                    ";
        }
        else if($type == 'C' || $type == 'Z' || $type == 'M'){
            $query = $query. "
                     CT.CONTRACT_NO = ?
                    ";
        }
        else if($type2 == 'DL' || $type2 == 'QT' || $type2 == 'QM'){
            $query = $query. "
                     QT.QT_NO = ?
                    ";
        }
        else if($type2 == 'CR'){
            $query = $query. "
                     FR.DOC_NO = ?
                    ";
        }
        else if($type2 == 'CO'){
            $query = $query. "
                     FO.DOC_NO = ?
                    ";
        }else{
            return array();
        }
        
        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($docNo));
        return $results;
                
    }
    
    /**
     * Checking on PR,CR,PO,CO
     * Return  List
     * @param type $docNo
     * @return type
     */
    protected function getListDocNoFulfillmentPRCRPOCOTracking($docNo){
        
        $type   = substr($docNo, 0, 2);
        
        
        $query  = "   SELECT DISTINCT 
                            FR.DOC_NO AS FR_DOC_NO, FR.DOC_TYPE AS FR_DOC_TYPE , 
                            FO.DOC_NO AS FO_DOC_NO, FO.DOC_TYPE AS FO_DOC_TYPE 
                           FROM FL_FULFILMENT_REQUEST FR 
                      LEFT JOIN FL_FULFILMENT_ORDER FO ON FR.FULFILMENT_REQ_ID = FO.FULFILMENT_REQ_ID 
                      WHERE 
                ";
       
        if($type == 'CR' || $type == 'PR'){
            $query = $query. "
                     FR.DOC_NO = ?
                    ";
        }
        else if($type == 'PO' || $type == 'CO'){
            $query = $query. "
                     FO.DOC_NO = ?
                    ";
        }else{
            return array();
        }
        
        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($docNo));
        return $results;
                
    }
    
    /**
     * Checking on LOA,CT,CR,CO
     * Return  List
     * @param type $docNo
     * @return type
     */
    protected function getListDocNoFulfillmentLOACTCRCOTracking($docNo){
        
        $type   = substr($docNo, 0, 1);
        $type2   = substr($docNo, 0, 2);
        $query  = "    SELECT DISTINCT 
                        LOA.LOA_NO AS LOA_DOC_NO, 
                        CT.CONTRACT_NO AS CT_DOC_NO,
                        FR.DOC_NO AS FR_DOC_NO, FR.DOC_TYPE AS FR_DOC_TYPE , FR.CREATED_DATE , 
                        FO.DOC_NO AS FO_DOC_NO, FO.DOC_TYPE AS FO_DOC_TYPE 
                             FROM SC_LOA LOA 
                                  LEFT JOIN CT_CONTRACT CT ON LOA.LOA_ID = CT.LOA_ID 
                                  LEFT JOIN FL_FULFILMENT_REQUEST FR ON CT.CONTRACT_ID = FR.CONTRACT_ID 
                                  LEFT JOIN FL_FULFILMENT_ORDER FO ON FR.FULFILMENT_REQ_ID = FO.FULFILMENT_REQ_ID  
                        WHERE 
                ";
       
        if($type == 'N' || $type == 'L' || $type == 'W' || $type == 'A'){
            $query = $query. "
                     LOA.LOA_NO = ?
                    ";
        }
        else if($type == 'C' || $type == 'Z' || $type == 'M'){
            $query = $query. "
                     CT.CONTRACT_NO = ?
                    ";
        }
        else if($type2 == 'CR'){
            $query = $query. "
                     FR.DOC_NO = ?
                    ";
        }
        else if($type2 == 'CO'){
            $query = $query. "
                     FO.DOC_NO = ?
                    ";
        }else{
            return array();
        }
        
        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($docNo));
        dd($results);
        return $results;
                
    }
    
    /**
     * Get Group ID by Doc No (PR)
     * @param type $docNo
     * @return type
     */
    protected function getGroupIdTrackingDiary($docNo){
        $objResult = DB::connection('oracle_nextgen_rpt')
                    ->table('PM_TRACKING_DIARY')
                    ->where('DOC_NO',$docNo)
                    ->first();
        return $objResult;
    }
    
    
    /**
     * Get Group ID by Doc No (PR)
     * @param type $data
     * @return type
     */
    protected function getListTrackingDiary($data){
        $list = DB::connection('oracle_nextgen_rpt')
                    ->table('PM_TRACKING_DIARY td')
                    ->join('PM_STATUS_DESC sd', 'td.STATUS_ID', '=', 'sd.STATUS_ID')
                    ->where('sd.LANGUAGE_CODE','en')
                    ->where(function($query)  use ($data) {
                        $query->whereIn('td.DOC_NO',$data["list_doc_no"]);
                        $query->orWhereIn('td.GROUP_ID',$data["list_group_id"]);
                    })
                    ->select('td.*','sd.STATUS_NAME')
                    ->orderBy('td.ACTIONED_DATE','desc')
                    ->get();
        return $list;
    }
    
    /**
     * Get Daily QT Published 
     * @param type $data
     * @return type
     */
    protected function getListQtPublished(){
        $query  = "  select qt.qt_id,qt.qt_no
                        , (select status_name from pm_status_desc where status_id = wf.status_id and language_code = 'en') as QT_STATUS 
                        , wf.status_id as QT_STATUS_ID  
                        , case when wf.status_id = 60007 
                           then qt.PUBLISH_DATE
                         end as QT_DATE
                     from sc_qt qt
                   inner join sc_workflow_status wf on qt.qt_id = wf.doc_id and wf.doc_type = 'QT' and wf.is_current = 1
                   where wf.STATUS_ID IN (60007) and trunc(qt.publish_date) = trunc(sysdate)
                   order by 4, wf.STATUS_ID, qt.qt_no
                ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query);
    }
    
    /**
     * Get Daily QT Closing 
     * @param type $data
     * @return type
     */
    protected function getListQtClosing(){
        $query  = "  select qt.qt_id,qt.qt_no
                        , (select status_name from pm_status_desc where status_id = wf.status_id and language_code = 'en') as QT_STATUS 
                        , wf.status_id as QT_STATUS_ID  
                        , case when wf.status_id = 60009
                           then qt.CLOSING_DATE
                         end as QT_DATE
                     from sc_qt qt
                   inner join sc_workflow_status wf on qt.qt_id = wf.doc_id and wf.doc_type = 'QT' and wf.is_current = 1
                   where wf.STATUS_ID IN (60009) and trunc(qt.closing_date) = trunc(sysdate)
                   order by 4, wf.STATUS_ID, qt.qt_no
                ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query);
    }
   

}
