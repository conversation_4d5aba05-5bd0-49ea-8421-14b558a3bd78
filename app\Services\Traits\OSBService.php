<?php

namespace App\Services\Traits;

use DB;
use Carbon\Carbon;
use App\Migrate\MigrateUtils;
use SSH;

/**
 * Description of Profile Management PMServiceTrait
 *
 * <AUTHOR>
 */
trait OSBService {
    
    /*
     * Get Doc Po || Doc CO 
     */
    public function getDocNoPoCo($docPrCr){
       $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT b.DOC_NO
                      FROM fl_fulfilment_request a, fl_fulfilment_order b
                     WHERE a.fulfilment_req_id = b.fulfilment_req_id AND a.doc_no = ? ", array($docPrCr));
        return $results; 
    }
    
    /**
     * 
     * @param type $docPrCr
     * @return type
     */
    public function getPoCoNo($docPrCr) {
        $poCoNoList = $this->getDocNoPoCo($docPrCr);
        if($poCoNoList && count($poCoNoList) > 0){
            return $poCoNo = $poCoNoList[0]->doc_no;
        }
        return null;
    }
    
    /**
     * @deprecated since version 1 ->> Not using this anymore
     * @param type $docNo
     * @return type
     */
    public function getLogGfmasByPoCo($docNo) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "   select a.trans_id,a.trans_type,a.service_code,a.TRANS_DATE,a.status_code,a.status,a.status_desc,
                    a.remarks_1,a.remarks_2,a.remarks_3 ,b.PAYLOAD_BODY
                    from osb_logging a, osb_logging_dtl b
                    where a.LOGGING_ID = b.LOGGING_ID and  a.trans_id in (SELECT   trans_id
                        FROM osb_logging
                       WHERE ( service_code in ('GFM-100','GFM-110') AND TRANS_TYPE = 'IBReq' and remarks_1 = ? )
                       or ( service_code in ('GFM-080','GFM-120','GFM-170') AND TRANS_TYPE = 'IBReq' and remarks_2 = ? ))
                    and a.trans_type in ('IBRes','OBRes','IBReq') and a.service_code in ('GFM-080','GFM-100','GFM-110', 'GFM-120','GFM-170') order by a.trans_date ", array($docNo, $docNo));
        return $results;
    }
    
    /**
     * @deprecated since version 1 ->> Not using this anymore
     * @param type $itemCode
     * @return type
     */
    public function getLogGfmasByMMINF($itemCode) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "   select a.trans_id,a.trans_type,a.service_code,a.TRANS_DATE,a.status_code,a.status,a.status_desc,
                a.remarks_1,a.remarks_2,a.remarks_3 ,b.PAYLOAD_BODY
                from osb_logging a, osb_logging_dtl b
                where a.LOGGING_ID = b.LOGGING_ID and  a.trans_id in (SELECT   trans_id
                    FROM osb_logging
                   WHERE  service_code = 'GFM-020' AND TRANS_TYPE = 'IBReq' and remarks_1 = ?) 
                and a.trans_type in ('IBRes','OBRes','IBReq') 
                and a.service_code =  'GFM-020' order by a.trans_date ", array($itemCode));
        return $results;
    }

   

    /**
     * Search WebService eP to 1GFMAS 
     * Get list distinct trans_id
     */
    public function search1GFMASWebService($search){
        //$trans_type = ['IBReq','IBRes'];
        $service_code = ['GFM-020','GFM-080','GFM-100','GFM-110', 'GFM-120','GFM-130', 'GFM-170','EPP-013'];
        $result = DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING')
                ->whereIn('service_code',$service_code)
                //->whereIn('trans_type',$trans_type)
                ->where(function ($query) use ($search) {
                    $query->orWhere('remarks_1', $search)
                          ->orWhere('remarks_2', $search)
                          ->orWhere('remarks_3', $search);
                })
                ->orderBy('trans_date','desc')
                ->select('trans_id','trans_date')
                ->distinct()
                ->take(50)
                ->get();
        if(count($result) > 0){
            return $result->pluck('trans_id');
        }else{
            return null;
        }
    }
    
    /**
     * Check status WS, receive Key Cert SPKI to eP
     * @param type $softcertRequestID
     * @param type $icNo
     * @return type
     */
    public function checkSuccessReceiveCertSPKI($softcertRequestID,$icNo) {
        $service_code = "SPK-010";
        return DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING')
                ->where('remarks_1',$softcertRequestID)
                ->where('remarks_2',$icNo)
                ->where('service_code',$service_code)
                ->count();
    }

    /**
     * Check status WS , successfull sent to SPKI
     * @param type $softcertRequestID
     * @param type $icNo
     * @return total
     */
    public function checkSuccessSentSPKI($softcertRequestID,$icNo) {
        $service_code = "SPK-020";
        return DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING')
                ->where('remarks_1',$softcertRequestID)
                ->where('remarks_3',$icNo)
                ->where('service_code',$service_code)
                ->count();
    }

    /**
     * get detial, sent/received Key Cert SPKI to eP / eP to SPKI
     * @param type $softcertRequestID
     * @param type $serviceCode
     * @return type
     */
    public function getDetailCertSPKI($softcertRequestID, $serviceCode) {
        $trans_type = 'IBReq';

        /* GET TRANS ID */
        $res = DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING a')
            ->where('a.remarks_1', $softcertRequestID)
            ->where('a.service_code', $serviceCode)
            ->select('a.trans_id')
            ->orderBy('a.trans_date','desc')
            ->first();

        return DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING a')
            ->join('OSB_LOGGING_DTL as b', 'b.logging_id', '=', 'a.logging_id')
            ->where('a.trans_id', $res->trans_id)
            ->where('a.trans_type', $trans_type)
            ->select('a.trans_id','a.trans_type','a.service_code','a.trans_date','a.status_code','a.status','a.status_desc',
                'a.remarks_1','a.remarks_2','a.remarks_3' ,'b.payload_body')
            ->orderBy('a.trans_date','desc')
            ->first();
    }

    /**
     * Get response OSB , successfull sent file to 1GFMAS
     * @param type $filename
     * @return Object Single Result 
     */
    public function getApive1GFMASResponse($filename){
        $trans_type = 'Status-BATCH';
        $service_code = "GFM-010";
        return DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING')
                ->where('remarks_1',$filename)
                ->where('trans_type',$trans_type)
                ->where('service_code',$service_code)
                ->first();
    }
    
    /**
     * Get response OSB , successfull sent file to 1GFMAS
     * @param type $epNo
     * @return Object List Result 
     */
    public function getApive1GFMASResponseByEpNo($epNo){
        $trans_type = 'Status-BATCH';
        $service_code = "GFM-010";
        return DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING')
                ->where('remarks_3',$epNo)
                ->where('trans_type',$trans_type)
                ->where('service_code',$service_code)
                ->get();
    }
    
    /**
     * Get contents APIVE 
     * @param type $transID
     * @return Object List Result 
     */
    public function getApiveContents1GFMAS($transID){
        return DB::connection('oracle_nextgen_rpt')->table('OSB_BATCH_FILE')
                ->where('trans_id',$transID)
                ->first();
    }
    
    /**
     * Get response 1GFMAS
     * @param type $filename
     * @return Object Single Result 
     */
    public function getApove1GFMASResponseByApivefile($filename){
        $service_code = "GFM-370";
        return DB::connection('oracle_nextgen_rpt')->table('DI_INTERFACE_LOG')
                ->where('file_name',$filename)
                ->where('service_code',$service_code)
                ->orderBy('created_date','desc')
                ->first();
    }
    
    /**
     * Get response 1GFMAS
     * @param type $filename
     * @return Object Single Result 
     */
    public function getAperr1GFMASResponseByApivefile($filename){
        $service_code = "GFM-090";
        return DB::connection('oracle_nextgen_rpt')->table('DI_INTERFACE_LOG')
                ->where('file_name',$filename)
                ->where('service_code',$service_code)
                ->orderBy('created_date','desc')
                ->first();
    }
    
    protected function getApoveFilename($filename){
        return str_replace("APIVE", "APOVE", $this->getAPIVEFilenameOUTExtra($filename));
    }
    protected function getAperrFilename($filename){
        return str_replace("APIVE", "APERR", $this->getAPIVEFilenameOUTExtra($filename));
    }
    protected function getAPIVEFilenameOUTExtra($filename){
        

        $date = substr($filename, 15, 8);

        $tarikhExtraFile = Carbon::createFromFormat('Ymd', '20180415')->startOfDay();
        $tarikh = Carbon::createFromFormat('Ymd', $date)->startOfDay();
        $isGreater = $tarikh->gt($tarikhExtraFile);
    
        if($isGreater == false){        
            $fileRemoveGPG = str_replace(".GPG", "", $filename);
            $seq = substr($fileRemoveGPG, 23); 
            $year = substr($filename, 15, 4);
            $monthday = substr($filename, 19, 4);
            if($seq > 999  && $seq <=1998){
               $seqNew = $seq - 999; 
               $filename = '1000APIVE400001'.($year-8).$monthday.$seqNew.'.GPG';
            }
            if($seq > 1998){
               $seqNew = $seq - 1998; 
               $filename = '1000APIVE400001'.($year-16).$monthday.$seqNew.'.GPG';
            }
        }
        return $filename;
    }
    
    /**
     * Search WebService eP to PHIS 
     */
    public function searchPHISWebService($search){
        //$trans_type = ['IBReq','IBRes'];
        $service_code = ['PHS-080','PHS-150','PHS-160','PHS-170','PHS-180','PHS-190','PHS-210','PHS-220'];
        $result = DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING')
                ->whereIn('service_code',$service_code)
               // ->whereIn('trans_type',$trans_type)
                ->where(function ($query) use ($search) {
                    $query->orWhere('remarks_1', $search)
                          ->orWhere('remarks_2', $search)
                          ->orWhere('remarks_3', $search);
                })
                ->orderBy('trans_date','desc')
                ->select('trans_id','trans_date')
                ->distinct()
                ->take(50)        
                ->get();
        if(count($result) > 0){
            return $result->pluck('trans_id');
        }else{
            return null;
        }
    }
    
    
    /**
     * get TransID by remarks on OSB_LOGGING 
     */
    public function getTransIdOSBLoggingByRemarks($search){
        $result = DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING')
                ->where(function ($query) use ($search) {
                    $query->orWhere('remarks_1', $search)
                          ->orWhere('remarks_2', $search)
                          ->orWhere('remarks_3', $search);
                })
                ->orderBy('trans_date','desc')
                ->select('trans_id','trans_date')
                ->distinct()
                ->take(50)
                ->get();
        if(count($result) > 0){
            return $result->pluck('trans_id');
        }else{
            return null;
        }
    }
    
    /**
     * get File Details by FileName
     */
    public function getBatchFileLog($fileName){
        $obj = DB::connection('oracle_nextgen_rpt')->table('OSB_BATCH_FILE')
                ->where('FILE_NAME', $fileName)
                ->orderBy('CREATED_DATE','desc')
                ->first();
        return $obj;
    }
    
    /**
     * Get File is processed. 
     * @param type $filename
     * @return Object Single Result 
     */
    public function getDetailDiInterfaceLog($filename){
        return DB::connection('oracle_nextgen_rpt')->table('DI_INTERFACE_LOG')
                ->where('file_name',$filename)
                ->orderBy('created_date','desc')
                ->first();
    }
    
    /**
     * Call WebService to decrypt content file from 1GFMAs
     */
    public function wsDecryptFile1GFMAS($objFile){
        $target = '1GFMAS'; //PHIS //EP
        $result = collect([]);
        $dataFile = trim($objFile->file_data);
        $xmlContents = "<x:Envelope xmlns:x='http://schemas.xmlsoap.org/soap/envelope/' xmlns:pgp1='http://www.ep.gov.my/Schema/1-0/PGP' xmlns:epm='http://www.ep.gov.my/Schema/1-0/epmf'>
            <x:Header/>
            <x:Body>
                <pgp1:EPMFRq>
                    <epm:RqHeader>
                    </epm:RqHeader>
                    <pgp1:PGPRq>
                        <pgp1:Mode>D</pgp1:Mode>
                        <pgp1:Target>$target</pgp1:Target>
                        <pgp1:Input>$dataFile</pgp1:Input>
                    </pgp1:PGPRq>
                </pgp1:EPMFRq>
            </x:Body>
            </x:Envelope>";
        $xmlContents = '"'.$xmlContents.'"'; 
        $url = "http://192.168.63.205:7011/Common/Utilities/PGPSO";
        $urlHeader = "'Content-Type: application/xml'";
        $commands  = [
            "curl -k ".$url." --header  ".$urlHeader."  -d ".$xmlContents,
        ];
        
        //dump($commands);
        SSH::into('osb')->run($commands, function($line) use (&$result) {
            $data = $line.PHP_EOL;
            $p = xml_parser_create();
            xml_parse_into_struct($p, $data, $vals, $index);
            xml_parser_free($p);

            if(count($vals) > 0){
                foreach ($vals as $val){
                    if($val["tag"] == 'X:BODY'){
                        //dump($val["value"]);
                        $result->push($val["value"]);
                    }
                }
            }
        });
        if(count($result) == 0){
            $result->push('Failed to decrypt data  '.$objFile->file_name);
        }
        return $result;
        
    }
    
    
    public function getListOSB1GFMASWebServiceDetails($search) {
        $listTransId = $this->search1GFMASWebService($search);
        $list = array();
        if ($listTransId != null) {
            $list = $this->searchOSBLogDetailsReqResByTransId($listTransId);
        }
        return $list;
    }
    
    public function getListOSBPHISWebServiceDetails($search) {
        $listTransId = $this->searchPHISWebService($search);
        $list = array();
        if ($listTransId != null) {
            $list = $this->searchOSBLogDetailsReqResByTransId($listTransId);
        }
        return $list;
    }
    
    /**
     * Get list of details OSB Logging for Type Request  and Type Response 
     * @param type $listTransId
     * @return type
     */
    public function searchOSBLogDetailsAllByTransId($listTransId) {

        return DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING a')
                ->join('OSB_LOGGING_DTL as b', 'b.logging_id', '=', 'a.logging_id')
                ->whereIn('a.trans_id',$listTransId)
                ->select('a.trans_id','a.trans_type','a.service_code','a.trans_date','a.status_code','a.status','a.status_desc',
                    'a.remarks_1','a.remarks_2','a.remarks_3' ,'b.payload_body') 
                ->orderBy('a.trans_date','desc')
                ->get();
    }
    
    
    /**
     * Get list of details OSB Logging for Type Request  and Type Response 
     * @param type $listTransId
     * @return type
     */
    public function searchOSBLogDetailsReqResByTransId($listTransId) {
        //$trans_type = ['IBRes','OBRes','OBReq','OBReq-DEC','OBRes-ENC','IBReq','IBReq-ENC','IBReq-DEC','OBRes-QUEUE'];
        $trans_type = ['IBRes','OBRes','IBReq','IBReq-DEC','IBRes-DEC'];
        $trans_id = $listTransId;
        return DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING a')
                ->join('OSB_LOGGING_DTL as b', 'b.logging_id', '=', 'a.logging_id')
                ->whereIn('a.trans_id',$trans_id)
                ->whereIn('a.trans_type',$trans_type)
                ->select('a.trans_id','a.trans_type','a.service_code','a.trans_date','a.status_code','a.status','a.status_desc',
                    'a.remarks_1','a.remarks_2','a.remarks_3' ,'b.payload_body') 
                ->orderBy('a.trans_date','desc')
                ->get();
    }
    
    /**
     * Get list for dashboard (Error Validation Exception Transaction Web Service) -- Last 3 days
     * @return list
     */
    protected function getDashboardStatisticWsValidationException() {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   SELECT TO_CHAR(TRANS_DATE,'YYYY-MM-DD') AS TRANS_DATE,COUNT(*) AS TOTAL FROM OSB_LOGGING WHERE 
                STATUS_DESC LIKE 'Consumer%'  
                AND STATUS='F' 
                AND  TRANS_DATE >= to_date(to_char(sysdate-2, 'yyyy-mm-dd') || '00:00:00','YYYY-MM-DD HH24:MI:SS') 
                GROUP BY TO_CHAR(TRANS_DATE,'YYYY-MM-DD')");

        return $results;
    }
    
    /**
     * Get list for dashboard (Error Item Code GFM-100 Web Service) -- Last 3 days
     * @return list
     */
    protected function getDashboardStatisticWsItemCodeInGFM100() {
        $tarikh =  Carbon::now()->subDays(1)->format('Y-m-d');
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   SELECT  TO_CHAR(TRANS_DATE,'YYYY-MM-DD') AS TRANS_DATE,
                    SUBSTR(A.STATUS_DESC, 0, 11) as ERROR_TYPE, 
                    COUNT(*) as TOTAL FROM OSB_LOGGING A 
                WHERE 
                  A.TRANS_ID IN (
                    SELECT DISTINCT TRANS_ID FROM ( 
                      SELECT TRANS_ID, REMARKS_1, TRANS_DATE, RANK() OVER (PARTITION BY REMARKS_1 ORDER BY TRANS_DATE DESC) RANK
                          FROM OSB_LOGGING
                          WHERE TRANS_TYPE = 'IBReq'
                          AND TRANS_ID IN (
                             SELECT DISTINCT(TRANS_ID) FROM OSB_LOGGING 
                             WHERE 
                             SERVICE_CODE = 'GFM-100' 
                             AND 
                             ( STATUS_DESC LIKE 'Kod barang%' OR   STATUS_DESC LIKE 'Unit ukuran%' )
                             AND TRANS_DATE >= TO_DATE('$tarikh 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
                          )
                        ) WHERE RANK = 1
                  )
                  AND  A.TRANS_TYPE = 'IBRes' 
                GROUP BY TO_CHAR(TRANS_DATE,'YYYY-MM-DD'),SUBSTR(A.STATUS_DESC, 0, 11) 
                ORDER BY TRANS_DATE DESC ");

        return $results;
    }
    
    /**
     * Get list for dashboard (Error Item Code GFM-020 MMINF Web Service) -- Last 3 days
     * @return list
     */
    protected function getDashboardStatisticWsItemCodeInMMINF() {
        $tarikh =  Carbon::now()->subDays(1)->format('Y-m-d');
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   SELECT  TO_CHAR(TRANS_DATE,'YYYY-MM-DD') AS TRANS_DATE,
                    SUBSTR(A.STATUS_DESC, 0, 8) as ERROR_TYPE, 
                    COUNT(*) as TOTAL FROM OSB_LOGGING A 
                WHERE 
                  A.TRANS_ID IN (
                    SELECT DISTINCT TRANS_ID FROM ( 
                      SELECT TRANS_ID, REMARKS_1, TRANS_DATE, RANK() OVER (PARTITION BY REMARKS_1 ORDER BY TRANS_DATE DESC) RANK
                          FROM OSB_LOGGING
                          WHERE TRANS_TYPE = 'IBReq'
                          AND TRANS_ID IN (
                             SELECT DISTINCT(TRANS_ID) FROM OSB_LOGGING 
                             WHERE 
                             SERVICE_CODE = 'GFM-020' 
                             AND 
                             ( STATUS_DESC LIKE '%wujud di 1GFMAS%' OR   STATUS_DESC LIKE '%MOHON CUBA SEBENTAR LAGI SEHINGGA BERJAYA'  )
                             AND TRANS_DATE >= TO_DATE('$tarikh 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
                          )
                        ) WHERE RANK = 1
                  )
                  AND  A.TRANS_TYPE = 'IBRes' 
                GROUP BY TO_CHAR(TRANS_DATE,'YYYY-MM-DD'),SUBSTR(A.STATUS_DESC, 0, 8) 
                ORDER BY TRANS_DATE DESC ");

        return $results;
    }
    
    /**
     * Get list transactions (Error Validation Exception Transaction Web Service) -- Last 3 days
     * @return list
     */
    protected function getListWsValidationException() {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   SELECT A.TRANS_ID,A.SERVICE_CODE,A.TRANS_TYPE,A.TRANS_DATE,A.STATUS_CODE,A.STATUS,A.STATUS_DESC,A.REMARKS_1,A.REMARKS_2,A.REMARKS_3, B.PAYLOAD_BODY FROM OSB_LOGGING A,OSB_LOGGING_DTL B WHERE 
                A.LOGGING_ID =B.LOGGING_ID  
                AND A.TRANS_ID IN (SELECT DISTINCT A.TRANS_ID FROM OSB_LOGGING A WHERE 
                  A.STATUS_DESC LIKE 'Consumer%'  
                  AND A.STATUS='F' 
                  AND  TRANS_DATE >= to_date(to_char(sysdate-2, 'yyyy-mm-dd') || '00:00:00','YYYY-MM-DD HH24:MI:SS')) 
                  ORDER BY  A.TRANS_ID DESC ");

        return $results;
    }
    
    /**
     * Get list transactions (Error No EJB Receiver in Web Service) -- Last 3 days
     * @return list
     */
    protected function getListWsErrNoEJBReceiver() {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   SELECT A.TRANS_ID,A.SERVICE_CODE,A.TRANS_TYPE,A.TRANS_DATE,A.STATUS_CODE,A.STATUS,A.STATUS_DESC,A.REMARKS_1,A.REMARKS_2,A.REMARKS_3, B.PAYLOAD_BODY 
                FROM OSB_LOGGING A,OSB_LOGGING_DTL B WHERE 
                A.LOGGING_ID =B.LOGGING_ID  
                AND A.TRANS_ID IN (
                    SELECT DISTINCT A.TRANS_ID FROM OSB_LOGGING A,OSB_LOGGING_DTL B  WHERE 
                    A.LOGGING_ID =B.LOGGING_ID 
                    AND A.STATUS_DESC = 'Service Not Found: OSB Service Callout action received SOAP Fault response' 
                    AND extractvalue(xmltype.createxml(B.PAYLOAD_BODY),'//con1:faultstring','xmlns:con1=\"http://www.bea.com/wli/sb/stages/transform/config\"') like '%EJBCLIENT000025%' 
                    AND A.TRANS_DATE  >= to_date(to_char(sysdate-2, 'yyyy-mm-dd') || '00:00:00','YYYY-MM-DD HH24:MI:SS')
                    
                )
                ORDER BY A.TRANS_ID DESC 
                ");

        return $results;
    }
    
    /**
     * Get list transactions (Error  EJB Time Out in Web Service) -- Last 3 days
     * @return list
     */
    protected function getListWsErrEJBTimeOut() {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   SELECT A.TRANS_ID,A.SERVICE_CODE,A.TRANS_TYPE,A.TRANS_DATE,A.STATUS_CODE,A.STATUS,A.STATUS_DESC,A.REMARKS_1,A.REMARKS_2,A.REMARKS_3, B.PAYLOAD_BODY FROM OSB_LOGGING A,OSB_LOGGING_DTL B WHERE 
                A.LOGGING_ID =B.LOGGING_ID  
                AND A.TRANS_ID IN (
                    SELECT DISTINCT A.TRANS_ID FROM OSB_LOGGING A,OSB_LOGGING_DTL B  WHERE 
                    A.LOGGING_ID =B.LOGGING_ID 
                    AND A.STATUS_DESC = 'Service Not Found: OSB Service Callout action received SOAP Fault response' 
                    AND extractvalue(xmltype.createxml(B.PAYLOAD_BODY),'//con1:faultstring','xmlns:con1=\"http://www.bea.com/wli/sb/stages/transform/config\"') like '%java.util.concurrent.TimeoutException: No invocation response received%' 
                    AND A.TRANS_DATE  >= to_date(to_char(sysdate-2, 'yyyy-mm-dd') || '00:00:00','YYYY-MM-DD HH24:MI:SS')
                    
                )
                ORDER BY A.TRANS_ID DESC 
                ");

        return $results;
    }
    
    /**
     * Get list transactions (Error  Item Code in Web Service GFM-100) -- Last 3 days
     * @return list
     */
    protected function getListWsErrItemCodeInGFM100() {
        $tarikh =  Carbon::now()->subDays(1)->format('Y-m-d');
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   SELECT  A.TRANS_ID,A.SERVICE_CODE,A.TRANS_TYPE,A.TRANS_DATE,A.STATUS_CODE,A.STATUS,A.STATUS_DESC,A.REMARKS_1,A.REMARKS_2,A.REMARKS_3, B.PAYLOAD_BODY 
                FROM OSB_LOGGING A,OSB_LOGGING_DTL B 
                WHERE 
                A.LOGGING_ID =B.LOGGING_ID  
                AND
                A.TRANS_ID IN (
                  SELECT DISTINCT TRANS_ID FROM ( 
                    SELECT TRANS_ID, REMARKS_1, TRANS_DATE, RANK() OVER (PARTITION BY REMARKS_1 ORDER BY TRANS_DATE DESC) RANK
                        FROM OSB_LOGGING
                        WHERE TRANS_TYPE = 'IBReq'
                        AND TRANS_ID IN (
                           SELECT DISTINCT(TRANS_ID) FROM OSB_LOGGING 
                           WHERE 
                           SERVICE_CODE = 'GFM-100' 
                           AND 
                           ( STATUS_DESC LIKE 'Kod barang%%' OR   STATUS_DESC LIKE 'Unit ukuran%'  )
                           AND TRANS_DATE >= TO_DATE('$tarikh 00:00:00', 'YYYY-MM-DD HH24:MI:SS') 

                        )
                      ) WHERE RANK = 1
                )
                AND  A.TRANS_TYPE IN ('IBReq','IBRes') 
              ORDER BY A.TRANS_ID , A.TRANS_TYPE
                ");

        return $results;
    }
    
    /**
     * Get list Files GFM-140)
     * sample date : 2018-04-19 00:00:00 
     * @return list
     */
    protected function getListFilesGFM140ByDateRange($dateStart,$dateEnd) {
        // sample date : 2018-04-19 00:00:00 
        
        $dtStartTime = Carbon::now();
        
        $query = "SELECT  * 
                    FROM OSB_BATCH_FILE A 
                    WHERE A.SERVICE_CODE = 'GFM-140' 
                    AND A.CREATED_DATE >= TO_DATE('$dateStart', 'YYYY-MM-DD HH24:MI:SS') 
                    AND A.CREATED_DATE < TO_DATE('$dateEnd', 'YYYY-MM-DD HH24:MI:SS') 
                ";
        dump($query);
        
        $results = DB::connection('oracle_nextgen_rpt')->select($query );
        
        $logsdata = self::class . ' Query Date Start : '.$dateStart.' , Query Date End : '.$dateEnd.' , Completed --- '.
                    ' , Total: '.count($results).' Taken Time : '
                    .  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        dump($logsdata);
        return $results;
    }
    
    /**
     * Get list transactions (Error  Item Code in Web Service GFM-100)
     * sample date : 2018-04-19 00:00:00 
     * @return list
     */
    protected function getListWsErrItemCodeInGFM100ByDateRange($dateStart,$dateEnd) {
        // sample date : 2018-04-19 00:00:00 
        
        $dtStartTime = Carbon::now();
        
        $query = "   SELECT  A.TRANS_ID,A.SERVICE_CODE,A.TRANS_TYPE,A.TRANS_DATE,A.STATUS_CODE,A.STATUS,A.STATUS_DESC,A.REMARKS_1,A.REMARKS_2,A.REMARKS_3
                FROM OSB_LOGGING A
                WHERE 
                A.TRANS_ID IN (
                  SELECT DISTINCT TRANS_ID FROM ( 
                    SELECT TRANS_ID, REMARKS_1, TRANS_DATE, RANK() OVER (PARTITION BY REMARKS_1 ORDER BY TRANS_DATE DESC) RANK
                        FROM OSB_LOGGING
                        WHERE TRANS_TYPE = 'IBReq'
                        AND TRANS_ID IN (
                           SELECT DISTINCT(TRANS_ID) FROM OSB_LOGGING 
                           WHERE 
                           SERVICE_CODE = 'GFM-100' 
                           AND 
                           ( STATUS_DESC LIKE 'Kod barang%%' OR   STATUS_DESC LIKE 'Unit ukuran%'  )
                           AND TRANS_DATE >= TO_DATE('$dateStart', 'YYYY-MM-DD HH24:MI:SS') 
                           AND TRANS_DATE < TO_DATE('$dateEnd', 'YYYY-MM-DD HH24:MI:SS') 
                        )
                      ) WHERE RANK = 1
                )
                AND  A.TRANS_TYPE IN ('IBReq','IBRes') 
              ORDER BY A.TRANS_ID , A.TRANS_TYPE
                ";
        dump($query);
        
        $results = DB::connection('oracle_nextgen_rpt')->select($query );
        
        $logsdata = self::class . ' Query Date Start : '.$dateStart.' , Query Date End : '.$dateEnd.' , Completed --- '.
                    ' , Total: '.count($results).' Taken Time : '
                    .  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        dump($logsdata);
        return $results;
    }
    
    /**
     * Get list transactions (Error  Item Code in Web Service GFM-020 MMINF) -- Last 3 days
     * @return list
     */
    protected function getListWsErrItemCodeInMMINF() {
        $tarikh =  Carbon::now()->subDays(1)->format('Y-m-d');

        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   SELECT  A.TRANS_ID,A.SERVICE_CODE,A.TRANS_TYPE,A.TRANS_DATE,A.STATUS_CODE,A.STATUS,A.STATUS_DESC,A.REMARKS_1,A.REMARKS_2,A.REMARKS_3, B.PAYLOAD_BODY 
                    FROM OSB_LOGGING A,OSB_LOGGING_DTL B 
                    WHERE 
                    A.LOGGING_ID =B.LOGGING_ID  
                    AND
                    A.TRANS_ID IN (
                      SELECT DISTINCT TRANS_ID FROM ( 
                        SELECT TRANS_ID, REMARKS_1, TRANS_DATE, RANK() OVER (PARTITION BY REMARKS_1 ORDER BY TRANS_DATE DESC) RANK
                            FROM OSB_LOGGING
                            WHERE TRANS_TYPE = 'IBReq'
                            AND TRANS_ID IN (
                               SELECT DISTINCT(TRANS_ID) FROM OSB_LOGGING 
                               WHERE 
                               SERVICE_CODE = 'GFM-020' 
                               AND 
                               ( STATUS_DESC LIKE '%wujud di 1GFMAS%' OR   STATUS_DESC LIKE '%MOHON CUBA SEBENTAR LAGI SEHINGGA BERJAYA'  )
                               AND TRANS_DATE >= TO_DATE('$tarikh 00:00:00', 'YYYY-MM-DD HH24:MI:SS') 
                                   
                            )
                          ) WHERE RANK = 1
                    )
                    AND  A.TRANS_TYPE IN ('IBReq','IBRes') 
                  ORDER BY A.TRANS_ID , A.TRANS_TYPE 
                ");


        return $results;
    }
    
    
    
    /**
     * Get list Statistic (Error  FIle Processing) -- Last 3 days
     * APERR, AR902, 1000GL
     * @return list
     */
    protected function getListStatisticFileErrProcessing() {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   SELECT TO_CHAR(A.TRANS_DATE, 'YYYY-MM-DD') AS TRANS_DATE , A.SERVICE_CODE,B.SERVICE_NAME,A.TRANS_TYPE, COUNT(*) AS TOTAL 
                FROM OSB_LOGGING A, OSB_SERVICE B 
                WHERE 
                  A.SERVICE_CODE = B.SERVICE_CODE 
                  AND A.TRANS_DATE >= TO_DATE(TO_CHAR(SYSDATE-2, 'YYYY-MM-DD') || '00:00:00','YYYY-MM-DD HH24:MI:SS') 
                  AND A.TRANS_TYPE = 'Status-BATCH'
                AND (
                  REMARKS_1 LIKE '%AR902%' 
                  OR REMARKS_1 LIKE '1000GLMDE%' 
                  OR REMARKS_1 LIKE '1000APERR400001%' 
                )
                GROUP BY TO_CHAR(A.TRANS_DATE, 'YYYY-MM-DD'),A.SERVICE_CODE,B.SERVICE_NAME,A.TRANS_TYPE 
                ORDER BY TO_CHAR(A.TRANS_DATE, 'YYYY-MM-DD') DESC
                ");

        return $results;
    }
    
    /**
     * Get list transactions (Error  FIle Processing) -- Last 3 days
     * APERR, AR902, 1000GL
     * @return list
     */
    protected function getListFileErrProcessing($serviceCode, $transDate) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   SELECT A.TRANS_DATE , A.SERVICE_CODE,B.SERVICE_NAME,A.TRANS_TYPE, A.REMARKS_1  AS FILE_NAME  
                FROM OSB_LOGGING A, OSB_SERVICE B 
                WHERE 
                  A.SERVICE_CODE = B.SERVICE_CODE 
                  AND A.SERVICE_CODE = ? 
                  AND TO_CHAR(A.TRANS_DATE, 'YYYY-MM-DD') = ? 
                  AND A.TRANS_TYPE = 'Status-BATCH'
                AND (
                  REMARKS_1 LIKE '%AR902%' 
                  OR REMARKS_1 LIKE '1000GLMDE%' 
                  OR REMARKS_1 LIKE '1000APERR400001%' 
                )
                ORDER BY TO_CHAR(A.TRANS_DATE, 'YYYY-MM-DD') DESC
                ", array($serviceCode,$transDate));

        return $results;
    }
    

    
    /**
     * Get list transactions Return Error 
     * @param type $dataSearch ( service_code,date_from,date_to)
     * @return type
     */
    protected function getListWsTransactionErrorBySearch($dataSearch) {

        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   SELECT  A.TRANS_ID,A.SERVICE_CODE,A.TRANS_TYPE,A.TRANS_DATE,A.STATUS_CODE,A.STATUS,A.STATUS_DESC,A.REMARKS_1,A.REMARKS_2,A.REMARKS_3, B.PAYLOAD_BODY 
                    FROM OSB_LOGGING A,OSB_LOGGING_DTL B 
                    WHERE 
                    A.LOGGING_ID =B.LOGGING_ID  
                    AND
                    A.TRANS_ID IN (
                      SELECT DISTINCT TRANS_ID FROM ( 
                        SELECT TRANS_ID, REMARKS_1, TRANS_DATE, RANK() OVER (PARTITION BY REMARKS_1 ORDER BY TRANS_DATE DESC) RANK
                            FROM OSB_LOGGING
                            WHERE TRANS_TYPE = 'IBReq'
                            AND TRANS_ID IN (
                               SELECT DISTINCT(TRANS_ID) FROM OSB_LOGGING A 
                               WHERE 
                                A.SERVICE_CODE = ?
                                AND A.TRANS_DATE >= TO_DATE(?, 'YYYY-MM-DD HH24:MI:SS') 
                                AND A.TRANS_DATE < TO_DATE(?, 'YYYY-MM-DD HH24:MI:SS') 
                                AND A.STATUS =  'F' 
                                AND A.TRANS_TYPE = 'IBRes' 
                            ) 
                          ) WHERE RANK = 1
                    )
                    AND  A.TRANS_TYPE IN ('IBReq','IBRes','IBReq-DEC','IBRes-DEC') 
                ORDER BY A.TRANS_DATE desc,A.TRANS_ID
                ", array(
                        $dataSearch->get('service_code'),
                        $dataSearch->get('date_start'),
                        $dataSearch->get('date_end')
                    )
                );

        return $results;
    }
}
