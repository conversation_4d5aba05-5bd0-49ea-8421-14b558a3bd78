<?php
/**
 * Created by PhpStorm.
 * User: iqbalfikri
 * Date: 3/14/2018
 * Time: 11:56 AM
 */

namespace App\Services\Traits;

use Log;
use DB;

trait OrganizationService
{

    /**
     * Get Query for   Users Info. Not include roles.
     * @param type $icno
     * @return type
     */
    public function getUserList($type,$value)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('PM_USER as PMU');
        $query->join('PM_USER_ORG as PMUO', 'PMU.USER_ID', '=', 'PMUO.USER_ID');
        $query->leftJoin('PM_LOGIN_HISTORY as PMLH', 'PMU.USER_ID', '=', 'PMLH.USER_ID');
        $query->where('PMU.RECORD_STATUS', 1);
        $query->where('PMUO.RECORD_STATUS', 1);
        if ($type == 'identification_no') {
            $query->where('PMU.IDENTIFICATION_NO', $value);
        }
        if ($type == 'org_profile_id') {
            $query->where('PMUO.ORG_PROFILE_ID', $value);
        }
        $query->select('PMU.USER_ID', 'PMU.LOGIN_ID', 'PMU.USER_NAME AS FULLNAME', 'PMU.NATIONALITY_ID', 'PMU.IDENTIFICATION_TYPE_ID');
        $query->addSelect('PMUO.ORG_PROFILE_ID','PMU.ORG_TYPE_ID', 'PMU.IDENTIFICATION_NO', 'PMU.DESIGNATION', 'PMU.EMAIL', 'PMU.RECORD_STATUS AS PMU_RECORD_STATUS');
        $query->addSelect('PMU.CREATED_DATE', 'PMU.CHANGED_DATE', 'PMU.MOBILE_COUNTRY', 'PMU.MOBILE_AREA', 'PMU.MOBILE_NO');
        $query->addSelect('PMU.PHONE_COUNTRY', 'PMU.PHONE_AREA', 'PMU.PHONE_NO', 'PMU.FAX_COUNTRY', 'PMU.FAX_AREA', 'PMU.FAX_NO', 'PMU.SALUTATION_ID');
        $query->addSelect('PMLH.LOGIN_DATE');

        return $query->get();

    }
    

    /**
     * Get Query for  users roles.
     * @param type $userId
     * @return type
     */
    public function getUserRole($userId)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('PM_USER as PMU');
        $query->leftJoin('PM_USER_ORG as PMUO', 'PMU.USER_ID', '=', 'PMUO.USER_ID');
        $query->leftJoin('PM_USER_ROLE as PMUR', 'PMUO.USER_ORG_ID', '=', 'PMUR.USER_ORG_ID');
        $query->leftJoin('PM_ROLE_DESC as PMRD', 'PMUR.ROLE_CODE', '=', 'PMRD.ROLE_CODE');
        $query->where('PMUO.RECORD_STATUS', 1);
        $query->where('PMUR.RECORD_STATUS', 1);
        $query->where('PMRD.LANGUAGE_CODE', 'en');
        if ($userId != null) {
            $query->where('PMU.USER_ID', $userId);
        }
        $query->select('PMU.USER_ID', 'PMUR.USER_ROLE_ID', 'PMUR.ROLE_CODE', 'PMRD.ROLE_NAME');

        return $query->get();

    }
    
    /**
     * Get Query for  users roles.
     * @param type $userId
     * @return type
     */
    public function getRoleDesc($roleCode,$language)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('PM_ROLE_DESC as PMRD');
        $query->where('PMRD.role_code', $roleCode);
        $query->where('PMRD.LANGUAGE_CODE', $language);
        return $query->first();

    }

    public function getPmOrganization($type,$value){
        $data = $this->getPmOrgValidity($type,$value);
        if(count($data) == 0){
            $data = $this->getPmOrgFactoring($type,$value);
        }
        return $data; 
    }        
   
    /**
     * Get Query for  Oranization Factoring Info.
     * @param type $orgCode
     * @return type
     */
    public function getPmOrgFactoring($type,$value)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('PM_FINANCIAL_ORG as PMFO');
        $query->join('PM_ORG_PROFILE as PMOP', 'PMFO.FINANCIAL_ORG_ID', '=', 'PMOP.FACTORING_ORG_ID');
        $query->join('PM_PARAMETER_DESC as PMPD', 'PMOP.ORG_TYPE_ID', '=', 'PMPD.PARAMETER_ID');
        $query->where('PMFO.RECORD_STATUS', 1);
        $query->where('PMPD.LANGUAGE_CODE', 'ms');
        if ($type == 'org_code') {
            $query->where('PMFO.BIZ_REG_NO', $value);
        }
        if ($type == 'org_profile_id') {
            $query->where('PMOP.ORG_PROFILE_ID', $value);
        }
        $query->select('PMOP.ORG_PROFILE_ID','PMOP.FACTORING_ORG_ID', 'PMFO.BIZ_REG_NO as ORG_CODE', 'PMFO.FIN_ORG_NAME as ORG_NAME', 'PMFO.RECORD_STATUS AS PMOV_STATUS', 'PMFO.CHANGED_DATE',
                 DB::raw("NULL as EFF_DATE"),DB::raw("NULL as EXP_DATE"));
        $query->addSelect('PMOP.ORG_TYPE_ID', 'PMOP.PARENT_ORG_PROFILE_ID', 'PMOP.RECORD_STATUS AS PMOP_STATUS', 'PMOP.CHANGED_DATE');
        $query->addSelect('PMPD.CODE_DESC');

        return $query->get();
    }

    /**
     * Get Query for  Oranization Info..
     * @param type $orgCode
     * @return type
     */
    public function getPmOrgValidity($type,$value)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('PM_ORG_VALIDITY as PMOV');
        $query->join('PM_ORG_PROFILE as PMOP', 'PMOV.ORG_PROFILE_ID', '=', 'PMOP.ORG_PROFILE_ID');
        $query->join('PM_PARAMETER_DESC as PMPD', 'PMOP.ORG_TYPE_ID', '=', 'PMPD.PARAMETER_ID');
        $query->where('PMPD.LANGUAGE_CODE', 'ms');
        $query->where('PMOV.RECORD_STATUS', 1);
        if ($type == 'org_code') {
            $query->where('PMOV.ORG_CODE', $value);
        }
        if ($type == 'org_profile_id') {
            $query->where('PMOV.ORG_PROFILE_ID', $value);
        }
        $query->select('PMOV.ORG_PROFILE_ID', 'PMOV.ORG_CODE', 'PMOV.ORG_NAME', 'PMOV.EFF_DATE', 'PMOV.EXP_DATE', 'PMOV.RECORD_STATUS AS PMOV_STATUS', 'PMOV.CHANGED_DATE');
        $query->addSelect('PMOP.ORG_TYPE_ID', 'PMOP.PARENT_ORG_PROFILE_ID', 'PMOP.RECORD_STATUS AS PMOP_STATUS', 'PMOP.CHANGED_DATE');
        $query->addSelect('PMPD.CODE_DESC');

        return $query->get();
    }

    /**
     * Get Query for  Organization  Info. 
     * @param type $orgProfileId
     * @return type
     */
    public function getPmOrgValidityByParentId($orgProfileId)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('PM_ORG_VALIDITY as PMOV');
        $query->join('PM_ORG_PROFILE as PMOP', 'PMOV.ORG_PROFILE_ID', '=', 'PMOP.ORG_PROFILE_ID');
        $query->leftJoin('PM_PARAMETER_DESC as PMPD', 'PMOP.ORG_TYPE_ID', '=', 'PMPD.PARAMETER_ID');
        $query->where('PMPD.LANGUAGE_CODE', 'ms');
        $query->where('PMOV.RECORD_STATUS', 1);
        if ($orgProfileId != null) {
            $query->where('PMOV.ORG_PROFILE_ID', $orgProfileId);
        }
        $query->select('PMOV.ORG_PROFILE_ID', 'PMOV.ORG_CODE', 'PMOV.ORG_NAME', 'PMOV.EFF_DATE', 'PMOV.EXP_DATE', 'PMOV.RECORD_STATUS AS PMOV_STATUS', 'PMOV.CHANGED_DATE');
        $query->addSelect('PMOP.ORG_TYPE_ID', 'PMOP.PARENT_ORG_PROFILE_ID', 'PMOP.RECORD_STATUS AS PMOP_STATUS', 'PMOP.CHANGED_DATE');
        $query->addSelect('PMPD.CODE_DESC');

        return $query->get();
    }

    /**
     * Get Query for   Users Group Info.
     * @param type $orgProfileId
     * @return type
     */
    public function getPmUserGroupByUserId($userId)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('PM_USER_GROUP_USER as PMUGU');
        $query->leftJoin('PM_USER_GROUP as PMUG', 'PMUGU.USER_GROUP_ID', '=', 'PMUG.USER_GROUP_ID');
        $query->where('PMUGU.RECORD_STATUS', 1);
        $query->where('PMUG.RECORD_STATUS', 1);
        $query->where('PMUGU.USER_ID', $userId);
        $query->select('PMUG.GROUP_CODE', 'PMUG.GROUP_NAME');

        return $query->get();
    }

}