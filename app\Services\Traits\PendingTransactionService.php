<?php

namespace App\Services\Traits;

use Log;
use DB;
use App\Services\EPService;
use Carbon\Carbon;

/**
 * Description : Get list pending transaction when user unable to delete MOF
 * <AUTHOR>
 */
trait PendingTransactionService {
    
    /**
     * Get list using collect([])
     * @param type $supplierId
     * @return results
     */
    protected function getListPendingTransaction($supplierId) { 
        
        $list = collect([]); 
        
        //query 1      
        $total1 = $this->getListContractIntegrationReqDTONxtgen($supplierId);
        $obj1 = collect([]);
        $obj1->put("transaction",'Contract Integration ReqDTO : CT');
        $obj1->put("total",$total1);
        $list->push($obj1);                
                
        //query 2
        $total2 = $this->getListContractRequestNextgen($supplierId);
        $obj2 = collect([]);
        $obj2->put("transaction",'FulfilmentReqDTO : Contract Request in eP');
        $obj2->put("total",$total2);
        $list->push($obj2);       
                
        //query 3
        $total3 = $this->getListContractOrderNextgen($supplierId);
        $obj3 = collect([]);
        $obj3->put("transaction",'FulfilmentReqDTO : Contract Order in eP');
        $obj3->put("total",$total3);
        $list->push($obj3);        
                       
        //query 4
        $total4 = $this->getListPurchaseRequestNextgen($supplierId);
        $obj4 = collect([]);
        $obj4->put("transaction",'FulfilmentReqDTO : Purchase Request in eP');
        $obj4->put("total",$total4);
        $list->push($obj4);
                        
        //query 5
        $total5 = $this->getListPurchaseOrderNextgen($supplierId);
        $obj5 = collect([]);
        $obj5->put("transaction",'FulfilmentReqDTO : Purchase Order in eP');
        $obj5->put("total",$total5);
        $list->push($obj5);
        
        //query 6
        $total6 = $this->getListContractRequestEP($supplierId);
        $obj6 = collect([]);
        $obj6->put("transaction",'FulfilmentReqDTO : Contract Request in old eP');
        $obj6->put("total",$total6);
        $list->push($obj6);
        
        //query 7
        $total7 = $this->getListContractOrderEP($supplierId);
        $obj7 = collect([]);
        $obj7->put("transaction",'FulfilmentReqDTO : Contract Order in old eP');
        $obj7->put("total",$total7);
        $list->push($obj7);
        
        //query 8
        $total8 = $this->getListPurchaseRequestEP($supplierId);
        $obj8 = collect([]);
        $obj8->put("transaction",'FulfilmentReqDTO : Purchase Request in old eP');
        $obj8->put("total",$total8);
        $list->push($obj8);
        
        //query 9
        $total9 = $this->getListPurchaseOrderEP($supplierId);
        $obj9 = collect([]);
        $obj9->put("transaction",'FulfilmentReqDTO : Purchase Order in old eP');
        $obj9->put("total",$total9);
        $list->push($obj9);        
        
           return $list;

    }
    
    /**
     * Get list for CT
     * @param type $supplierID
     * @return results
     */
    protected function getListContractIntegrationReqDTONxtgen($supplierID) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT COUNT (*) as total 
                FROM ct_contract
                WHERE supplier_id = $supplierID AND record_status = 1");
                return $results[0]->total;
    }
    
    /**
     * Get list for CR pending transaction in old eP
     * @param type $supplierID
     * @return results
     */
    protected function getListContractRequestEP($supplierID){
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT COUNT (a.fulfilment_req_id) as total
                FROM fl_fulfilment_request a LEFT JOIN fl_workflow_status b
                ON b.doc_id = a.fulfilment_req_id
                AND b.doc_type = a.doc_type
                AND b.is_current = 1
                WHERE a.supplier_id = $supplierID
                AND a.doc_type = 'CR'
                AND a.record_status = 1
                AND b.status_id NOT IN
                               (40800, 40810, 40900, 40910, 40911, 40940, 40950, 41431)
                AND TO_CHAR(a.CREATED_DATE, 'YYYY-MM-DD') < '2018-01-01'");
            return $results[0]->total;
        
    }
    
    /**
     * Get list for CR pending transaction in Nextgen
     * @param type $supplierID
     * @return results
     */
    protected function getListContractRequestNextgen($supplierID){
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT COUNT (a.fulfilment_req_id) as total
                FROM fl_fulfilment_request a LEFT JOIN fl_workflow_status b
                ON b.doc_id = a.fulfilment_req_id
                AND b.doc_type = a.doc_type
                AND b.is_current = 1
                WHERE a.supplier_id = $supplierID
                AND a.doc_type = 'CR'
                AND a.record_status = 1
                AND b.status_id NOT IN
                               (40800, 40810, 40900, 40910, 40911, 40940, 40950, 41431)
                AND TO_CHAR(a.CREATED_DATE, 'YYYY-MM-DD') > '2017-12-31'");
            return $results[0]->total;
        
    }
    
    /**
     * Get list for CO pending transaction in old eP
     * @param type $supplierID
     * @return results
     */
    protected function getListContractOrderEP($supplierID){
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT COUNT (a.fulfilment_order_id) as total
                FROM fl_fulfilment_order a LEFT JOIN fl_workflow_status b
                ON b.doc_id = a.fulfilment_order_id
                AND b.doc_type = a.doc_type
                AND b.is_current = 1
                LEFT JOIN fl_fulfilment_request c
                ON a.fulfilment_req_id = c.fulfilment_req_id
                WHERE c.supplier_id = $supplierID
                AND a.doc_type = 'CO'
                AND a.record_status = 1
                AND b.status_id NOT IN (41535, 41900, 41910, 41920, 41930, 41431)
                AND TO_CHAR(a.CREATED_DATE, 'YYYY-MM-DD') < '2018-01-01'");
            return $results[0]->total;
        
    }
    
    /**
     * Get list for CO pending transaction in Nextgen
     * @param type $supplierID
     * @return results
     */
    protected function getListContractOrderNextgen($supplierID){
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT COUNT (a.fulfilment_order_id) as total
                FROM fl_fulfilment_order a LEFT JOIN fl_workflow_status b
                ON b.doc_id = a.fulfilment_order_id
                AND b.doc_type = a.doc_type
                AND b.is_current = 1
                LEFT JOIN fl_fulfilment_request c
                ON a.fulfilment_req_id = c.fulfilment_req_id
                WHERE c.supplier_id = $supplierID
                AND a.doc_type = 'CO'
                AND a.record_status = 1
                AND b.status_id NOT IN (41535, 41900, 41910, 41920, 41930, 41431)
                AND TO_CHAR(a.CREATED_DATE, 'YYYY-MM-DD') > '2017-12-31'");
            return $results[0]->total;
        
    }
    
    /**
     * Get list for PR pending transaction in old eP
     * @param type $supplierID
     * @return results
     */
    protected function getListPurchaseRequestEP($supplierID){
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT COUNT (a.fulfilment_req_id) as total
                FROM fl_fulfilment_request a LEFT JOIN fl_workflow_status b
                ON b.doc_id = a.fulfilment_req_id
                AND b.doc_type = a.doc_type
                AND b.is_current = 1
                WHERE a.supplier_id = $supplierID
                AND a.doc_type = 'PR'
                AND a.record_status = 1
                AND b.status_id NOT IN
                (40300, 40310, 40400, 40410, 40430, 40440, 40450, 41431, 41035)
                AND TO_CHAR(a.CREATED_DATE, 'YYYY-MM-DD') < '2018-01-01'");
            return $results[0]->total;
        
    }
    
    /**
     * Get list for PR pending transaction in Nextgen
     * @param type $supplierID
     * @return results
     */
    protected function getListPurchaseRequestNextgen($supplierID){
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT COUNT (a.fulfilment_req_id) as total
                FROM fl_fulfilment_request a LEFT JOIN fl_workflow_status b
                ON b.doc_id = a.fulfilment_req_id
                AND b.doc_type = a.doc_type
                AND b.is_current = 1
                WHERE a.supplier_id = $supplierID
                AND a.doc_type = 'PR'
                AND a.record_status = 1
                AND b.status_id NOT IN
                (40300, 40310, 40400, 40410, 40430, 40440, 40450, 41431, 41035)
                AND TO_CHAR(a.CREATED_DATE, 'YYYY-MM-DD') > '2017-12-31'");
            return $results[0]->total;
        
    }
    
    /**
     * Get list for PR pending transaction in old eP
     * @param type $supplierID
     * @return results
     */
    protected function getListPurchaseOrderEP($supplierID){
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT COUNT (a.fulfilment_order_id) as total
                FROM fl_fulfilment_order a LEFT JOIN fl_workflow_status b
                ON b.doc_id = a.fulfilment_order_id
                AND b.doc_type = a.doc_type
                AND b.is_current = 1
                LEFT JOIN fl_fulfilment_request c
                ON a.fulfilment_req_id = c.fulfilment_req_id
                WHERE c.supplier_id = $supplierID
                AND a.doc_type = 'PO'
                AND a.record_status = 1
                AND b.status_id NOT IN (41035, 41400, 41410, 41310, 41420, 41430, 41431)
                AND TO_CHAR(a.CREATED_DATE, 'YYYY-MM-DD') < '2018-01-01'");
            return $results[0]->total;
        
    }
    
    /**
     * Get list for PR pending transaction in Nextgen
     * @param type $supplierID
     * @return results
     */
    protected function getListPurchaseOrderNextgen($supplierID){
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT COUNT (a.fulfilment_order_id) as total
                FROM fl_fulfilment_order a LEFT JOIN fl_workflow_status b
                ON b.doc_id = a.fulfilment_order_id
                AND b.doc_type = a.doc_type
                AND b.is_current = 1
                LEFT JOIN fl_fulfilment_request c
                ON a.fulfilment_req_id = c.fulfilment_req_id
                WHERE c.supplier_id = $supplierID
                AND a.doc_type = 'PO'
                AND a.record_status = 1
                AND b.status_id NOT IN (41035, 41400, 41410, 41310, 41420, 41430, 41431)
                AND TO_CHAR(a.CREATED_DATE, 'YYYY-MM-DD') > '2017-12-31'");
            return $results[0]->total;
        
    }
    
    
}

