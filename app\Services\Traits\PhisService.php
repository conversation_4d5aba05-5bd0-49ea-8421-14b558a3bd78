<?php

namespace App\Services\Traits;

use Log;
use DB;
use App\Services\EPService;
use Carbon\Carbon;


/**
 * Description of Profile Management PMServiceTrait
 *
 * <AUTHOR>
 */
trait PhisService {

    /**
     * show CR details
     * @return list
     */    
       
    protected function getPhisNo($phisNo) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
           "select ax.PHIS_NO as order_reference_no, 
            bx.changed_date as created_date,
            ax.doc_no as cr_no, ex.DOC_NO as co_no,
            dx.status_name as status_ep , v.ORG_CODE as ptj_code
            from fl_fulfilment_request ax, 
            fl_workflow_status bx, 
            pm_status cx, 
            pm_status_desc dx, 
            FL_FULFILMENT_ORDER ex,
            pm_org_profile pm,
            pm_org_validity v,
            pm_parameter a,
            pm_parameter_desc b,
            pm_org_profile pm1, 
            pm_org_validity v1,
            pm_parameter a1,
            pm_parameter_desc b1,
            pm_org_profile pm2, 
            pm_org_validity v2,
            pm_parameter a2,
            pm_parameter_desc b2,
            pm_org_profile pm3, 
            pm_org_validity v3,
            pm_parameter a3,
            pm_parameter_desc b3
            where ax.fulfilment_req_id = bx.doc_id
            and bx.status_id = cx.status_id
            and cx.status_id = dx.status_id
            and dx.language_code ='en'
            and ex.FULFILMENT_REQ_ID=ax.FULFILMENT_REQ_ID
            and bx.doc_type in ('CR')
            and pm.org_profile_id = v.org_profile_id
            and ax.CREATED_ORG_PROFILE_ID = pm.ORG_PROFILE_ID
            AND a.parameter_id = pm.org_type_id
            AND a.parameter_id = b.parameter_id
            AND b.language_code = 'en'
            AND pm.record_status = 1
            AND v.record_status = 1
            AND pm1.org_profile_id = v1.org_profile_id
            AND a1.parameter_id = pm1.org_type_id
            AND a1.parameter_id = b1.parameter_id
            AND b1.language_code = 'en'
            AND pm1.record_status = 1
            AND v1.record_status = 1
            AND pm1.org_profile_id = pm.parent_org_profile_id
            AND pm2.org_profile_id = v2.org_profile_id
            AND a2.parameter_id = pm2.org_type_id
            AND a2.parameter_id = b2.parameter_id
            AND b2.language_code = 'en'
            AND pm2.record_status = 1
            AND v2.record_status = 1
            AND pm2.org_profile_id = pm1.parent_org_profile_id
            AND pm3.org_profile_id = v3.org_profile_id
            AND a3.parameter_id = pm3.org_type_id
            AND a3.parameter_id = b3.parameter_id
            AND b3.language_code = 'en'
            AND pm3.record_status = 1
            AND v3.record_status = 1
            AND pm3.org_profile_id = pm2.parent_org_profile_id
            and ax.phis_no = ? 
            ORDER BY bx.changed_date DESC", array($phisNo));        

        return $results;
    }
    
    /**
     * show CR/CO details
     * @return list
     */   
    
    protected function getDocNoCRCO($docNo){
        $parameters = array();
        $type   = substr($docNo, 0, 2); 
        $query = "select ax.PHIS_NO as order_reference_no, 
            bx.changed_date as created_date,
            ax.doc_no as cr_no, ex.DOC_NO as co_no,
            dx.status_name as status_ep , v.ORG_CODE as ptj_code
            from fl_fulfilment_request ax, 
            fl_workflow_status bx, 
            pm_status cx, 
            pm_status_desc dx, 
            FL_FULFILMENT_ORDER ex,
            pm_org_profile pm,
            pm_org_validity v,
            pm_parameter a,
            pm_parameter_desc b,
            pm_org_profile pm1, 
            pm_org_validity v1,
            pm_parameter a1,
            pm_parameter_desc b1,
            pm_org_profile pm2, 
            pm_org_validity v2,
            pm_parameter a2,
            pm_parameter_desc b2,
            pm_org_profile pm3, 
            pm_org_validity v3,
            pm_parameter a3,
            pm_parameter_desc b3
            where ax.fulfilment_req_id = bx.doc_id
            and bx.status_id = cx.status_id
            and cx.status_id = dx.status_id
            and dx.language_code ='en'
            and ex.FULFILMENT_REQ_ID=ax.FULFILMENT_REQ_ID
            and bx.doc_type in ('CR')
            and pm.org_profile_id = v.org_profile_id
            and ax.CREATED_ORG_PROFILE_ID = pm.ORG_PROFILE_ID
            AND a.parameter_id = pm.org_type_id
            AND a.parameter_id = b.parameter_id
            AND b.language_code = 'en'
            AND pm.record_status = 1
            AND v.record_status = 1
            AND pm1.org_profile_id = v1.org_profile_id
            AND a1.parameter_id = pm1.org_type_id
            AND a1.parameter_id = b1.parameter_id
            AND b1.language_code = 'en'
            AND pm1.record_status = 1
            AND v1.record_status = 1
            AND pm1.org_profile_id = pm.parent_org_profile_id
            AND pm2.org_profile_id = v2.org_profile_id
            AND a2.parameter_id = pm2.org_type_id
            AND a2.parameter_id = b2.parameter_id
            AND b2.language_code = 'en'
            AND pm2.record_status = 1
            AND v2.record_status = 1
            AND pm2.org_profile_id = pm1.parent_org_profile_id
            AND pm3.org_profile_id = v3.org_profile_id
            AND a3.parameter_id = pm3.org_type_id
            AND a3.parameter_id = b3.parameter_id
            AND b3.language_code = 'en'
            AND pm3.record_status = 1
            AND v3.record_status = 1
            AND pm3.org_profile_id = pm2.parent_org_profile_id";        
        if($type == 'CR'){            
            $query = $query." AND ax.doc_no = ?  ORDER BY bx.changed_date DESC";
            array_push($parameters, $docNo);
        }
        if($type == 'CO'){
            $query = $query." AND ex.doc_no = ? ORDER BY bx.changed_date DESC";
            array_push($parameters, $docNo);
        }
        
        $results = DB::connection('oracle_nextgen_rpt')->select($query, $parameters);        
        return $results;

    }  
    
    
    
   

}
