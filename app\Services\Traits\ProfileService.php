<?php

namespace App\Services\Traits;

use DB;

/**
 * Description of Profile Management PMServiceTrait
 *
 * <AUTHOR>
 */
trait ProfileService {

    public function getListUomByUomName($input) {
        $inputSearch = '%' . strtolower($input) . '%';
        $results = DB::connection('oracle_nextgen_rpt')->select(
                " SELECT * FROM pm_uom
                    WHERE record_status = 1 
                    AND ( LOWER(uom_name) LIKE ? OR LOWER(uom_code) LIKE ? ) 
                    ORDER BY uom_name asc", array($inputSearch,$inputSearch));
        return $results;
    }

    public function getUserRoles($userId) {
        $query = DB::connection('oracle_nextgen_rpt')->table('PM_USER as pu');
        $query->join('PM_USER_ORG as puo', 'pu.USER_ID', '=', 'puo.USER_ID');
        $query->join('PM_USER_ROLE as pur', 'puo.USER_ORG_ID', '=', 'pur.USER_ORG_ID');
        $query->where('pu.record_status', 1);
        $query->where('puo.record_status', 1);
        $query->where('pur.record_status', 1);
        $query->where('pu.user_id', $userId);
        return $query->get();
    }

    public function getLastSigningSPKIorGPKI($icno,$type) {
        $query = DB::connection('oracle_nextgen_rpt')->table('pm_digi_sign');
        $query->where('original_data','like', '%'.$icno.'%');
        if($type != null && $type == 'SPKI'){
          $query->whereNotNull('spki_signed_data');
        }
        if($type != null && $type == 'GPKI'){
          $query->whereNotNull('gpki_signed_data');
        }
        $query->orderBy('changed_date','desc');
        return $query->first();
    }
    
}
