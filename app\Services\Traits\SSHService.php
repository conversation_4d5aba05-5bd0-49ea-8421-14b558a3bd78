<?php

namespace App\Services\Traits;

use SSH;

/**
 * Description of Profile Management PMServiceTrait
 *
 * <AUTHOR>
 */
trait SSHService {

    protected function getListAPIVEOutFolder(){
        $commands  = [
            'cd /batch/1GFMAS/OUT',
            'find 1000APIVE40000*.GPG',
            'exit',
        ];
        $filesFound = array();
        SSH::into('portal')->run($commands, function($line) use (&$filesFound)  {
            $data = $line.PHP_EOL;
            $arrayData  = (explode("\n",$data));
            foreach($arrayData  as $str){
                $filename = trim(substr($str, -33)); 
                $checkSpaceExist = strpos($filename, ' ');
                if($checkSpaceExist !== false){
                   $arrayFilename =  explode(' ', $filename);
                   $filename = $arrayFilename[1];
                }
                $pos = strpos($filename, '1000APIVE40000120');
                if($pos !== false){
                    if(strlen($filename) > 25 ){
                        array_push($filesFound,trim($filename));   
                    }
                }

            }

        });
        
        return $filesFound;
    }
    
    protected function getListAPOVEInFolder(){
        $commands  = [
            'cd /batch/1GFMAS/IN',
            'find 1000APOVE40000*.GPG',
            'exit',
        ];
        $filesFound = array();
        SSH::into('portal')->run($commands, function($line) use (&$filesFound)  {
            $data = $line.PHP_EOL;
            $arrayData  = (explode("\n",$data));
            foreach($arrayData  as $str){
                $filename = trim(substr($str, -33)); 
                $checkSpaceExist = strpos($filename, ' ');
                if($checkSpaceExist !== false){
                   $arrayFilename =  explode(' ', $filename);
                   $filename = $arrayFilename[1];
                }
                $pos = strpos($filename, '1000APOVE40000120');
                if($pos !== false){
                    if(strlen($filename) > 25 ){
                        array_push($filesFound,trim($filename));   
                    }
                }

            }

        });
        
        return $filesFound;
    }
    
    protected function getList1GfmasFolderOUT(){
        $commands  = [
            'cd /batch/1GFMAS/OUT',
            'ls -lrt *.GPG',
            'exit',
        ];
        $filesFound = array();
        SSH::into('portal')->run($commands, function($line) use (&$filesFound)  {
            $data = $line.PHP_EOL;
            $arrayData  = (explode("\n",$data));
            foreach($arrayData  as $str){
                $pos = strpos($str, '.GPG');
                if($pos !== false){
                    $checkingFileGPG = substr($str, -3);
                    if($checkingFileGPG && $checkingFileGPG == 'GPG'){
                        $filename = trim(substr($str, -33)); 
                        $checkSpaceExist = strpos($filename, ' ');
                        if($checkSpaceExist !== false){
                           $arrayFilename =  explode(' ', $filename);
                           $filename = $arrayFilename[1];
                        }
                        array_push($filesFound,trim($filename)); 
                    }
                }
            }
        });
        
        return $filesFound;
    }
    
    protected function getList1GfmasFolderIN(){
        $commands  = [
            'cd /batch/1GFMAS/IN',
            'ls -lrt *.GPG',
            'exit',
        ];
        $filesFound = array();
        SSH::into('portal')->run($commands, function($line) use (&$filesFound)  {
            $data = $line.PHP_EOL;
            $arrayData  = (explode("\n",$data));
            foreach($arrayData  as $str){
                $pos = strpos($str, '.GPG');
                if($pos !== false){
                    $filename = trim(substr($str, -33)); 
                    $checkSpaceExist = strpos($filename, ' ');
                    if($checkSpaceExist !== false){
                       $arrayFilename =  explode(' ', $filename);
                       $filename = $arrayFilename[1];
                    }
                    array_push($filesFound,trim($filename));  
                }
            }
        });
        
        return $filesFound;
    }
    
    protected function getList1GfmasServerFolderIN(){
       
        $data = array();
        $commands  = [
            "echo 'ls -lr' | sftp -oPort=2022 eperolehan@10.38.206.73:IN",
            "exit",
        ];
        SSH::into('osb')->run($commands, function($line) use (&$data)  {
            $result = $line.PHP_EOL;
            $arrayData  = (explode("\n",$result));
            foreach($arrayData  as $str){
                $pos = strpos($str, '.GPG');
                if($pos !== false){
                    $filename = trim(substr($str, -33)); 
                    $checkSpaceExist = strpos($filename, ' ');
                    if($checkSpaceExist !== false){
                       $arrayFilename =  explode(' ', $filename);
                       $filename = $arrayFilename[1];
                    }
                    array_push($data,trim($filename)); 
                 }
            }
        });
        return $data;
    }
    
    protected function getList1GfmasServerFolderOUT(){
       
        $data = array();
        $commands  = [
            "echo 'ls -lr' | sftp -oPort=2022 eperolehan@10.38.206.73:OUT",
            "exit",
        ];
        SSH::into('osb')->run($commands, function($line) use (&$data)  {
            $result = $line.PHP_EOL;
            $arrayData  = (explode("\n",$result));
            foreach($arrayData  as $str){
                $pos = strpos($str, '.GPG');
                if($pos !== false){
                    $filename = trim(substr($str, -33)); 
                    $checkSpaceExist = strpos($filename, ' ');
                    if($checkSpaceExist !== false){
                       $arrayFilename =  explode(' ', $filename);
                       $filename = $arrayFilename[1];
                    }
                    array_push($data,trim($filename)); 
                 }
            }
        });
        return $data;
    }
    
    
    protected function getListFiles1GFMASFolderIN(){
        $commands  = [
            'cd /batch/1GFMAS/IN',
            'find 1000APOVE40000*.GPG',
            'exit',
        ];
        $filesFound = array();
        SSH::into('portal')->run($commands, function($line) use (&$filesFound)  {
            $data = $line.PHP_EOL;
            $arrayData  = (explode("\n",$data));
            foreach($arrayData  as $str){
                $filename = trim(substr($str, -33)); 
                $checkSpaceExist = strpos($filename, ' ');
                if($checkSpaceExist !== false){
                   $arrayFilename =  explode(' ', $filename);
                   $filename = $arrayFilename[1];
                }
                $pos = strpos($filename, '1000APOVE40000120');
                if($pos !== false){
                    if(strlen($filename) > 25 ){
                        array_push($filesFound,trim($filename));   
                    }
                }

            }

        });
        
        return $filesFound;
    }

}
