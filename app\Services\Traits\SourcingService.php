<?php

namespace App\Services\Traits;

use Log;
use DB;
use Carbon\Carbon;

/**
 * Description of Profile Management PMServiceTrait
 *
 * <AUTHOR>
 */
trait SourcingService {
    
   
    protected function getListAllCommitteeMembersQT($docNo) {
         $results = DB::connection('oracle_nextgen_rpt')->select(
                " SELECT   D.QT_NO, A.COMMITTEE_ID, B.COMMITTEE_TYPE_ID, P1.PARAMETER_CODE MEMBER_CODE,
                        P2.CODE_NAME MEMBER_DESC, C.USER_ID, C.MEMBER_NAME, C.IC_PASSPORT,
                        PP1.PARAMETER_CODE ROLE_CODE, PP2.CODE_NAME ROLE_DESC
                   FROM SC_QT_COMMITTEE A,
                        SC_COMMITTEE B,
                        PM_PARAMETER P1,
                        PM_PARAMETER_DESC P2,
                        PM_PARAMETER_TYPE P3,
                        SC_COMMITTEE_MEMBER C,
                        PM_PARAMETER PP1,
                        PM_PARAMETER_DESC PP2,
                        PM_PARAMETER_TYPE PP3,
                        SC_QT D
                  WHERE A.COMMITTEE_ID = B.COMMITTEE_ID
                    AND B.COMMITTEE_TYPE_ID = P1.PARAMETER_ID
                    AND P1.PARAMETER_TYPE = P3.PARAMETER_TYPE
                    AND P1.PARAMETER_ID = P2.PARAMETER_ID
                    AND P2.LANGUAGE_CODE = 'en'
                    AND B.COMMITTEE_ID = C.COMMITTEE_ID
                    AND C.MEMBER_ROLE_ID = PP1.PARAMETER_ID
                    AND PP1.PARAMETER_TYPE = PP3.PARAMETER_TYPE
                    AND PP1.PARAMETER_ID = PP2.PARAMETER_ID
                    AND PP2.LANGUAGE_CODE = 'en'
                    AND D.QT_ID = A.QT_ID
                    AND D.QT_NO = ? 
               ORDER BY A.QT_ID, B.COMMITTEE_TYPE_ID, C.MEMBER_ROLE_ID  ", array($docNo));
        return $results;
    }
    
    
    protected function getListProposalSupplierByQuotationTender($docNo) {
         $results = DB::connection('oracle_nextgen_rpt')->select(
                " SELECT A.QT_NO, C.PROPOSAL_NO, C.PROPOSAL_SUBMIT_DATE, C.MOF_NO,
                        C.SUPPLIER_NAME, DECODE (C.IS_SUBMITTED, 0, 'NO', 1, 'YES') SUBMITTED
                   FROM SC_QT A, SC_QT_SUPPLIER B, SC_QT_PROPOSAL C
                 WHERE A.QT_ID = B.QT_ID
                    AND B.QT_SUPPLIER_ID = C.QT_SUPPLIER_ID
                    AND A.QT_NO = ?  ", array($docNo));
        return $results;
    }
    
    protected function getListTaklimatOrLawatanTapakByQuotationTender($docNo) {
         $results = DB::connection('oracle_nextgen_rpt')->select(
                " SELECT   a.qt_no ,s.company_name,e.mof_no, g.disqualified_stage, 
                        DECODE (c.bsv_type, 'B', 'Briefing', 'S', 'Site Visit', 'A', 'B and SV') as BSV_TYPE, d.is_attended,
                        d.is_post_registered, d.qt_approval_request_id,f.approver_action_id
                   FROM sc_qt a, 
                        sc_qt_bsv b, 
                        sc_qt_bsv_dtl c, 
                        sc_qt_bsv_registration d, 
                        sm_mof_account e, 
                        sm_supplier s, 
                        sc_qt_supplier g, 
                        sc_qt_approval_request f 
                  WHERE a.qt_id = b.qt_id 
                    AND a.qt_id = g.qt_id 
                    AND b.qt_bsv_id = c.qt_bsv_id 
                    AND c.qt_bsv_dtl_id = d.qt_bsv_dtl_id 
                    AND d.supplier_id = e.supplier_id 
                    AND g.supplier_id = e.supplier_id 
                    AND g.supplier_id = s.supplier_id 
                    AND d.qt_approval_request_id = f.qt_approval_request_id(+) 
                    AND a.qt_no = ?  ", array($docNo));
        return $results;
    }
    

}
