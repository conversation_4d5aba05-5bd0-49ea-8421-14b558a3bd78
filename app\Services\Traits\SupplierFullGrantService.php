<?php

namespace App\Services\Traits;

use Log;
use DB;
use App\Services\EPService;
use Carbon\Carbon;

/**
 * This service will connect user has fully grant CREATE/UPDATE/DELETE FUNCTION.
 * Please be careful using DB::connection('oracle_nextgen_fullgrant').
 * Any action function  CREATE/UPDATE/DELETE and query will impact performance and data integrity.
 *
 * <AUTHOR>
 */
trait SupplierFullGrantService {


    /**
     * Get Query for  Supplier Users Info.
     * @param type $applId
     * @param type $personnelId
     * @return type
     */
    public function getSMSupplierUsersDetailsByPersonnel($applId, $personnelId) {
        /*
         * Sample Query test to Oracle Query

         */
        $query = DB::connection('oracle_nextgen_fullgrant')->table('SM_PERSONNEL as P');
        $query->join('SM_SUPPLIER as S', 'P.APPL_ID', '=', 'S.LATEST_APPL_ID');
        $query->leftJoin('PM_USER as U', 'P.USER_ID', '=', 'U.USER_ID');
        $query->leftJoin('PM_LOGIN_HISTORY as LH', 'U.USER_ID', '=', 'LH.USER_ID');
        $query->leftJoin('SM_MOF_ACCOUNT as MA', 'S.SUPPLIER_ID', '=', 'MA.SUPPLIER_ID');

        if ($applId != null) {
            $query->where('P.APPL_ID', $applId);
        }
        if ($personnelId != null) {
            $query->where('P.PERSONNEL_ID', $personnelId);
        }
        // SM_PERSONNEL has REV_NO, need to get latest one
        $query->where('P.REV_NO', DB::raw("(select max(per.rev_no) from SM_PERSONNEL per WHERE per.APPL_ID=P.APPL_ID and per.IDENTIFICATION_NO = P.IDENTIFICATION_NO)"));
        
        $query->select('U.USER_ID', 'U.LOGIN_ID', 'U.USER_NAME AS FULLNAME', 'U.NATIONALITY_ID', 'U.IDENTIFICATION_TYPE_ID');
        $query->addSelect('U.ORG_TYPE_ID', 'U.IDENTIFICATION_NO', 'U.DESIGNATION', 'U.EMAIL', 'U.RECORD_STATUS AS U_RECORD_STATUS');
        $query->addSelect('U.CREATED_DATE', 'U.CHANGED_DATE', 'U.MOBILE_COUNTRY', 'U.MOBILE_AREA', 'U.MOBILE_NO');
        $query->addSelect('U.PHONE_COUNTRY', 'U.PHONE_AREA', 'U.PHONE_NO', 'U.FAX_COUNTRY', 'U.FAX_AREA', 'U.FAX_NO', 'U.SALUTATION_ID');
        $query->addSelect('P.PERSONNEL_ID', 'P.RECORD_STATUS AS P_RECORD_STATUS', 'P.TITLE_ID', 'P.NAME as P_NAME', 'P.IDENTIFICATION_NO as P_IDENTIFICATION_NO', 'P.NATIONALITY_ID as P_NATIONALITY_ID', 'U.IDENTIFICATION_TYPE_ID as P_IDENTIFICATION_TYPE_ID');
        $query->addSelect('P.APPL_ID','P.DESIGNATION as P_DESIGNATION', 'P.IS_SOFTCERT as P_IS_SOFTCERT', 'P.EP_ROLE as P_EP_ROLE');
        $query->addSelect('P.CREATED_DATE as P_CREATED_DATE', 'P.CHANGED_DATE as P_CHANGED_DATE', 'P.REV_NO as P_REV_NO');
        $query->addSelect('P.IS_AUTHORIZED', 'P.IS_CONTACT_PERSON', 'P.IS_CONTRACT_SIGNER', 'P.IS_EQUITY_OWNER', 'P.IS_MGT', 'P.IS_DIRECTOR', 'P.IS_BUMI as P_IS_BUMI');
        $query->addSelect('P.PHONE_COUNTRY as P_PHONE_COUNTRY', 'P.PHONE_AREA as P_PHONE_AREA', 'P.PHONE_NO as P_PHONE_NO', 'P.MOBILE_COUNTRY as P_MOBILE_COUNTRY', 'P.MOBILE_AREA as P_MOBILE_AREA', 'P.MOBILE_NO as P_MOBILE_NO', 'P.EMAIL as P_EMAIL');
        $query->addSelect('S.SUPPLIER_ID', 'S.COMPANY_NAME', 'S.ESTABLISH_DATE', 'S.EP_NO', 'S.REG_NO', 'S.BUSINESS_TYPE', 'S.STATUS_ID as S_STATUS_ID', 'S.RECORD_STATUS as S_RECORD_STATUS', 'S.CREATED_DATE as S_CREATED_DATE', 'S.CHANGED_DATE as S_CHANGED_DATE');
        $query->addSelect('S.IS_BUMI', 'S.IS_CATALOGUE_UPLOADED', 'S.LATEST_APPL_ID');
        $query->addSelect('MA.MOF_NO', 'MA.EFF_DATE as MA_EFF_DATE', 'MA.EXP_DATE as MA_EXP_DATE', 'MA.RECORD_STATUS AS MA_RECORD_STATUS');
        $query->addSelect('LH.LOGIN_DATE');
        $query->orderBy('P.EP_ROLE');
        $data = array(
            "sql" => $query->toSql(),
            "parameter" => $query->getBindings()
        );

        //Log::debug(self::class . ' :: ' . __FUNCTION__ . ' >> SQL   :   ' . json_encode($data));
        return $query->first();
    }
    
    /**
     * Get Query for  Supplier Users Info.
     * @param type $applId
     * @param type $personnelId
     * @return type
     */
    public function getSMSupplierUsersDetailsInProgressApplByPersonnel($applId, $personnelId) {
        /*
         * Sample Query test to Oracle Query

         */
        $query = DB::connection('oracle_nextgen_fullgrant')->table('SM_PERSONNEL as P');
        $query->join('SM_APPL as A', 'P.APPL_ID', '=', 'A.APPL_ID');
        $query->join('SM_SUPPLIER as S', 'A.SUPPLIER_ID', '=', 'S.SUPPLIER_ID');
        $query->leftJoin('PM_USER as U', 'P.USER_ID', '=', 'U.USER_ID');
        $query->leftJoin('PM_LOGIN_HISTORY as LH', 'U.USER_ID', '=', 'LH.USER_ID');
        $query->leftJoin('SM_MOF_ACCOUNT as MA', 'S.SUPPLIER_ID', '=', 'MA.SUPPLIER_ID');

        if ($applId != null) {
            $query->where('P.APPL_ID', $applId);
        }
        if ($personnelId != null) {
            $query->where('P.PERSONNEL_ID', $personnelId);
        }
        // SM_PERSONNEL has REV_NO, need to get latest one
        $query->where('P.REV_NO', DB::raw("(select max(per.rev_no) from SM_PERSONNEL per WHERE per.APPL_ID=P.APPL_ID and per.IDENTIFICATION_NO = P.IDENTIFICATION_NO)"));
        
        $query->select('U.USER_ID', 'U.LOGIN_ID', 'U.USER_NAME AS FULLNAME', 'U.NATIONALITY_ID', 'U.IDENTIFICATION_TYPE_ID');
        $query->addSelect('U.ORG_TYPE_ID', 'U.IDENTIFICATION_NO', 'U.DESIGNATION', 'U.EMAIL', 'U.RECORD_STATUS AS U_RECORD_STATUS');
        $query->addSelect('U.CREATED_DATE', 'U.CHANGED_DATE', 'U.MOBILE_COUNTRY', 'U.MOBILE_AREA', 'U.MOBILE_NO');
        $query->addSelect('U.PHONE_COUNTRY', 'U.PHONE_AREA', 'U.PHONE_NO', 'U.FAX_COUNTRY', 'U.FAX_AREA', 'U.FAX_NO', 'U.SALUTATION_ID');
        $query->addSelect('P.PERSONNEL_ID', 'P.RECORD_STATUS AS P_RECORD_STATUS', 'P.TITLE_ID', 'P.NAME as P_NAME', 'P.IDENTIFICATION_NO as P_IDENTIFICATION_NO', 'P.NATIONALITY_ID as P_NATIONALITY_ID', 'U.IDENTIFICATION_TYPE_ID as P_IDENTIFICATION_TYPE_ID');
        $query->addSelect('P.APPL_ID','P.DESIGNATION as P_DESIGNATION', 'P.IS_SOFTCERT as P_IS_SOFTCERT', 'P.EP_ROLE as P_EP_ROLE');
        $query->addSelect('P.CREATED_DATE as P_CREATED_DATE', 'P.CHANGED_DATE as P_CHANGED_DATE', 'P.REV_NO as P_REV_NO');
        $query->addSelect('P.IS_AUTHORIZED', 'P.IS_CONTACT_PERSON', 'P.IS_CONTRACT_SIGNER', 'P.IS_EQUITY_OWNER', 'P.IS_MGT', 'P.IS_DIRECTOR', 'P.IS_BUMI as P_IS_BUMI');
        $query->addSelect('P.PHONE_COUNTRY as P_PHONE_COUNTRY', 'P.PHONE_AREA as P_PHONE_AREA', 'P.PHONE_NO as P_PHONE_NO', 'P.MOBILE_COUNTRY as P_MOBILE_COUNTRY', 'P.MOBILE_AREA as P_MOBILE_AREA', 'P.MOBILE_NO as P_MOBILE_NO', 'P.EMAIL as P_EMAIL');
        $query->addSelect('S.SUPPLIER_ID', 'S.COMPANY_NAME', 'S.ESTABLISH_DATE', 'S.EP_NO', 'S.REG_NO', 'S.BUSINESS_TYPE', 'S.STATUS_ID as S_STATUS_ID', 'S.RECORD_STATUS as S_RECORD_STATUS', 'S.CREATED_DATE as S_CREATED_DATE', 'S.CHANGED_DATE as S_CHANGED_DATE');
        $query->addSelect('S.IS_BUMI', 'S.IS_CATALOGUE_UPLOADED', 'S.LATEST_APPL_ID');
        $query->addSelect('MA.MOF_NO', 'MA.EFF_DATE as MA_EFF_DATE', 'MA.EXP_DATE as MA_EXP_DATE', 'MA.RECORD_STATUS AS MA_RECORD_STATUS');
        $query->addSelect('LH.LOGIN_DATE');
        $query->orderBy('P.EP_ROLE');
        $data = array(
            "sql" => $query->toSql(),
            "parameter" => $query->getBindings()
        );
        //dump($data);
        //Log::debug(self::class . ' :: ' . __FUNCTION__ . ' >> SQL   :   ' . json_encode($data));
        return $query->first();
    }
    
    /**
     * Get list info for process current application for supplier 
     * @param type $supplierID
     * @return type
     */
    public function getInProgressWorkFlowSupplierProcessUsingFullGrant($supplierID,$identification_no) {

        $results = DB::connection('oracle_nextgen_fullgrant')->select(
                " SELECT   wf.is_current, sup.supplier_id, sup.company_name, 
                    CASE
                       WHEN appl.supplier_type IN ('K', 'J')
                          THEN 'MOF'
                       ELSE 'Basic'
                    END AS supplier_type,
                    sup.ep_no, appl.appl_id,
                    (select ppd.code_name from pm_parameter pp , pm_parameter_desc ppd where 
                    pp.PARAMETER_ID = ppd.PARAMETER_ID and ppd.LANGUAGE_CODE = 'en'  
                    and ppd.RECORD_STATUS = 1 
                    and pp.RECORD_STATUS = 1 
                    and pp.PARAMETER_TYPE = 'AT' 
                    and pp.PARAMETER_CODE = appl.appl_type) appl_type , 
                    appl.appl_no,appl.is_active_appl,appl.changed_date,
                    (SELECT usr.user_name
                       FROM pm_user usr
                      WHERE usr.user_id = appl.created_by) appl_created_by,
                    appl.created_date AS appl_created_date, appl.changed_date AS appl_change_date,
                    wf.created_date AS wf_created_date, wf.changed_date AS wf_changed_date, appl.is_questionnaire,
                    appl.is_resubmit,
                    appl.status_id  appl_status_id,
                    (SELECT status_name
                       FROM pm_status_desc
                      WHERE status_id = appl.status_id
                        AND language_code = 'en')  appl_status,
                    wf.status_id AS wf_status_id,
                    (SELECT status_name
                       FROM pm_status_desc
                      WHERE status_id = wf.status_id
                        AND language_code = 'en') AS wf_Status,
                    sp.personnel_id 
               FROM sm_supplier sup,
                    sm_personnel sp,
                    sm_appl appl,
                    sm_workflow_status wf
              WHERE sup.supplier_id = appl.supplier_id
                AND sp.appl_id = appl.appl_id 
                AND appl.appl_id = wf.doc_id (+)
                AND sup.SUPPLIER_ID = ? 
                AND sp.identification_no = ? 
                AND appl.IS_ACTIVE_APPL = 1 
           ORDER BY appl.appl_id DESC,wf.created_date DESC, wf.is_current DESC ", array($supplierID,$identification_no));

        return $results;
    }
    
    /**
     * UPDATE SM PERSONNEL
     * @param type $personnelId
     * @param type $fields
     */
    public function updateSMPersonnel($personnelId,$fields) {
        $query =  DB::connection('oracle_nextgen_fullgrant')
                ->table('SM_PERSONNEL')
                ->where('personnel_id', $personnelId);
        $query->update($fields);
        
        $logQuery = collect([]);
        $logQuery->put('table',$query->from);      
        $logQuery->put('where',$query->wheres);
        $logQuery->put('update',$fields);
        return $logQuery;
    }
    
    
    /*
     * Return single object
     * Parameters personnel_id,identification_no,appl_id
     */
    public function getSMPersonnelDetailsOnly($parameters) {
        
        $query =  DB::connection('oracle_nextgen_fullgrant')
                ->table('SM_PERSONNEL');
        if($parameters->has('personnel_id')){
            $query->where('personnel_id', $parameters->get('personnel_id'));
        }
        if($parameters->has('identification_no')){
            dump('identification_no: '.$parameters->get('identification_no'));
            $query->where('identification_no', $parameters->get('identification_no'));
        }
        if($parameters->has('appl_id')){
            dump('appl_id: '.$parameters->get('appl_id'));
            $query->where('appl_id', $parameters->get('appl_id'));
        }
        $query->orderBy('rev_no','desc');
        dump($query->toSql());
        dump($query->wheres);
        $query->first();
    }
    
    
}
