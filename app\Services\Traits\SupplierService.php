<?php

namespace App\Services\Traits;

use Log;
use DB;
use App\Services\EPService;
use Carbon\Carbon;

/**
 * Description of Profile Management PMServiceTrait
 *
 * <AUTHOR>
 */
trait SupplierService {

    /**
     * Get list of softcert request (with cert if exist) each supplier & user
     * @param type $epNo
     * @param type $userId
     * @return type
     */
    public function getListSoftCertRequest($epNo, $userId) {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SOFTCERT_REQUEST as PSR');
        $query->leftJoin('PM_DIGI_CERT as PDC', 'PSR.SOFTCERT_REQUEST_ID', '=', 'PDC.SOFTCERT_REQUEST_ID');
        $query->where('PSR.EP_NO', $epNo);
        $query->where('PSR.USER_ID', $userId);
        $query->select('PSR.SOFTCERT_REQUEST_ID', 'PSR.USER_ID', 'PSR.EP_NO', 'PSR.RECORD_STATUS', 'PSR.CREATED_DATE', 'PSR.CHANGED_DATE');
        $query->addSelect('PSR.IS_FREE', 'PSR.RESPONSE_STATUS', 'PSR.REQUEST_MODE', 'PSR.REMARK', 'PSR.REASON_CODE');
        $query->addSelect('PDC.RECORD_STATUS AS PDC_RECORD_STATUS', 'PDC.CERT_SERIAL_NO', 'PDC.VALID_FROM', 'PDC.VALID_TO', 'PDC.ORN', 'PDC.PRN', 'PDC.CERT_MODE', 'PDC.CERT_ISSUER', 'PDC.CREATED_DATE as PDC_CREATED_DATE', 'PDC.CHANGED_DATE as PDC_CHANGED_DATE');
        $query->orderBy('PSR.CREATED_DATE', 'desc');
        $result = $query->get();
        if (count($result) > 0) {
            return $result;
        }
        return array();
    }

    /**
     * Gte eP No in SAP_VENDOR_CODE
     * @return String 
     */
    public function getEpNoBySapVendorCode($sapVendorCode) {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SAP_VENDOR_CODE as A');
        $query->where('A.SAP_VENDOR_CODE', $sapVendorCode);
        $query->where('A.RECORD_STATUS', 1);
        $result = $query->first();
        if (count($result) > 0) {
            return $result->ep_no;
        }
        return null;
    }
    
    /**
     * Gte basic info for supplier.
     * @param $search
     * @return String  eP NO
     */
    public function getEpNoSmSupplier($search) {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SUPPLIER as A');
        $query->whereNotNull('A.EP_NO');
        $query->where(function ($query) use ($search) {
                    $query->orWhere('A.REG_NO', strtoupper($search))
                          ->orWhere('A.COMPANY_NAME', strtoupper($search))
                          ->orWhere('A.LATEST_APPL_ID', intval($search));
                });
        $query->orderBy('A.RECORD_STATUS','asc');
        $result = $query->get();
        if (count($result) > 0) {
            foreach ($result as $obj){
                if($obj->record_status == 1){
                    return $obj->ep_no;
                }
            }
            return $result[0]->ep_no;
        }
        return null;
    }
    
    /**
     * Gte basic info for supplier.
     * @param $search
     * @return String  eP NO
     */
    public function getEpNoSmSupplierByApplNo($search) {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SUPPLIER as A');
        $query->join('SM_APPL as B', 'A.SUPPLIER_ID', '=', 'B.SUPPLIER_ID');
        $query->whereNotNull('A.EP_NO');
        $query->where(function ($query) use ($search) {
                    $query->orWhere('B.APPL_NO', strtoupper($search))
                          ->orWhere('B.APPL_ID', intval($search));
                });
        $query->orderBy('A.RECORD_STATUS','asc');
        $result = $query->get();
        if (count($result) > 0) {
            foreach ($result as $obj){
                if($obj->record_status == 1){
                    return $obj->ep_no;
                }
            }
            return $result[0]->ep_no;
        }
        return null;
    }
    
    /**
     * Gte basic info for supplier.
     * @param type $applID
     * @return type
     */
    public function getBasicSupplierInfo($applID) {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_COMPANY_BASIC as SCB');
        $query->leftJoin('SM_COMPANY_ADDRESS as SCD', 'SCB.COMPANY_BASIC_ID', '=', 'SCD.COMPANY_BASIC_ID');
        $query->leftJoin('SM_ADDRESS as SA', 'SCD.ADDRESS_ID', '=', 'SA.ADDRESS_ID');
        $query->leftJoin('PM_DIVISION as PD', 'SA.DIVISION_ID', '=', 'PD.DIVISION_ID');
        $query->leftJoin('PM_CITY as PCT', 'SA.CITY_ID', '=', 'PCT.CITY_ID');
        $query->leftJoin('PM_DISTRICT as PDR', 'SA.DISTRICT_ID', '=', 'PDR.DISTRICT_ID');
        $query->leftJoin('PM_STATE as PS', 'SA.STATE_ID', '=', 'PS.STATE_ID');
        $query->leftJoin('PM_COUNTRY as PCR', 'SA.COUNTRY_ID', '=', 'PCR.COUNTRY_ID');
        $query->where('SCB.APPL_ID', $applID);
        $query->select('SCB.COMPANY_BASIC_ID', 'SCB.APPL_ID', 'SCB.COMPANY_NAME');
        $query->addSelect('SCB.IS_WITH_FEDERAL', 'SCB.IS_WITH_STATE', 'SCB.IS_WITH_STATUTORY', 'SCB.IS_WITH_GLC', 'SCB.IS_WITH_OTHERS', 'SCB.RECORD_STATUS');
        $query->addSelect('SCB.PHONE_COUNTRY', 'SCB.PHONE_AREA', 'SCB.PHONE_NO', 'SCB.FAX_COUNTRY', 'SCB.FAX_AREA', 'SCB.FAX_NO');
        $query->addSelect('SA.ADDRESS_ID', 'SA.ADDRESS_1', 'SA.ADDRESS_2', 'SA.ADDRESS_3', 'SA.POSTCODE');
        $query->addSelect('PD.DIVISION_NAME', 'PCT.CITY_NAME', 'PDR.DISTRICT_NAME', 'PS.STATE_NAME', 'PCR.COUNTRY_NAME');
        $query->orderBy('SCB.REV_NO', 'desc');
        $result = $query->first();
        if (count($result) > 0) {
            return $result;
        }
        return array();
    }

    /**
     * Get Application No (Approved)
     * @param type $supplierID
     * @param type $applID
     * @return type
     */
    public function getListAttachmentRejectOrCancel($applID) {

        $query = DB::connection('oracle_nextgen_rpt')->table('sm_appl as appl');
        $query->join('sm_attachment as att', 'appl.appl_id', '=', 'att.doc_id');
        $query->where('appl.appl_id', $applID);
        $query->select('appl.appl_id','appl.supplier_id','appl.appl_type','appl.appl_no');
        $query->addSelect('att.attachment_id','att.doc_type','att.file_name','att.file_desc','att.file_path','att.created_date as att_created_date' );
        return $query->get();

    }
    
    /**
     * Get Application No (Approved)
     * @param type $supplierID
     * @param type $applID
     * @return type
     */
    public function getListRemarksRejectOrCancel($applID) {

        $query = DB::connection('oracle_nextgen_rpt')->table('sm_appl as appl');
        $query->join('sm_remark as rem', 'appl.appl_id', '=', 'rem.doc_id');
        $query->where('appl.appl_id', $applID);
        $query->select('appl.appl_id','appl.supplier_id','appl.appl_type','appl.appl_no');
        $query->addSelect('rem.remark_id','rem.doc_type','rem.doc_id','rem.remark','rem.created_date as rem_created_date' );
        return $query->get();

    }
    
    /**
     * Get Application No (Approved)
     * @param type $supplierID
     * @param type $applID
     * @return type
     */
    public function getWorkFlowSupplierProcess($supplierID, $applID) {

        $results = DB::connection('oracle_nextgen_rpt')->select(
                " SELECT   wf.is_current, sup.supplier_id, sup.company_name,
                    CASE
                       WHEN appl.supplier_type IN ('K', 'J')
                          THEN 'MOF'
                       ELSE 'Basic'
                    END AS supplier_type,
                    sup.ep_no, appl.appl_id,
                    appl.record_status,
                    appl.appl_no, 
                    (select ppd.code_name from pm_parameter pp , pm_parameter_desc ppd where 
                    pp.PARAMETER_ID = ppd.PARAMETER_ID and ppd.LANGUAGE_CODE = 'en'  
                    and ppd.RECORD_STATUS = 1 
                    and pp.RECORD_STATUS = 1 
                    and pp.PARAMETER_TYPE = 'AT' 
                    and pp.PARAMETER_CODE = appl.appl_type) appl_type , 
                    (SELECT usr.user_name
                       FROM pm_user usr
                      WHERE usr.user_id = appl.created_by) appl_created_by,
                    appl.created_date AS appl_created_date, appl.changed_date AS appl_change_date,
                    wf.created_date AS wf_created_date, wf.changed_date AS wf_changed_date, appl.is_questionnaire,
                    appl.status_id  appl_status_id,
                    (SELECT status_name
                       FROM pm_status_desc
                      WHERE status_id = appl.status_id
                        AND language_code = 'en')  appl_status,
                    wf.status_id AS wf_status_id,
                    (SELECT status_name
                       FROM pm_status_desc
                      WHERE status_id = wf.status_id
                        AND language_code = 'en') AS wf_Status
               FROM sm_supplier sup,
                    sm_appl appl,
                    sm_workflow_status wf
              WHERE sup.supplier_id = appl.supplier_id
                AND appl.appl_id = wf.doc_id
                AND sup.SUPPLIER_ID = ?
                AND appl.appl_id = ?
           ORDER BY wf.created_date DESC, wf.is_current DESC ", array($supplierID, $applID));

        return $results;
    }
    
    /**
     * Get list info for process current application for supplier 
     * @param type $supplierID
     * @return type
     */
    public function getInProgressWorkFlowSupplierProcess($supplierID) {

        $results = DB::connection('oracle_nextgen_rpt')->select(
                " SELECT   wf.is_current, sup.supplier_id, sup.company_name, 
                    CASE
                       WHEN appl.supplier_type IN ('K', 'J')
                          THEN 'MOF'
                       ELSE 'Basic'
                    END AS supplier_type,
                    sup.ep_no, appl.appl_id,
                    (select ppd.code_name from pm_parameter pp , pm_parameter_desc ppd where 
                    pp.PARAMETER_ID = ppd.PARAMETER_ID and ppd.LANGUAGE_CODE = 'en'  
                    and ppd.RECORD_STATUS = 1 
                    and pp.RECORD_STATUS = 1 
                    and pp.PARAMETER_TYPE = 'AT' 
                    and pp.PARAMETER_CODE = appl.appl_type) appl_type , 
                    appl.appl_no,appl.is_active_appl,appl.changed_date,
                    (SELECT usr.user_name
                       FROM pm_user usr
                      WHERE usr.user_id = appl.created_by) appl_created_by,
                    appl.created_date AS appl_created_date, appl.changed_date AS appl_change_date,
                    wf.created_date AS wf_created_date, wf.changed_date AS wf_changed_date, appl.is_questionnaire,
                    appl.is_resubmit,
                    appl.status_id  appl_status_id,
                    (SELECT status_name
                       FROM pm_status_desc
                      WHERE status_id = appl.status_id
                        AND language_code = 'en')  appl_status,
                    wf.status_id AS wf_status_id,
                    (SELECT status_name
                       FROM pm_status_desc
                      WHERE status_id = wf.status_id
                        AND language_code = 'en') AS wf_Status
               FROM sm_supplier sup,
                    sm_appl appl,
                    sm_workflow_status wf
              WHERE sup.supplier_id = appl.supplier_id
                AND appl.appl_id = wf.doc_id (+)
                AND sup.SUPPLIER_ID = ?
                AND appl.IS_ACTIVE_APPL = 1 
           ORDER BY wf.created_date DESC, wf.is_current DESC ", array($supplierID));

        return $results;
    }

    /**
     * Get activation link. This link still not unique by personnel. At least 99% tepat if email is not same others personnel.
     * @param type $companyName
     * @param type $ssmNO
     * @param type $icno
     * @param type $email
     * @return type
     */
    public function getActivationLink($companyName, $ssmNO, $icno, $email) {
        $query = DB::connection('oracle_nextgen_rpt')->table('PM_NOTIFY_MESSAGE as P');
        $query->where('P.EMAIL_LIST', $email);
        $query->where('P.CONTENT_PARAM', 'like', $companyName . '|||' . $ssmNO . '|||%');
        $query->orderBy('CREATED_DATE', 'desc');
        return $query->first();
    }

    /**
     * Get trustgate info from List Migration Trustgate
     * @param type $mofNo
     * @param type $icNo
     * @return type
     */
    protected function getTrustgateDetailInfo($mofNo, $icNo) {
        $query = DB::table('softcert_trustgate_mig as stm');
        $query->where('stm.mof_no', $mofNo);
        $query->where('stm.ic_no', $icNo);
        return $query->first();
    }

    /**
     * Get Query for  Supplier Users Info. Not include roles.
     * @param type $dateStart
     * @param type $dateEnd
     * @param type $take
     * @param type $nextSkip
     * @return type
     */
    public function getSMSupplierUsersActiveByICNO($icno) {
        /*
         * Sample Query test to Oracle Query

         */
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_PERSONNEL as P');
        $query->join('SM_SUPPLIER as S', 'P.APPL_ID', '=', 'S.LATEST_APPL_ID');
        $query->leftJoin('PM_USER as U', 'P.USER_ID', '=', 'U.USER_ID');
        $query->leftJoin('SM_MOF_ACCOUNT as MA', 'S.SUPPLIER_ID', '=', 'MA.SUPPLIER_ID');
        $query->leftJoin('PM_LOGIN_HISTORY as LH', 'U.USER_ID', '=', 'LH.USER_ID');
        $query->where('P.IDENTIFICATION_NO', $icno);
        // SM_PERSONNEL has REV_NO, need to get latest one
        $query->where('P.REV_NO', DB::raw("(select max(rev_no) from SM_PERSONNEL WHERE APPL_ID=P.APPL_ID and IDENTIFICATION_NO = P.IDENTIFICATION_NO)"));
        
        $query->select('U.USER_ID', 'U.LOGIN_ID', 'U.USER_NAME AS FULLNAME', 'U.NATIONALITY_ID', 'U.IDENTIFICATION_TYPE_ID');
        $query->addSelect('U.ORG_TYPE_ID', 'U.IDENTIFICATION_NO', 'U.DESIGNATION', 'U.EMAIL', 'U.RECORD_STATUS AS U_RECORD_STATUS');
        $query->addSelect('U.CREATED_DATE', 'U.CHANGED_DATE', 'U.MOBILE_COUNTRY', 'U.MOBILE_AREA', 'U.MOBILE_NO');
        $query->addSelect('U.PHONE_COUNTRY', 'U.PHONE_AREA', 'U.PHONE_NO', 'U.FAX_COUNTRY', 'U.FAX_AREA', 'U.FAX_NO', 'U.SALUTATION_ID');
        $query->addSelect('P.PERSONNEL_ID', 'P.RECORD_STATUS AS P_RECORD_STATUS', 'P.TITLE_ID', 'P.NAME as P_NAME', 'P.IDENTIFICATION_NO as P_IDENTIFICATION_NO', 'P.NATIONALITY_ID as P_NATIONALITY_ID', 'U.IDENTIFICATION_TYPE_ID as P_IDENTIFICATION_TYPE_ID', 'P.IS_EQUITY_OWNER as P_IS_EQUITY_OWNER');
        $query->addSelect('P.DESIGNATION as P_DESIGNATION', 'P.IS_SOFTCERT as P_IS_SOFTCERT', 'P.EP_ROLE as P_EP_ROLE');
        $query->addSelect('P.CREATED_DATE as P_CREATED_DATE', 'P.CHANGED_DATE as P_CHANGED_DATE');
        $query->addSelect('P.IS_AUTHORIZED', 'P.IS_CONTACT_PERSON', 'P.IS_CONTRACT_SIGNER', 'P.IS_EQUITY_OWNER', 'P.IS_MGT', 'P.IS_DIRECTOR', 'P.IS_BUMI');
        $query->addSelect('P.PHONE_COUNTRY as P_PHONE_COUNTRY', 'P.PHONE_AREA as P_PHONE_AREA', 'P.PHONE_NO as P_PHONE_NO', 'P.MOBILE_COUNTRY as P_MOBILE_COUNTRY', 'P.MOBILE_AREA as P_MOBILE_AREA', 'P.MOBILE_NO as P_MOBILE_NO', 'P.EMAIL as P_EMAIL');
        $query->addSelect('S.SUPPLIER_ID', 'S.COMPANY_NAME', 'S.ESTABLISH_DATE', 'S.EP_NO', 'S.REG_NO', 'S.BUSINESS_TYPE', 'S.STATUS_ID as S_STATUS_ID', 'S.RECORD_STATUS as S_RECORD_STATUS', 'S.CREATED_DATE as S_CREATED_DATE', 'S.CHANGED_DATE as S_CHANGED_DATE');
        $query->addSelect('S.BUSINESS_TYPE', 'S.IS_BUMI', 'S.IS_CATALOGUE_UPLOADED', 'S.LATEST_APPL_ID');
        $query->addSelect('MA.MOF_NO', 'MA.EXP_DATE as MA_EXP_DATE', 'MA.RECORD_STATUS AS MA_RECORD_STATUS');
        $query->addSelect('LH.LOGIN_DATE');
        $query->orderBy('P.EP_ROLE');
        $data = array(
            "sql" => $query->toSql(),
            "parameter" => $query->getBindings()
        );

        //Log::debug(self::class . ' :: ' . __FUNCTION__ . ' >> SQL   :   ' . json_encode($data));
        return $query->get();
    }

    /**
     * populate $data put another fields
     * @param type $data  = refer $dataObj from query selection
     */
    public function populatePersonnelUserData(&$data){
        $data->is_activate_key = false;
        if (strlen($data->p_ep_role) > 0  && strlen($data->p_email) > 0 && $data->p_record_status == 8){
            $recNotify = $this->getActivationLink($data->company_name, $data->reg_no, $data->p_identification_no, $data->p_email);
            if($recNotify){
                $data->content_param = $recNotify->content_param;
                $contents = explode("|||", $recNotify->content_param);
                $data->link = $contents[3];
                $data->activation_key = $contents[2];
                $data->is_sent = $recNotify->is_sent;
                $data->activation_changed_date = $recNotify->changed_date;
                $data->is_activate_key = true;
            }
        }

        $data->u_record_status = $data->u_record_status. ' - '.EPService::$RECORD_STATUS[$data->u_record_status];
        $data->s_record_status = $data->s_record_status. ' - '.EPService::$RECORD_STATUS[$data->s_record_status];
        $data->ma_record_status = $data->ma_record_status. ' - '.EPService::$RECORD_STATUS[$data->ma_record_status];

        $data->p_is_softcert = $data->p_is_softcert. ' - '.EPService::$SOFTCERT_STATUS[$data->p_is_softcert];
        $data->p_record_status = $data->p_record_status. ' - '.EPService::$RECORD_STATUS[$data->p_record_status];

        $data->is_equity_owner = EPService::$YES_NO[$data->is_equity_owner];
        $data->is_authorized = EPService::$YES_NO[$data->is_authorized];
        $data->is_contact_person = EPService::$YES_NO[$data->is_contact_person];
        $data->is_contract_signer= EPService::$YES_NO[$data->is_contract_signer];
        $data->is_mgt= EPService::$YES_NO[$data->is_mgt];
        $data->is_director= EPService::$YES_NO[$data->is_director];
        $data->is_bumi= EPService::$YES_NO[$data->is_bumi];

        /** Get Softcert Request && (cert info if exist) **/
        $listSoftCert = $this->getListSoftCertRequest($data->ep_no, $data->user_id);
        if(count($listSoftCert) > 0){
            foreach ($listSoftCert as $sCert) {
                $sCert->record_status = $sCert->record_status. ' - '.EPService::$RECORD_STATUS[$sCert->record_status];
                $sCert->is_free = EPService::$YES_NO[$sCert->is_free];

                /** Checking in OSB **/
                $sCert->is_success_SPK010 = 0;
                $sCert->is_success_SPK020 = 0;
                if($sCert->cert_serial_no == null){
                    $sCert->is_success_SPK010 = $this->checkSuccessReceiveCertSPKI($sCert->softcert_request_id, $data->p_identification_no);
                    $sCert->is_success_SPK020 = $this->checkSuccessSentSPKI($sCert->softcert_request_id, $data->p_identification_no);
                }

                /** For Trustgate in List Migration **/
                $date = Carbon::parse($sCert->created_date);
                $dateNotTrustgateMigrate = Carbon::create(2017, 12, 31);
                $sCert->is_trustgate = false;
                if($date->lte($dateNotTrustgateMigrate)){
                    $sCert->is_trustgate = true;
                    $sCert->is_trustgate_data = false;
                    $trustGateSoftData = $this->getTrustgateDetailInfo($data->mof_no, $data->identification_no);
                    if($trustGateSoftData){
                        $sCert->is_trustgate_data = true;
                        $sCert->trustgate_expired_date = $trustGateSoftData->cert_expiry_date;
                    }
                }
            }
        }
        $data->listSoftCert = $listSoftCert;

        /* Get Roles for users has Login ID */
        $roles = $this->getUserRoles($data->user_id);
        $data->roles = $roles;
    }
    
    /**
     * Get Query for  Supplier Users Info. Not include roles.
     * @param type $dateStart
     * @param type $dateEnd
     * @param type $take
     * @param type $nextSkip
     * @return type
     */
    public function getSMSupplierUsersDetailsByMofNOorEpNo($mofno, $epNo) {
        /*
         * Sample Query test to Oracle Query

         */
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_PERSONNEL as P');
        $query->join('SM_SUPPLIER as S', 'P.APPL_ID', '=', 'S.LATEST_APPL_ID');
        if ($mofno != null) {
            $query->join('SM_MOF_ACCOUNT as MA', 'S.SUPPLIER_ID', '=', 'MA.SUPPLIER_ID');
        }
        if ($epNo != null && $mofno == null) {
            $query->leftJoin('SM_MOF_ACCOUNT as MA', 'S.SUPPLIER_ID', '=', 'MA.SUPPLIER_ID');
        }
        $query->leftJoin('PM_USER as U', 'P.USER_ID', '=', 'U.USER_ID');
        $query->leftJoin('PM_LOGIN_HISTORY as LH', 'U.USER_ID', '=', 'LH.USER_ID');
        //$query->whereNotIn('P.RECORD_STATUS', [9]);
        if ($mofno != null) {
            $query->where('MA.MOF_NO', $mofno);
        }
        if ($epNo != null) {
            $query->where('S.EP_NO', $epNo);
        }
        // SM_PERSONNEL has REV_NO, need to get latest one
        $query->where('P.REV_NO', DB::raw("(select max(rev_no) from SM_PERSONNEL WHERE APPL_ID=P.APPL_ID and IDENTIFICATION_NO = P.IDENTIFICATION_NO)"));
        
        $query->select('U.USER_ID', 'U.LOGIN_ID', 'U.USER_NAME AS FULLNAME', 'U.NATIONALITY_ID', 'U.IDENTIFICATION_TYPE_ID');
        $query->addSelect('U.ORG_TYPE_ID', 'U.IDENTIFICATION_NO', 'U.DESIGNATION', 'U.EMAIL', 'U.RECORD_STATUS AS U_RECORD_STATUS');
        $query->addSelect('U.CREATED_DATE', 'U.CHANGED_DATE', 'U.MOBILE_COUNTRY', 'U.MOBILE_AREA', 'U.MOBILE_NO');
        $query->addSelect('U.PHONE_COUNTRY', 'U.PHONE_AREA', 'U.PHONE_NO', 'U.FAX_COUNTRY', 'U.FAX_AREA', 'U.FAX_NO', 'U.SALUTATION_ID');
        $query->addSelect('P.PERSONNEL_ID', 'P.RECORD_STATUS AS P_RECORD_STATUS', 'P.TITLE_ID', 'P.NAME as P_NAME', 'P.IDENTIFICATION_NO as P_IDENTIFICATION_NO', 'P.NATIONALITY_ID as P_NATIONALITY_ID', 'U.IDENTIFICATION_TYPE_ID as P_IDENTIFICATION_TYPE_ID');
        $query->addSelect('P.APPL_ID','P.DESIGNATION as P_DESIGNATION', 'P.IS_SOFTCERT as P_IS_SOFTCERT', 'P.EP_ROLE as P_EP_ROLE');
        $query->addSelect('P.CREATED_DATE as P_CREATED_DATE', 'P.CHANGED_DATE as P_CHANGED_DATE', 'P.REV_NO as P_REV_NO');
        $query->addSelect('P.IS_AUTHORIZED', 'P.IS_CONTACT_PERSON', 'P.IS_CONTRACT_SIGNER', 'P.IS_EQUITY_OWNER', 'P.IS_MGT', 'P.IS_DIRECTOR', 'P.IS_BUMI');
        $query->addSelect('P.PHONE_COUNTRY as P_PHONE_COUNTRY', 'P.PHONE_AREA as P_PHONE_AREA', 'P.PHONE_NO as P_PHONE_NO', 'P.MOBILE_COUNTRY as P_MOBILE_COUNTRY', 'P.MOBILE_AREA as P_MOBILE_AREA', 'P.MOBILE_NO as P_MOBILE_NO', 'P.EMAIL as P_EMAIL');
        $query->addSelect('S.SUPPLIER_ID', 'S.COMPANY_NAME', 'S.ESTABLISH_DATE', 'S.EP_NO', 'S.REG_NO', 'S.BUSINESS_TYPE', 'S.STATUS_ID as S_STATUS_ID', 'S.RECORD_STATUS as S_RECORD_STATUS', 'S.CREATED_DATE as S_CREATED_DATE', 'S.CHANGED_DATE as S_CHANGED_DATE');
        $query->addSelect('S.IS_CATALOGUE_UPLOADED', 'S.LATEST_APPL_ID');
        $query->addSelect('MA.MOF_NO', 'MA.EFF_DATE as MA_EFF_DATE', 'MA.EXP_DATE as MA_EXP_DATE', 'MA.RECORD_STATUS AS MA_RECORD_STATUS');
        $query->addSelect('LH.LOGIN_DATE');
        $query->orderBy('P.EP_ROLE');
        $data = array(
            "sql" => $query->toSql(),
            "parameter" => $query->getBindings()
        );

        //Log::debug(self::class . ' :: ' . __FUNCTION__ . ' >> SQL   :   ' . json_encode($data));
        return $query->get();
    }

    /**
     * Get catalogue for suppliers
     * @param type $supplierId
     * @return type
     */
    protected function getTotalItemsSupplier($supplierID) {
        $query = DB::connection('oracle_nextgen_rpt')->table('CM_SUPPLIER_ITEM as csi');
        $query->where('csi.supplier_id', $supplierID);
        $query->whereIn('csi.item_status', ['A','U']);
        $query->where('csi.record_status', 1);
        return $query->count();
    }

    /**
     * Get list for supplier branch
     * @param type $supplierId
     * @return type
     */
    protected function getListSupplierBranch($applID) {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SUPPLIER_BRANCH as sb');
        $query->join('SM_ADDRESS as sa', 'sb.ADDRESS_ID', '=', 'sa.ADDRESS_ID');
        $query->leftJoin('PM_DIVISION as pd', 'sa.DIVISION_ID', '=', 'pd.DIVISION_ID');
        $query->leftJoin('PM_CITY as pct', 'sa.CITY_ID', '=', 'pct.CITY_ID');
        $query->leftJoin('PM_DISTRICT as pdr', 'sa.DISTRICT_ID', '=', 'pdr.DISTRICT_ID');
        $query->leftJoin('PM_STATE as ps', 'sa.STATE_ID', '=', 'ps.STATE_ID');
        $query->leftJoin('PM_COUNTRY as pcr', 'sa.COUNTRY_ID', '=', 'pcr.COUNTRY_ID');
        $query->leftJoin('SM_GST_SUPPLIER as gst',function($join){
                $join->on('gst.BRANCH_CODE','=','sb.BRANCH_CODE') 
                     ->on('gst.IS_CURRENT', DB::raw('1'));
            });
        $query->where('sb.APPL_ID', $applID);
        $query->where('sb.REV_NO', DB::raw("(select max(rev_no) from SM_SUPPLIER_BRANCH WHERE APPL_ID=sb.APPL_ID)"));
        $query->select('sb.*');
        $query->addSelect('sa.ADDRESS_1', 'sa.ADDRESS_2', 'sa.ADDRESS_3', 'sa.POSTCODE');
        $query->addSelect('pd.DIVISION_NAME', 'pct.CITY_NAME', 'pdr.DISTRICT_NAME', 'ps.STATE_NAME', 'pcr.COUNTRY_NAME');
        $query->addSelect('gst.GST_REG_NO', 'gst.GST_EFF_DATE', 'gst.GST_DECLARED_DATE');
        return $query->get();
    }

    /**
     * Get list for supplier bank
     * @param type $supplierId
     * @return type
     */
    protected function getListSupplierBank($applID) {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SUPPLIER_BANK as sb');
        $query->join('PM_FINANCIAL_ORG as fo', 'fo.FINANCIAL_ORG_ID', '=', 'sb.FINANCIAL_ORG_ID');
        $query->where('sb.APPL_ID', $applID);
        $query->where('sb.REV_NO', DB::raw("(select max(rev_no) from SM_SUPPLIER_BANK WHERE APPL_ID=sb.APPL_ID)"));
        $query->select('sb.*');
        $query->addSelect('fo.FIN_ORG_NAME','fo.record_status as bank_status');
        return $query->get();
    }

    /**
     * Get SapVendorCode for supplier. This to verify this supplier already registered into 1GFMAS or not. 
     * @param type $epNo
     * @return type
     */
    protected function getMainSapVendorCode($epNo) {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SAP_VENDOR_CODE as svc');
        $query->where('svc.ep_no', $epNo);
        $query->where('svc.record_status', 1);
        $query->whereNull('svc.branch_code');
        return $query->first();
    }

    /**
     * Get SapVendorCode for supplier. This to verify this supplier already registered into 1GFMAS or not.
     * @param type $branchCode
     * @return first row
     */
    protected function getMainSapVendorCodeByBranchCode($epNo, $branchCode) {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SAP_VENDOR_CODE as svc');
        $query->where('svc.ep_no', $epNo);
        $query->where('svc.branch_code', $branchCode);
        $query->where('svc.record_status', 1);
        return $query->first();
    }

    /**
     * Get MOF Supplier Status
     * @param type $supplierId
     * @return type
     */
    protected function getSupplierMofStatus($supplierId) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
                " SELECT ss.ep_no,sa.appl_id,ss.supplier_id,
                    DECODE (ss.is_bumi, 1, 'Bumi', 'Non-Bumi') bumi_status,
                    CASE
                       WHEN sa.reg_status_id IN ('1')
                          THEN 'Not defined'
                       WHEN sa.reg_status_id IN ('2')
                          THEN 'Bumiputera'
                       WHEN sa.reg_status_id IN ('3')
                          THEN 'Registered'
                       WHEN sa.reg_status_id IN ('4')
                          THEN 'Bumi operate from home'
                       WHEN sa.reg_status_id IN ('5')
                          THEN 'Non bumi operate from home'
                       WHEN sa.reg_status_id IN ('6')
                          THEN 'Joint venture bumi'
                       WHEN sa.reg_status_id IN ('7')
                          THEN 'Joint venture non bumi'
                       WHEN sa.reg_status_id IN ('8')
                          THEN 'Foreign company'
                    END AS type
               FROM sm_appl sa, sm_supplier ss
              WHERE sa.appl_id = ss.latest_appl_id AND ss.supplier_id = ? ", array($supplierId));
        if (count($results) > 0) {
            return $results[0];
        }
        return null;
    }

    /**
     * Get list cert for Supplier 
     * @param type $supplierId
     * @return List type
     */
    protected function getSupplierMofVirtCert($supplierId) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
                " SELECT  DECODE (smc.is_bumi_cert, 1, 'Bumi Cert', 'Mof Cert')  cert_type, smc.cert_serial_no, smc.eff_date, smc.exp_date,
                         smc.record_status,smc.appl_id
                    FROM sm_mof_account sma, sm_mof_cert smc
                   WHERE sma.MOF_ACCOUNT_ID = smc.MOF_ACCOUNT_ID 
                     AND smc.RECORD_STATUS = 1 
                     AND sma.supplier_id = ? ", array($supplierId));
        if (count($results) > 0) {
            return $results;
        }
        return null;
    }

    /**
     * Get list of records payment for supplier
     * @param type $supplierID
     * @return type
     */
    public function getPaymentSuppliers($supplierID) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "select ss.supplier_id,ss.company_name,ss.ep_no,
                    pb.bill_no,pb.bill_type,pb.bill_date,pb.bill_ref_id,pb.payment_due_date,pb.bill_amt,
                    pp.payment_date,pp.payment_id,
                    CONCAT(CONCAT((select code_desc from pm_parameter_desc where parameter_id = pp.payment_mode_id and language_code = 'ms'), ' - '), pp.payment_mode_id) as payment_mode,
                    pp.payment_amt,
                    CONCAT(CONCAT((select status_name from PM_STATUS_DESC where status_id = pp.status_id and language_code = 'ms'), ' - '), pp.status_id) as status,
                    pp.receipt_no , ppo.payment_gateway
                  from sm_supplier ss , py_bill pb ,
                    py_payment pp , py_payment_dtl  ppd ,
                    py_payment_order ppo where
                    ss.supplier_id = pb.ORG_PROFILE_ID
                    and pb.BILL_ID = pp.BILL_ID (+)
                    and pp.PAYMENT_ID = ppd.PAYMENT_ID (+)
                    and pp.PAYMENT_ID = ppo.PAYMENT_ID (+)
                    and ss.SUPPLIER_ID = ?", array($supplierID));
        return $results;
    }

    /**
     * Get list of records pending payment for supplier
     * @param type $supplierID
     * @return type
     */
    public function getListPendingPaymentSuppliers() {
        
        $dateCreatedFilter = '2018-05-31';
        /*
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT SS.SUPPLIER_ID,SS.COMPANY_NAME,SS.EP_NO,PP.PAYMENT_ID AS ORDER_ID,PP.PAYMENT_AMT,
                    PB.BILL_NO,PB.BILL_TYPE,PB.BILL_DATE,PB.BILL_REF_ID,PB.PAYMENT_DUE_DATE,PP.CREATED_DATE,
                    PPO.PAYMENT_GATEWAY,  PPO.PAYMENT_ORDER_ID, PP.RECEIPT_NO  
                    FROM  SM_SUPPLIER SS, PY_BILL PB , PY_PAYMENT PP  , PY_PAYMENT_ORDER PPO , PY_PAYMENT_RESPONSE PPR
                    WHERE SS.SUPPLIER_ID = PB.ORG_PROFILE_ID AND PB.BILL_ID = PP.BILL_ID  
                    AND PP.PAYMENT_ID = PPO.PAYMENT_ID 
                    AND PPO.PAYMENT_ORDER_ID = PPR.PAYMENT_ORDER_ID (+) 
                    AND PPR.PAYMENT_ORDER_ID is null 
                    ORDER BY PB.BILL_DATE desc");
        */
        
        //dd($listOrderIdFailed);
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SUPPLIER as ss');
        $query->join('PY_BILL as PB', 'SS.SUPPLIER_ID', '=', 'PB.ORG_PROFILE_ID');
        $query->join('PY_PAYMENT as PP', 'PB.BILL_ID', '=', 'PP.BILL_ID');
        $query->join('PY_PAYMENT_ORDER as PPO', 'PP.PAYMENT_ID', '=', 'PPO.PAYMENT_ID');
        $query->leftJoin('PY_PAYMENT_RESPONSE as PPR', 'PPO.PAYMENT_ORDER_ID', '=', 'PPR.PAYMENT_ORDER_ID');
        $query->whereDate('PP.CREATED_DATE','>',$dateCreatedFilter);
        $query->whereNull('PPR.PAYMENT_ORDER_ID');
        $query->select('SS.SUPPLIER_ID','SS.COMPANY_NAME','SS.EP_NO','PP.PAYMENT_ID AS ORDER_ID','PP.PAYMENT_AMT');
        $query->addSelect('PB.BILL_NO','PB.BILL_TYPE','PB.BILL_DATE','PB.BILL_REF_ID','PB.PAYMENT_DUE_DATE','PP.CREATED_DATE');
        $query->addSelect('PPO.PAYMENT_GATEWAY','PPO.PAYMENT_ORDER_ID','PP.RECEIPT_NO');
        $query->orderBy('PB.BILL_DATE','desc');
                
        $result = $query->get();
        
        $data = DB::connection('mysql_ep_support')->table('ep_payment_failed')
                ->whereDate('payment_created','>',$dateCreatedFilter)
                ->select('order_id')->get();
       
        $newdata = collect([]);
        foreach ($result as $obj){
            //$listOrderIdFailed->contains('order_id', $obj->order_id);
            if( $data->contains('order_id', $obj->order_id)){
                
            }else{
                $newdata->push($obj);
            }
            
        }
        
        //dd(count($newdata));
        return $newdata;
    }
    
    /**
     * Get list of records pending payment for supplier
     * @param type $supplierID
     * @return type
     */
    public function getPendingPaymentSuppliersByOrderID($orderID) {
        
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SUPPLIER as ss');
        $query->join('PY_BILL as PB', 'SS.SUPPLIER_ID', '=', 'PB.ORG_PROFILE_ID');
        $query->join('PY_PAYMENT as PP', 'PB.BILL_ID', '=', 'PP.BILL_ID');
        $query->join('PY_PAYMENT_ORDER as PPO', 'PP.PAYMENT_ID', '=', 'PPO.PAYMENT_ID');
        $query->leftJoin('PY_PAYMENT_RESPONSE as PPR', 'PPO.PAYMENT_ORDER_ID', '=', 'PPR.PAYMENT_ORDER_ID');
        $query->whereNull('PPR.PAYMENT_ORDER_ID');
        $query->where('PP.PAYMENT_ID',$orderID);
        $query->select('SS.SUPPLIER_ID','SS.COMPANY_NAME','SS.EP_NO','PP.PAYMENT_ID AS ORDER_ID','PP.PAYMENT_AMT');
        $query->addSelect('PB.BILL_NO','PB.BILL_TYPE','PB.BILL_DATE','PB.BILL_REF_ID','PB.PAYMENT_DUE_DATE','PP.CREATED_DATE');
        $query->addSelect('PPO.PAYMENT_GATEWAY','PPO.PAYMENT_ORDER_ID','PP.RECEIPT_NO');
        $query->orderBy('PB.BILL_DATE','desc');
                
        return $query->first();
    }
    
    /**
     * Get list of records for 0.4 percentage revenue
     * @param type $supplierID
     * @return type
     */
    public function getPaymentStatPercentage() {

        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT
              TO_CHAR(a.created_date, 'mm')   AS MONTH_NUM,
              TO_CHAR(a.created_date, 'yyyy') AS YEAR,
              TO_CHAR(TRUNC(a.created_date, 'mm'), 'month', 'nls_date_language=american') MONTH,
              SUM(pa.fee_amt) AS SUM_AMOUNT
            FROM fl_payment_advice pa, fl_workflow_status a, fl_fulfilment_request fr
            WHERE a.doc_id = pa.fulfilment_order_id
                  AND fr.fulfilment_req_id = pa.fulfilment_req_id
                  AND a.status_id IN (41530, 41535, 41030, 41035)
                  AND a.doc_type IN ('PO', 'CO')
                  -- and trunc(fr.AG_APPROVED_DATE) between '01-Mar-2018' and '31-Mar-2018'
                  AND fr.financial_year = '2018'
            GROUP BY TO_CHAR(a.created_date, 'mm'),
              TO_CHAR(a.created_date, 'yyyy'),
              TRUNC(a.created_date, 'mm')
            ORDER BY 1 ASC");

        return $results;
    }

    /**
     * Get list of records payment statistics
     * @param type $supplierID
     * @return type
     */
    public function getPaymentStatInfo() {

        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT
              TO_CHAR(pp.CHANGED_DATE, 'YYYY') AS YEAR,
              TO_CHAR(pp.CHANGED_DATE, 'MM')   AS MONTH,
              pb.bill_type,
              decode(pb.bill_type,
                     'R', 'Registration Fee',
                     'P', 'Processing Fee',
                     'S', 'Softcert Fee')      as bill_type,
              SUM(pp.PAYMENT_AMT)              as TOTAL_AMT
            FROM py_bill pb,
              py_payment pp
            WHERE pb.bill_id = pp.bill_id
                  AND pp.receipt_no IN (
              select distinct receipt_no
              FROM py_bill pb,
                py_payment pp
              WHERE pb.bill_id = pp.bill_id
                    AND pp.receipt_no IS NOT NULL
            )
            GROUP BY TO_CHAR(pp.CHANGED_DATE, 'YYYY'), TO_CHAR(pp.CHANGED_DATE, 'MM'), pb.bill_type
            order by year, month");

        return $results;
    }

    /**
     * Get details response payment (if necessary call this method)
     * @param type $paymentOrderID
     * @return type
     */
    public function getPaymentSuppliersResponse($paymentOrderID) {
        return DB::connection('oracle_nextgen_rpt')->table('PY_PAYMENT_RESPONSE')
                        ->where('PAYMENT_ORDER_ID', $paymentOrderID)->first();
    }

    /**
     * Get HQ GST info from SM_GST_SUPPLIER
     * @param type $supplierId
     * @return type
     */
    protected function getHqGstInfo($supplierId) {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_GST_SUPPLIER as GST');
        $query->where('GST.SUPPLIER_ID', $supplierId);
        $query->where('GST.BRANCH_CODE', null);
        $query->where('GST.IS_CURRENT', 1);
        return $query->first();
    }

    /**
     * Get list for supplier branch
     * @param type $applID
     * @return type
     */
    protected function getListSupplierCategoryCode($applID) {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SUPPLIER_CATEGORY as ssc');
        $query->leftJoin('PM_CATEGORY_L1 as l1', 'ssc.CATEGORY_L1_ID', '=', 'l1.CATEGORY_L1_ID');
        $query->leftJoin('PM_CATEGORY_L2 as l2', 'ssc.CATEGORY_L2_ID', '=', 'l2.CATEGORY_L2_ID');
        $query->leftJoin('PM_CATEGORY_L3 as l3', 'ssc.CATEGORY_L3_ID', '=', 'l3.CATEGORY_L3_ID');
        $query->where('ssc.APPL_ID', $applID);
        $query->where('ssc.RECORD_STATUS', 1);
        $query->whereNotNull('ssc.APPROVED_DATE');
        $query->select('ssc.*');
        $query->addSelect('l1.CATEGORY_L1_CODE', 'l2.CATEGORY_L2_CODE', 'l3.CATEGORY_L3_CODE');
        return $query->get();
    }

    /**
     * Get list for supplier branch
     * @param type $serviceCode
     * @return list
     */
    protected function getDashboardGfmasByServiceCode($serviceCode) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select a.service_code, a.trans_type, to_char(a.trans_date, 'yyyy-mm-dd') dates, count(a.trans_id) as APIVE_1GFMAS
            from osb_logging a
            where a.service_code like ?
            and a.TRANS_TYPE='Status-BATCH'
            and (a.status_desc = 'Batch File Successfully Transferred' or a.status_desc = 'Batch File Transfer Successfully Initiated')
            and trans_date >= to_date(to_char(sysdate, 'yyyy-mm-dd') || '00:00:00','yyyy-mm-dd hh24:mi:ss')
            and trans_date <= to_date(to_char(sysdate, 'yyyy-mm-dd') || '23:59:59','yyyy-mm-dd hh24:mi:ss')
            group by a.service_code, a.trans_type, to_char(a.trans_date, 'yyyy-mm-dd')
            order by a.service_code, a.trans_type", array($serviceCode));

        return $results;
    }

    /**
     * Get list for dashboard 1gfmas-apive quartz
     * @return list
     */
    protected function getDashboardQuartzFired() {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT JOB_GROUP, JOB_NAME, TRIGGER_STATE,
            TIMESTAMP '1970-01-01 08:00:00.000' + NUMTODSINTERVAL(PREV_FIRE_TIME / 1000, 'SECOND') AS PREV_FIRED,
            TIMESTAMP '1970-01-01 08:00:00.000' + NUMTODSINTERVAL(NEXT_FIRE_TIME / 1000, 'SECOND') AS NEXT_FIRED
            FROM QRTZ_TRIGGERS
            WHERE JOB_GROUP = '1GFMAS INTEGRATION JOB GROUP'
            AND JOB_NAME = 'MASTER DATA VENDOR JOB(Outbound)'
            ORDER BY JOB_GROUP");

        return $results;
    }

    /**
     * Get list for dashboard 1gfmas-apive quartz execution
     * @return list
     */
    protected function getDashboardQuartzExecution() {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT JOB_GROUP, JOB_NAME, FINISH_EXECUTION_DATE, EXECUTION_DURATION,
            to_char(to_date(ROUND(EXECUTION_DURATION / 1000), 'SSSSS'), 'HH24:MI:SS') AS DURATION
            FROM QRTZ_AUDIT_LOG
            WHERE FINISH_EXECUTION_DATE >= to_date(to_char(sysdate, 'yyyy-mm-dd') || '00:00:00','yyyy-mm-dd hh24:mi:ss')
            AND FINISH_EXECUTION_DATE <= to_date(to_char(sysdate, 'yyyy-mm-dd') || '23:59:59','yyyy-mm-dd hh24:mi:ss')
            AND JOB_GROUP = '1GFMAS INTEGRATION JOB GROUP'
            AND JOB_NAME = 'MASTER DATA VENDOR JOB(Outbound)'
            AND ROWNUM < 2
            ORDER BY FINISH_EXECUTION_DATE DESC");

        return $results;
    }

    /**
     * Get list for dashboard 1gfmas-apive quartz execution
     * @return list
     */
    protected function getDashboardDiInterfaceLog() {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT * FROM DI_INTERFACE_LOG WHERE PROCESS_ID = 'APIVE' AND IS_CURRENT = 1");

        return $results;
    }

    /**
     * Get list for dashboard EJB OSB
     * @return list
     */
    protected function getDashboardEjbOsb() {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select to_char(a.trans_date,'yyyy-mm-dd') trans_date, count(distinct(a.trans_id)) total_invoke,
            count(case when a.status_desc = 'Service Not Found: OSB Service Callout action received SOAP Fault response' and extractvalue(xmltype.createxml(b.payload_body),'//con1:faultstring','xmlns:con1=\"http://www.bea.com/wli/sb/stages/transform/config\"') like '%EJBCLIENT000025%' then 1 else null end) no_ejb_receiver,
            count(case when a.status_desc = 'Service Not Found: OSB Service Callout action received SOAP Fault response' and extractvalue(xmltype.createxml(b.payload_body),'//con1:faultstring','xmlns:con1=\"http://www.bea.com/wli/sb/stages/transform/config\"') like '%java.util.concurrent.TimeoutException: No invocation response received%' then 1 else null end) ejb_timeout
            from osb_logging a, osb_logging_dtl b
            where a.trans_id in (
                select distinct(trans_id) from osb_logging
                where service_code in ('EPP-201','EPP-405','EPP-102','EPP-100','EPP-501','EPP-403','EPP-302','EPP-206','EPP-301','EPP-105','EPP-402','EPP-200','PHS-210','EPP-412','EPP-101','EPP-106','EPP-205','EPP-013','EPP-204','EPP-404','SPK-010','EPP-400','EPP-202','EPP-406','EPP-103','EPP-500','EPP-502')
                and trans_date >= to_date(to_char(sysdate-2, 'yyyy-mm-dd') || '00:00:00','YYYY-MM-DD HH24:MI:SS') ) 
            and a.logging_id = b.logging_id 
            and a.trans_date >= to_date(to_char(sysdate-2, 'yyyy-mm-dd') || '00:00:00','YYYY-MM-DD HH24:MI:SS') 
            group by to_char(a.trans_date,'yyyy-mm-dd')
            order by to_char(a.trans_date,'yyyy-mm-dd') desc");

        return $results;
    }
    
    /**
     * Get list for dashboard OSB Retry
     * @return list
     */
    protected function getDashboardOsbRetry() {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT SERVICE_NAME, TARGET_SYSTEM, count(SERVICE_NAME) AS counts FROM OSB_RETRY_DTL
            GROUP BY SERVICE_NAME, TARGET_SYSTEM");

        return $results;
    }
    
    /**
     * Get list for dashboard OSB Notification Retry
     * @return list
     */
    protected function getDashboardOsbNotifyRetry() {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT SERVICE_NAME, TARGET_SYSTEM, count(SERVICE_NAME) AS counts FROM OSB_NOTIFY_RETRY_DTL
            GROUP BY SERVICE_NAME, TARGET_SYSTEM");

        return $results;
    }
    
    /**
     * Get list for dashboard OSB Notification Retry
     * @return list
     */
    protected function getDashboardOsbBatchRetry() {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT SERVICE_CODE, TARGET_SYSTEM, count(SERVICE_CODE) AS counts FROM OSB_BATCH_RETRY_DTL
            GROUP BY SERVICE_CODE, TARGET_SYSTEM");

        return $results;
    }

    /**
     * check supplier attendance for QT
     * @return list
     */
    protected function checkQTSupplierAttendance($qtNo) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT e.mof_no, a.QT_NO, a.QT_TITLE, f.*, c.BSV_DATE, 
              d.SUPPLIER_ID, d.QT_APPROVAL_REQUEST_ID, d.IS_ATTENDED, d.IS_POST_REGISTERED, d.RECORD_STATUS AS APPROVAL_STATUS
              FROM sc_qt a, sc_qt_bsv b, SC_QT_BSV_DTL c, SC_QT_BSV_REGISTRATION d, sm_mof_account e, SC_QT_BSV_ATTENDANCE f
             WHERE a.qt_id = b.qt_id
               AND b.QT_BSV_ID = c.QT_BSV_ID
               AND c.QT_BSV_DTL_ID = d.QT_BSV_DTL_ID
               AND d.SUPPLIER_ID = e.SUPPLIER_ID
               AND d.QT_BSV_REGISTRATION_ID = f.QT_BSV_REGISTRATION_ID
               AND a.qt_no = ?
               ORDER BY f.is_attended DESC", array($qtNo));

        return $results;
    }

    /**
     * check supplier attendance for QT
     * @return list
     */
    protected function getScQt($qtNo) {
        $query = DB::connection('oracle_nextgen_rpt')->table('SC_QT as QT');
        $query->where('QT.QT_NO', $qtNo);
        return $query->first();
    }

    /**
     * check supplier qualify for QT
     * @return list
     */
    protected function checkDisqualifiedStage($qtNo, $supplierId) {
        $query = DB::connection('oracle_nextgen_rpt')->table('SC_QT as QT');
        $query->leftJoin('SC_QT_SUPPLIER as QTS', 'QT.QT_ID', '=', 'QTS.QT_ID');
        $query->where('QT.QT_NO', $qtNo);
        $query->where('QTS.SUPPLIER_ID', $supplierId);
        $query->addSelect('QTS.QT_ID', 'QTS.INVITATION_TYPE', 'QTS.DISQUALIFIED_STAGE', 'QTS.RECORD_STATUS');
        return $query->first();
    }

    /**
     * check supplier approve request for QT
     * @return list
     */
    protected function checkQTApproveRequest($supplierId, $qtNo) {
        $query = DB::connection('oracle_nextgen_rpt')->table('SC_QT as QT');
        $query->leftJoin('SC_QT_BSV as QTB', 'QT.QT_ID', '=', 'QTB.QT_ID');
        $query->leftJoin('SC_QT_BSV_DTL as QTBD', 'QTB.QT_BSV_ID', '=', 'QTBD.QT_BSV_ID');
        $query->leftJoin('SC_QT_BSV_REGISTRATION as QTBR', 'QTBD.QT_BSV_DTL_ID', '=', 'QTBR.QT_BSV_DTL_ID');
        $query->where('QT.QT_NO', $qtNo);
        $query->where('QTBR.SUPPLIER_ID', $supplierId);
        $query->addSelect('QTBR.*', 'QTBD.BSV_DATE');
//        $query->orderBy('QTBR.CHANGED_DATE', 'desc');
        return $query->first();
    }

    /**
     * check supplier approve request for QT
     * @return list
     */
    protected function checkQTApproveRequestDetail($qtApprovalReqId) {
        $query = DB::connection('oracle_nextgen_rpt')->table('sc_qt_approval_request as QTAR');
        $query->where('QTAR.qt_approval_request_id', $qtApprovalReqId);
        $query->addSelect('QTAR.RECORD_STATUS as APPROVAL_STATUS','QTAR.approval_request_type_id', 'QTAR.approver_action_id');
        return $query->first();
    }

    /**
     * check supplier approve request for QT
     * @return list
     */
    protected function getParameterDesc($paramId) {
        $query = DB::connection('oracle_nextgen_rpt')->table('PM_PARAMETER_DESC as PD');
        $query->where('PD.LANGUAGE_CODE', 'ms');
        $query->where('PD.PARAMETER_ID', $paramId);
        $query->addSelect('PD.CODE_DESC');
        return $query->first();
    }

    /**
     * get supplier detail
     * @return row
     */
    protected function getSupplierDetail($supplierId) {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SUPPLIER as SS');
        $query->where('SS.SUPPLIER_ID', $supplierId);
        return $query->first();
    }

    /**
     * get qt bsv attendance detail
     * @return row
     */
    protected function getQtBsvAttendance($qtBsvAttendanceId) {
        $query = DB::connection('oracle_nextgen_rpt')->table('SC_QT_BSV_ATTENDANCE as BSVA');
        $query->where('BSVA.QT_BSV_ATTENDANCE_ID', $qtBsvAttendanceId);
        return $query->first();
    }

    /**
     * check item before trigger
     * @return list
     */
    protected function selectPreMminf($extCode, $uomCode, $itemName) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select sri.request_item_id, ci.extension_code, sri.item_name, pu.uom_code, sri.changed_date
            from cm_item ci, sc_request_item sri, pm_uom pu
            where ci.item_id = sri.item_id
            and sri.uom_id = pu.uom_id
            and ci.extension_code = ?
            and pu.uom_code = ?
            and sri.item_name = ?", array($extCode, $uomCode, $itemName));

        return $results;
    }

    /**
     * check item before trigger
     * @return list
     */
    protected function getMminfReqItemId($docNo, $itemCode) {
        
        $parameters = array();
        $query = "SELECT
                FFR.DOC_NO ,
                FFID.* 
                FROM FL_FULFILMENT_ITEM_ADDR FFID , 
                     FL_DELIVERY_ADDRESS FDA ,
                     FL_FULFILMENT_REQUEST FFR 
                WHERE FFID.FULFILMENT_ADDR_ID = FDA.DELIVERY_ADDRESS_ID 
                AND FDA.FULFILMENT_REQ_ID  = FFR.FULFILMENT_REQ_ID ";
        if($docNo != null && strlen($docNo) > 0){
            $query = $query." AND FFR.DOC_NO = ? ";
            array_push($parameters, $docNo);
        }
        if($itemCode != null && strlen($itemCode) > 0){
            $query = $query." AND FFID.ITEM_CODE = ? ";
            array_push($parameters, $itemCode);
        }
        $results = DB::connection('oracle_nextgen_rpt')->select($query, $parameters);
        return $results;
        
    }

    /**
     * check item before trigger
     * @return list
     */
    protected function getMminfSearchDetail($reqItemId) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT ci.ITEM_NAME,
              ci.EXTENSION_CODE,
              sri.REQUEST_ITEM_ID,
              sri.ITEM_NAME,
              sri.ITEM_DESC,
              sri.CREATED_DATE,
              sri.CHANGED_DATE,
              pu.UOM_CODE
            FROM cm_item ci,
              sc_request_item sri,
              pm_uom pu
            WHERE ci.ITEM_ID        = sri.ITEM_ID
            AND sri.UOM_ID          = pu.UOM_ID
            AND sri.REQUEST_ITEM_ID = ? ", array($reqItemId));

        return $results;
    }

    protected function getMminfSearchDetailByListReqItemId($listReqItemId) {
        $query = DB::connection('oracle_nextgen_rpt')->table('CM_ITEM CI');
        $query->join('SC_REQUEST_ITEM as SRI', 'CI.ITEM_ID', '=', 'SRI.ITEM_ID');
        $query->join('PM_UOM as PU', 'SRI.UOM_ID', '=', 'PU.UOM_ID');
        $query->whereIn('SRI.REQUEST_ITEM_ID', $listReqItemId);
        $query->select('CI.ITEM_NAME','CI.EXTENSION_CODE');
        $query->addSelect('SRI.REQUEST_ITEM_ID','SRI.ITEM_NAME','SRI.ITEM_DESC','SRI.CREATED_DATE','SRI.CHANGED_DATE');
        $query->addSelect('PU.UOM_CODE');
        $query->orderBy('SRI.CHANGED_DATE');
        return $query->get();
    }
    
    /**
     * Get list for mminf dashboard DI_INTERFACE_LOG
     * @return list
     */
    protected function getDashboardMminfDiInterfaceLog() {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT * FROM DI_INTERFACE_LOG WHERE PROCESS_ID = 'MMINF' AND IS_CURRENT = 1");

        return $results;
    }

    /**
     * Get list for mminf dashboard QRTZ_TRIGGERS
     * @return list
     */
    protected function getDashboardMMinfQuartz() {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT JOB_GROUP, JOB_NAME, TRIGGER_STATE,
            TIMESTAMP '1970-01-01 08:00:00.000' + NUMTODSINTERVAL(PREV_FIRE_TIME / 1000, 'SECOND') AS PREV_FIRED,
            TIMESTAMP '1970-01-01 08:00:00.000' + NUMTODSINTERVAL(NEXT_FIRE_TIME / 1000, 'SECOND') AS NEXT_FIRED
            FROM QRTZ_TRIGGERS
            WHERE JOB_GROUP = '1GFMAS INTEGRATION JOB GROUP'
            AND JOB_NAME = 'MASTER DATA MATERIAL INFORMATION JOB'
            ORDER BY JOB_GROUP");

        return $results;
    }

    /**
     * Get list for dashboard 1gfmas-apive quartz execution
     * @return list
     */
    protected function getDashboardMMinfQuartzExecution() {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT JOB_GROUP, JOB_NAME, FINISH_EXECUTION_DATE, EXECUTION_DURATION,
            to_char(to_date(ROUND(EXECUTION_DURATION / 1000), 'SSSSS'), 'HH24:MI:SS') AS DURATION
            FROM QRTZ_AUDIT_LOG
            WHERE FINISH_EXECUTION_DATE >= to_date(to_char(sysdate, 'yyyy-mm-dd') || '00:00:00','yyyy-mm-dd hh24:mi:ss')
            AND FINISH_EXECUTION_DATE <= to_date(to_char(sysdate, 'yyyy-mm-dd') || '23:59:59','yyyy-mm-dd hh24:mi:ss')
            AND JOB_GROUP = '1GFMAS INTEGRATION JOB GROUP'
            AND JOB_NAME = 'MASTER DATA MATERIAL INFORMATION JOB'
            AND ROWNUM < 2
            ORDER BY FINISH_EXECUTION_DATE DESC");

        return $results;
    }

    /**
     * check supplier before trigger apive
     * @return list
     */
    protected function getApiveTriggerInfo($ePno) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT SS.SUPPLIER_ID, SS.COMPANY_NAME, SS.EP_NO, SS.CREATED_DATE, SS.CHANGED_DATE, SS.RECORD_STATUS, SS.REG_NO, SMA.MOF_NO
            FROM SM_SUPPLIER SS
            LEFT JOIN SM_MOF_ACCOUNT SMA ON SS.SUPPLIER_ID = SMA.SUPPLIER_ID
            WHERE SS.EP_NO = ? ", array($ePno));

        return $results;
    }
    
}
