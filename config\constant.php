<?php

return [

    /*
    |--------------------------------------------------------------------------
    | DEFAULT
    |--------------------------------------------------------------------------
    */
    'default_password_self_portal'        => 'P@ssword1234',

    
    /*
    |--------------------------------------------------------------------------
    | EMAIL TEMPLATE
    |--------------------------------------------------------------------------
    */
    //'email_sender'                => '<EMAIL>',
    'email_sender'                => '<EMAIL>',
    'email_sender_name'           => 'Pentadbir',

    'email_sender_epss'                => '<EMAIL>',
    'email_sender_name_epss'           => 'eP Auto Notify',
    
    'mail_ep_host_casb'           => 'smtp.office365.com',
    'mail_ep_port_casb'           => '587',
    'mail_ep_encryption_casb'     => 'TLS',
    'mail_ep_username_casb'       => '<EMAIL>',
    'mail_ep_password_casb'       => 'Casb@!@#$%^&*',
    'email_sender_casb'           => '<EMAIL>',
    'email_sender_name_casb'      => 'CASB DO NOT REPLY',
    
    /*
    | Specific Roles to access advance information eP 
    | Check roles in .env file. IF not setup, set by default access roles. 
    */
    'roles_adv_ep'  =>  ['Group IT Coordinator','Group Middleware','Group IT Specialist(Production Support)','Group IT Specialist(Developer)','Approver','Group Archisoft Build Team','Group IT Specialist(Database Admin)','Group IT Specialist','Group IT Specialist(Network Admin)'],
    'users_dev_ep'  =>  ['iqbalfikri','mohdshamsul','shahril','hazman','lilian2','sharudin','lilian'],
    'roles_patch_ep'  =>  ['Group Middleware','Group IT Specialist(Production Support)'],
    

];
