<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Remote Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default connection that will be used for SSH
    | operations. This name should correspond to a connection name below
    | in the server list. Each connection will be manually accessible.
    |
    */

    'default' => 'production',

    /*
    |--------------------------------------------------------------------------
    | Remote Server Connections
    |--------------------------------------------------------------------------
    |
    | These are the servers that will be accessible via the SSH task runner
    | facilities of Laravel. This feature radically simplifies executing
    | tasks on your servers, such as deploying out these applications.
    |
    */

    'connections' => [
        'osb' => [
            'host'      => '*************',
            'username'  => 'support',
            'password'  => '5uPP0rt!@#',
            'key'       => '',
            'keytext'   => '',
            'keyphrase' => '',
            'agent'     => '',
            'timeout'   => 15,
        ],
        'portal' => [
            'host'      => '*************',
            'username'  => 'support',
            'password'  => '5uPP0rt!@#',
            'key'       => '',
            'keytext'   => '',
            'keyphrase' => '',
            'agent'     => '',
            'timeout'   => 30,
        ],

        'crm-ep' => [
            'host'      => env('SSH_HOST_CRMEP', '**************'),
            'username'  => env('SSH_USERNAME_CRMEP', 'suppcrm'),
            'password'  => env('SSH_PASSWORD_CRMEP', 'cDc@2022'),
            'key'       => '',
            'keytext'   => '',
            'keyphrase' => '',
            'agent'     => '',
            'timeout'   => 30,
        ],

        'crm-ssm' => [
            'host'      => env('SSH_HOST_CRMSSM', '**************'),
            'username'  => env('SSH_USERNAME_CRMSSM', 'suppcrm'),
            'password'  => env('SSH_PASSWORD_CRMSSM', 'cRm@2017'),
            'key'       => '',
            'keytext'   => '',
            'keyphrase' => '',
            'agent'     => '',
            'timeout'   => 30,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Remote Server Groups
    |--------------------------------------------------------------------------
    |
    | Here you may list connections under a single group name, which allows
    | you to easily access all of the servers at once using a short name
    | that is extremely easy to remember, such as "web" or "database".
    |
    */

    'groups' => [
        'web' => ['production'],
    ],

];
