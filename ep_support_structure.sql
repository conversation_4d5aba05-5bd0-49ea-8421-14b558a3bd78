/*
SQLyog Ultimate v12.09 (64 bit)
MySQL - 5.7.17-log : Database - ep_support
*********************************************************************
*/


/*!40101 SET NAMES utf8 */;

/*!40101 SET SQL_MODE=''*/;

/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
/*Table structure for table `ep_action_log` */

DROP TABLE IF EXISTS `ep_action_log`;

CREATE TABLE `ep_action_log` (
  `id` bigint(12) unsigned NOT NULL AUTO_INCREMENT,
  `action_name` varchar(255) NOT NULL,
  `action_type` varchar(255) NOT NULL,
  `action_data` text NOT NULL,
  `action_parameter` varchar(255) DEFAULT NULL,
  `action_status` varchar(100) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `created_by` varchar(200) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` varchar(200) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1719 DEFAULT CHARSET=utf8;

/*Table structure for table `ep_activity_access` */

DROP TABLE IF EXISTS `ep_activity_access`;

CREATE TABLE `ep_activity_access` (
  `id` bigint(12) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(50) NOT NULL,
  `username` varchar(100) NOT NULL,
  `access_url` varchar(255) NOT NULL,
  `access_detail` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=456388 DEFAULT CHARSET=utf8;

/*Table structure for table `ep_login_history` */

DROP TABLE IF EXISTS `ep_login_history`;

CREATE TABLE `ep_login_history` (
  `id` bigint(12) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(50) NOT NULL,
  `username` varchar(100) NOT NULL,
  `last_login` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=124 DEFAULT CHARSET=utf8;

/*Table structure for table `ep_payment_failed` */

DROP TABLE IF EXISTS `ep_payment_failed`;

CREATE TABLE `ep_payment_failed` (
  `id` bigint(12) NOT NULL AUTO_INCREMENT,
  `supplier_id` bigint(12) DEFAULT NULL,
  `company_name` varchar(200) DEFAULT NULL,
  `ep_no` varchar(50) DEFAULT NULL,
  `order_id` bigint(12) NOT NULL,
  `payment_amt` varchar(10) DEFAULT NULL,
  `bill_no` varchar(50) DEFAULT NULL,
  `bill_type` varchar(10) DEFAULT NULL,
  `bill_date` date DEFAULT NULL,
  `bill_ref_id` varchar(50) DEFAULT NULL,
  `payment_due_date` date DEFAULT NULL,
  `payment_created` datetime DEFAULT NULL,
  `payment_gateway` varchar(50) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `created_by` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_id` (`order_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5774 DEFAULT CHARSET=utf8;

/*Table structure for table `ep_task` */

DROP TABLE IF EXISTS `ep_task`;

CREATE TABLE `ep_task` (
  `task_id` bigint(12) NOT NULL AUTO_INCREMENT,
  `category_id` bigint(12) NOT NULL,
  `case_no` bigint(12) DEFAULT NULL,
  `case_type` varchar(50) DEFAULT NULL,
  `entity_name` varchar(200) DEFAULT NULL,
  `description` text,
  `resolution` text,
  `status` tinyint(1) DEFAULT '0',
  `is_deleted` tinyint(1) DEFAULT '0',
  `created_at` datetime DEFAULT NULL,
  `created_by` varchar(100) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` varchar(100) DEFAULT NULL,
  `completed_at` datetime DEFAULT NULL,
  `completed_by` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`task_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4723 DEFAULT CHARSET=utf8;

/*Table structure for table `ep_task_category` */

DROP TABLE IF EXISTS `ep_task_category`;

CREATE TABLE `ep_task_category` (
  `category_id` bigint(12) NOT NULL AUTO_INCREMENT,
  `category_name` varchar(200) DEFAULT NULL,
  `category_desc` text,
  PRIMARY KEY (`category_id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8;

/*Table structure for table `ep_task_missing` */

DROP TABLE IF EXISTS `ep_task_missing`;

CREATE TABLE `ep_task_missing` (
  `task_id` bigint(12) NOT NULL AUTO_INCREMENT,
  `case_no` varchar(20) DEFAULT NULL,
  `case_status` varchar(100) DEFAULT NULL,
  `case_id` varchar(100) DEFAULT NULL,
  `case_created` datetime DEFAULT NULL,
  `batch` varchar(200) DEFAULT NULL,
  `doc_no` text,
  `module` varchar(100) DEFAULT NULL,
  `process_status` varchar(100) DEFAULT '00',
  `composite_instance_id` text,
  `problem` text,
  `resolution` text,
  `is_case_closed` tinyint(1) DEFAULT '0',
  `is_deleted` tinyint(1) DEFAULT '0',
  `created_at` datetime DEFAULT NULL,
  `created_by` varchar(100) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` varchar(100) DEFAULT NULL,
  `completed_at` datetime DEFAULT NULL,
  `completed_by` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`task_id`),
  KEY `case_no` (`case_no`)
) ENGINE=InnoDB AUTO_INCREMENT=1767 DEFAULT CHARSET=utf8;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
