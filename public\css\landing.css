/* Minimal CSS for Backend-Only Landing Page */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f8f9fa;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 40px 20px;
}

.header {
    text-align: center;
    margin-bottom: 40px;
    padding: 30px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header h1 {
    color: #2c3e50;
    margin: 0 0 10px 0;
    font-size: 2.5em;
}

.header p {
    color: #7f8c8d;
    font-size: 1.2em;
    margin: 0;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.status-card {
    background: white;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-left: 4px solid #3498db;
}

.status-card.success {
    border-left-color: #27ae60;
}

.status-card.warning {
    border-left-color: #f39c12;
}

.status-card.error {
    border-left-color: #e74c3c;
}

.status-card h3 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 1.3em;
}

.status-card p {
    margin: 0;
    color: #7f8c8d;
}

.status-value {
    font-size: 1.5em;
    font-weight: bold;
    color: #2c3e50;
}

.info-section {
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.info-section h2 {
    color: #2c3e50;
    margin: 0 0 20px 0;
    font-size: 1.8em;
    border-bottom: 2px solid #ecf0f1;
    padding-bottom: 10px;
}

.info-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.info-list li {
    padding: 8px 0;
    border-bottom: 1px solid #ecf0f1;
}

.info-list li:last-child {
    border-bottom: none;
}

.badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85em;
    font-weight: bold;
    text-transform: uppercase;
}

.badge.active {
    background-color: #d4edda;
    color: #155724;
}

.badge.scheduled {
    background-color: #cce5ff;
    color: #004085;
}

.footer {
    text-align: center;
    margin-top: 40px;
    padding: 20px;
    color: #7f8c8d;
    font-size: 0.9em;
}

.timestamp {
    font-family: 'Courier New', monospace;
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.9em;
}

@media (max-width: 768px) {
    .container {
        padding: 20px 10px;
    }
    
    .header h1 {
        font-size: 2em;
    }
    
    .status-grid {
        grid-template-columns: 1fr;
    }
}
