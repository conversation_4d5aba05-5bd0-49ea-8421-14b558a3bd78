@props(['headers', 'rows', 'searchable' => true, 'sortable' => true])

<div class="bg-white shadow-sm rounded-lg border border-gray-200">
    @if($searchable)
        <div class="p-4 border-b border-gray-200">
            <div class="relative">
                <input 
                    type="text" 
                    placeholder="Search..." 
                    class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    onkeyup="filterTable(this)"
                >
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
            </div>
        </div>
    @endif

    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    @foreach($headers as $header)
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider {{ $sortable ? 'cursor-pointer hover:bg-gray-100' : '' }}" 
                            @if($sortable) onclick="sortTable(this)" @endif>
                            {{ $header }}
                            @if($sortable)
                                <span class="ml-1 text-gray-400">↕</span>
                            @endif
                        </th>
                    @endforeach
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                @foreach($rows as $row)
                    <tr class="hover:bg-gray-50">
                        @foreach($row as $cell)
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {!! $cell !!}
                            </td>
                        @endforeach
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    @if(empty($rows))
        <div class="text-center py-12">
            <div class="text-gray-500">No data available</div>
        </div>
    @endif
</div>

<script>
function filterTable(input) {
    const filter = input.value.toLowerCase();
    const table = input.closest('.bg-white').querySelector('table');
    const rows = table.querySelectorAll('tbody tr');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(filter) ? '' : 'none';
    });
}

function sortTable(header) {
    const table = header.closest('table');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const columnIndex = Array.from(header.parentNode.children).indexOf(header);
    
    // Toggle sort direction
    const isAsc = header.classList.contains('asc');
    header.classList.toggle('asc', !isAsc);
    header.classList.toggle('desc', isAsc);
    
    // Sort rows
    rows.sort((a, b) => {
        const aText = a.children[columnIndex].textContent.trim();
        const bText = b.children[columnIndex].textContent.trim();
        
        // Try numeric sort first
        const aNum = parseFloat(aText);
        const bNum = parseFloat(bText);
        
        if (!isNaN(aNum) && !isNaN(bNum)) {
            return isAsc ? bNum - aNum : aNum - bNum;
        }
        
        // Fallback to string sort
        return isAsc ? bText.localeCompare(aText) : aText.localeCompare(bText);
    });
    
    // Reorder rows in DOM
    rows.forEach(row => tbody.appendChild(row));
}
</script>