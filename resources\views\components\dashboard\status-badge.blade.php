@props(['status', 'text' => null])

@php
$badgeClasses = [
    'connected' => 'bg-green-100 text-green-800 border-green-200',
    'failed' => 'bg-red-100 text-red-800 border-red-200',
    'warning' => 'bg-yellow-100 text-yellow-800 border-yellow-200',
    'untested' => 'bg-gray-100 text-gray-800 border-gray-200',
    'production' => 'bg-red-100 text-red-800 border-red-200',
    'staging' => 'bg-yellow-100 text-yellow-800 border-yellow-200',
    'development' => 'bg-green-100 text-green-800 border-green-200',
    'local' => 'bg-blue-100 text-blue-800 border-blue-200'
];

$badgeClass = $badgeClasses[$status] ?? $badgeClasses['untested'];
$displayText = $text ?? ucfirst($status);
@endphp

<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border {{ $badgeClass }}">
    {{ $displayText }}
</span>