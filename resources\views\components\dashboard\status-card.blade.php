@props(['title', 'value', 'description', 'status' => 'neutral', 'icon' => null])

@php
$statusClasses = [
    'success' => 'border-green-200 bg-green-50',
    'warning' => 'border-yellow-200 bg-yellow-50', 
    'danger' => 'border-red-200 bg-red-50',
    'neutral' => 'border-gray-200 bg-white'
];

$statusClass = $statusClasses[$status] ?? $statusClasses['neutral'];
@endphp

<div class="rounded-lg border {{ $statusClass }} p-6 shadow-sm transition-all hover:shadow-md">
    <div class="flex items-center justify-between">
        <div class="flex-1">
            <h3 class="text-sm font-medium text-gray-600">{{ $title }}</h3>
            <div class="mt-2">
                <div class="text-2xl font-bold text-gray-900">{{ $value }}</div>
                @if($description)
                    <p class="text-sm text-gray-500 mt-1">{{ $description }}</p>
                @endif
            </div>
        </div>
        @if($icon)
            <div class="ml-4 flex-shrink-0">
                <div class="text-2xl">{{ $icon }}</div>
            </div>
        @endif
    </div>
</div>