@props(['tabs'])

<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <!-- Tab Navigation -->
    <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
            @foreach($tabs as $index => $tab)
                <button 
                    class="tab-button py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 {{ $index === 0 ? 'border-primary text-primary' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}"
                    onclick="switchTab({{ $index }})"
                >
                    {{ $tab['label'] }}
                </button>
            @endforeach
        </nav>
    </div>

    <!-- Tab Content -->
    <div class="p-6">
        @foreach($tabs as $index => $tab)
            <div 
                class="tab-content {{ $index === 0 ? 'block' : 'hidden' }}" 
                id="tab-{{ $index }}"
            >
                {!! $tab['content'] !!}
            </div>
        @endforeach
    </div>
</div>

<script>
function switchTab(activeIndex) {
    // Update tab buttons
    const buttons = document.querySelectorAll('.tab-button');
    buttons.forEach((button, index) => {
        if (index === activeIndex) {
            button.classList.remove('border-transparent', 'text-gray-500');
            button.classList.add('border-primary', 'text-primary');
        } else {
            button.classList.remove('border-primary', 'text-primary');
            button.classList.add('border-transparent', 'text-gray-500');
        }
    });

    // Update tab content
    const contents = document.querySelectorAll('.tab-content');
    contents.forEach((content, index) => {
        content.classList.toggle('hidden', index !== activeIndex);
        content.classList.toggle('block', index === activeIndex);
    });

    // Load data for the active tab if needed
    loadTabData(activeIndex);
}

function loadTabData(tabIndex) {
    const endpoints = [
        '/dashboard/scheduled-tasks',
        '/dashboard/console-commands', 
        '/dashboard/database-connections',
        '/dashboard/recent-logs'
    ];

    if (endpoints[tabIndex]) {
        // You can implement AJAX loading here if needed
        // fetch(endpoints[tabIndex]).then(response => response.json()).then(data => { ... });
    }
}
</script>