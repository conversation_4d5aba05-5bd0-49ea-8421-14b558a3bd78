@php
$connections = $connections ?? [];
@endphp

<div class="space-y-6">
    <div class="flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-900">Database Connection Details</h3>
        <div class="text-sm text-gray-500">
            {{ count($connections) }} connections configured
        </div>
    </div>

    @if(!empty($connections))
        <x-dashboard.data-table 
            :headers="['Connection', 'Type', 'Host:Port', 'Database', 'Status', 'Tables', 'Response Time', 'Details']"
            :rows="collect($connections)->map(function($conn) {
                $hostPort = $conn['host'];
                if (isset($conn['port']) && $conn['port'] !== 'N/A') {
                    $hostPort .= ':' . $conn['port'];
                }
                
                return [
                    '<div class=\'font-medium text-gray-900\'>' . e($conn['name']) . '</div>',
                    '<span class=\'inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800\'>' . 
                        strtoupper(e($conn['driver'])) . 
                    '</span>',
                    '<code class=\'text-sm\'>' . e($hostPort) . '</code>',
                    '<code class=\'text-sm\'>' . e($conn['database']) . '</code>',
                    '<x-dashboard.status-badge :status=\'' . $conn['status'] . '\' />',
                    '<span class=\'text-sm font-medium\'>' . number_format($conn['table_count']) . '</span>',
                    '<span class=\'text-sm ' . ($conn['response_time'] > 1000 ? 'text-red-600' : ($conn['response_time'] > 500 ? 'text-yellow-600' : 'text-green-600')) . '\'>' . 
                        $conn['response_time'] . 'ms' .
                    '</span>',
                    ($conn['error'] ? 
                        '<div class=\'text-xs text-red-600\' title=\'' . e($conn['error']) . '\'>' . 
                            e(Str::limit($conn['error'], 40)) . 
                        '</div>' : 
                        '<span class=\'text-green-600\'>✓</span>'
                    )
                ];
            })->toArray()"
        />

        <!-- Connection Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            @php
            $connectedCount = collect($connections)->where('status', 'connected')->count();
            $failedCount = collect($connections)->where('status', 'failed')->count();
            $totalTables = collect($connections)->sum('table_count');
            $avgResponseTime = collect($connections)->where('status', 'connected')->avg('response_time');
            @endphp

            <x-dashboard.metric-card 
                label="Connected"
                :value="$connectedCount . '/' . count($connections)"
                :description="round(($connectedCount / count($connections)) * 100) . '% success rate'"
                icon="✅"
            />

            <x-dashboard.metric-card 
                label="Failed"
                :value="$failedCount"
                :description="$failedCount > 0 ? 'Needs attention' : 'All healthy'"
                icon="❌"
            />

            <x-dashboard.metric-card 
                label="Total Tables"
                :value="number_format($totalTables)"
                description="Across all databases"
                icon="📊"
            />

            <x-dashboard.metric-card 
                label="Avg Response"
                :value="round($avgResponseTime, 1) . 'ms'"
                :description="$avgResponseTime < 100 ? 'Excellent' : ($avgResponseTime < 500 ? 'Good' : 'Slow')"
                icon="⚡"
            />
        </div>
    @else
        <div class="text-center py-12 bg-gray-50 rounded-lg">
            <div class="text-gray-500 mb-2">🗄️</div>
            <div class="text-gray-500">No database connections configured</div>
        </div>
    @endif

    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h4 class="text-sm font-medium text-yellow-900 mb-2">Database Configuration</h4>
        <div class="text-sm text-yellow-700 space-y-1">
            <p>• Database connections are configured in <code class="bg-yellow-100 px-1 rounded">config/database.php</code></p>
            <p>• Connection credentials are stored in environment variables</p>
            <p>• Red status indicates connection failures - check credentials and network connectivity</p>
            <p>• Response times over 500ms may indicate network or performance issues</p>
        </div>
    </div>
</div>