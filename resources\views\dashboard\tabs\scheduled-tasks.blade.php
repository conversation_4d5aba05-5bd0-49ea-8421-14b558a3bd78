@php
$dashboardService = app(\App\Services\DashboardService::class);
$tasks = $dashboardService->getScheduledTasksInfo();
@endphp

<div class="space-y-6">
    <div class="flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-900">Scheduled Tasks</h3>
        <div class="text-sm text-gray-500">
            Total: {{ count($tasks) }} tasks
        </div>
    </div>

    @if(!empty($tasks))
        <x-dashboard.data-table 
            :headers="['Task Name', 'Schedule', 'Human Readable', 'Description', 'Next Run', 'Timezone']"
            :rows="collect($tasks)->map(function($task) {
                return [
                    '<code class=\'text-sm bg-gray-100 px-2 py-1 rounded\'>' . e($task['command']) . '</code>',
                    '<code class=\'text-xs bg-blue-50 px-2 py-1 rounded text-blue-700\'>' . e($task['expression']) . '</code>',
                    '<span class=\'text-sm font-medium text-green-700\'>' . e($task['human_readable']) . '</span>',
                    '<span class=\'text-sm text-gray-600\'>' . e($task['description']) . '</span>',
                    '<span class=\'text-sm text-gray-900\'>' . e($task['next_run']) . '</span>',
                    '<span class=\'text-xs text-gray-500\'>' . e($task['timezone']) . '</span>'
                ];
            })->toArray()"
        />
    @else
        <div class="text-center py-12 bg-gray-50 rounded-lg">
            <div class="text-gray-500 mb-2">📅</div>
            <div class="text-gray-500">No scheduled tasks configured</div>
            <div class="text-sm text-gray-400 mt-1">Tasks will appear here when configured in the Laravel scheduler</div>
        </div>
    @endif

    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 class="text-sm font-medium text-blue-900 mb-2">About Scheduled Tasks</h4>
        <p class="text-sm text-blue-700">
            These are tasks configured in Laravel's task scheduler. They run automatically based on their cron expressions. 
            To view the actual scheduler configuration, check <code>app/Console/Kernel.php</code>.
        </p>
    </div>
</div>