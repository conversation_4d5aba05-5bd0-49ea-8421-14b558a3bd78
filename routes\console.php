<?php

use App\Migrate\CrmUlysses\MigrateProduct;
use App\Migrate\Ep\MigrateOrgGovernment;
use App\Migrate\Ep\UpdatePersonnelSoftcert;
use App\Migrate\Crm\SetUsernamePasswordPTJUsers;
use App\Migrate\Crm\SendEmailUsernameSelfPortal;
use Carbon\Carbon;
use App\Report\Crm\CaseAverageOpenPortal;
use App\Report\Crm\CaseAverageOpenPortalDaily;
use App\Report\Crm\CaseAverageOpenPortalMonthly;
use App\Migrate\Crm\FixedCRMData;
use App\Migrate\Nextgen\MigrateSupplierUsersNextgen;
use App\Migrate\Nextgen\MigrateOrgGovernmentNextgen;
use App\Migrate\Nextgen\MigrateOrgGovernmenUserstNextgen;
use App\Migrate\Nextgen\MigrateSupplierNextgen;
use App\Migrate\Nextgen\SyncGovernmentInfo;
use App\Migrate\Nextgen\SyncGovernmentUsersInfo;
use App\Migrate\Nextgen\SyncSupplierInfo;
use App\Migrate\Nextgen\SyncSupplierUsersInfo;
use App\Migrate\MigrateUtils;
use App\Migrate\Nextgen\CRMService;
use App\Migrate\Crm\FixedPhone;
use App\Migrate\Crm\UpdateSLA;
use App\Migrate\Crm\UpdateCheckCaseStatusExcel;
use App\Migrate\Crm\UpdateTaskMissingExcel;
use App\Migrate\Crm\ModifyCase;
use App\Report\Crm\CaseDetailReport;
use App\Models\Cases;
use App\Document\LmsTrainingCertGenerator;
use App\Document\LmsTrainingNotes;
use App\Report\Crm\CaseDetailIncidentService;
use App\Migrate\Crm\AutocloseCase;
use App\Report\Crm\CaseDetailEnquiry;
use App\Report\Crm\CaseDetailByMinistry;
use App\Migrate\Crm\DuplicateCase;
use App\Migrate\Crm\checkSLA4HourTask;
use App\Migrate\Crm\checkCSSLA;
use App\Report\Crm\CaseDetailAll;
use App\Report\Crm\statisticPerformanceAgentReport;
use App\Migrate\Crm\UpdateTopPtj;
use App\Migrate\Crm\BlastEmail;
use App\Report\Crm\DailyCollectionCdciReport;
use App\Report\Crm\statisticSupplierCasesMonitoringReport;
use App\Report\Crm\statisticPerformanceOutboundReport;
use App\Migrate\Crm\UpdateCaseStatus;
use App\Migrate\Crm\FindCase;
use App\Migrate\Crm\CreateRITTask;
use App\Report\Aspect\ReportAspectDailyCallDetail;
use App\Report\Crm\CaseCrmOnsiteByPtj;
use App\Migrate\Crm\UpdateCaseS3;
use App\Migrate\Casb\MigrateAccount;
use App\Migrate\Casb\MigrateCases;
use App\Report\MyTvAltel\CaseDetailReport as MyTvAltelCaseDetailReport;
use App\Migrate\Crm\CheckCaseExecutionDate;
use App\Report\Crm\CaseDetailAllTask;
use App\Migrate\Crm\CheckBugsCase;
use App\Report\CrmSsm\CaseDetailReport as CrmSsmCaseDetailReport;
use App\Report\CrmSsm\TestCaseDetailReport as TestCrmSsmCaseDetailReport;
use App\Report\CrmSsm\OpenCaseDetailReport;
use App\Migrate\CrmSsm\MigrateCustomerProfile;
use App\Migrate\CrmSsm\MigrateCase as CrmSsmMigrateCase;
use App\Migrate\CrmSsm\MigrateCaseHistory as CrmSsmMigrateCaseHistory;
use App\Migrate\Crm\SyncAccountEp;
use App\Report\CrmJbal\CaseDetailReportCrmJbal;
use App\Http\Controllers\CrmSsm\EmailListener;
use App\Migrate\Crm\InvalidCase;
use App\Http\Controllers\CrmSsm\CloseEscalatedCase;
use App\Http\Controllers\CrmSsm\DeleteInvalidCase;
use App\Migrate\CrmSsm\UpdateCaseDetail;
use App\Migrate\CrmSsm\CheckEmailInboundCrmSsm;
use App\Migrate\CrmSsm\DeleteDuplicateCaseSsm;
use App\Migrate\CrmSsm\CheckMissingTask;
use App\Report\Crm\CaseDetailPerubahanData;
use App\Report\CrmGamuda\CaseDetailReport as CrmGamudaCaseDetailReport;
use App\Migrate\Crm\CheckAssignedCaseNoPendingTask;
use App\Migrate\Common\SetInactiveUserCRM;
use App\Migrate\Crm\AutoApprovalS4;
use App\Migrate\Crm\AutoFillCategoryEaduan;
use App\Migrate\CrmVantage\MigrateAccount as MigrateAccountCrmVantage;
use App\Migrate\CrmVantage\MigrateContact as MigrateContactCrmVantage;
use App\Migrate\CrmVantage\MigrateCase as MigrateCaseCrmVantage;
use App\Migrate\CrmVantage\MigrateUser as MigrateUserCrmVantage;
use App\Migrate\Crm\PomSlaManagement;
use App\Report\Crm\CaseRejectedEaduan;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Schedule;

/*
  |--------------------------------------------------------------------------
  | Console Routes
  |--------------------------------------------------------------------------
  |
  | This file is where you may define all of your Closure based console
  | commands. Each Closure is bound to a command instance allowing a
  | simple approach to interacting with each command's IO methods.
  |
 */

Artisan::command('migrate-product', function () {
    MigrateProduct::runMigrate();
})->describe('Migrate Produk Category and Product from Ulysses into SuiteCRM');


Artisan::command('migrate-crm', function () {
    MigrateOrgGovernment::runMigrate();
    //$data = MigrateOrgGovernment::getQueryUsersPtjFromEp(18599);
    //dd($data);
});

Artisan::command('test-mail', function () {
    sendErrorEmail("Test email is successful.... Tq");
    //$data = MigrateOrgGovernment::getQueryUsersPtjFromEp(18599);
    //dd($data);
});

Artisan::command('sample', function () {
    $filename = '1000APOVE4000012018041602119.GPG';
    $date = substr($filename, 15, 8);

    $tarikhExtraFile = Carbon::createFromFormat('Ymd', '20180415')->startOfDay();
    $tarikh = Carbon::createFromFormat('Ymd', $date)->startOfDay();
    $isGreater = $tarikh->gt($tarikhExtraFile);
    dd($isGreater);
});

/*
Artisan::command('migrate-specialist', function () {
    MigrateSpecialist::runMigrate();
})->describe('Migrate users specialist from ulysses into SuiteCRM.');

Artisan::command('migrate-staff', function () {
    MigrateStaff::runMigrate();
})->describe('Migrate users staff from ulysses into SuiteCRM.');

Artisan::command('migrate-invoice', function () {
    $dateStart = '2017-02-01';
    $dateEnd = '2017-05-01';
    MigrateInvoice::runMigrate($dateStart, $dateEnd);
})->describe('Migrate invoices and related from ulysses into SuiteCRM.');

Artisan::command('insert-user-group', function () {
    InsertUserGroup::runMigrate();
})->describe('Migrate list users from excel into suitecrm.');

Artisan::command('migrateinvoicefrommysql', function () {
    MigrateInvoiceFromMysql::runMigrate();
})->describe('Migrate invoices and related from data in MYSQL(Already migrated ulysses into MYSQL)  into SuiteCRM.');

Artisan::command('migrate-invoice-mysql', function () {
    $dateStart = '2017-01-01';
    $dateEnd = '2017-03-30';
    MigrateInvoiceFromMysql::runMigrate($dateStart, $dateEnd);
})->describe('Migrate based specific date : invoices and related table from data in MYSQL(Already migrated ulysses into MYSQL)  into SuiteCRM.');

Artisan::command('insert-trainee-crm', function () {
    TraineeListCrm::runMigrate();
})->describe('Execute excel files to insert / update list users into database. This for training purpose. Do not simply run If you do know about it. Execute this command, will update users based on list to be first time login. ');

Artisan::command('insert-secondgroup-user', function () {
    SecondGroupUserMigration::runMigrate();
})->describe('Execute excel files to insert multiple securitygroups into database. ');

Artisan::command('clean-ptj-active', function () {
    CleaningPTJActive::runCleaning();
})->describe('Execute excel files to set ALL PTJ as Active based on Excel. This program will set all account (PTJ) as InActive then proceed to UPDATE based on list excel as ACTIVE Account PTJ.');

Artisan::command('fix-data-crm', function () {
    FixedCRMData::runFixedData();
})->describe('After Migration, some fields need to fixed by program to get real data such as created_by and modified_user_id. ');

Artisan::command('clean-users-role', function () {
    CleanUsersGroup::runCleaning();
})->describe('Execute excel files to clean users roles based on Excel. This program will remove users not include in Excel files');
*/

/*
Artisan::command('migrate-invoice', function () {
    $dateStart = '2017-02-01';
    $dateEnd = '2017-04-01';
    MigrateInvoice::runMigrate($dateStart, $dateEnd);
})->describe('Migrate invoices and related from ulysses into SuiteCRM.');
*/

Artisan::command('fix-data-crm', function () {
    FixedCRMData::runFixedData();
})->describe('After Migration, some fields need to fixed by program to get real data such as created_by and modified_user_id. ');


Artisan::command('map-nextgen-gov', function () {
    $dtStartTime = Carbon::now();
    MigrateOrgGovernmentNextgen::runMigrate();
    MigrateOrgGovernmenUserstNextgen::runMigrate();
    var_dump('COMMAND:: map-nextgen-gov -> Completed  --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    Log::info('COMMAND:: map-nextgen-gov -> Completed  --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
})->describe('Integration CRM with Nextgen. One time Migration -- Map Org Gov. code CRM with Nextgen (PM Modules)');
Artisan::command('map-nextgen-gov-users', function () {
    MigrateOrgGovernmenUserstNextgen::runMigrate();
})->describe('Integration CRM with Nextgen. One time Migration -- Map Org Gov. Users CRM with Nextgen');

Artisan::command('map-nextgen-gov-others', function () {
    $dtStartTime = Carbon::now();
    SyncGovernmentInfo::createGovernmentInfo();
    SyncGovernmentInfo::createGovernmentFactoringInfo();
    var_dump('COMMAND:: map-nextgen-gov-others -> Completed  --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    Log::info('COMMAND:: map-nextgen-gov-others -> Completed  --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
})->describe('Integration CRM with Nextgen. One time Migration -- Map Org Gov. Others CRM with Nextgen');


Artisan::command('map-nextgen-supplier', function () {
    $dtStartTime = Carbon::now();
    MigrateSupplierNextgen::runMigrate();
    var_dump('COMMAND:: map-nextgen-supplier -> Completed  --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    Log::info('COMMAND:: map-nextgen-supplier -> Completed  --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
})->describe('Integration CRM with Nextgen. One time Migration -- Map Account(SUPPLIER) CRM with Nextgen (SM Modules)');

Artisan::command('supplier-mof-check-more-than-one', function () {
    $dtStartTime = Carbon::now();
    MigrateSupplierNextgen::runMigrateMofMoreThanOneGenerated();
    var_dump('COMMAND:: supplier-mof-check-more-than-one -> Completed  --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    Log::info('COMMAND:: supplier-mof-check-more-than-one -> Completed  --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
})->describe('Integration CRM with EP Supplier MOF with more than one generated ');


Artisan::command('map-nextgen-supplier-users', function () {
    $dtStartTime = Carbon::now();
    MigrateSupplierUsersNextgen::runMigrate();
    var_dump('COMMAND:: map-nextgen-supplier-users -> Completed  --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    Log::info('COMMAND:: map-nextgen-supplier-users -> Completed  --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
})->describe('Integration CRM with Nextgen. One time Migration -- Map Contacts(SUPPLIER) CRM with Nextgen (SM Modules)');


Artisan::command('update-nextgen-gov', function () {
    SyncGovernmentInfo::runUpdateGovernmentInfo();
})->describe('Integration CRM with Nextgen. Map Org Gov. code CRM with Nextgen');
Artisan::command('update-nextgen-gov-users', function () {
    SyncGovernmentUsersInfo::runUpdateGovernmentUsersInfo();
})->describe('Integration CRM with Nextgen. Update Users Government CRM with Nextgen');


Artisan::command('update-nextgen-supplier', function () {
    $dtStartTime = Carbon::now();

    $periodMinute = env('DATA_PERIOD_BY_MINUTE', '5');
    //'05-JUL-17 05.08.54.982000000 PM';

    $dateStart = '2017-07-05 17:08:00';
    $dateEnd = '2017-07-05 17:09:00';

    $cDateStart = Carbon::parse('2017-07-05 05:07:00');
    $cDateEnd = Carbon::parse('2017-07-05 05:09:00');

    $logScheduler = CRMService::addLogScheduler($cDateStart, $cDateEnd, $periodMinute, 'UpdateSupplierInfo');
    if ($logScheduler == null) {
        Log::info(' Action Stop! Other Program still running. ');
        exit;
    }

    //$dateStart = '05-JUL-17 05.08.00 PM';
    //$dateEnd = '05-JUL-17 05.09.00 PM';

    SyncSupplierInfo::runUpdateSupplierInfo($logScheduler, $dateStart, $dateEnd);

    $logsdata = ' SUCCESS - executing Integration Nextgen (UpdateSupplierInfo) to CRM on ' . Carbon::now()->format('d-M-y') . ' , Completed --- Taken Time : ' .  json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    Log::info($logsdata);
})->describe('Integration CRM with Nextgen. Map Supplier CRM with Supplier Nextgen');
Artisan::command('update-nextgen-supplier-users', function () {
    SyncSupplierUsersInfo::runUpdateSupplierUsersInfo();
})->describe('Integration CRM with Nextgen. Update Users Supplier CRM with Nextgen');


Artisan::command('set-username-users-ptj', function () {
    SetUsernamePasswordPTJUsers::run();
})->describe('CRM -> set username and password for List of PTJ Users in Excel');

Artisan::command('send-emails-users-ptj', function () {
    SendEmailUsernameSelfPortal::run();
})->describe('CRM -> send email users ptj ->  username and password');

Artisan::command('fixed-state', function () {
    FixedCRMData::runFixedData();
})->describe('CRM -> fixed address state into value_code ');

Artisan::command('fixed-phone-number', function () {
    FixedPhone::runFixedPhone();
})->describe('CRM -> fixed phone number by removing dash (-), plus symbol (+), space ( ), six (6) at the front string ');

Artisan::command('update-sla-param', function () {
    $dateStart = '2024-08-21';
    $dateEnd = Carbon::now();
    UpdateSLA::runUpdateSLA($dateStart, $dateEnd);
})->describe('CRM -> stop sla');

Artisan::command('update-softcert', function () {
    UpdatePersonnelSoftcert::run();
})->describe('Generate - Script Update Softcert');

Artisan::command('get-icno', function () {
    $icno = '630128125483';
    $xmlContents = "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/' xmlns:crs='http://tempuri.org/CRSService'><soapenv:Header/><soapenv:Body><crs:retrieveCitizensDataReq><AgencyCode>130101</AgencyCode><BranchCode>JPNCRS</BranchCode><UserId>JPNADM2</UserId><TransactionCode>T2</TransactionCode><RequestDateTime>2016-04-26T10:41:00</RequestDateTime><ICNumber>$icno</ICNumber><RequestIndicator>A</RequestIndicator></crs:retrieveCitizensDataReq></soapenv:Body></soapenv:Envelope>";
    $xmlContents = '"' . $xmlContents . '"';
    $urlIdentity = "https://esb.myidentity.gov.my:81/crsservice";
    $urlHeader = "'Content-Type: application/xml'";
    $commands  = [
        "curl -k " . $urlIdentity . " --header  " . $urlHeader . "  -d " . $xmlContents,
    ];
    SSH::into('osb')->run($commands, function ($line) use ($icno) {
        $data = $line . PHP_EOL;
        if (strpos($data, $icno) !== false) {
            $p = xml_parser_create();
            xml_parse_into_struct($p, $data, $vals, $index);
            xml_parser_free($p);
            dd($vals);
        }
    });
})->describe('OSB get ICNO');


Artisan::command('get-apive', function () {

    $epNo = "eP-1000I011E";
    $commands  = [
        'cd /batch/Temp',
        'grep -E "' . $epNo . '" 1000APIVE40000120*.GPG',
    ];
    $filesFound = array();
    SSH::into('portal')->run($commands, function ($line) use ($epNo, &$filesFound) {
        $data = $line . PHP_EOL;
        var_dump($data);

        if (strpos($data, $epNo) !== false) {
            $arrayData  = (explode("\n", $data));
            foreach ($arrayData  as $str) {
                $filename = substr($str, 0, 30);
                //var_dump($filename);
                if (strlen($filename) > 25)
                    array_push($filesFound, trim($filename));
            }
        }
        var_dump($filesFound);
    });
})->describe('Portal get APIVE');

Artisan::command('check-gfmas-out', function () {
    $commands  = [
        'cd /batch/1GFMAS/OUT',
        'find 1000APIVE40000*.GPG',
        'exit',
    ];
    $filesFound = array();
    SSH::into('portal')->run($commands, function ($line) use (&$filesFound) {
        $data = $line . PHP_EOL;
        $arrayData  = (explode("\n", $data));
        foreach ($arrayData  as $str) {
            $filename = substr($str, 0, 31);
            $pos = strpos($filename, '1000APIVE40000120');
            if ($pos !== false) {
                if (strlen($filename) > 25) {
                    array_push($filesFound, trim($filename));
                }
            }
        }
    });
    var_dump($filesFound);
})->describe('Portal check APIVE OUT Folder');

Artisan::command('check-gfmas-in', function () {
    $commands  = [
        'cd /batch/1GFMAS/IN',
        'find 1000APOVE40000120*.GPG',
        'exit',
    ];
    $filesFound = array();
    SSH::into('portal')->run($commands, function ($line) use (&$filesFound) {
        $data = $line . PHP_EOL;
        $arrayData  = (explode("\n", $data));
        foreach ($arrayData  as $str) {
            $filename = substr($str, 0, 31);
            //var_dump($filename);
            if (strlen($filename) > 25)
                array_push($filesFound, trim($filename));
        }
        var_dump($filesFound);
    });
})->describe('Portal check APIVE IN Folder');

Artisan::command('gfmas-alive', function () {
    $data = array();
    $commands  = [
        "echo 'exit' | sftp -oPort=2022 eperolehan@10.38.206.73",
    ];
    SSH::into('osb')->run($commands, function ($line) use (&$data) {
        $result = $line . PHP_EOL;
        var_dump($result);
        array_push($data, trim($result));
    });
    dd($data);
})->describe('OSB - OSB get connection 1GFMAS');

Artisan::command('gfmas-out', function () {
    $data = array();
    $commands  = [
        "echo 'ls -lr' | sftp -oPort=2022 eperolehan@10.38.206.73:OUT",
        "exit",
    ];
    SSH::into('osb')->run($commands, function ($line) use (&$data) {
        $result = $line . PHP_EOL;
        $arrayData  = (explode("\n", $result));
        foreach ($arrayData  as $str) {
            $pos = strpos($str, '.GPG');
            if ($pos !== false) {
                $filename = substr($str, -31);
                array_push($data, trim($filename));
            }
        }
        //array_push($data,trim($result));
    });
    dd($data);
})->describe('OSB - OSB get connection 1GFMAS');


Artisan::command('gfmas-in', function () {
    $data = array();
    $commands  = [
        "echo 'ls -lr' | sftp -oPort=2022 eperolehan@10.38.206.73:IN",
        "exit",
    ];
    SSH::into('osb')->run($commands, function ($line) use (&$data) {
        $result = $line . PHP_EOL;
        $arrayData  = (explode("\n", $result));
        foreach ($arrayData  as $str) {
            $pos = strpos($str, '.GPG');
            if ($pos !== false) {
                $filename = substr($str, -31);
                array_push($data, trim($filename));
            }
        }
    });
    dd($data);
})->describe('OSB - OSB get connection 1GFMAS');



// 2018/SM/KC-23022018-0004/SD/
// FARNOR-GLOBAL.pdf
Artisan::command('portal-get-cert-mof', function () {
    $remotePath =  "/docuRepo/prd/ngep/2018/SM/VIRTUAL_CERT/589670_20180116_143832.pdf";
    $localPath = "C:\\kaizen\\589670_20180116_143832.pdf";
    SSH::into('portal')->get($remotePath, $localPath);
})->describe('Portal to get file pdf');

Artisan::command('portal-get-file-supplier', function () {
    $remotePath =  "/docuRepo/prd/ngep/2018/SM/KC-23022018-0004/SD/FARNOR-GLOBAL.pdf";
    $localPath = "C:\\kaizen\\KC-23022018-0004_FARNOR-GLOBAL.pdf";
    SSH::into('portal')->get($remotePath, $localPath);
})->describe('Portal to get file pdf');

Artisan::command('portal-get-file-temp', function () {
    $filename = "1102AR50240000120180517001.GPG";
    $remotePath =  "/batch/Temp/" . $filename;
    $localPath = "C:\\kaizen\\" . $filename;
    SSH::into('portal')->get($remotePath, $localPath);
})->describe('Portal to get file pdf');

Artisan::command('portal-get-file', function () {
    $filename = "1102AR50240000120180517001.GPG";
    $remotePath =  "/batch/1GFMAS/ARCHIVE/OUT" . $filename;
    $localPath = "C:\\kaizen\\" . $filename;
    SSH::into('portal')->get($remotePath, $localPath);
})->describe('Portal to get file pdf');



Artisan::command('update-excel', function () {
    UpdateCheckCaseStatusExcel::run();
})->describe('Load and re-update excel');

Artisan::command('update-task-excel', function () {
    UpdateTaskMissingExcel::run();
})->describe('Load and re-update excel');
Artisan::command('check-task-missing-status', function () {
    UpdateTaskMissingExcel::runCheckCaseStatus();
})->describe('Load and re-update excel');
Artisan::command('update-task-missing-info', function () {
    UpdateTaskMissingExcel::updateCaseInfoInTaskMissing();
})->describe('Load and re-update excel');
Artisan::command('add-task-missing', function () {
    UpdateTaskMissingExcel::runAddCaseAsTaskMissing();
})->describe('Load and re-update excel');
Artisan::command('add-cases-redmine', function () {
    UpdateTaskMissingExcel::addCaseAsRedmine();
})->describe('Load and re-update excel');


Artisan::command('modify-task-codification', function () {
    ModifyCase::modifyTaskAssignToCodification();
})->describe('Load and re-update task');
Artisan::command('resolve-task', function () {
    ModifyCase::resolveTask();
})->describe('Load and re-update task');
Artisan::command('resolve-task-excel', function () {
    ModifyCase::resolveTaskByExcel();
})->describe('Load and re-update task');
Artisan::command('modify-task-dm', function () {
    ModifyCase::modifyTaskAssignToDataManagement();
})->describe('Load and re-update task');
Artisan::command('modify-task-dm-excel', function () {
    ModifyCase::modifyTaskForDMByExcel();
})->describe('Load from excel and re-update task status ');



Artisan::command('report-case-stat', function () {
    $dateReport = Carbon::now();
    $report = new CaseStatisticReport;
    //$report->runMyTest($dateReport);
    $report->runStatisticUsers($dateReport);
})->describe('Case Statistic Report - Mytest');

Artisan::command('email-case-average', function () {
    $dateReport = Carbon::yesterday();
    $report = new CaseAverageOpenPortal;
    $report->runTest($dateReport);
})->describe('CS Response Time (Open Portal) - Yesterday');

Artisan::command('crm-cases-cs-response-param', function () {
    $reportDate = $this->ask('Date? (DD-MM-YYYY)');
    $contactMode = $this->ask('Contact Mode? (Open Portal/Email)');
    $this->info("Generating report for date: $reportDate and contact mode: $contactMode");
    $dateReport = Carbon::parse($reportDate);
    $report = new CaseAverageOpenPortal;
    $report->runTest($dateReport, $contactMode);
})->describe('CS Response Time By Selected Date and Contact Mode');

// daily response CS
Artisan::command('cs-average-daily-ondate', function () {
    $reportDate = $this->ask('Date? (DD-MM-YYYY)');
    $this->info("Generating report for date: $reportDate");
    $dateReport = Carbon::parse($reportDate);
    $report = new CaseAverageOpenPortalDaily;
    $report->runTest($dateReport);
})->describe('CS Response Time (Open Portal) By Selected Date');

//monthly response CS
Artisan::command('cs-average-monthly-param', function () {
    $reportDate = $this->ask('Date? (DD-MM-YYYY)');
    $this->info("Generating report for date: $reportDate");
    $dateReport = Carbon::parse($reportDate);
    $report = new CaseAverageOpenPortalMonthly;
    $report->run($dateReport);
})->describe('CS Response Time (Open Portal) By Month');
Artisan::command('report-case', function () {
    $report = new CaseStatisticReport;
    $dateReport = Carbon::now();
    $report->runToSpecificPerson($dateReport);
})->describe('Case Statistic Report - Mytest');


Artisan::command('report-cases', function () {
    $dateStart = '2018-01-01';
    $dateEnd = Carbon::yesterday()->format('Y-m-d');

    $dataParameter = collect([]);
    $dataParameter->put("date_start", $dateStart);  //mandatory
    $dataParameter->put("date_end", $dateEnd); //mandatory

    //Adding optinal, must add also checking optional in Query
    //    $dataParameter->put("status", 'Open_Assigned'); //optional
    //$dataParameter->put("request_type", 'enquiry'); //optional

    dump($dataParameter);
    dump($dataParameter->get('date_start'));

    CaseDetailReport::run($dataParameter);
    //    CaseDetailReport::run($dateStart,$dateEnd);

})->describe('Case Detail Incident/Service Report');

Artisan::command('case-detail-incident-service-param', function () {

    $dateStart = $this->ask('Type Date Start (YYYY-MM-DD) Sample 2018-01-01 :');

    $dateEnd = $this->ask('Type Date End (YYYY-MM-DD) Sample 2018-01-31 :');

    if (strlen($dateStart) == 10 && strlen($dateEnd) == 10) {
        CaseDetailIncidentService::run($dateStart, $dateEnd);
    } else {
        dd("Invalid Length!");
    }
})->describe('Case Detail Incident/Service Report');

Artisan::command('case-detail-incident-service-adhoc', function () {

    $dateStart = $this->ask('Type Date Start (YYYY-MM-DD) Sample 2018-01-01 :');
    $dateEnd = $this->ask('Type Date End (YYYY-MM-DD) Sample 2018-01-31 :');
    $status = $this->ask('Type Status: Open/Closed');

    if (strlen($dateStart) == 10 && strlen($dateEnd) == 10 && $status != '') {
        CaseDetailIncidentService::runAdhoc($dateStart, $dateEnd, $status);
    } else {
        dd("Invalid Length!");
    }
})->describe('Case Detail Incident/Service Report');

Artisan::command('case-detail-incident-service-adhoc-middleware', function () {

    $dateStart = $this->ask('Type Date Start (YYYY-MM-DD) Sample 2018-01-01 :');
    $dateEnd = $this->ask('Type Date End (YYYY-MM-DD) Sample 2018-01-31 :');

    if (strlen($dateStart) == 10 && strlen($dateEnd) == 10) {
        CaseDetailIncidentService::runAdhoc($dateStart, $dateEnd, null);
    } else {
        dd("Invalid Length!");
    }
})->describe('Case Detail Incident/Service Report');

Artisan::command('case-detail-enquiry-param', function () {

    $dateStart = $this->ask('Type Date Start (YYYY-MM-DD) Sample 2018-01-01 :');

    $dateEnd = $this->ask('Type Date End (YYYY-MM-DD) Sample 2018-01-31 :');

    if (strlen($dateStart) == 10 && strlen($dateEnd) == 10) {
        CaseDetailEnquiry::run($dateStart, $dateEnd);
    } else {
        dd("Invalid Length!");
    }
})->describe('Case Detail Enquiry Report');

Artisan::command('case-rejected-eaduan-param', function () {

    $dateStart = $this->ask('Type Date Start (YYYY-MM-DD) Sample 2018-01-01 :');

    $dateEnd = $this->ask('Type Date End (YYYY-MM-DD) Sample 2018-01-31 :');

    if (strlen($dateStart) == 10 && strlen($dateEnd) == 10) {
        CaseRejectedEaduan::run($dateStart, $dateEnd);
    } else {
        dd("Invalid Length!");
    }
})->describe('Case Detail Incident/Service Report');

Artisan::command('case-detail-param', function () {

    $dateStart = $this->ask('Type Date Start (YYYY-MM-DD) Sample 2018-01-01 :');

    $dateEnd = $this->ask('Type Date End (YYYY-MM-DD) Sample 2018-01-31 :');

    if (strlen($dateStart) == 10 && strlen($dateEnd) == 10) {
        CaseDetailAll::run($dateStart, $dateEnd);
    } else {
        dd("Invalid Length!");
    }
})->describe('Case Detail Report');

Artisan::command('case-detail-param-adhoc', function () {

    $dateStart = $this->ask('Type Date Start (YYYY-MM-DD) Sample 2018-01-01 :');

    $dateEnd = $this->ask('Type Date End (YYYY-MM-DD) Sample 2018-01-31 :');

    $requestType = array('incident', 'service', 'enquiry');
    $notIncludeCategory = array(10721, 10722, 10719, 10720); //to be excluded
    $notIncludeSubCategory = array('10712_15034', '10714_15842', '10713_15534', '10704_12247', '10711_13213'); //to be excluded

    if (strlen($dateStart) == 10 && strlen($dateEnd) == 10) {
        CaseDetailAll::runAdhoc(
            $dateStart,
            $dateEnd,
            $requestType,
            $notIncludeCategory,
            $notIncludeSubCategory
        );
    } else {
        dd("Invalid Length!");
    }
})->describe('Case Detail Report');

Artisan::command('case-detail-ministry-param', function () {

    $dateStart = $this->ask('Type Date Start (YYYY-MM-DD) Sample 2018-01-01 :');

    $dateEnd = $this->ask('Type Date End (YYYY-MM-DD) Sample 2018-01-31 :');

    $ministry = $this->ask('Ministry Name Sample KEMENTERIAN KESIHATAN :');

    if (strlen($dateStart) == 10 && strlen($dateEnd) == 10) {
        CaseDetailByMinistry::run($dateStart, $dateEnd, $ministry);
    } else {
        dd("Invalid Length!");
    }
})->describe('Case Detail Incident/Service Report');

Artisan::command('decrypt-data', function () {
    $target = '1GFMAS'; //PHIS //EP
    $data = '-----BEGIN PGP MESSAGE-----
Version: BCPG v1.50

hI4DkObbW5fUX4kQAf4igzW+bSIf2GerDnZ+TIQG9oCEY5BsqTD/PcbtER5EXiF1
obHaPT2+C62tAYyrcdPuv00hXKICEavQeoaOTbKKAf9nj0tK0gHEHstgvTDzp2uT
hD+ZuHk2FiauyvnNTNTZ8F2uWcUFlk2CIyKS7Hwg7XuddOTUZ6Q8/uM+b8SMTmu5
0q8BBuJCySt6L2p9XcpY6oz4atL0FzdhgZiUn7QvxvDpNQm3dUA/b6i/8WiZE7bg
ZcPxoTDVhLTmTVO5lQSS5KbXOwW6W5+c4woLCNooCbDcYOLLjYTWIqywCz/qKYGz
IzTVv8BdWjPjxBiq+P7BFjTKLBTItab18TsvK8MQGacWMMmUsHqZTJ+2qFWkvXOP
5/zvrmXyF438qzZGBTA1jDpnRBHDoVItZfT6d0R7m86s
=c+2q
-----END PGP MESSAGE-----';

    $xmlContents = "<x:Envelope xmlns:x='http://schemas.xmlsoap.org/soap/envelope/' xmlns:pgp1='http://www.ep.gov.my/Schema/1-0/PGP' xmlns:epm='http://www.ep.gov.my/Schema/1-0/epmf'>
        <x:Header/>
        <x:Body>
            <pgp1:EPMFRq>
                <epm:RqHeader>
                </epm:RqHeader>
                <pgp1:PGPRq>
                    <pgp1:Mode>D</pgp1:Mode>
                    <pgp1:Target>$target</pgp1:Target>
                    <pgp1:Input>$data</pgp1:Input>
                </pgp1:PGPRq>
            </pgp1:EPMFRq>
        </x:Body>
        </x:Envelope>";
    //$xmlContents = "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/' xmlns:crs='http://tempuri.org/CRSService'><soapenv:Header/><soapenv:Body><crs:retrieveCitizensDataReq><AgencyCode>130101</AgencyCode><BranchCode>JPNCRS</BranchCode><UserId>JPNADM2</UserId><TransactionCode>T2</TransactionCode><RequestDateTime>2016-04-26T10:41:00</RequestDateTime><ICNumber>$icno</ICNumber><RequestIndicator>A</RequestIndicator></crs:retrieveCitizensDataReq></soapenv:Body></soapenv:Envelope>";
    $xmlContents = '"' . $xmlContents . '"';
    $url = "http://192.168.63.205:7011/Common/Utilities/PGPSO";
    $urlHeader = "'Content-Type: application/xml'";
    $commands  = [
        "curl -k " . $url . " --header  " . $urlHeader . "  -d " . $xmlContents,
    ];
    SSH::into('osb')->run($commands, function ($line) {
        $data = $line . PHP_EOL;
        //dump($data);
        //if (strpos($data, $icno) !== false) {
        $p = xml_parser_create();
        xml_parse_into_struct($p, $data, $vals, $index);
        xml_parser_free($p);

        if (count($vals) > 0) {
            foreach ($vals as $val) {
                if ($val["tag"] == 'X:BODY') dump($val["value"]);
            }
        }

        //}
    });
})->describe('OSB get ICNO');


/**
 * Send an e-mail Test Only
 *
 * @param  Request  $error
 * @return Response
 */
if (!function_exists('sendErrorEmail')) {
    function sendErrorEmail($error)
{
    $data = array(
        "to" => ['<EMAIL>'],
        "subject" => 'Server:' . env('APP_ENV') . '- Test Only'
    );
    try {
        Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function ($m) use ($data) {
            $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
            $m->to($data["to"])
                //->cc($data["cc"])
                //->attach(storage_path('app/exports/cases').'/CaseDetailReport_2018-03-01_to_2018-03-31.xlsx')
                ->subject($data["subject"]);
        });
        dump('done send');
    } catch (\Exception $e) {
        Log::error('Console :: sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
        return $e;
    }
}
}



Artisan::command('case-redmine', function () {
    $redmineNoList = array(
        '19417', '19419', '19415', '18986', '18983', '18982', '18964', '18479', '18576', '18889', '18873', '18867', '18794', '18665', '18661', '18655', '18654', '18625', '18624', '19051', '19021', '18962', '18961', '19101', '19100', '19099', '19098', '19404', '19312', '19298', '19197', '19186', '19169', '19172', '19173', '19176', '19195', '19200', '19202', '19299', '19300', '19310', '19316', '19341', '19347'
    );

    foreach ($redmineNoList as $value) {
        $list = Cases::where('redmine_number', 'like', '%' . $value . '%')->get();
        foreach ($list as $obj) {
            //dump('Case No: '.$obj->case_number.'    ,Status: '.$obj->status.'    ,Resolution: '.$obj->resolution);
            dump('RedmineNo: ' . $value . '     Caseno:' . $obj->case_number . '    Status:' . $obj->status);
        }
    }
})->describe('Find Case No. by Redmine No. ');

Artisan::command('auto-close-case', function () {
    AutocloseCase::runCloseCase();
})->describe('Run Auto Close Case');

Artisan::command('checking-duplicate-case', function () {
    DuplicateCase::runCheckingDuplicateCase();
})->describe('Run Checking Duplicate Case');

Artisan::command('update-sla-flag3-task', function () {
    checkSLA4HourTask::runCheckingSLA4HourTask();
})->describe('Run Update Task Flag 3');

Artisan::command('update-cs-sla-task', function () {
    checkCSSLA::runCheckingCSSLA();
})->describe('Run Update SLA for CS');

//Artisan::command('auto-update-stop-time-cs', function(){
//    ActualStopTimeCS::runCheckingActualStopTimeCS();
//})->describe('Run Update STOP ACTUAL SLA for CS');

Artisan::command('case-detail-eaduan-email', function () {
    // CaseDetailOpenPortalEmail::run();
})->describe('Case Detail eAduan & Email Report');

Artisan::command('case-statistic-performance-agent-param', function () {
    $reportDate = $this->ask('Date? (YYYY-MM-DD)');
    statisticPerformanceAgentReport::run($reportDate);
})->describe('Statistic Performance Agent Report By Specific Date');

Artisan::command('auto-update-usertimesetting-crm', function () {
    // UpdateTimeSettingUserCRM::runUpdateTimeSettingUserCRM();
})->describe('Run Update SLA for CS');

Artisan::command('crm-update-top-ptj', function () {
    UpdateTopPtj::runUpdatePtj();
})->describe('Run Update Top PTJ 500');

Artisan::command('crm-blast-email', function () {

    $dateStart = Carbon::now()->subHour(9)->format('Y-m-d H:i:s');
    $dateEnd = Carbon::now()->subHour(8)->format('Y-m-d H:i:s');

    BlastEmail::blast($dateStart, $dateEnd);
})->describe('Send email to supplier/ptj when case resolved');

Artisan::command('daily-collection-cdci-report', function () {

    $dateStart = $this->ask('Type Date Start (YYYY-MM-DD) Sample 2018-01-01 :');

    $dateEnd = $this->ask('Type Date End (YYYY-MM-DD) Sample 2018-01-31 :');

    if (strlen($dateStart) == 10 && strlen($dateEnd) == 10) {
        DailyCollectionCdciReport::run($dateStart, $dateEnd);
    } else {
        dd("Invalid Length!");
    }
})->describe('Daily Collection Cdci Report');

Artisan::command('top-supplier-casemonitoring', function () {
    $dateReport = Carbon::yesterday();
    $report = new statisticSupplierCasesMonitoringReport;
    $report->runTest($dateReport);
})->describe('Statistic for CRM Cases Monitoring for Top 15 Supplier - Today');

Artisan::command('case-statistic-performance-outbound', function () {
    statisticPerformanceOutboundReport::run();
})->describe('Statistic Performance Agent Outbound Report');

Artisan::command('lms-cert-gen', function () {
    LmsTrainingCertGenerator::manualCertGenerator();
})->describe('Manual LMS Cert Generator');

Artisan::command('lms-cert-gen-event', function () {
    $eventCode = $this->ask('Please enter the event code:');
    LmsTrainingCertGenerator::certGenByEventCode($eventCode);
})->describe('Manual LMS Cert Generator by event code');

Artisan::command('lms-cert-gen-event-email', function () {
    $eventCode = $this->ask('Please enter the event code:');
    $email = $this->ask('Please enter participant email address:');
    LmsTrainingCertGenerator::certGenByEventCodeEmail($eventCode, $email);
})->describe('Manual LMS Cert Generator by event code and email');

Artisan::command('lms-note-gen', function () {
    LmsTrainingNotes::AutoLMSNotes();
})->describe('Auto LMS Notes Generator');

Artisan::command('check-case-status', function () {
    $dateReport = Carbon::now();
    UpdateCaseStatus::runUpdateCaseStatus($dateReport);
})->describe('Check Case Status with Null Value, then Update');

Artisan::command('find-case', function () {

    $documentNo = array(
        "skor", "score"
    );
    FindCase::runCheckingCase($documentNo);
})->describe('Find cases with given document number');

Artisan::command('auto-create-task-rit', function () {
    CreateRITTask::runCheckingCreateRITTask();
})->describe('Run Auto Create Task RIT');

Artisan::command('case-detail-onsite-support', function () {

    $dateStart = $this->ask('Type Date Start (YYYY-MM-DD) Sample 2018-01-01 :');

    $dateEnd = $this->ask('Type Date End (YYYY-MM-DD) Sample 2018-01-31 :');

    if (strlen($dateStart) == 10 && strlen($dateEnd) == 10) {
        CaseCrmOnsiteByPtj::run($dateStart, $dateEnd);
    } else {
        dd("Invalid Length!");
    }
})->describe('Case Detail Report');


Artisan::command('aspect-daily-call-detail-param', function () {

    $dateInput = $this->ask('Type Date (YYYY-MM-DD) Sample 2018-01-01 :');

    if (strlen($dateInput) == 10) {
        ReportAspectDailyCallDetail::runConsole($dateInput);
    } else {
        dd("Invalid Length!");
    }
})->describe('Aspect Daily Call Detail');

Artisan::command('checking-sla-severity', function () {

    UpdateCaseS3::runCheckingUpdateCaseS3();
})->describe('Checking Open Case With Severity');

// MIGRATE CRM MYTV/ALTEL //
Artisan::command('migrate-casb-account', function () {

    $type = $this->ask('Customer Type? Altel/Mytv.. Must type correctly as this will be inserted into database');
    MigrateAccount::runMigrate($type);
})->describe('Execute excel files to set ALL PTJ as Active based on Excel. This program will set all account (PTJ) as InActive then proceed to UPDATE based on list excel as ACTIVE Account PTJ.');

Artisan::command('migrate-casb-cases', function () {

    $type = $this->ask('Customer Type? Altel/Mytv.. Must type correctly as this will be inserted into database');
    MigrateCases::runMigrate($type);
})->describe('for CASB CRM cases migration.');

Artisan::command('case-dtl-n-stat-caseby-ctctmode', function () {
    $dtStartTime = Carbon::now();
    $dateStartReport = $this->ask('Date Start. Sample : 2021-01-31');
    $dateEndReport = $this->ask('Date End. Sample : 2021-01-31');

    try {
        $logsdata = 'MyTvAltelCaseDetailReport > Query Date Start : ' . $dtStartTime . ' , '
            . 'Query Date End : ' . Carbon::now();
        Log::info($logsdata);
        $report = new MyTvAltelCaseDetailReport;
        $report->run($dateStartReport, $dateEndReport);
    } catch (\Exception $exc) {
        Log::error('>> error happen!! ' . $exc->getMessage());
        echo $exc->getTraceAsString();
    }
})->describe('report case detail and statistic case by contact mode');

Artisan::command('check-execution-date-callin', function () {
    CheckCaseExecutionDate::run();
})->describe('Check Null Execution Date For Case Call-in');

Artisan::command('case-detail-alltask', function () {

    $dateStart = $this->ask('Type Date Start (YYYY-MM-DD) Sample 2018-01-01 :');

    $dateEnd = $this->ask('Type Date End (YYYY-MM-DD) Sample 2018-01-31 :');

    if (strlen($dateStart) == 10 && strlen($dateEnd) == 10) {
        CaseDetailAllTask::run($dateStart, $dateEnd);
    } else {
        dd("Invalid Length!");
    }
})->describe('Case Detail All Task Report');

Artisan::command('check-bug-cases', function () {
    CheckBugsCase::checkBugs();
});

Artisan::command('case-dtl-crm-ssm', function () {
    $dtStartTime = Carbon::now();
    $dateStartReport = $this->ask('Date Start. Sample : 2021-01-31');
    $dateEndReport = $this->ask('Date End. Sample : 2021-01-31');

    try {
        $logsdata = 'CrmSsmCaseDetailReport > Query Date Start : ' . $dtStartTime . ' , '
            . 'Query Date End : ' . Carbon::now();
        Log::info($logsdata);
        $report = new CrmSsmCaseDetailReport;
        $report->run($dateStartReport, $dateEndReport);
    } catch (\Exception $exc) {
        Log::error('>> error happen!! ' . $exc->getMessage());
        echo $exc->getTraceAsString();
    }
})->describe('report case detail crm ssm');

Artisan::command('test-case-dtl-crm-ssm', function () {
    $dtStartTime = Carbon::now();
    $dateStartReport = $this->ask('Date Start. Sample : 2021-01-31');
    $dateEndReport = $this->ask('Date End. Sample : 2021-01-31');

    try {
        $logsdata = 'CrmSsmCaseDetailReport > Query Date Start : ' . $dtStartTime . ' , '
            . 'Query Date End : ' . Carbon::now();
        Log::info($logsdata);
        $report = new TestCrmSsmCaseDetailReport;
        $report->run($dateStartReport, $dateEndReport);
    } catch (\Exception $exc) {
        Log::error('>> error happen!! ' . $exc->getMessage());
        echo $exc->getTraceAsString();
    }
})->describe('report case detail crm ssm');

Artisan::command('case-dtl-closed-crm-ssm', function () {
    $dtStartTime = Carbon::now();
    $dateStartReport = $this->ask('Date Start. Sample : 2021-01-31');
    $dateEndReport = $this->ask('Date End. Sample : 2021-01-31');

    try {
        $logsdata = 'CrmSsmCaseDetailReport > Query Date Start : ' . $dtStartTime . ' , '
            . 'Query Date End : ' . Carbon::now();
        Log::info($logsdata);
        $report = new CrmSsmCaseDetailReport;
        $report->runClosedOnly($dateStartReport, $dateEndReport);
    } catch (\Exception $exc) {
        Log::error('>> error happen!! ' . $exc->getMessage());
        echo $exc->getTraceAsString();
    }
})->describe('report case detail crm ssm');

Artisan::command('open-case-dtl-crm-ssm', function () {

    try {
        $logsdata = 'OpenCaseDetailReport';
        Log::info($logsdata);
        $report = new OpenCaseDetailReport;
        $report->run();
    } catch (\Exception $exc) {
        Log::error('>> error happen!! ' . $exc->getMessage());
        echo $exc->getTraceAsString();
    }
})->describe('report open case detail crm ssm');

Artisan::command('daily-open-case-dtl-crm-ssm-param', function () {

    try {
        $logsdata = 'OpenCaseDetailReport';
        Log::info($logsdata);
        $report = new OpenCaseDetailReport;
        $dateStart  = Carbon::yesterday()->format('Y-m-d');
        $dateEnd    = Carbon::now()->format('Y-m-d');
        $report->runOpenDaily($dateStart, $dateEnd);
    } catch (\Exception $exc) {
        Log::error('>> error happen!! ' . $exc->getMessage());
        echo $exc->getTraceAsString();
    }
})->describe('report open case detail crm ssm');

Artisan::command('case-dtl-crm-jbal', function () {
    $dtStartTime = Carbon::now();
    $dateStartReport = $this->ask('Date Start. Sample : 2021-01-31');
    $dateEndReport = $this->ask('Date End. Sample : 2021-01-31');

    try {
        $logsdata = 'CaseDetailReportCrmJbal > Query Date Start : ' . $dtStartTime . ' , '
            . 'Query Date End : ' . Carbon::now();
        Log::info($logsdata);
        $report = new CaseDetailReportCrmJbal;
        $report->run($dateStartReport, $dateEndReport, null);
    } catch (\Exception $exc) {
        Log::error('>> error happen!! ' . $exc->getMessage());
        echo $exc->getTraceAsString();
    }
})->describe('report case detail crm jbal');

Artisan::command('migrate-customer-profile-crmssm', function () {
    MigrateCustomerProfile::runMigrate();
})->describe('Read excel to migrate data customer profile to accounts table');

Artisan::command('migrate-case-crmssm', function () {
    CrmSsmMigrateCase::runMigrateCase();
})->describe('Read excel to migrate data cases into crm cases table');

Artisan::command('migrate-case-history-crmssm', function () {
    CrmSsmMigrateCaseHistory::runMigrateCaseHistory();
})->describe('Read excel to migrate case history into crm tasks table');

Artisan::command('update-case-description-crmssm', function () {
    CrmSsmMigrateCase::copyDescription();
})->describe('Copy description from tasks to cases');

Artisan::command('sync-all-organization', function () {
    $lastDate = '2020-12-31'; //$this->ask('Max Date To Check Account. Sample : 2021-01-31');
    if (strlen($lastDate) == 10) {
        SyncAccountEp::run($lastDate);
    } else {
        dd("Invalid Length!");
    }
})->describe('Recheck government account in crm to sync with ep');

Artisan::command('check-inbound-email-ssm', function () {
    EmailListener::checkEmailListener();
});

Artisan::command('check-invalid-casecrm', function () {
    InvalidCase::run();
});

Artisan::command('check-spam-email-crm-ep-param', function () {
    InvalidCase::checkSpamEmailCases();
});

Artisan::command('close-escalated-case', function () {
    $dateStart = $this->ask('Date Start. Sample : 2021-01-31');
    $dateEnd = $this->ask('Date End. Sample : 2021-01-31');
    CloseEscalatedCase::closeEscalatedCase($dateStart, $dateEnd);
});

Artisan::command('delete-invalid-case-param', function () {
    $dateStart = $this->ask('Date Start. Sample : 2021-01-31');
    $dateEnd = $this->ask('Date End. Sample : 2021-01-31');
    DeleteInvalidCase::deleteInvalidCase($dateStart, $dateEnd);
});

Artisan::command('update-case-ssm', function () {
    // NO NEED TO MANUALLY RENAME. PUT FILE AND RUN
    // Define the folder where your CSV files are located.
    $folderPath = 'app/Migrate/CrmSsm/data';

    $csvFiles = glob("$folderPath/*.csv");
    if (empty($csvFiles)) {
        $this->error('No CSV files found in the specified folder.');
        return;
    }

    // Sort the CSV files based on date modified in descending order.
    usort($csvFiles, function ($a, $b) {
        return filemtime($b) - filemtime($a);
    });

    // Display a list of available CSV files for the user to choose from.
    $selectedFile = $this->choice('Select a CSV file to process:', array_map('basename', $csvFiles));

    // Process the selected CSV file with only the filename and extension.
    $filenameWithExtension = basename($selectedFile);
    UpdateCaseDetail::run($filenameWithExtension);
})->describe('Update case details from a CSV file.');

Artisan::command('close-case-ssm', function () {
    $dateStart = $this->ask('Date Start. Sample : 2021-01-31');
    $dateEnd = $this->ask('Date End. Sample : 2021-01-31');
    UpdateCaseDetail::closeCase($dateStart, $dateEnd);
});

Artisan::command('close-case-ssm-bulk', function () {
    $limit = $this->ask('Enter the amount of record you want to process. Recommended 1000 if not urgent');
    UpdateCaseDetail::closeCaseBulk($limit);
});

Artisan::command('update-cases-bulk-closed-table', function () {
    $fileName = $this->ask('Name and extension of file only. File should be in app/Migrate/CrmSsm/data');
    UpdateCaseDetail::readCaseNumberFromExcel($fileName);
});

Artisan::command('update-inbound-ssm', function () {
    CheckEmailInboundCrmSsm::runCheckEmailInboundSchedulerCrmSsm();
});

Artisan::command('duplicate-case-ssm', function () {
    DeleteDuplicateCaseSsm::runCheckingDuplicateCase();
});

Artisan::command('delete-emptydesc-case-ssm-param', function () {
    $dateStart = $this->ask('Date Start. Sample : 2021-01-31');
    $dateEnd = $this->ask('Date End. Sample : 2021-01-31');
    DeleteInvalidCase::deleteEmptyDescCase($dateStart, $dateEnd);
});

Artisan::command('missing-task-ssm', function () {
    CheckMissingTask::run();
});

Artisan::command('pending-send-report', function () {
    // ReportMonitoringController::checkPendingSendReport();
});

Artisan::command('case-detail-perubahandata', function () {

    $dateStart = $this->ask('Type Date Start (YYYY-MM-DD) Sample 2018-01-01 :');
    $dateEnd = $this->ask('Type Date End (YYYY-MM-DD) Sample 2018-01-31 :');

    if (strlen($dateStart) == 10 && strlen($dateEnd) == 10) {
        CaseDetailPerubahanData::run($dateStart, $dateEnd);
    } else {
        dd("Invalid Length!");
    }
})->describe('Case Detail Perubahan');

Artisan::command('case-detail-crm-garuda-adhoc', function () {
    $dateStart = $this->ask('Type Date Start (YYYY-MM-DD) Sample 2018-01-01 :');
    $dateEnd = $this->ask('Type Date End (YYYY-MM-DD) Sample 2018-01-31 :');
    CrmGamudaCaseDetailReport::run($dateStart, $dateEnd);
})->describe('Get Case details for yesterday');

Artisan::command('case-open-task-completed-param', function () {
    CheckAssignedCaseNoPendingTask::run();
})->describe('Check case with status assigned/resolved with no pending task');

Artisan::command('set-inactive-user-crm', function () {
    // Ask for the Domain ID
    $domainId = $this->ask('Please enter your Domain ID');

    // Define the folder where your Excel files are located
    $folderPath = 'app/Migrate/Common/data';

    // Get all Excel files in the folder
    $files = glob("$folderPath/*.xlsx");

    // List the files and ask the user to choose one
    foreach ($files as $index => $file) {
        $this->line(($index + 1) . ". " . basename($file));
    }
    $fileIndex = $this->ask('Choose a file by entering its number');

    // Get the chosen file
    $filePath = $files[$fileIndex - 1];

    // Load the Excel file
    $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($filePath);

    // Get the first sheet in the Excel file
    $worksheet = $spreadsheet->getActiveSheet();

    // Iterate over the rows in the sheet
    foreach ($worksheet->getRowIterator() as $rowIndex => $row) {
        // Skip the first row (header)
        if ($rowIndex === 1) {
            continue;
        }
        // Get the cell iterator
        $cellIterator = $row->getCellIterator();

        // This loops all cells, even if a cell value is not set.
        // By default, only cells that have a value set will be iterated.
        $cellIterator->setIterateOnlyExistingCells(false);

        // Get the user email from the first cell of the row
        $userEmail = $cellIterator->current()->getValue();

        // If the cell is empty, break the loop
        if (empty($userEmail)) {
            break;
        }

        // Run the SetInactiveUserCrm method for the user email
        try {
            $setInactiveUserCrm = app(SetInactiveUserCrm::class);
            $results = $setInactiveUserCrm->run($userEmail, $domainId);

            // Display the results in the console
            foreach ($results as $result) {
                $this->info("User email: {$result['userEmail']}, Status: {$result['status']}, DB_CONNECTION: {$result['DB_CONNECTION']}");
            }
        } catch (Exception $e) {
            $this->error("Error setting user as inactive: " . $e->getMessage());
        }
    }
})->describe('Set user as inactive in CRM');

Artisan::command('reverse-set-inactive-user-crm', function () {
    // Ask for the Domain ID
    $domainId = $this->ask('Please enter your Domain ID');

    // Define the folder where your Excel files are located
    $folderPath = 'app/Migrate/Common/data';

    // Get all Excel files in the folder
    $files = glob("$folderPath/*.xlsx");

    // List the files and ask the user to choose one
    foreach ($files as $index => $file) {
        $this->line(($index + 1) . ". " . basename($file));
    }
    $fileIndex = $this->ask('Choose a file by entering its number');

    // Get the chosen file
    $filePath = $files[$fileIndex - 1];

    // Load the Excel file
    $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($filePath);

    // Get the first sheet in the Excel file
    $worksheet = $spreadsheet->getActiveSheet();

    // Iterate over the rows in the sheet
    foreach ($worksheet->getRowIterator() as $rowIndex => $row) {
        // Skip the first row (header)
        if ($rowIndex === 1) {
            continue;
        }
        // Get the cell iterator
        $cellIterator = $row->getCellIterator();

        // This loops all cells, even if a cell value is not set.
        // By default, only cells that have a value set will be iterated.
        $cellIterator->setIterateOnlyExistingCells(false);

        // Get the user email from the first cell of the row
        $userEmail = $cellIterator->current()->getValue();

        // If the cell is empty, break the loop
        if (empty($userEmail)) {
            break;
        }

        // Run the SetInactiveUserCrm method for the user email
        try {
            $setInactiveUserCrm = app(SetInactiveUserCrm::class);
            $results = $setInactiveUserCrm->reverse($userEmail, $domainId);

            // Display the results in the console
            foreach ($results as $result) {
                $this->info("User email: {$result['userEmail']}, Status: {$result['status']}, DB_CONNECTION: {$result['DB_CONNECTION']}");
            }
        } catch (Exception $e) {
            $this->error("Error setting user as active: " . $e->getMessage());
        }
    }
})->describe('Set the users back as active in CRM');

Artisan::command('auto-approve-s4-crm', function () { 
    AutoApprovalS4::run();
})->describe('Set the users back as active in CRM');


//crm vantage 
Artisan::command('migrate-account-crmvantage', function () { 
    MigrateAccountCrmVantage::runMigrate();
})->describe('Migrate Accounts From Vantage Point to CRM');

Artisan::command('migrate-contact-crmvantage', function () { 
    MigrateContactCrmVantage::runMigrate();
})->describe('Migrate Contacts From Vantage Point to CRM');

Artisan::command('migrate-case-crmvantage', function () { 
    MigrateCaseCrmVantage::runMigrate();
})->describe('Migrate Cases From Vantage Point to CRM');

Artisan::command('migrate-user-crmvantage', function () { 
    MigrateUserCrmVantage::runMigrate();
})->describe('Migrate Users From Vantage Point to CRM');

Artisan::command('poms-sla-flag2', function () { 
    $dateSla = $this->ask('Date SLA Stop. Sample : 2021-01-31');
    PomSlaManagement::checkingSLAFlag2($dateSla);
})->describe('Checking SLA CRM eP For Initial Task (Flag 2)');

Artisan::command('poms-sla-flag3', function () { 
    $dateSla = $this->ask('Date SLA Stop. Sample : 2021-01-31');
    PomSlaManagement::checkingSLAFlag3($dateSla);
})->describe('Checking SLA CRM eP For RIT Task (Flag 3)');

Artisan::command('poms-sla-severity', function () { 
    $dateSla = $this->ask('Date SLA Stop. Sample : 2021-01-31');
    PomSlaManagement::checkingSLASeverity($dateSla);
})->describe('Checking SLA CRM eP For Severity Task (Flag s1/s2/s3)');

Artisan::command('poms-sla-s4', function () { 
    $dateSla = $this->ask('Date SLA Stop. Sample : 2021-01-31');
    PomSlaManagement::checkingSLAS4($dateSla);
})->describe('Checking SLA CRM eP For S4 Task (Flag 4)');

Artisan::command('auto-fill-category-cancelled-eaduan', function () {
    AutoFillCategoryEaduan::runUpdateCaseCatgories();
})->describe('Run Auto Fill Case Cancelled Eaduan');

/*
|--------------------------------------------------------------------------
| Laravel 12 Scheduled Tasks
|--------------------------------------------------------------------------
|
| Here you may define all of your scheduled tasks using Laravel 12's new
| scheduling approach. These tasks were migrated from app/Console/Kernel.php
| to follow Laravel 12's best practices.
|
*/

// Schedule for CRM - Nextgen Integration
Schedule::command('integrate-nextgen-government')->everyFiveMinutes()->unlessBetween('04:00', '06:00');
Schedule::command('integrate-nextgen-government-users')->everyFiveMinutes()->unlessBetween('04:00', '06:00');
Schedule::command('integrate-nextgen-supplier')->everyFiveMinutes()->unlessBetween('04:00', '06:00');
Schedule::command('integrate-nextgen-supplier-users')->everyFiveMinutes()->unlessBetween('04:00', '06:00');

// Schedule send report CRM
Schedule::command('crm-cases-resolved')->dailyAt('06:10');
Schedule::command('case-detail-incident-service')->dailyAt('06:15');
Schedule::command('case-detail-enquiry')->monthlyOn(1, '06:30');
Schedule::command('case-detail-daily')->dailyAt('17:30');
Schedule::command('case-detail-monthly')->monthlyOn(1, '03:00');
Schedule::command('case-detail-incident-service-monthly')->monthlyOn(1, '7.00');
Schedule::command('case-rejected-eaduan-monthly')->monthlyOn(1, '8.30');

// SLA and Case Management
Schedule::command('update-sla')->everyFiveMinutes()->unlessBetween('04:00', '06:00');
Schedule::command('check-case-status-task-missing')->everyFiveMinutes();
Schedule::command('auto-close-case')->dailyAt('06:05');
Schedule::command('crm-cases-cs-response')->dailyAt('08:32');
Schedule::command('cs-average-monthly')->monthlyOn(1, '08:40');
Schedule::command('check-email-inbound')->everyMinute();
Schedule::command('checking-duplicate-case')->everyFiveMinutes()->unlessBetween('04:00', '06:00');
Schedule::command('update-cs-sla-task')->everyFiveMinutes()->unlessBetween('04:00', '06:00');
Schedule::command('case-detail')->dailyAt('06:20');
Schedule::command('case-statistic-performance-agent')->dailyAt('08:30');
Schedule::command('crm-cs-response-daily')->dailyAt('18:10');
Schedule::command('daily-collection-cdci-report')->dailyAt('06:30');
Schedule::command('top-supplier-casemonitoring')->dailyAt('18:00');
Schedule::command('case-statistic-performance-outbound')->dailyAt('08:30');
Schedule::command('lms-cert-gen')->dailyAt('09:00');
Schedule::command('lms-note-gen')->dailyAt('18:00');
Schedule::command('check-case-status')->hourly();
Schedule::command('update-sla-flag3-task')->everyFiveMinutes()->unlessBetween('04:00', '06:00');
Schedule::command('auto-ack-severity')->everyFiveMinutes()->unlessBetween('04:00', '06:00');
Schedule::command('auto-update-cases-severity')->twiceDaily(15, 22);
Schedule::command('auto-create-task-rit')->everyTenMinutes();
Schedule::command('case-detail-onsite-support-daily')->dailyAt('9:00');
Schedule::command('case-detail-onsite-support-today')->dailyAt('17:00');
Schedule::command('auto-check-execution-date-callin')->hourly();
Schedule::command('check-spam-email-crm-ep')->everyMinute();

// Additional scheduled tasks
Schedule::command('incident-case-ageing-specialist')->dailyAt('08:01');
Schedule::command('auto-check-bug-cases')->everyFiveMinutes()->unlessBetween('04:00', '06:00');
Schedule::command('checking-invalid-case')->everyFiveMinutes()->unlessBetween('04:00', '06:00');
Schedule::command('case-open-task-completed')->dailyAt('10:00');
Schedule::command('auto-approval-s4')->hourly()->unlessBetween('04:00', '06:00');
Schedule::command('pom-sla-management')->dailyAt('9:30');
Schedule::command('auto-fill-category-cancelled-eaduan')->dailyAt('06:45');

// CASB Report
Schedule::command('case-detail-mytv-altel-today')->dailyAt('08:10');
Schedule::command('case-detail-mytv-altel-weekly')->weeklyOn(1, '8:15'); // run every week on monday
Schedule::command('case-detail-mytv-altel-monthly')->monthlyOn(1, '8:30'); // run monthly on first day

// CRM SSM Bugs
Schedule::command('delete-invalid-case')->everyFiveMinutes();
Schedule::command('update-inbound-ssm')->everyMinute();
Schedule::command('duplicate-case-ssm')->everyFiveMinutes();
Schedule::command('delete-emptydesc-case-ssm')->everyFiveMinutes();
Schedule::command('missing-task')->hourly();
Schedule::command('open-invalid-case')->everyFiveMinutes();
