<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\SSH;
use App\Http\Controllers\BatchController;
use App\Http\Controllers\CrmSsm\CrmSsmController;
use App\Http\Controllers\DashboardController;

/*
|--------------------------------------------------------------------------
| Backend-Only Web Routes
|--------------------------------------------------------------------------
|
| This file contains essential routes for backend operations including:
| - File downloads for reports
| - REST API endpoints for data synchronization
| - Landing page
| - CRM integration endpoints
|
*/

// Dashboard - shows comprehensive system status
Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard.index');

// Dashboard API endpoints
Route::get('/dashboard/status', [DashboardController::class, 'getSystemStatus'])->name('dashboard.status');
Route::get('/dashboard/database-connections', [DashboardController::class, 'testDatabaseConnections'])->name('dashboard.database-connections');
Route::get('/dashboard/scheduled-tasks', [DashboardController::class, 'getScheduledTasks'])->name('dashboard.scheduled-tasks');
Route::get('/dashboard/console-commands', [DashboardController::class, 'getConsoleCommands'])->name('dashboard.console-commands');
Route::get('/dashboard/recent-logs', [DashboardController::class, 'getRecentLogs'])->name('dashboard.recent-logs');

// Redirect login attempts to external system
Route::get('/login', function () {
    return redirect(env('APP_EPSS_URL', 'https://epss.eperolehan.gov.my/login'));
});

/*
|--------------------------------------------------------------------------
| File Download Routes - Essential for Backend Operations
|--------------------------------------------------------------------------
*/

Route::get('/crm/casedetail/download/{fileName}', function ($fileName) {
    $headers = [
        'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
        'Cache-Control' => 'max-age=0',
    ];
    $fullPath = storage_path('app/exports/cases/'.$fileName);
    return response()->download($fullPath, $fileName, $headers);
});

Route::get('/aspect/report/download/{fileName}', function ($fileName) {
    $headers = [
        'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
        'Cache-Control' => 'max-age=0',
    ];
    $fullPath = storage_path('app/exports/aspect/'.$fileName);
    return response()->download($fullPath, $fileName, $headers);
});

Route::get('/crmssm/download/{fileName}', function ($fileName) {
    $headers = [
        'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
        'Cache-Control' => 'max-age=0',
    ];
    $fullPath = storage_path('app/exports/crmssm/'.$fileName);
    return response()->download($fullPath, $fileName, $headers);
});

Route::get('/crmgamuda/download/{fileName}', function ($fileName) {
    $headers = [
        'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
        'Cache-Control' => 'max-age=0',
    ];
    $fullPath = storage_path('app/exports/crmgamuda/'.$fileName);
    return response()->download($fullPath, $fileName, $headers);
});

Route::get('/crmjbal/download/{fileName}', function ($fileName) {
    $headers = [
        'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
        'Cache-Control' => 'max-age=0',
    ];
    $fullPath = storage_path('app/exports/crmssm/'.$fileName);
    return response()->download($fullPath, $fileName, $headers);
});

/*
|--------------------------------------------------------------------------
| Report Generation Routes - Used by Console Commands
|--------------------------------------------------------------------------
*/

Route::get('/case-stat-report', function () {
    $report = new \App\Report\Crm\CaseStatisticReport;
    $dateReport = \Carbon\Carbon::now();
    $report->runToSpecificPerson($dateReport);
    return "Successfully sent email";
});

Route::get('/case-stat-report-users', function () {
    $report = new \App\Report\Crm\CaseStatisticReport;
    $dateReport = \Carbon\Carbon::now();
    $report->runStatisticUsers($dateReport);
    return "Successfully sent email";
});

/*
|--------------------------------------------------------------------------
| Data Synchronization API Routes - External System Integration
|--------------------------------------------------------------------------
*/

Route::get('/rest/user/{loginID}', [BatchController::class, 'syncUser']);
Route::get('/rest/supplier/user/{loginID}', [BatchController::class, 'syncSupplierUser']);
Route::get('/rest/organization/user/{loginID}', [BatchController::class, 'syncOrganizationUser']);
Route::get('/rest/organization/orgcode/{orgcode}', [BatchController::class, 'syncOrganization']);
Route::get('/rest/organization/orgname/{orgname}', [BatchController::class, 'syncOrganizationByName']);

/*
|--------------------------------------------------------------------------
| CRM Integration Routes
|--------------------------------------------------------------------------
*/

Route::get('/rest/crm/{caseNumber}', [CrmSsmController::class, 'updateCase']);

/*
|--------------------------------------------------------------------------
| Document Download Routes - Essential for Backend Operations
|--------------------------------------------------------------------------
*/

Route::get('/download/mofcert/{mofCert}', function ($mofCert) {
    $data = DB::connection('oracle_nextgen_rpt')->table('SM_MOF_CERT')->where('cert_serial_no',$mofCert)
            ->where('record_status',1)->first();
    if($data){
        if($data->file_path != null){
            $remotePath =  "/docuRepo/prd/ngep/".$data->file_path."/".$data->file_name;
            $contents = SSH::into('portal')->getString($remotePath);
            return response()->attachmentPdf($contents);
        }
    }
    return "Supplier must login to eP and download first!";
});

Route::get('/download/attachment/cancel-reject/{attId}', function ($attId) {
    $data = DB::connection('oracle_nextgen_rpt')->table('sm_attachment')->where('attachment_id',$attId)->first();
    if($data){
        if($data->file_path != null){
            $remotePath =  "/docuRepo/prd/ngep/".$data->file_path."/".$data->file_name;
            $contents = SSH::into('portal')->getString($remotePath);
            return response()->attachmentPdf($contents);
        }
    }
    return "Document not found!";
});