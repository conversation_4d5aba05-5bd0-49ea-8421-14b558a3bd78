<?php
$dashboardService = app(\App\Services\DashboardService::class);
$tasks = $dashboardService->getScheduledTasksInfo();
?>

<div class="space-y-6">
    <div class="flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-900">Scheduled Tasks</h3>
        <div class="text-sm text-gray-500">
            Total: <?php echo e(count($tasks)); ?> tasks
        </div>
    </div>

    <?php if(!empty($tasks)): ?>
        <?php if (isset($component)) { $__componentOriginaled257b9288cec3ffd7548707d7eed3eb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginaled257b9288cec3ffd7548707d7eed3eb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.data-table','data' => ['headers' => ['Task Name', 'Schedule', 'Human Readable', 'Description', 'Next Run', 'Timezone'],'rows' => collect($tasks)->map(function($task) {
                return [
                    '<code class=\'text-sm bg-gray-100 px-2 py-1 rounded\'>' . e($task['command']) . '</code>',
                    '<code class=\'text-xs bg-blue-50 px-2 py-1 rounded text-blue-700\'>' . e($task['expression']) . '</code>',
                    '<span class=\'text-sm font-medium text-green-700\'>' . e($task['human_readable']) . '</span>',
                    '<span class=\'text-sm text-gray-600\'>' . e($task['description']) . '</span>',
                    '<span class=\'text-sm text-gray-900\'>' . e($task['next_run']) . '</span>',
                    '<span class=\'text-xs text-gray-500\'>' . e($task['timezone']) . '</span>'
                ];
            })->toArray()]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.data-table'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['headers' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(['Task Name', 'Schedule', 'Human Readable', 'Description', 'Next Run', 'Timezone']),'rows' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(collect($tasks)->map(function($task) {
                return [
                    '<code class=\'text-sm bg-gray-100 px-2 py-1 rounded\'>' . e($task['command']) . '</code>',
                    '<code class=\'text-xs bg-blue-50 px-2 py-1 rounded text-blue-700\'>' . e($task['expression']) . '</code>',
                    '<span class=\'text-sm font-medium text-green-700\'>' . e($task['human_readable']) . '</span>',
                    '<span class=\'text-sm text-gray-600\'>' . e($task['description']) . '</span>',
                    '<span class=\'text-sm text-gray-900\'>' . e($task['next_run']) . '</span>',
                    '<span class=\'text-xs text-gray-500\'>' . e($task['timezone']) . '</span>'
                ];
            })->toArray())]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginaled257b9288cec3ffd7548707d7eed3eb)): ?>
<?php $attributes = $__attributesOriginaled257b9288cec3ffd7548707d7eed3eb; ?>
<?php unset($__attributesOriginaled257b9288cec3ffd7548707d7eed3eb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaled257b9288cec3ffd7548707d7eed3eb)): ?>
<?php $component = $__componentOriginaled257b9288cec3ffd7548707d7eed3eb; ?>
<?php unset($__componentOriginaled257b9288cec3ffd7548707d7eed3eb); ?>
<?php endif; ?>
    <?php else: ?>
        <div class="text-center py-12 bg-gray-50 rounded-lg">
            <div class="text-gray-500 mb-2">📅</div>
            <div class="text-gray-500">No scheduled tasks configured</div>
            <div class="text-sm text-gray-400 mt-1">Tasks will appear here when configured in the Laravel scheduler</div>
        </div>
    <?php endif; ?>

    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 class="text-sm font-medium text-blue-900 mb-2">About Scheduled Tasks</h4>
        <p class="text-sm text-blue-700">
            These are tasks configured in Laravel's task scheduler. They run automatically based on their cron expressions. 
            To view the actual scheduler configuration, check <code>app/Console/Kernel.php</code>.
        </p>
    </div>
</div><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\dashboard\tabs\scheduled-tasks.blade.php ENDPATH**/ ?>