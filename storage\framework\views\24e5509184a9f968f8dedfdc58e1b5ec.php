<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['status', 'text' => null]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['status', 'text' => null]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
$badgeClasses = [
    'connected' => 'bg-green-100 text-green-800 border-green-200',
    'failed' => 'bg-red-100 text-red-800 border-red-200',
    'warning' => 'bg-yellow-100 text-yellow-800 border-yellow-200',
    'untested' => 'bg-gray-100 text-gray-800 border-gray-200',
    'production' => 'bg-red-100 text-red-800 border-red-200',
    'staging' => 'bg-yellow-100 text-yellow-800 border-yellow-200',
    'development' => 'bg-green-100 text-green-800 border-green-200',
    'local' => 'bg-blue-100 text-blue-800 border-blue-200'
];

$badgeClass = $badgeClasses[$status] ?? $badgeClasses['untested'];
$displayText = $text ?? ucfirst($status);
?>

<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border <?php echo e($badgeClass); ?>">
    <?php echo e($displayText); ?>

</span><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\components\dashboard\status-badge.blade.php ENDPATH**/ ?>