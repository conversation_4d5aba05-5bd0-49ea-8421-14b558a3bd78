<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo e($title ?? 'eP CRM Integration Dashboard'); ?></title>
    <meta name="robots" content="noindex, nofollow">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        success: '#10B981',
                        warning: '#F59E0B',
                        danger: '#EF4444'
                    }
                }
            }
        }
    </script>
    <style>
        :root {
            --primary: #3B82F6;
            --success: #10B981;
            --warning: #F59E0B;
            --danger: #EF4444;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="min-h-screen">
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-6">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">eP CRM Integration</h1>
                        <p class="text-sm text-gray-500 mt-1">System Dashboard & Monitoring</p>
                    </div>
                    <div class="text-right">
                        <div class="text-sm text-gray-500">Last Updated</div>
                        <div class="text-sm font-medium text-gray-900" id="timestamp"><?php echo e(now()->format('Y-m-d H:i:s T')); ?></div>
                    </div>
                </div>
            </div>
        </header>

        <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <?php echo e($slot); ?>

        </main>
    </div>

    <script>
        // Auto-refresh timestamp every 30 seconds
        setInterval(function() {
            document.getElementById('timestamp').textContent = new Date().toLocaleString();
        }, 30000);
    </script>
</body>
</html><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views/components/dashboard/layout.blade.php ENDPATH**/ ?>