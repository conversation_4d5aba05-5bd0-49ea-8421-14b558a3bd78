<?php
$dashboardService = app(\App\Services\DashboardService::class);
$commands = $dashboardService->getArtisanCommands();
?>

<div class="space-y-6">
    <div class="flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-900">Artisan Console Commands</h3>
        <div class="text-sm text-gray-500">
            Total: <?php echo e(count($commands)); ?> commands
        </div>
    </div>

    <?php if(!empty($commands)): ?>
        <?php if (isset($component)) { $__componentOriginaled257b9288cec3ffd7548707d7eed3eb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginaled257b9288cec3ffd7548707d7eed3eb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.data-table','data' => ['headers' => ['Command', 'Description', 'Category', 'Usage Example'],'rows' => collect($commands)->map(function($command) {
                return [
                    '<div class=\'space-y-1\'>' .
                        '<code class=\'text-sm bg-gray-100 px-2 py-1 rounded block\'>' . e($command['signature']) . '</code>' .
                        (count($command['arguments']) > 0 ? 
                            '<div class=\'text-xs text-gray-500\'>Args: ' . collect($command['arguments'])->pluck('name')->implode(', ') . '</div>' 
                            : ''
                        ) .
                    '</div>',
                    '<span class=\'text-sm text-gray-700\'>' . e($command['description']) . '</span>',
                    '<span class=\'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\'>' . 
                        e($command['category']) . 
                    '</span>',
                    '<div class=\'relative\'>' .
                        '<code class=\'text-xs bg-green-50 px-2 py-1 rounded text-green-700 cursor-pointer\' onclick=\'copyToClipboard(this)\' title=\'Click to copy\'>' . 
                            e($command['usage_example']) . 
                        '</code>' .
                    '</div>'
                ];
            })->toArray()]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.data-table'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['headers' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(['Command', 'Description', 'Category', 'Usage Example']),'rows' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(collect($commands)->map(function($command) {
                return [
                    '<div class=\'space-y-1\'>' .
                        '<code class=\'text-sm bg-gray-100 px-2 py-1 rounded block\'>' . e($command['signature']) . '</code>' .
                        (count($command['arguments']) > 0 ? 
                            '<div class=\'text-xs text-gray-500\'>Args: ' . collect($command['arguments'])->pluck('name')->implode(', ') . '</div>' 
                            : ''
                        ) .
                    '</div>',
                    '<span class=\'text-sm text-gray-700\'>' . e($command['description']) . '</span>',
                    '<span class=\'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\'>' . 
                        e($command['category']) . 
                    '</span>',
                    '<div class=\'relative\'>' .
                        '<code class=\'text-xs bg-green-50 px-2 py-1 rounded text-green-700 cursor-pointer\' onclick=\'copyToClipboard(this)\' title=\'Click to copy\'>' . 
                            e($command['usage_example']) . 
                        '</code>' .
                    '</div>'
                ];
            })->toArray())]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginaled257b9288cec3ffd7548707d7eed3eb)): ?>
<?php $attributes = $__attributesOriginaled257b9288cec3ffd7548707d7eed3eb; ?>
<?php unset($__attributesOriginaled257b9288cec3ffd7548707d7eed3eb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaled257b9288cec3ffd7548707d7eed3eb)): ?>
<?php $component = $__componentOriginaled257b9288cec3ffd7548707d7eed3eb; ?>
<?php unset($__componentOriginaled257b9288cec3ffd7548707d7eed3eb); ?>
<?php endif; ?>
    <?php else: ?>
        <div class="text-center py-12 bg-gray-50 rounded-lg">
            <div class="text-gray-500 mb-2">⚡</div>
            <div class="text-gray-500">No Artisan commands available</div>
        </div>
    <?php endif; ?>

    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
        <h4 class="text-sm font-medium text-green-900 mb-2">How to Use Commands</h4>
        <div class="text-sm text-green-700 space-y-1">
            <p>• Click on any usage example to copy it to your clipboard</p>
            <p>• Run commands from your terminal: <code class="bg-green-100 px-1 rounded">php artisan [command]</code></p>
            <p>• Use <code class="bg-green-100 px-1 rounded">php artisan help [command]</code> for detailed help</p>
        </div>
    </div>
</div>

<script>
function copyToClipboard(element) {
    const text = element.textContent;
    navigator.clipboard.writeText(text).then(function() {
        // Show feedback
        const original = element.textContent;
        element.textContent = 'Copied!';
        element.classList.add('bg-green-200');
        
        setTimeout(function() {
            element.textContent = original;
            element.classList.remove('bg-green-200');
        }, 1000);
    }).catch(function(err) {
        console.error('Could not copy text: ', err);
    });
}
</script><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views/dashboard/tabs/console-commands.blade.php ENDPATH**/ ?>