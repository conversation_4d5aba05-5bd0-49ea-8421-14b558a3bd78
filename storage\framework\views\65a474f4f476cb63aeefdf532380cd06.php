<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['label', 'value', 'description' => null, 'icon' => null]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['label', 'value', 'description' => null, 'icon' => null]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
    <div class="flex items-center">
        <?php if($icon): ?>
            <div class="flex-shrink-0 mr-3">
                <div class="text-lg"><?php echo e($icon); ?></div>
            </div>
        <?php endif; ?>
        <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-gray-600 truncate"><?php echo e($label); ?></p>
            <div class="flex items-baseline">
                <p class="text-xl font-semibold text-gray-900"><?php echo e($value); ?></p>
            </div>
            <?php if($description): ?>
                <p class="text-xs text-gray-500 mt-1"><?php echo e($description); ?></p>
            <?php endif; ?>
        </div>
    </div>
</div><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views/components/dashboard/metric-card.blade.php ENDPATH**/ ?>