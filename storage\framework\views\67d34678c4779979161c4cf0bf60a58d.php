<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['title', 'value', 'description', 'status' => 'neutral', 'icon' => null]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['title', 'value', 'description', 'status' => 'neutral', 'icon' => null]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
$statusClasses = [
    'success' => 'border-green-200 bg-green-50',
    'warning' => 'border-yellow-200 bg-yellow-50', 
    'danger' => 'border-red-200 bg-red-50',
    'neutral' => 'border-gray-200 bg-white'
];

$statusClass = $statusClasses[$status] ?? $statusClasses['neutral'];
?>

<div class="rounded-lg border <?php echo e($statusClass); ?> p-6 shadow-sm transition-all hover:shadow-md">
    <div class="flex items-center justify-between">
        <div class="flex-1">
            <h3 class="text-sm font-medium text-gray-600"><?php echo e($title); ?></h3>
            <div class="mt-2">
                <div class="text-2xl font-bold text-gray-900"><?php echo e($value); ?></div>
                <?php if($description): ?>
                    <p class="text-sm text-gray-500 mt-1"><?php echo e($description); ?></p>
                <?php endif; ?>
            </div>
        </div>
        <?php if($icon): ?>
            <div class="ml-4 flex-shrink-0">
                <div class="text-2xl"><?php echo e($icon); ?></div>
            </div>
        <?php endif; ?>
    </div>
</div><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\components\dashboard\status-card.blade.php ENDPATH**/ ?>