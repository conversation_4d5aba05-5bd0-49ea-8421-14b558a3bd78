<?php
$dashboardService = app(\App\Services\DashboardService::class);
$logs = $dashboardService->getRecentLogs();
?>

<div class="space-y-6">
    <div class="flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-900">Recent System Logs</h3>
        <div class="text-sm text-gray-500">
            Last <?php echo e(count($logs)); ?> entries
        </div>
    </div>

    <!-- Log Level Filter -->
    <div class="flex flex-wrap gap-2">
        <button onclick="filterLogs('all')" class="log-filter-btn active px-3 py-1 text-sm rounded-full border border-gray-300 bg-gray-100 text-gray-700">
            All
        </button>
        <button onclick="filterLogs('ERROR')" class="log-filter-btn px-3 py-1 text-sm rounded-full border border-red-300 bg-red-50 text-red-700">
            Errors
        </button>
        <button onclick="filterLogs('WARNING')" class="log-filter-btn px-3 py-1 text-sm rounded-full border border-yellow-300 bg-yellow-50 text-yellow-700">
            Warnings
        </button>
        <button onclick="filterLogs('INFO')" class="log-filter-btn px-3 py-1 text-sm rounded-full border border-blue-300 bg-blue-50 text-blue-700">
            Info
        </button>
        <button onclick="filterLogs('DEBUG')" class="log-filter-btn px-3 py-1 text-sm rounded-full border border-gray-300 bg-gray-50 text-gray-700">
            Debug
        </button>
    </div>

    <?php if(!empty($logs)): ?>
        <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
            <div class="max-h-96 overflow-y-auto">
                <?php $__currentLoopData = $logs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $log): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php
                    $levelColors = [
                        'ERROR' => 'bg-red-50 border-red-200 text-red-800',
                        'CRITICAL' => 'bg-red-100 border-red-300 text-red-900',
                        'WARNING' => 'bg-yellow-50 border-yellow-200 text-yellow-800',
                        'INFO' => 'bg-blue-50 border-blue-200 text-blue-800',
                        'DEBUG' => 'bg-gray-50 border-gray-200 text-gray-700',
                        'NOTICE' => 'bg-green-50 border-green-200 text-green-800'
                    ];
                    $levelClass = $levelColors[$log['level']] ?? $levelColors['DEBUG'];
                    ?>
                    
                    <div class="log-entry border-b border-gray-100 p-4 hover:bg-gray-50" data-level="<?php echo e($log['level']); ?>">
                        <div class="flex items-start space-x-3">
                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium <?php echo e($levelClass); ?>">
                                <?php echo e($log['level']); ?>

                            </span>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between mb-1">
                                    <span class="text-xs text-gray-500"><?php echo e($log['timestamp']); ?></span>
                                </div>
                                <p class="text-sm text-gray-900 break-words"><?php echo e($log['message']); ?></p>
                                <?php if(strlen($log['full_message']) > 200): ?>
                                    <button 
                                        onclick="toggleFullMessage(this)" 
                                        class="text-xs text-blue-600 hover:text-blue-800 mt-1"
                                    >
                                        Show full message
                                    </button>
                                    <div class="hidden mt-2 text-xs text-gray-600 bg-gray-50 p-2 rounded">
                                        <?php echo e($log['full_message']); ?>

                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    <?php else: ?>
        <div class="text-center py-12 bg-gray-50 rounded-lg">
            <div class="text-gray-500 mb-2">📋</div>
            <div class="text-gray-500">No recent log entries found</div>
            <div class="text-sm text-gray-400 mt-1">Log entries will appear here as they are generated</div>
        </div>
    <?php endif; ?>

    <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
        <h4 class="text-sm font-medium text-purple-900 mb-2">About System Logs</h4>
        <div class="text-sm text-purple-700 space-y-1">
            <p>• Logs are read from <code class="bg-purple-100 px-1 rounded">storage/logs/laravel.log</code></p>
            <p>• Only the most recent 50 entries are displayed</p>
            <p>• Use the filter buttons above to view specific log levels</p>
            <p>• Critical and error logs require immediate attention</p>
        </div>
    </div>
</div>

<script>
function filterLogs(level) {
    // Update button states
    document.querySelectorAll('.log-filter-btn').forEach(btn => {
        btn.classList.remove('active', 'bg-gray-200');
        btn.classList.add('bg-gray-50');
    });
    
    event.target.classList.add('active', 'bg-gray-200');
    event.target.classList.remove('bg-gray-50');
    
    // Filter log entries
    document.querySelectorAll('.log-entry').forEach(entry => {
        if (level === 'all' || entry.dataset.level === level) {
            entry.style.display = 'block';
        } else {
            entry.style.display = 'none';
        }
    });
}

function toggleFullMessage(button) {
    const fullMessageDiv = button.nextElementSibling;
    if (fullMessageDiv.classList.contains('hidden')) {
        fullMessageDiv.classList.remove('hidden');
        button.textContent = 'Hide full message';
    } else {
        fullMessageDiv.classList.add('hidden');
        button.textContent = 'Show full message';
    }
}
</script><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\dashboard\tabs\system-logs.blade.php ENDPATH**/ ?>