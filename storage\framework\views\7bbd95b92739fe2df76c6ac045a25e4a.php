<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['tabs']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['tabs']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <!-- Tab Navigation -->
    <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
            <?php $__currentLoopData = $tabs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $tab): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <button 
                    class="tab-button py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 <?php echo e($index === 0 ? 'border-primary text-primary' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?>"
                    onclick="switchTab(<?php echo e($index); ?>)"
                >
                    <?php echo e($tab['label']); ?>

                </button>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </nav>
    </div>

    <!-- Tab Content -->
    <div class="p-6">
        <?php $__currentLoopData = $tabs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $tab): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div 
                class="tab-content <?php echo e($index === 0 ? 'block' : 'hidden'); ?>" 
                id="tab-<?php echo e($index); ?>"
            >
                <?php echo $tab['content']; ?>

            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</div>

<script>
function switchTab(activeIndex) {
    // Update tab buttons
    const buttons = document.querySelectorAll('.tab-button');
    buttons.forEach((button, index) => {
        if (index === activeIndex) {
            button.classList.remove('border-transparent', 'text-gray-500');
            button.classList.add('border-primary', 'text-primary');
        } else {
            button.classList.remove('border-primary', 'text-primary');
            button.classList.add('border-transparent', 'text-gray-500');
        }
    });

    // Update tab content
    const contents = document.querySelectorAll('.tab-content');
    contents.forEach((content, index) => {
        content.classList.toggle('hidden', index !== activeIndex);
        content.classList.toggle('block', index === activeIndex);
    });

    // Load data for the active tab if needed
    loadTabData(activeIndex);
}

function loadTabData(tabIndex) {
    const endpoints = [
        '/dashboard/scheduled-tasks',
        '/dashboard/console-commands', 
        '/dashboard/database-connections',
        '/dashboard/recent-logs'
    ];

    if (endpoints[tabIndex]) {
        // You can implement AJAX loading here if needed
        // fetch(endpoints[tabIndex]).then(response => response.json()).then(data => { ... });
    }
}
</script><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\components\dashboard\tab-container.blade.php ENDPATH**/ ?>