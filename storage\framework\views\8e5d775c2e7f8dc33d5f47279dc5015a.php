<!DOCTYPE html>
<!--[if IE 8]>         <html class="no-js lt-ie9"> <![endif]-->
<!--[if IE 9]>         <html class="no-js lt-ie10"> <![endif]-->
<!--[if gt IE 9]><!--> <html class="no-js"> <!--<![endif]-->
    <head>
        <meta charset="utf-8">

        <title>eP Support System</title>

        <meta name="description" content="eP Supplier">
        <meta name="author" content="eP Supplier">
        <meta name="robots" content="noindex, nofollow">

        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">

        <!-- Icons -->
        <!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->
        <link rel="shortcut icon" href="/img/favicon.png">
        <link rel="apple-touch-icon" href="/img/icon57.png" sizes="57x57">
        <link rel="apple-touch-icon" href="/img/icon72.png" sizes="72x72">
        <link rel="apple-touch-icon" href="/img/icon76.png" sizes="76x76">
        <link rel="apple-touch-icon" href="/img/icon114.png" sizes="114x114">
        <link rel="apple-touch-icon" href="/img/icon120.png" sizes="120x120">
        <link rel="apple-touch-icon" href="/img/icon144.png" sizes="144x144">
        <link rel="apple-touch-icon" href="/img/icon152.png" sizes="152x152">
        <link rel="apple-touch-icon" href="/img/icon180.png" sizes="180x180">
        <!-- END Icons -->

        <!-- Stylesheets -->
        <!-- Bootstrap is included in its original form, unaltered -->
        <link rel="stylesheet" href="/css/bootstrap.min.css">

        <!-- Related styles of various icon packs and plugins -->
        <link rel="stylesheet" href="https://cdn.datatables.net/1.10.16/css/jquery.dataTables.min.css">
        <link rel="stylesheet" href="https://cdn.datatables.net/buttons/1.5.1/css/buttons.dataTables.min.css">
        
        <!-- Related styles of various icon packs and plugins -->
        <link rel="stylesheet" href="/css/plugins-backend.css">
        



        <!-- The main stylesheet of this template. All Bootstrap overwrites are defined in here -->
        <link rel="stylesheet" href="/css/main-backend.css">

        <!-- Include a specific file here from css/themes/ folder to alter the default theme of the template -->

        <!-- The themes stylesheet of this template (for using specific theme color in individual elements - must included last) -->
        <link rel="stylesheet" href="/css/themes-backend.css">

        <link rel="stylesheet" href="/css/themes/fancy.css">
        <!-- END Stylesheets -->

        <!-- Modernizr (browser feature detection library) & Respond.js (enables responsive CSS code on browsers that don't support it, eg IE8) -->
        <script src="/js/vendor/modernizr-respond.min.js"></script>
    </head>
    <body>
        <!-- Page Wrapper -->
        <!-- In the PHP version you can set the following options from inc/config file -->
        <!--
            Available classes:

            'page-loading'      enables page preloader
        -->
        <div id="page-wrapper">
            <!-- Preloader -->
            <!-- Preloader functionality (initialized in js/app.js) - pageLoading() -->
            <!-- Used only if page preloader is enabled from inc/config (PHP version) or the class 'page-loading' is added in #page-wrapper element (HTML version) -->
            <div class="preloader themed-background">
                <h1 class="push-top-bottom text-light text-center"><strong>Sila Tunggu Sebentar..</strong></h1>
                <div class="inner">
                    <h3 class="text-light visible-lt-ie9 visible-lt-ie10"><strong>Loading..</strong></h3>
                    <div class="preloader-spinner hidden-lt-ie9 hidden-lt-ie10"></div>
                </div>
            </div>
            <!-- END Preloader -->

            <!-- Page Container -->
            <!-- In the PHP version you can set the following options from inc/config file -->
            <!--
                Available #page-container classes:

                '' (None)                                       for a full main and alternative sidebar hidden by default (> 991px)

                'sidebar-visible-lg'                            for a full main sidebar visible by default (> 991px)
                'sidebar-partial'                               for a partial main sidebar which opens on mouse hover, hidden by default (> 991px)
                'sidebar-partial sidebar-visible-lg'            for a partial main sidebar which opens on mouse hover, visible by default (> 991px)
                'sidebar-mini sidebar-visible-lg-mini'          for a mini main sidebar with a flyout menu, enabled by default (> 991px + Best with static layout)
                'sidebar-mini sidebar-visible-lg'               for a mini main sidebar with a flyout menu, disabled by default (> 991px + Best with static layout)

                'sidebar-alt-visible-lg'                        for a full alternative sidebar visible by default (> 991px)
                'sidebar-alt-partial'                           for a partial alternative sidebar which opens on mouse hover, hidden by default (> 991px)
                'sidebar-alt-partial sidebar-alt-visible-lg'    for a partial alternative sidebar which opens on mouse hover, visible by default (> 991px)

                'sidebar-partial sidebar-alt-partial'           for both sidebars partial which open on mouse hover, hidden by default (> 991px)

                'sidebar-no-animations'                         add this as extra for disabling sidebar animations on large screens (> 991px) - Better performance with heavy pages!

                'style-alt'                                     for an alternative main style (without it: the default style)
                'footer-fixed'                                  for a fixed footer (without it: a static footer)

                'disable-menu-autoscroll'                       add this to disable the main menu auto scrolling when opening a submenu

                'header-fixed-top'                              has to be added only if the class 'navbar-fixed-top' was added on header.navbar
                'header-fixed-bottom'                           has to be added only if the class 'navbar-fixed-bottom' was added on header.navbar

                'enable-cookies'                                enables cookies for remembering active color theme when changed from the sidebar links
            -->
            <?php if(Auth::user()): ?>
            <div id="page-container" class="sidebar-no-animations sidebar-visible-lg">
            <?php else: ?>
            <div id="page-container" class="sidebar-no-animations">
            <?php endif; ?>

                <?php if(Auth::user()): ?>
                <!-- Main Sidebar -->
                <div id="sidebar">
                    <!-- Wrapper for scrolling functionality -->
                    <div id="sidebar-scroll">
                        <!-- Sidebar Content -->
                        <div class="sidebar-content">
                            <!-- Brand -->
                            <a href="/home" class="sidebar-brand">
                                <i class="gi gi-bank"></i><span class="sidebar-nav-mini-hide"><strong>eP Support</strong></span>
                            </a>
                            <!-- END Brand -->

                            <!-- User Info -->
                            <div class="sidebar-section sidebar-user clearfix sidebar-nav-mini-hide">

                                <div class="sidebar-user-name"><strong><?php echo e(Auth::user()->first_name); ?></strong></div>
                                
                                    
                                
                                <ul class="sidebar-nav">
                                    <li class="sidebar-header"></span>
                                        <span class="sidebar-header-title">
                                            <?php $__currentLoopData = Auth::user()->roles(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <small><?php echo e($role->name); ?></small><br />
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </span>
                                    </li>
                                </ul>
                                <ul class="sidebar-nav">
                                    <li class="sidebar-header">
                                        <span class="sidebar-header-options clearfix"><a href="<?php echo e(url('/logout')); ?>"
                                                                                         onclick="event.preventDefault();
                                                         document.getElementById('logout-form').submit();"
                                                                                         data-toggle="tooltip" data-placement="bottom" title="Logout"><i class="gi gi-exit"></i></a></span>
                                        <span class="sidebar-header-title"></span>
                                        <form id="logout-form" action="<?php echo e(url('/logout')); ?>" method="POST" style="display: none;">
                                            <?php echo e(csrf_field()); ?>

                                        </form>
                                    </li>
                                </ul>
                            </div>
                            <!-- END User Info -->

                            <!-- Sidebar Navigation -->
                            <ul class="sidebar-nav">
                                <li class="<?php echo e(Request::is('/support/task') ? 'active' : ''); ?>">
                                    <a href="<?php echo e(url("/support/task")); ?>"><i class="fa fa-tasks sidebar-nav-icon"></i>
                                        <span class="sidebar-nav-mini-hide">Tasks</span></a>
                                </li>
                                <li class="<?php echo e(Request::is('/support/task-missing') ? 'active' : ''); ?>">
                                    <a href="<?php echo e(url("/support/task-missing")); ?>"><i class="fa fa-tasks sidebar-nav-icon"></i>
                                        <span class="sidebar-nav-mini-hide">Missing Task List</span></a>
                                </li>
                                <li>
                                    <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
                                        <i class="gi gi-certificate sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Pembekal</span></a>
                                    <ul>
                                        <li>
                                            <a href="<?php echo e(url("/find/mofno")); ?>">Carian MOF No.</a>
                                        </li>
                                        <li>
                                            <a href="<?php echo e(url("/find/epno")); ?>">Carian EP No.</a>
                                        </li>
                                        <li>
                                            <a href="<?php echo e(url("/find/icno")); ?>">Carian IC No.</a>
                                        </li>
                                    </ul>
                                </li>
                                <li>
                                    <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
                                        <i class="gi gi-building sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Organisasi</span></a>
                                    <ul>
                                        <li>
                                            <a href="<?php echo e(url("/find/orgcode")); ?>">Carian Org Code</a>
                                        </li>
                                        <li>
                                            <a href="<?php echo e(url("/find/org/icno")); ?>">Carian IC No.</a>
                                        </li>
                                    </ul>
                                </li>
                                <li>
                                    <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
                                        <i class="fa fa-list sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Item</span></a>
                                    <ul>
                                        <li>
                                            <a href="<?php echo e(url("/find/uom")); ?>">Carian UOM</a>
                                        </li>
                                        <li>
                                            <a href="<?php echo e(url("/find/item")); ?>">Carian Items </a>
                                        </li>
                                        <li>
                                            <a href="<?php echo e(url("/find/items/unspsc")); ?>">Carian UNSPSC Items </a>
                                        </li>
                                        <li>
                                            <a href="<?php echo e(url("/find/items/supplier")); ?>">Carian Item Pembekal </a>
                                        </li>
                                        <li>
                                            <a href="<?php echo e(url("/find/items/codi-task")); ?>">Item Task History </a>
                                        </li>
                                    </ul>
                                </li>
                                <li class="<?php echo e(Request::is('/find/identity') ? 'active' : ''); ?>">
                                    <a href="<?php echo e(url("/find/identity")); ?>/111111002222"><i class="hi hi-user sidebar-nav-icon"></i>
                                        <span class="sidebar-nav-mini-hide">Carian Identity JPN</span></a>
                                </li>
                                
                                <li>
                                    <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
                                        <i class="gi gi-certificate sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Sourcing & Fulfillment</span></a>
                                    <ul>
                                        <li class="<?php echo e(Request::is('/find/trans/docno/') ? 'active' : ''); ?>">
                                            <a href="<?php echo e(url("/find/trans/docno/insert_docno")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                                                <span class="sidebar-nav-mini-hide">Carian Transaksi Doc No.</span></a>
                                        </li>
                                        <li class="<?php echo e(Request::is('/find/trans/track/docno/') ? 'active' : ''); ?>">
                                            <a href="<?php echo e(url("/find/trans/track/docno")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                                                <span class="sidebar-nav-mini-hide">Carian Tracking Diary</span></a>
                                        </li>
                                    </ul>
                                </li>
                                <li>
                                    <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
                                        <i class="gi gi-certificate sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">BPM</span></a>
                                    <ul>
                                        
                                        <li class="">
                                          <a href="<?php echo e(url("/find/bpm/task/docno")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                                              <span class="sidebar-nav-mini-hide">Carian Tugasan</span></a>
                                        </li>
                                        
                                    </ul>
                                </li>
                                <?php if(Auth::user()->isDevUsersEp()): ?>
                                <li class="<?php echo e(Request::is('/report/payment') ? 'active' : ''); ?>">
                                    <a href="<?php echo e(url("/report/payment")); ?>"><i class="fa fa-money sidebar-nav-icon"></i>
                                        <span class="sidebar-nav-mini-hide">Payment Report</span></a>
                                </li>
                                <?php endif; ?>
                                <?php if(Auth::user()->isAdvRolesEp()): ?>
                                <li class="<?php echo e(Request::is('/support/molpay/payment') ? 'active' : ''); ?>">
                                    <a href="<?php echo e(url("/support/molpay/payment")); ?>"><i class="fa fa-cc-visa sidebar-nav-icon"></i>
                                        <span class="sidebar-nav-mini-hide">eP MOLPAY (Payment)</span></a>
                                </li>
                                <li>
                                    <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
                                        <i class="gi gi-settings sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">App scheduler eP</span></a>
                                    <ul>
                                        <li>
                                            <a href="<?php echo e(url("/find/app-scheduler/SIT")); ?>">SIT</a>
                                        </li>
                                         <li>
                                            <a href="<?php echo e(url("/find/app-scheduler/Prod")); ?>">Production</a>
                                        </li>
                                    </ul>
                                </li>
                                <li>
                                    <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
                                        <i class="gi gi-settings sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Integration eP</span></a>
                                    <ul>
                                        <li class="">
                                            <a href="<?php echo e(url("/dashboard/gfmas")); ?>">Dashboard</a>
                                        </li>
                                        
                                        <li class="">
                                            <a href="<?php echo e(url("/find/osb/log")); ?>">OSB Log</a>
                                        </li>
                                        <li class="">
                                            <a href="<?php echo e(url("/find/osb/error")); ?>">OSB Log Error</a>
                                        </li>
                                        <li class="">
                                            <a href="<?php echo e(url("/find/osb/batch/file")); ?>">Batch File Log</a>
                                        </li>
                                        <li>
                                            <a href="#" class="sidebar-nav-submenu"><i class="fa fa-angle-left sidebar-nav-indicator"></i>1GFMAS</a>
                                            <ul>
                                                <li>
                                                    <a href="<?php echo e(url("/dashboard/gfmas")); ?>">Dashboard</a>
                                                </li>
                                                <li>
                                                  <a href="<?php echo e(url("/find/1gfmas/ws")); ?>">Carian Web Service Log</a>
                                                </li>
                                                <li>
                                                  <a href="<?php echo e(url("/find/gfmas/apive")); ?>/insert_epno">Carian 1GFMAS APIVE File</a>
                                                </li>
                                                <li>
                                                    <a href="<?php echo e(url("/trigger/gfmas/apive/")); ?>">APIVE Trigger</a>
                                                </li>
                                                <li>
                                                    <a href="<?php echo e(url("/trigger/gfmas/mminf/")); ?>">MMINF Trigger</a>
                                                </li>
                                                <li>
                                                    <a href="<?php echo e(url("/list/1gfmas/folder")); ?>">Batch Trigger</a>
                                                </li>
                                            </ul>
                                        </li>
                                        <li>
                                            <a href="#" class="sidebar-nav-submenu"><i class="fa fa-angle-left sidebar-nav-indicator"></i>PHIS</a>
                                            <ul>
                                                <li class="">
                                                  <a href="<?php echo e(url("/find/phis/ws")); ?>">Carian Web Service Log</a>
                                                </li>
                                                <li class="">
                                                  <a href="<?php echo e(url("/find/phis/view/")); ?>">Carian Order Details</a>
                                                </li>
                                            </ul>
                                        </li>
                                        
                                    </ul>
                                </li>
                                
                               
                                
                                <li>
                                    <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
                                        <i class="gi gi-certificate sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">QT</span></a>
                                    <ul>

                                        <li class="">
                                            <a href="<?php echo e(url("/find/qt/lawatan")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                                                <span class="sidebar-nav-mini-hide">Taklimat/Lawatan Tapak</span></a>
                                        </li>
                                        <li class="">
                                            <a href="<?php echo e(url("/find/qt/proposal")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                                                <span class="sidebar-nav-mini-hide">Semakan Tawaran</span></a>
                                        </li>
                                        <li class="">
                                            <a href="<?php echo e(url("/find/qt/committee")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                                                <span class="sidebar-nav-mini-hide">Ahli Jawatan Kuasa</span></a>
                                        </li>

                                    </ul>
                                </li>
                                <?php endif; ?>
                            </ul>
                            <!-- END Sidebar Navigation -->



                        </div>
                        <!-- END Sidebar Content -->
                    </div>
                    <!-- END Wrapper for scrolling functionality -->
                </div>
                <!-- END Main Sidebar -->
                <?php endif; ?>
                <!-- Main Container -->
                <div id="main-container">
                    <!-- Header -->
                    <!-- In the PHP version you can set the following options from inc/config file -->
                    <!--
                        Available header.navbar classes:

                        'navbar-default'            for the default light header
                        'navbar-inverse'            for an alternative dark header

                        'navbar-fixed-top'          for a top fixed header (fixed sidebars with scroll will be auto initialized, functionality can be found in js/app.js - handleSidebar())
                            'header-fixed-top'      has to be added on #page-container only if the class 'navbar-fixed-top' was added

                        'navbar-fixed-bottom'       for a bottom fixed header (fixed sidebars with scroll will be auto initialized, functionality can be found in js/app.js - handleSidebar()))
                            'header-fixed-bottom'   has to be added on #page-container only if the class 'navbar-fixed-bottom' was added
                    -->

                    <?php if(Auth::user()): ?>
                    <!-- END Header -->
                        <header class="navbar navbar-default">
                            <!-- Left Header Navigation -->
                            <ul class="nav navbar-nav-custom">
                                <!-- Main Sidebar Toggle Button -->
                                <li>
                                    <a href="javascript:void(0)" onclick="App.sidebar('toggle-sidebar');this.blur();">
                                        <i class="fa fa-bars fa-fw"></i>
                                    </a>
                                </li>
                                <!-- END Main Sidebar Toggle Button -->
                            </ul>
                            <!-- END Left Header Navigation -->

                            <!-- Search Form -->
                            <?php echo $__env->yieldContent('header'); ?>
                            <!-- END Search Form -->

                            <!-- Right Header Navigation -->
                            <ul class="nav navbar-nav-custom pull-right">
                            </ul>
                            <!-- END Right Header Navigation -->
                        </header>
                    <?php endif; ?>

                    <!-- Page content -->
                    <div id="page-content">
                        <?php echo $__env->yieldContent('content'); ?>
                    </div>
                    <!-- END Page Content -->

                    <!-- Footer -->
                    <footer class="clearfix">
                        <div class="pull-right">
                            &nbsp;
                        </div>
                        <div class="pull-left">
                            <span id="year-copy"></span> &copy; <a href="#" target="_blank">Direkacipta oleh Commerce Dot Com Sdn Bhd</a>
                        </div>
                    </footer>
                    <!-- END Footer -->
                </div>
                <!-- END Main Container -->
            </div>
            <!-- END Page Container -->
        </div>
        <!-- END Page Wrapper -->

        <!-- Scroll to top link, initialized in js/app.js - scrollToTop() -->
        <a href="#" id="to-top"><i class="fa fa-angle-double-up"></i></a>


        <!-- jQuery, Bootstrap.js, jQuery plugins and Custom JS code -->
        <script src="/js/vendor/jquery-1.12.0.min.js"></script>
        <script src="/js/vendor/bootstrap.min.js"></script>
        <script src="/js/vendor/defiant.min.js"></script>
        <script src="/js/plugins-backend.js"></script>
        <script src="https://cdn.datatables.net/buttons/1.5.1/js/dataTables.buttons.min.js"></script>
        <script src="https://cdn.datatables.net/buttons/1.5.1/js/buttons.flash.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.32/pdfmake.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.32/vfs_fonts.js"></script>
        <script src="https://cdn.datatables.net/buttons/1.5.1/js/buttons.html5.min.js"></script>
        <script src="https://cdn.datatables.net/buttons/1.5.1/js/buttons.print.min.js"></script>

        <script src="/js/app-backend.js"></script>

        <!-- Load and execute javascript code used only in this page -->
        <?php echo $__env->yieldContent('jsprivate'); ?>


    </body>
</html>
<?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\layouts\guest-dash.blade.php ENDPATH**/ ?>