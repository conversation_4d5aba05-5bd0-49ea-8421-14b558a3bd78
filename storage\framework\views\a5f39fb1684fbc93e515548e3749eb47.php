<?php if (isset($component)) { $__componentOriginalcbd4b493d51bfac881dfb1e0f199e331 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalcbd4b493d51bfac881dfb1e0f199e331 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <!-- System Status Overview - Two Column Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Left Column - System Status Overview -->
        <div class="space-y-6">
            <h2 class="text-xl font-semibold text-gray-900">System Status Overview</h2>
            
            <!-- Environment and Version Info -->
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <?php if (isset($component)) { $__componentOriginalf907dd6429c6d1621252c6797ea40bf4 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf907dd6429c6d1621252c6797ea40bf4 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.status-card','data' => ['title' => 'Environment','value' => strtoupper($systemMetrics['environment']),'description' => 'Application environment','status' => $systemMetrics['environment'] === 'production' ? 'danger' : ($systemMetrics['environment'] === 'staging' ? 'warning' : 'success'),'icon' => '🏗️']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.status-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Environment','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(strtoupper($systemMetrics['environment'])),'description' => 'Application environment','status' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($systemMetrics['environment'] === 'production' ? 'danger' : ($systemMetrics['environment'] === 'staging' ? 'warning' : 'success')),'icon' => '🏗️']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf907dd6429c6d1621252c6797ea40bf4)): ?>
<?php $attributes = $__attributesOriginalf907dd6429c6d1621252c6797ea40bf4; ?>
<?php unset($__attributesOriginalf907dd6429c6d1621252c6797ea40bf4); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf907dd6429c6d1621252c6797ea40bf4)): ?>
<?php $component = $__componentOriginalf907dd6429c6d1621252c6797ea40bf4; ?>
<?php unset($__componentOriginalf907dd6429c6d1621252c6797ea40bf4); ?>
<?php endif; ?>
                
                <?php if (isset($component)) { $__componentOriginalf907dd6429c6d1621252c6797ea40bf4 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf907dd6429c6d1621252c6797ea40bf4 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.status-card','data' => ['title' => 'Laravel Version','value' => $systemMetrics['laravel_version'],'description' => 'Framework version','status' => 'neutral','icon' => '⚡']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.status-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Laravel Version','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($systemMetrics['laravel_version']),'description' => 'Framework version','status' => 'neutral','icon' => '⚡']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf907dd6429c6d1621252c6797ea40bf4)): ?>
<?php $attributes = $__attributesOriginalf907dd6429c6d1621252c6797ea40bf4; ?>
<?php unset($__attributesOriginalf907dd6429c6d1621252c6797ea40bf4); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf907dd6429c6d1621252c6797ea40bf4)): ?>
<?php $component = $__componentOriginalf907dd6429c6d1621252c6797ea40bf4; ?>
<?php unset($__componentOriginalf907dd6429c6d1621252c6797ea40bf4); ?>
<?php endif; ?>
            </div>

            <!-- System Metrics -->
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <?php if (isset($component)) { $__componentOriginalc57c361e10a050969f1a9ef52bca9009 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc57c361e10a050969f1a9ef52bca9009 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.metric-card','data' => ['label' => 'PHP Version','value' => $systemMetrics['php_version'],'icon' => '🐘']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.metric-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'PHP Version','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($systemMetrics['php_version']),'icon' => '🐘']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc57c361e10a050969f1a9ef52bca9009)): ?>
<?php $attributes = $__attributesOriginalc57c361e10a050969f1a9ef52bca9009; ?>
<?php unset($__attributesOriginalc57c361e10a050969f1a9ef52bca9009); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc57c361e10a050969f1a9ef52bca9009)): ?>
<?php $component = $__componentOriginalc57c361e10a050969f1a9ef52bca9009; ?>
<?php unset($__componentOriginalc57c361e10a050969f1a9ef52bca9009); ?>
<?php endif; ?>
                
                <?php if (isset($component)) { $__componentOriginalc57c361e10a050969f1a9ef52bca9009 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc57c361e10a050969f1a9ef52bca9009 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.metric-card','data' => ['label' => 'Memory Usage','value' => $systemMetrics['memory_usage'],'icon' => '💾']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.metric-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Memory Usage','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($systemMetrics['memory_usage']),'icon' => '💾']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc57c361e10a050969f1a9ef52bca9009)): ?>
<?php $attributes = $__attributesOriginalc57c361e10a050969f1a9ef52bca9009; ?>
<?php unset($__attributesOriginalc57c361e10a050969f1a9ef52bca9009); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc57c361e10a050969f1a9ef52bca9009)): ?>
<?php $component = $__componentOriginalc57c361e10a050969f1a9ef52bca9009; ?>
<?php unset($__componentOriginalc57c361e10a050969f1a9ef52bca9009); ?>
<?php endif; ?>
                
                <?php if (isset($component)) { $__componentOriginalc57c361e10a050969f1a9ef52bca9009 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc57c361e10a050969f1a9ef52bca9009 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.metric-card','data' => ['label' => 'Available Disk Space','value' => $systemMetrics['disk_space'],'icon' => '💿']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.metric-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Available Disk Space','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($systemMetrics['disk_space']),'icon' => '💿']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc57c361e10a050969f1a9ef52bca9009)): ?>
<?php $attributes = $__attributesOriginalc57c361e10a050969f1a9ef52bca9009; ?>
<?php unset($__attributesOriginalc57c361e10a050969f1a9ef52bca9009); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc57c361e10a050969f1a9ef52bca9009)): ?>
<?php $component = $__componentOriginalc57c361e10a050969f1a9ef52bca9009; ?>
<?php unset($__componentOriginalc57c361e10a050969f1a9ef52bca9009); ?>
<?php endif; ?>
                
                <?php if (isset($component)) { $__componentOriginalc57c361e10a050969f1a9ef52bca9009 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc57c361e10a050969f1a9ef52bca9009 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.metric-card','data' => ['label' => 'Cache Driver','value' => strtoupper($systemMetrics['cache_driver']),'icon' => '⚡']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.metric-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Cache Driver','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(strtoupper($systemMetrics['cache_driver'])),'icon' => '⚡']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc57c361e10a050969f1a9ef52bca9009)): ?>
<?php $attributes = $__attributesOriginalc57c361e10a050969f1a9ef52bca9009; ?>
<?php unset($__attributesOriginalc57c361e10a050969f1a9ef52bca9009); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc57c361e10a050969f1a9ef52bca9009)): ?>
<?php $component = $__componentOriginalc57c361e10a050969f1a9ef52bca9009; ?>
<?php unset($__componentOriginalc57c361e10a050969f1a9ef52bca9009); ?>
<?php endif; ?>
            </div>

            <!-- Primary Database Status -->
            <?php if (isset($component)) { $__componentOriginalf907dd6429c6d1621252c6797ea40bf4 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf907dd6429c6d1621252c6797ea40bf4 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.status-card','data' => ['title' => 'Primary Database','value' => $systemMetrics['primary_db_status'] === 'connected' ? 'Connected' : 'Disconnected','description' => 'Main database connectivity','status' => $systemMetrics['primary_db_status'] === 'connected' ? 'success' : 'danger','icon' => '🗄️']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.status-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Primary Database','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($systemMetrics['primary_db_status'] === 'connected' ? 'Connected' : 'Disconnected'),'description' => 'Main database connectivity','status' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($systemMetrics['primary_db_status'] === 'connected' ? 'success' : 'danger'),'icon' => '🗄️']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf907dd6429c6d1621252c6797ea40bf4)): ?>
<?php $attributes = $__attributesOriginalf907dd6429c6d1621252c6797ea40bf4; ?>
<?php unset($__attributesOriginalf907dd6429c6d1621252c6797ea40bf4); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf907dd6429c6d1621252c6797ea40bf4)): ?>
<?php $component = $__componentOriginalf907dd6429c6d1621252c6797ea40bf4; ?>
<?php unset($__componentOriginalf907dd6429c6d1621252c6797ea40bf4); ?>
<?php endif; ?>
        </div>

        <!-- Right Column - Database Connections Summary -->
        <div class="space-y-6">
            <h2 class="text-xl font-semibold text-gray-900">Database Connections Summary</h2>
            
            <?php if (isset($component)) { $__componentOriginald252a05a39bd8195e9631cbc0c301848 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald252a05a39bd8195e9631cbc0c301848 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.connection-status','data' => ['connections' => $databaseConnections]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.connection-status'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['connections' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($databaseConnections)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald252a05a39bd8195e9631cbc0c301848)): ?>
<?php $attributes = $__attributesOriginald252a05a39bd8195e9631cbc0c301848; ?>
<?php unset($__attributesOriginald252a05a39bd8195e9631cbc0c301848); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald252a05a39bd8195e9631cbc0c301848)): ?>
<?php $component = $__componentOriginald252a05a39bd8195e9631cbc0c301848; ?>
<?php unset($__componentOriginald252a05a39bd8195e9631cbc0c301848); ?>
<?php endif; ?>
        </div>
    </div>

    <!-- Tab Navigation System -->
    <?php if (isset($component)) { $__componentOriginal735b0dd19d7db504d73627708979372b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal735b0dd19d7db504d73627708979372b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.tab-container','data' => ['tabs' => [
        [
            'label' => 'Scheduled Tasks',
            'content' => view('dashboard.tabs.scheduled-tasks')->render()
        ],
        [
            'label' => 'Console Commands', 
            'content' => view('dashboard.tabs.console-commands')->render()
        ],
        [
            'label' => 'Database Details',
            'content' => view('dashboard.tabs.database-details', ['connections' => $databaseConnections])->render()
        ],
        [
            'label' => 'System Logs',
            'content' => view('dashboard.tabs.system-logs')->render()
        ]
    ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.tab-container'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['tabs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
        [
            'label' => 'Scheduled Tasks',
            'content' => view('dashboard.tabs.scheduled-tasks')->render()
        ],
        [
            'label' => 'Console Commands', 
            'content' => view('dashboard.tabs.console-commands')->render()
        ],
        [
            'label' => 'Database Details',
            'content' => view('dashboard.tabs.database-details', ['connections' => $databaseConnections])->render()
        ],
        [
            'label' => 'System Logs',
            'content' => view('dashboard.tabs.system-logs')->render()
        ]
    ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal735b0dd19d7db504d73627708979372b)): ?>
<?php $attributes = $__attributesOriginal735b0dd19d7db504d73627708979372b; ?>
<?php unset($__attributesOriginal735b0dd19d7db504d73627708979372b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal735b0dd19d7db504d73627708979372b)): ?>
<?php $component = $__componentOriginal735b0dd19d7db504d73627708979372b; ?>
<?php unset($__componentOriginal735b0dd19d7db504d73627708979372b); ?>
<?php endif; ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalcbd4b493d51bfac881dfb1e0f199e331)): ?>
<?php $attributes = $__attributesOriginalcbd4b493d51bfac881dfb1e0f199e331; ?>
<?php unset($__attributesOriginalcbd4b493d51bfac881dfb1e0f199e331); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalcbd4b493d51bfac881dfb1e0f199e331)): ?>
<?php $component = $__componentOriginalcbd4b493d51bfac881dfb1e0f199e331; ?>
<?php unset($__componentOriginalcbd4b493d51bfac881dfb1e0f199e331); ?>
<?php endif; ?><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views/dashboard.blade.php ENDPATH**/ ?>