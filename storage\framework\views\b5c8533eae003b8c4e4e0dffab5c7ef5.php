<?php
$connections = $connections ?? [];
?>

<div class="space-y-6">
    <div class="flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-900">Database Connection Details</h3>
        <div class="text-sm text-gray-500">
            <?php echo e(count($connections)); ?> connections configured
        </div>
    </div>

    <?php if(!empty($connections)): ?>
        <?php if (isset($component)) { $__componentOriginaled257b9288cec3ffd7548707d7eed3eb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginaled257b9288cec3ffd7548707d7eed3eb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.data-table','data' => ['headers' => ['Connection', 'Type', 'Host:Port', 'Database', 'Status', 'Tables', 'Response Time', 'Details'],'rows' => collect($connections)->map(function($conn) {
                $hostPort = $conn['host'];
                if (isset($conn['port']) && $conn['port'] !== 'N/A') {
                    $hostPort .= ':' . $conn['port'];
                }
                
                return [
                    '<div class=\'font-medium text-gray-900\'>' . e($conn['name']) . '</div>',
                    '<span class=\'inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800\'>' . 
                        strtoupper(e($conn['driver'])) . 
                    '</span>',
                    '<code class=\'text-sm\'>' . e($hostPort) . '</code>',
                    '<code class=\'text-sm\'>' . e($conn['database']) . '</code>',
                    '<x-dashboard.status-badge bind:status=\'' . $conn['status'] . '\' />',
                    '<span class=\'text-sm font-medium\'>' . number_format($conn['table_count']) . '</span>',
                    '<span class=\'text-sm ' . ($conn['response_time'] > 1000 ? 'text-red-600' : ($conn['response_time'] > 500 ? 'text-yellow-600' : 'text-green-600')) . '\'>' . 
                        $conn['response_time'] . 'ms' .
                    '</span>',
                    ($conn['error'] ? 
                        '<div class=\'text-xs text-red-600\' title=\'' . e($conn['error']) . '\'>' . 
                            e(Str::limit($conn['error'], 40)) . 
                        '</div>' : 
                        '<span class=\'text-green-600\'>✓</span>'
                    )
                ];
            })->toArray()]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.data-table'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['headers' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(['Connection', 'Type', 'Host:Port', 'Database', 'Status', 'Tables', 'Response Time', 'Details']),'rows' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(collect($connections)->map(function($conn) {
                $hostPort = $conn['host'];
                if (isset($conn['port']) && $conn['port'] !== 'N/A') {
                    $hostPort .= ':' . $conn['port'];
                }
                
                return [
                    '<div class=\'font-medium text-gray-900\'>' . e($conn['name']) . '</div>',
                    '<span class=\'inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800\'>' . 
                        strtoupper(e($conn['driver'])) . 
                    '</span>',
                    '<code class=\'text-sm\'>' . e($hostPort) . '</code>',
                    '<code class=\'text-sm\'>' . e($conn['database']) . '</code>',
                    '<x-dashboard.status-badge bind:status=\'' . $conn['status'] . '\' />',
                    '<span class=\'text-sm font-medium\'>' . number_format($conn['table_count']) . '</span>',
                    '<span class=\'text-sm ' . ($conn['response_time'] > 1000 ? 'text-red-600' : ($conn['response_time'] > 500 ? 'text-yellow-600' : 'text-green-600')) . '\'>' . 
                        $conn['response_time'] . 'ms' .
                    '</span>',
                    ($conn['error'] ? 
                        '<div class=\'text-xs text-red-600\' title=\'' . e($conn['error']) . '\'>' . 
                            e(Str::limit($conn['error'], 40)) . 
                        '</div>' : 
                        '<span class=\'text-green-600\'>✓</span>'
                    )
                ];
            })->toArray())]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginaled257b9288cec3ffd7548707d7eed3eb)): ?>
<?php $attributes = $__attributesOriginaled257b9288cec3ffd7548707d7eed3eb; ?>
<?php unset($__attributesOriginaled257b9288cec3ffd7548707d7eed3eb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaled257b9288cec3ffd7548707d7eed3eb)): ?>
<?php $component = $__componentOriginaled257b9288cec3ffd7548707d7eed3eb; ?>
<?php unset($__componentOriginaled257b9288cec3ffd7548707d7eed3eb); ?>
<?php endif; ?>

        <!-- Connection Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <?php
            $connectedCount = collect($connections)->where('status', 'connected')->count();
            $failedCount = collect($connections)->where('status', 'failed')->count();
            $totalTables = collect($connections)->sum('table_count');
            $avgResponseTime = collect($connections)->where('status', 'connected')->avg('response_time');
            ?>

            <?php if (isset($component)) { $__componentOriginalc57c361e10a050969f1a9ef52bca9009 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc57c361e10a050969f1a9ef52bca9009 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.metric-card','data' => ['label' => 'Connected','value' => $connectedCount . '/' . count($connections),'description' => round(($connectedCount / count($connections)) * 100) . '% success rate','icon' => '✅']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.metric-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Connected','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($connectedCount . '/' . count($connections)),'description' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(round(($connectedCount / count($connections)) * 100) . '% success rate'),'icon' => '✅']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc57c361e10a050969f1a9ef52bca9009)): ?>
<?php $attributes = $__attributesOriginalc57c361e10a050969f1a9ef52bca9009; ?>
<?php unset($__attributesOriginalc57c361e10a050969f1a9ef52bca9009); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc57c361e10a050969f1a9ef52bca9009)): ?>
<?php $component = $__componentOriginalc57c361e10a050969f1a9ef52bca9009; ?>
<?php unset($__componentOriginalc57c361e10a050969f1a9ef52bca9009); ?>
<?php endif; ?>

            <?php if (isset($component)) { $__componentOriginalc57c361e10a050969f1a9ef52bca9009 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc57c361e10a050969f1a9ef52bca9009 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.metric-card','data' => ['label' => 'Failed','value' => $failedCount,'description' => $failedCount > 0 ? 'Needs attention' : 'All healthy','icon' => '❌']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.metric-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Failed','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($failedCount),'description' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($failedCount > 0 ? 'Needs attention' : 'All healthy'),'icon' => '❌']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc57c361e10a050969f1a9ef52bca9009)): ?>
<?php $attributes = $__attributesOriginalc57c361e10a050969f1a9ef52bca9009; ?>
<?php unset($__attributesOriginalc57c361e10a050969f1a9ef52bca9009); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc57c361e10a050969f1a9ef52bca9009)): ?>
<?php $component = $__componentOriginalc57c361e10a050969f1a9ef52bca9009; ?>
<?php unset($__componentOriginalc57c361e10a050969f1a9ef52bca9009); ?>
<?php endif; ?>

            <?php if (isset($component)) { $__componentOriginalc57c361e10a050969f1a9ef52bca9009 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc57c361e10a050969f1a9ef52bca9009 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.metric-card','data' => ['label' => 'Total Tables','value' => number_format($totalTables),'description' => 'Across all databases','icon' => '📊']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.metric-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Total Tables','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(number_format($totalTables)),'description' => 'Across all databases','icon' => '📊']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc57c361e10a050969f1a9ef52bca9009)): ?>
<?php $attributes = $__attributesOriginalc57c361e10a050969f1a9ef52bca9009; ?>
<?php unset($__attributesOriginalc57c361e10a050969f1a9ef52bca9009); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc57c361e10a050969f1a9ef52bca9009)): ?>
<?php $component = $__componentOriginalc57c361e10a050969f1a9ef52bca9009; ?>
<?php unset($__componentOriginalc57c361e10a050969f1a9ef52bca9009); ?>
<?php endif; ?>

            <?php if (isset($component)) { $__componentOriginalc57c361e10a050969f1a9ef52bca9009 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc57c361e10a050969f1a9ef52bca9009 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.metric-card','data' => ['label' => 'Avg Response','value' => round($avgResponseTime, 1) . 'ms','description' => $avgResponseTime < 100 ? 'Excellent' : ($avgResponseTime < 500 ? 'Good' : 'Slow'),'icon' => '⚡']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.metric-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Avg Response','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(round($avgResponseTime, 1) . 'ms'),'description' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($avgResponseTime < 100 ? 'Excellent' : ($avgResponseTime < 500 ? 'Good' : 'Slow')),'icon' => '⚡']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc57c361e10a050969f1a9ef52bca9009)): ?>
<?php $attributes = $__attributesOriginalc57c361e10a050969f1a9ef52bca9009; ?>
<?php unset($__attributesOriginalc57c361e10a050969f1a9ef52bca9009); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc57c361e10a050969f1a9ef52bca9009)): ?>
<?php $component = $__componentOriginalc57c361e10a050969f1a9ef52bca9009; ?>
<?php unset($__componentOriginalc57c361e10a050969f1a9ef52bca9009); ?>
<?php endif; ?>
        </div>
    <?php else: ?>
        <div class="text-center py-12 bg-gray-50 rounded-lg">
            <div class="text-gray-500 mb-2">🗄️</div>
            <div class="text-gray-500">No database connections configured</div>
        </div>
    <?php endif; ?>

    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h4 class="text-sm font-medium text-yellow-900 mb-2">Database Configuration</h4>
        <div class="text-sm text-yellow-700 space-y-1">
            <p>• Database connections are configured in <code class="bg-yellow-100 px-1 rounded">config/database.php</code></p>
            <p>• Connection credentials are stored in environment variables</p>
            <p>• Red status indicates connection failures - check credentials and network connectivity</p>
            <p>• Response times over 500ms may indicate network or performance issues</p>
        </div>
    </div>
</div><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views/dashboard/tabs/database-details.blade.php ENDPATH**/ ?>